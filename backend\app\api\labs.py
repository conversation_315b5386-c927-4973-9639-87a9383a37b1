"""
API calls for dealing with labs

Functions:
    GET /list_users_for_lab #Get all users (+ their roles) that work in the current user's lab
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from ..database import get_db
from ..crud import staff as staff_crud
from ..schemas.user import User
from ..common.constants import UserRole
from ..core.dependencies import enforce_role_selection
from ..core.logging import log_error, log_warning, log_info

router = APIRouter()

@router.get("/") #response_model=List[User])
async def list_users_for_lab(
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Returns all users (and their roles) for the current users lab

    ### Response Model
    - A list of dicts that has 2 items. "user" that holds all info about the user and "role" that holds that users hold

    ### Errors:
    - 403 Forbidden: If the user does not have access to see all the users. 
    - 500 Internal Server Error: If an unexpected error occurs during the retrieval
    """
    try:
        if current_user.role != UserRole.LAB_ADMIN:
            log_warning(f"Access denied in list_users_for_lab: User {current_user.user_id} with role {current_user.role} attempted to list lab users")
            raise PermissionError("Not the correct role")
        
        result = (staff_crud.get_users_in_lab(db, current_user.lab))

        staff = []
        for row in result:
            new_user = User(
                email=row[0].email,
                role=row[1],
                is_active=row[0].is_active,
                lab=current_user.lab,
                user_id = row[0].user_id,
                azure_ad_id = str(row[0].user_id)
            )
            staff.append(new_user)
            
        log_info(f"User {current_user.user_id} listed {len(staff)} lab users")
        return staff
    
    except PermissionError as e:
        log_warning(f"Permission error in list_users_for_lab: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail="Not authorized")
    except Exception as e:
        log_error(f"Unexpected error in list_users_for_lab: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Server Error")