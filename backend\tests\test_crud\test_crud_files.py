"""
Test suite for CRUD operations on File objects.

This class contains tests for the following functions:
- upload_file:
    - test_upload_file_success
    - test_upload_file_db_error
    - test_upload_file_invalid_data
    - test_file_size_limits
    - test_file_name_special_characters
    - test_upload_file_rollback
    - test_file_size_validation
- download_file:
    - test_download_file_access_control
    - test_download_file_not_found
- get_file:
    - test_get_file_admin_access
- publish_file:
    - test_publish_file
    - test_publish_file_non_admin
    - test_publish_file_not_found
    - test_publish_file_db_error
- list_files:
    - test_list_files_with_filters
    - test_list_files_scientist_sees_only_published
    - test_list_files_multiple_filters
    - test_list_files_empty_result
    - test_list_files_invalid_uuid_filter
- delete_file:
    - test_delete_file_success
    - test_delete_file_db_error
- Other:
    - test_file_model_schema
    - test_mock_verification
    - test_concurrent_file_operations
"""

import uuid
import pytest
from sqlalchemy.orm import Session
from app.crud.file import (
    upload_file,
    download_file,
    get_file,
    publish_file,
    list_files,
    delete_file
)
from app.models.file import File
from app.schemas.file import FileCreate
from app.common.constants import UserRole, ERROR_MESSAGES
from app.common.exceptions import NotFoundError, AccessDeniedError, ValidationError
from fastapi import HTTPException
from unittest.mock import patch
from sqlalchemy.exc import SQLAlchemyError

from tests.test_utils.constants import FILE_TEST_CONSTANTS
from tests.test_utils.helpers import TestHelpers
from tests.test_utils.verification import FileVerifier, verify_crud_error_response

class TestFileCRUD:
    @pytest.fixture(autouse=True)
    def setup(self, file_test_setup):
        self.db = file_test_setup["db"]
        self.storage_service = file_test_setup["storage_service"]

    @pytest.mark.asyncio
    async def test_upload_file_success(self, db: Session, test_admin):
        file_data = TestHelpers.Files.create_file_data(
            uploaded_by=test_admin.user_id,
            storage_id=FILE_TEST_CONSTANTS["MOCK_RESPONSES"]["storage_id"]
        )

        file = upload_file(db, file_data, UserRole.LAB_ADMIN)
        
        FileVerifier.verify_common_fields(file.__dict__)
        FileVerifier.verify_response(file.__dict__, {
            "file_name": FILE_TEST_CONSTANTS["TEST_FILES"]["base"]["file_name"],
            "storage_id": FILE_TEST_CONSTANTS["MOCK_RESPONSES"]["storage_id"]
        })

    def test_get_file_admin_access(self, db: Session, test_admin):
        # Create a test file using helper
        file = TestHelpers.Files.create_test_file(db, test_admin.user_id)

        # Admin should be able to access unpublished file
        retrieved_file = get_file(db, file.file_id, UserRole.LAB_ADMIN)
        
        FileVerifier.verify_common_fields(retrieved_file.__dict__)
        FileVerifier.verify_response(retrieved_file.__dict__, {
            "file_id": file.file_id,
            "file_name": file.file_name
        })

    def test_publish_file(self, db: Session, test_admin):
        file = TestHelpers.Files.create_test_file(db, test_admin.user_id)

        # Publish the file
        updated_file = publish_file(db, str(file.file_id), True, UserRole.LAB_ADMIN)
        
        FileVerifier.verify_common_fields(updated_file.__dict__)
        FileVerifier.verify_response(updated_file.__dict__, {
            "file_id": file.file_id,
            "is_published": True
        })

    def test_publish_file_non_admin(self, db: Session, test_user):
        file_id = str(uuid.uuid4())
        with pytest.raises(AccessDeniedError) as exc_info:
            publish_file(db, file_id, True, UserRole.SCIENTIST)
        verify_crud_error_response(exc_info.value, 403, ERROR_MESSAGES['file']['unauthorized_publish'])

    def test_list_files_with_filters(self, db: Session, test_admin, test_requisition):
        # Create test files with different attributes
        files = []
        for i in range(3):
            file = TestHelpers.Files.create_test_file(
                db,
                test_admin.user_id,
                {
                    **FILE_TEST_CONSTANTS["TEST_FILES"]["batch"][i],
                    "requisition_id": test_requisition.req_id if i == 0 else None
                }
            )
            files.append(file)

        # Test filtering by requisition_id
        filtered_files = list_files(
            db, 
            UserRole.LAB_ADMIN,
            requisition_id=str(test_requisition.req_id)
        )
        assert len(filtered_files) == 1
        
        FileVerifier.verify_common_fields(filtered_files[0].__dict__)
        FileVerifier.verify_response(filtered_files[0].__dict__, {
            "requisition_id": str(test_requisition.req_id)
        })

    def test_delete_file_success(self, db: Session, test_admin):
        file = TestHelpers.Files.create_test_file(db, test_admin.user_id)

        # Delete the file
        result = delete_file(db, str(file.file_id), UserRole.LAB_ADMIN)
        
        assert result == {
            "message": ERROR_MESSAGES["file"]["delete_success"],
            "file_id": str(file.file_id)
        }

    @pytest.mark.asyncio
    async def test_download_file_access_control(self, db: Session, test_admin):
        file = TestHelpers.Files.create_test_file(db, test_admin.user_id)

        # Test admin access to unpublished file
        result = download_file(db, str(file.file_id), UserRole.LAB_ADMIN)
        
        FileVerifier.verify_common_fields(result.__dict__)
        FileVerifier.verify_response(result.__dict__, {
            "file_id": str(file.file_id)
        })

        # Test scientist access to unpublished file
        with pytest.raises(AccessDeniedError) as exc_info:
            download_file(db, str(file.file_id), UserRole.SCIENTIST)
        verify_crud_error_response(exc_info.value, 403, ERROR_MESSAGES["file"]["unauthorized_download"])

    def test_download_file_not_found(self, db: Session):
        non_existent_id = str(uuid.uuid4())
        with pytest.raises(NotFoundError) as exc_info:
            download_file(db, non_existent_id, UserRole.LAB_ADMIN)
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["file"]["not_found"])

    def test_list_files_scientist_sees_only_published(self, db: Session, test_admin):
        # Create both published and unpublished files
        files = []
        for i in range(4):
            file_data = FILE_TEST_CONSTANTS["TEST_FILES"]["unpublished"].copy()
            file_data["is_published"] = i % 2 == 0  # alternate between published and unpublished
            file = TestHelpers.Files.create_test_file(db, test_admin.user_id, file_data)
            files.append(file)

        # Scientist should only see published files
        scientist_files = list_files(db, UserRole.SCIENTIST)
        assert len(scientist_files) == 2
        
        # Verify each file in the list
        for file in scientist_files:
            FileVerifier.verify_common_fields(file.__dict__)
            FileVerifier.verify_response(file.__dict__, {
                "is_published": True
            })

        # Admin should see all files
        admin_files = list_files(db, UserRole.LAB_ADMIN)
        assert len(admin_files) == 4
        for file in admin_files:
            FileVerifier.verify_common_fields(file.__dict__)

    def test_upload_file_db_error(self, db: Session, test_admin):
        file_data = TestHelpers.Files.create_file_data(
            uploaded_by=test_admin.user_id
        )
        
        with patch.object(Session, 'commit', side_effect=SQLAlchemyError("Database error")):
            with pytest.raises(ValidationError) as exc_info:
                upload_file(db, file_data, UserRole.LAB_ADMIN)
            verify_crud_error_response(exc_info.value, 400, "Database error")

    def test_upload_file_invalid_data(self, db: Session, test_admin):
        # Test with empty filename
        with pytest.raises(ValueError) as exc_info:
            file_data = TestHelpers.Files.create_file_data(
                file_name="",  # Empty filename
                uploaded_by=test_admin.user_id
            )
            upload_file(db, file_data, UserRole.LAB_ADMIN)

    def test_list_files_multiple_filters(self, db: Session, test_admin, test_requisition):
        # Create files with different combinations of filters
        files = [
            File(
                file_id=uuid.uuid4(),
                file_name=FILE_TEST_CONSTANTS["TEST_FILES"]["base"]["file_name"],
                storage_id=FILE_TEST_CONSTANTS["TEST_FILES"]["base"]["storage_id"],
                uploaded_by=test_admin.user_id,
                is_published=True,
                requisition_id=test_requisition.req_id,
                req_sample_id=test_requisition.samples[0].sample_id if hasattr(test_requisition, 'samples') and test_requisition.samples and test_requisition.samples[0] else None,
                file_type=FILE_TEST_CONSTANTS["TEST_FILES"]["base"]["file_type"],
                file_size=FILE_TEST_CONSTANTS["TEST_FILES"]["base"]["file_size"]
            ),
            File(
                file_id=uuid.uuid4(),
                file_name=FILE_TEST_CONSTANTS["TEST_FILES"]["base"]["file_name"],
                storage_id=FILE_TEST_CONSTANTS["TEST_FILES"]["base"]["storage_id"],
                uploaded_by=test_admin.user_id,
                is_published=True,
                requisition_id=test_requisition.req_id,
                file_type=FILE_TEST_CONSTANTS["TEST_FILES"]["base"]["file_type"],
                file_size=FILE_TEST_CONSTANTS["TEST_FILES"]["base"]["file_size"]
            )
        ]
        
        for file in files:
            db.add(file)
        db.commit()
        
        # Test multiple filter combinations
        result1 = list_files(db, UserRole.LAB_ADMIN, requisition_id=str(test_requisition.req_id))
        assert len(result1) == 2
        for file in result1:
            FileVerifier.verify_common_fields(file.__dict__)
            FileVerifier.verify_response(file.__dict__, {
                "requisition_id": str(test_requisition.req_id)
            })

    def test_publish_file_not_found(self, db: Session):
        non_existent_id = str(uuid.uuid4())
        with pytest.raises(NotFoundError) as exc_info:
            publish_file(db, non_existent_id, True, UserRole.LAB_ADMIN)
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["file"]["not_found"])

    def test_publish_file_db_error(self, db: Session, test_admin):
        file = TestHelpers.Files.create_test_file(db, test_admin.user_id)
        
        with patch.object(Session, 'commit', side_effect=SQLAlchemyError("Database error")):
            with pytest.raises(ValidationError) as exc_info:
                publish_file(db, str(file.file_id), True, UserRole.LAB_ADMIN)
            verify_crud_error_response(exc_info.value, 400, "Database error")

    def test_delete_file_db_error(self, db: Session, test_admin):
        file = TestHelpers.Files.create_test_file(db, test_admin.user_id)
        
        with patch.object(Session, 'commit', side_effect=SQLAlchemyError("Database error")):
            with pytest.raises(ValidationError) as exc_info:
                delete_file(db, str(file.file_id), UserRole.LAB_ADMIN)
            verify_crud_error_response(exc_info.value, 400, "Database error")

    def test_list_files_empty_result(self, db: Session):
        files = list_files(db, UserRole.LAB_ADMIN)
        assert len(files) == 0
        assert isinstance(files, list)

    def test_list_files_invalid_uuid_filter(self, db: Session):
        # The ValueError in list_files function before it hits the database
        try:
            list_files(
                db, 
                UserRole.LAB_ADMIN, 
                requisition_id="invalid-uuid"
            )
            pytest.fail("Should have raised ValueError")
        except (ValueError, SQLAlchemyError) as exc:
            pass 

    def test_file_size_limits(self, db: Session, test_admin):
        """Test file size limits"""
        # Test with very large file size
        large_file = TestHelpers.Files.create_file_data(
            file_size=FILE_TEST_CONSTANTS["FILE_SIZES"]["large"],
            uploaded_by=test_admin.user_id
        )
        
        file1 = upload_file(db, large_file, UserRole.LAB_ADMIN)
        
        FileVerifier.verify_common_fields(file1.__dict__)
        FileVerifier.verify_response(file1.__dict__, {
            "file_size": FILE_TEST_CONSTANTS["FILE_SIZES"]["large"]
        })

        # Test with zero file size
        with pytest.raises(ValueError):
            zero_file = TestHelpers.Files.create_file_data(
                file_size=FILE_TEST_CONSTANTS["FILE_SIZES"]["zero"],
                uploaded_by=test_admin.user_id
            )
            upload_file(db, zero_file, UserRole.LAB_ADMIN)

    def test_concurrent_file_operations(self, db: Session, test_admin):
        file = TestHelpers.Files.create_test_file(db, test_admin.user_id)
        
        # Simulate concurrent operations using context manager
        with patch('sqlalchemy.orm.Session.commit') as mock_commit:
            mock_commit.side_effect = SQLAlchemyError("Deadlock")
            with pytest.raises(ValidationError) as exc_info:
                publish_file(db, str(file.file_id), True, UserRole.LAB_ADMIN)
            verify_crud_error_response(exc_info.value, 400, "Deadlock")

    def test_file_name_special_characters(self, db: Session, test_admin):
        special_chars = FILE_TEST_CONSTANTS["SPECIAL_FILENAMES"]
        
        for file_name in special_chars:
            file_data = TestHelpers.Files.create_file_data(
                file_name=file_name,
                uploaded_by=test_admin.user_id
            )
            
            file = upload_file(db, file_data, UserRole.LAB_ADMIN)
            
            FileVerifier.verify_common_fields(file.__dict__)
            FileVerifier.verify_response(file.__dict__, {
                "file_name": file_name
            })

    def test_file_model_schema(self, db: Session):
        """Test that the File model schema is correctly set up"""
        from sqlalchemy import inspect
        
        inspector = inspect(db.bind)
        columns = {col['name'] for col in inspector.get_columns('files')}
        
        expected_columns = {
            'file_id',
            'requisition_id',
            'req_sample_id',
            'req_sample_test_id',
            'file_name',
            'storage_id',
            'file_type',
            'file_size',
            'uploaded_by',
            'is_published',
            'created_at',
            'updated_at'
        }
        
        missing_columns = expected_columns - columns
        assert not missing_columns, f"Missing columns: {missing_columns}"

    def test_mock_verification(self, mock_storage):
        """Verify that the mock is properly set up"""
        assert hasattr(mock_storage, 'upload_file')
        assert hasattr(mock_storage, 'download_file')
        assert hasattr(mock_storage, 'delete_file')
        assert not mock_storage.upload_file.called

    def test_upload_file_rollback(self, db: Session, test_admin):
        """Test that failed uploads rollback database changes"""
        file_data = TestHelpers.Files.create_file_data(
            uploaded_by=test_admin.user_id
        )
        
        # Count files before
        initial_count = db.query(File).count()
        
        # Force an error during commit using context manager
        with patch('sqlalchemy.orm.Session.commit') as mock_commit:
            mock_commit.side_effect = SQLAlchemyError("Test error")
            with pytest.raises(ValidationError) as exc_info:
                upload_file(db, file_data, UserRole.LAB_ADMIN)
            verify_crud_error_response(exc_info.value, 400, "Test error")
        
        # Verify no files were added
        assert db.query(File).count() == initial_count

    def test_file_size_validation(self, db: Session, test_admin):
        """Test file size validation"""
        test_sizes = FILE_TEST_CONSTANTS["FILE_SIZES"]
        
        # Test with negative file size
        with pytest.raises(ValueError):
            file_data = TestHelpers.Files.create_file_data(
                file_size=test_sizes["negative"],
                uploaded_by=test_admin.user_id
            )
            upload_file(db, file_data, UserRole.LAB_ADMIN)

        # Test with zero file size
        with pytest.raises(ValueError):
            file_data = TestHelpers.Files.create_file_data(
                file_size=test_sizes["zero"],
                uploaded_by=test_admin.user_id
            )
            upload_file(db, file_data, UserRole.LAB_ADMIN)

        # Test with normal file size
        file_data = TestHelpers.Files.create_file_data(
            file_size=test_sizes["normal"],
            uploaded_by=test_admin.user_id
        )
        file = upload_file(db, file_data, UserRole.LAB_ADMIN)
        
        FileVerifier.verify_common_fields(file.__dict__)
        FileVerifier.verify_response(file.__dict__, {
            "file_size": test_sizes["normal"]
        })
