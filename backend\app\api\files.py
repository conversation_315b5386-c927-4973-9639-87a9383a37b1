"""
Work to be done: Check if IDs relate to user's current lab
API endpoints for file operations in the LIMS system.
"""
import json
from uuid import UUID

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    UploadFile,
    Query,
    Request, Form
)
from fastapi.responses import Response
from sqlalchemy.orm import Session
from typing import List, Optional
from ..database import get_db
from ..crud import file as file_crud
from ..schemas.file import File, FileCreate, PublishFileRequest
from ..core.dependencies import enforce_role_selection
from ..schemas.user import User
from ..common.constants import UserRole, ERROR_MESSAGES
from ..services.storage_service import storage_service
from ..common.exceptions import NotFoundError, AccessDeniedError, ValidationError
from ..core.logging import log_error, log_warning, log_info

router = APIRouter()

@router.post("/")
async def upload_file(
    file: UploadFile,
    requisition_id: Optional[UUID] = Form(default=None),
    req_sample_id: Optional[UUID] = Form(default=None),
    req_sample_test_id: Optional[UUID] = Form(default=None),
    current_user: User = Depends(enforce_role_selection),
    db: Session = Depends(get_db)
):
    """
    Upload a file with optional associations to requisition, sample, or test.

    Args:
        file (UploadFile): The file to be uploaded
        requisition_id (UUID, optional): UUID of associated requisition
        req_sample_id (UUID, optional): UUID of associated sample
        req_sample_test_id (UUID, optional): UUID of associated test
        current_user (User): Current authenticated user
        db (Session): Database session dependency

    Returns:
        File: Created file record

    Raises:
        HTTPException: For various error conditions
    """
    try:
        # Check role first before any file operations
        if current_user.role == UserRole.SCIENTIST:
            log_warning(f"Access denied in upload_file: User {current_user.user_id} with role {current_user.role} attempted to upload file")
            raise AccessDeniedError(ERROR_MESSAGES["file"]["unauthorized_upload"])

        file_contents = await file.read()
        storage_id = await storage_service.upload_file(
            file_contents,
            file.filename,
            file.content_type
        )

        try:
            file_create = FileCreate(
                file_name=file.filename,
                storage_id=storage_id,
                file_type=file.content_type,
                file_size=len(file_contents),
                uploaded_by=current_user.user_id,
                requisition_id=requisition_id,
                req_sample_id=req_sample_id,
                req_sample_test_id=req_sample_test_id
            )

            new_file = file_crud.upload_file(db=db, file=file_create, user_role=current_user.role)
            
            log_info(f"User {current_user.user_id} uploaded file {new_file.file_id}")
            
            return new_file
        except Exception as e:
            # If database operation fails, try to clean up the uploaded file
            try:
                await storage_service.delete_file(storage_id)
            except Exception as cleanup_error:
                log_error(f"Failed to clean up storage after failed upload: {cleanup_error}", exc_info=True)
            raise e

    except AccessDeniedError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in upload_file: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log_error(f"Error during file upload: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=ERROR_MESSAGES["file"]["upload_failed"].format(error="An unexpected error occurred"))

@router.get("/list")
async def list_files(
    requisition_id: Optional[UUID] = None,
    req_sample_id: Optional[UUID] = None,
    req_sample_test_id: Optional[UUID] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Get files based on optional filters for requisition, sample, and test.

    Args:
        requisition_id (UUID, optional): Filter by requisition ID
        req_sample_id (UUID, optional): Filter by sample ID
        req_sample_test_id (UUID, optional): Filter by test ID
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        List[File]: List of matching files

    Raises:
        HTTPException: If operation fails
    """
    try:
        files = file_crud.list_files(
            db=db,
            user_role=current_user.role,
            requisition_id=requisition_id,
            req_sample_id=req_sample_id,
            req_sample_test_id=req_sample_test_id
        )
        log_info(f"User {current_user.user_id} listed {len(files)} files")
        return files

    except ValidationError as e:
        log_warning(f"Validation error in list_files: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log_error(f"Error listing files: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=ERROR_MESSAGES["file"]["list_failed"].format(error="An unexpected error occurred"))

@router.get("/{file_id}/download")
async def download_file(
    file_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Download a file by ID.

    Args:
        file_id (UUID): ID of the file to download
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        Response: File content with appropriate headers

    Raises:
        HTTPException: If file not found or operation fails
    """
    try:
        file_record = file_crud.download_file(
            db=db,
            file_id=file_id,
            user_role=current_user.role
        )

        # Get file content from storage service
        try:
            file_content = await storage_service.download_file(file_record.storage_id)
        except Exception as e:
            log_error(f"Storage service error during download: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=ERROR_MESSAGES["file"]["storage_error"].format(error=str(e)))
        
        # Ensure file_content is bytes
        if not isinstance(file_content, bytes):
            file_content = file_content.encode()

        # Create response with file content and appropriate headers
        response = Response(
            content=file_content,
            media_type=file_record.file_type or 'application/octet-stream',
            headers={
                "Content-Disposition": f'attachment; filename="{file_record.file_name}"'
            }
        )
        
        log_info(f"User {current_user.user_id} downloaded file {file_id}")
        
        return response

    except NotFoundError as e:
        log_info(f"File not found in download_file: {str(e)} - Requested ID: {file_id}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in download_file: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in download_file: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log_error(f"Error downloading file: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=ERROR_MESSAGES["file"]["download_failed"].format(error="An unexpected error occurred"))

@router.get("/{file_id}")
async def get_file_metadata(
    file_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Get file metadata by ID.

    Args:
        file_id (UUID): ID of the file to retrieve
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        File: File metadata

    Raises:
        HTTPException: If file not found or operation fails
    """
    try:
        file = file_crud.get_file(db=db, file_id=file_id, user_role=current_user.role)
        return file

    except NotFoundError as e:
        log_info(f"File not found in get_file_metadata: {str(e)} - Requested ID: {file_id}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in get_file_metadata: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        log_error(f"Error retrieving file metadata: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=ERROR_MESSAGES["file"]["metadata_failed"].format(error="An unexpected error occurred"))

@router.put("/{file_id}/publish")
async def publish_file(
    file_id: UUID,
    publish_data: PublishFileRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Publish or unpublish a file.

    Args:
        file_id (UUID): ID of the file to update
        publish_data (PublishFileRequest): Publishing status data
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        File: Updated file record

    Raises:
        HTTPException: If file not found or operation fails
    """
    try:
        updated_file = file_crud.publish_file(
            db=db,
            file_id=file_id,
            is_published=publish_data.is_published,
            user_role=current_user.role
        )
        
        status = "published" if publish_data.is_published else "unpublished"
        log_info(f"User {current_user.user_id} {status} file {file_id}")
        
        return updated_file

    except NotFoundError as e:
        log_info(f"File not found in publish_file: {str(e)} - Requested ID: {file_id}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in publish_file: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in publish_file: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log_error(f"Error publishing file: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=ERROR_MESSAGES["file"]["publish_failed"].format(error="An unexpected error occurred"))

@router.delete("/{file_id}")
async def delete_file(
    file_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Delete a file.

    Args:
        file_id (UUID): ID of the file to delete
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        dict: Success message with file ID

    Raises:
        HTTPException: If file not found or operation fails
    """
    try:
        # Check user permissions first
        if current_user.role == UserRole.SCIENTIST:
            log_warning(f"Unauthorized delete attempt by user {current_user.user_id} with role {current_user.role}")
            raise AccessDeniedError(ERROR_MESSAGES["file"]["unauthorized_delete"])

        # Get file record to confirm it exists
        file = file_crud.get_file(db=db, file_id=file_id, user_role=current_user.role)
        
        # Delete from storage service first
        try:
            await storage_service.delete_file(file.storage_id)
        except Exception as e:
            log_error(f"Storage service error during delete: {str(e)}")
            # Continue with database deletion even if storage deletion fails
            # This is a design decision - we may want to handle this differently later on
            
        # Delete from database
        log_info(f"User {current_user.user_id} deleted file {file_id}")
        result = file_crud.delete_file(db=db, file_id=file_id, user_role=current_user.role)
        return result

    except NotFoundError as e:
        log_info(f"File not found in delete_file: {str(e)} - Requested ID: {file_id}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in delete_file: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in delete_file: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log_error(f"Error deleting file: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=ERROR_MESSAGES["file"]["delete_failed"].format(error="An unexpected error occurred"))
