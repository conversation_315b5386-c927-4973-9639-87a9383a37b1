"""
CRUD functions for relations with the Staff Tables

Functions:
    - get_staff_by_user_id - Get all relations for a certain user
    - get_staff_with_lab_names_by_user_id - Get all relations for a certain user with lab names
    - get_combination - Gets the record with a certain user, lab and role
    - get_users_in_lab - Gets all users that work in a certain lab
    - has_role - Check if a user has a specific role in the given lab    - create_staff - Creates a new staff
    - edit_staff - Edits an existing staff
    - delete_staff - Deletes an existing staff

"""
import uuid
from sqlalchemy.orm import Session
from sqlalchemy import select, join
from ..models.staff import Staff
from ..models.user import User
from ..models.labs import Lab
from ..schemas.staff import Staff<PERSON>reate, StaffType
from ..common.constants import UserRole, ERROR_MESSAGES

from ..core.logging import log_info, log_error, log_warning
from ..common.exceptions import NotFoundError

def get_staff_by_user_id(db: Session, user_id: str):
    """
    Retrieve all records the belongs to a certain user

    - **db**: DB session to query
    - **user_id**: ID of users who's labs/roles we want to get

    Response Model:
    - Returns a list of staff
    """
    log_info(f"Executing query to find all staff records for user: {user_id}")
    try:
        staff_records = db.query(Staff).filter(Staff.user_id == user_id).all()
        log_info(f"Successfully retrieved {len(staff_records)} staff records for user: {user_id}")
        return staff_records
    except Exception as e:
        log_error(f"Error retrieving staff records for user {user_id}: {str(e)}")
        raise

def get_staff_by_staff_id(db: Session, staff_id: str):
    """
    Retrieve record of staff by staff_id

    - **db**: DB session to query
    - **staff_id**: ID of the staff we want to get

    Response Model:
    - Returns a list of staff
    """
    return db.query(Staff).filter(Staff.staff_id == staff_id).first()

def get_staff_by_staff_id(db: Session, staff_id: str):
    """
    Retrieve record of staff by staff_id

    - **db**: DB session to query
    - **staff_id**: ID of the staff we want to get

    Response Model:
    - Returns a list of staff
    """
    return db.query(Staff).filter(Staff.staff_id == staff_id).first()

def get_combination(db: Session, user_id: str, lab_id: str, role: UserRole):
    """
    Retrieve all records that belong to a certain user, with a certain role for a certain lab
    This function is used to check if such combination exists in the DB

    - **db**: DB session to query
    - **user_id**: ID of the user
    - **lab_id**: ID of the lab
    - **role**: Role of the user in the lab

    Response Model:
    - Returns a list of staff
    """
    log_info(f"Executing query to find staff record with user_id: {user_id}, lab_id: {lab_id}, role: {role}")
    try:
        # Build the query based on whether lab_id is None or not
        query = db.query(Staff).filter(Staff.user_id == user_id, Staff.role == role)
        
        if lab_id is None:
            query = query.filter(Staff.lab_id.is_(None))
        else:
            query = query.filter(Staff.lab_id == lab_id)
            
        staff = query.all()
        
        if staff:
            log_info(f"Found staff record for user: {user_id}, lab: {lab_id}, role: {role}")
        else:
            log_warning(f"No staff record found for user: {user_id}, lab: {lab_id}, role: {role}")
        return staff
    except Exception as e:
        log_error(f"Error retrieving staff record for user {user_id}, lab {lab_id}, role {role}: {str(e)}")
        raise

def get_users_in_lab(db: Session, lab_id: str):
    """
    Retrieve all records of users working in a certain lab
    - **db** DB session to query
    - **lab_id**: str of uuid

    Response Model:
    - Returns a list of tuples, each containing a User and a role
    """
    log_info(f"Executing join query to find all users in lab: {lab_id}")
    try:
        user_roles = db.query(User, Staff.role).join(Staff, User.user_id == Staff.user_id).filter(Staff.lab_id == lab_id).all()
        log_info(f"Successfully retrieved {len(user_roles)} users in lab: {lab_id}")
        return user_roles
    except Exception as e:
        log_error(f"Error retrieving users in lab {lab_id}: {str(e)}")
        raise

def has_role(db: Session, user_id: str, lab_id: str, role: UserRole) -> bool:
    """
    Check if a user has a specific role in the given lab
    
    Args:
        db (Session): Database session
        user_id (str): User ID to check
        lab_id (str): Lab ID to check
        role (UserRole): Role to check for
        
    Returns:
        bool: True if user has the role in the lab, False otherwise
    """
    log_info(f"Checking if user: {user_id} has role: {role} in lab: {lab_id}")
    try:
        staff = get_combination(db, user_id, lab_id, role)
        has_role = len(staff) > 0
        log_info(f"User {user_id} has role {role} in lab {lab_id}: {has_role}")
        return has_role
    except Exception as e:
        log_error(f"Error checking role for user {user_id} in lab {lab_id}: {str(e)}")
        return False

def create_staff(db: Session, new_Staff: StaffCreate, lab_id: str):
    """
    Create a new staff in the DB by inputting a role, user_id and lab
    - **db** : DB session to add to
    - **new_Staff**: A StaffCreate object, that has the user_id and role
    - **lab_id** - Lab Id to add the staff to

    Response Model:
    - Returns newly made staff
    """
    # Check if the role is scientist and lab_id is provided
    if new_Staff.role == UserRole.SCIENTIST and lab_id:
        log_error(f"Attempted to create scientist with lab_id: {lab_id}")
        raise ValueError(ERROR_MESSAGES["staff"]["scientist_lab"])
    
    # For scientists, explicitly set lab_id to None
    actual_lab_id = None if new_Staff.role == UserRole.SCIENTIST else lab_id
    
    # For non-scientists, ensure lab_id is provided
    if new_Staff.role != UserRole.SCIENTIST and not actual_lab_id:
        log_error(f"Non-scientist roles must have a lab_id")
        raise ValueError(ERROR_MESSAGES["staff"]["non_scientist_lab"])
    
    # Check if combination exists - for scientists, we need to check with lab_id=None
    combo_exists = get_combination(db, new_Staff.user_id, actual_lab_id, new_Staff.role)
    
    if combo_exists:
        log_error(f"The combination of user id {new_Staff.user_id}, lab id {actual_lab_id}, and role {new_Staff.role} already exists")
        raise ValueError(ERROR_MESSAGES["staff"]["combo_exists"])
    
    try:
        db_staff = Staff(**new_Staff.model_dump(), staff_id=uuid.uuid4(), lab_id=actual_lab_id)

        db.add(db_staff)
        db.commit()
        db.refresh(db_staff)
        return db_staff

    except Exception as e:
        log_error(f"Database Error: {str(e)}")
        db.rollback()
        raise e

def edit_staff(db: Session, edit_staff: StaffType):
    """
    Edit a staff's role
    - **db** : DB session to add to
    - **edit_staff**: A StaffType object that has the staff_id, lab_id, user_id and role

    Response Model:
    - Returns newly editted staff
    """
    #First Lets check if its trying to change into a scientist (impossible)
    if edit_staff.role == UserRole.SCIENTIST:
        log_error(f"Can't change role for {edit_staff.user_id} to scientist")
        raise ValueError(ERROR_MESSAGES["staff"]["scientist_change"])

    # For non-scientists, ensure lab_id is provided
    if edit_staff.role != UserRole.SCIENTIST and not edit_staff.lab_id:
        log_error(f"Non-scientist roles must have a lab_id")
        raise ValueError(ERROR_MESSAGES["staff"]["non_scientist_lab"])

    #Second lets check to see if the combo that we are trying to change into exists
    combo_exists = get_combination(db, edit_staff.user_id, edit_staff.lab_id, edit_staff.role)

    if combo_exists:
        log_error(f"The combination of user id {edit_staff.user_id}, lab id {edit_staff.lab_id}, and role {edit_staff.role} already exists")
        raise ValueError(ERROR_MESSAGES["staff"]["combo_exists"])

    #So now we know this combo doesn't exist
    #Now get the row that we are trying to change (AKA to OG)
    db_staff = db.query(Staff).filter(Staff.staff_id == edit_staff.staff_id).first()

    if not db_staff:
        log_error(f"Staff {edit_staff.staff_id} is not found")
        raise ValueError(ERROR_MESSAGES["staff"]["not_found"])
    
    # Verify that the user_id matches
    if str(db_staff.user_id) != str(edit_staff.user_id):
        log_error(f"Staff {edit_staff.staff_id} does not belong to user {edit_staff.user_id}")
        raise ValueError(ERROR_MESSAGES["staff"]["not_found"])
    
    # Verify that the lab_id matches (if not a scientist)
    if db_staff.lab_id is not None and str(db_staff.lab_id) != str(edit_staff.lab_id):
        log_error(f"Staff {edit_staff.staff_id} does not belong to lab {edit_staff.lab_id}")
        raise ValueError(ERROR_MESSAGES["staff"]["not_found"])
    
    #IFF lab_admin becoming lab_personnel, check to see if an lab_admin still exists
    if db_staff.role == UserRole.LAB_ADMIN and edit_staff.role != UserRole.LAB_ADMIN:
        remaining_admins = db.query(Staff).filter(Staff.staff_id != edit_staff.staff_id, Staff.lab_id == edit_staff.lab_id, Staff.role == UserRole.LAB_ADMIN).count()
        
        if remaining_admins < 1:
            log_error(f"Must have at least 1 lab admin left in lab {edit_staff.lab_id}")
            raise ValueError(ERROR_MESSAGES["staff"]["last_admin"])
    
    #Finally edit the row with the new role and commit
    try: 
        setattr(db_staff, "role", edit_staff.role)

        db.commit()
        db.refresh(db_staff)
        return db_staff
    except Exception as e: 
        log_error(f"Database Error: {str(e)}")
        db.rollback()
        raise e
    
def delete_staff(db: Session, staff_id: str):
    """
    Create a new staff in the DB by inputting a role, user_id and lab
    - **db** : DB session to add to
    - **deleted_staff**: staff_id to be deleted

    Response Model:
    - Returns message saying staff was successful delete
    """
    #Check to see if combo exists
    combo_exists = db.query(Staff).filter(Staff.staff_id == staff_id).first()

    if not combo_exists:
        log_error(f"Staff {staff_id} is not found")
        raise ValueError(ERROR_MESSAGES["staff"]["combo_not_exists"])

    #check to see if an lab admin will still exist
    if combo_exists.role == UserRole.LAB_ADMIN and combo_exists.lab_id is not None:
        remaining_admins = db.query(Staff).filter(
            Staff.staff_id != combo_exists.staff_id, 
            Staff.lab_id == combo_exists.lab_id,  
            Staff.role == UserRole.LAB_ADMIN
        ).count()
        
        if remaining_admins < 1:
            log_error(f"Must have at least 1 lab admin left in lab {combo_exists.lab_id}")
            raise ValueError(ERROR_MESSAGES["staff"]["last_admin"])
    
    #Deletes row
    try:
        db.delete(combo_exists)
        db.commit()
        return {"message": "Staff member deleted successfully"}
    
    except Exception as e:
        log_error(f"Database Error: {str(e)}")
        db.rollback()
        raise e

def get_staff_with_lab_names_by_user_id(db: Session, user_id: str):
    """
    Retrieve all staff records for a user with lab names included

    - **db**: DB session to query
    - **user_id**: ID of users who's labs/roles we want to get

    Response Model:
    - Returns a list of tuples (staff, lab_name) where lab_name is None for scientists
    """
    log_info(f"Executing query to find all staff records with lab names for user: {user_id}")
    try:
        # Join Staff with Lab to get lab names
        query = db.query(Staff, Lab.lab_name).outerjoin(
            Lab, Staff.lab_id == Lab.lab_id
        ).filter(Staff.user_id == user_id)
        
        results = query.all()
        log_info(f"Successfully retrieved {len(results)} staff records with lab names for user: {user_id}")
        return results
    except Exception as e:
        log_error(f"Error retrieving staff records with lab names for user {user_id}: {str(e)}")
        raise
