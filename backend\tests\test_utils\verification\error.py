"""
Error response verification helpers for API response testing.
"""

from typing import Optional, Any, Dict, Union
from fastapi.testclient import TestClient
from requests import Response
from app.common.exceptions import LIMSError


def verify_api_error_response(response: Response, expected_status_code: int, expected_message: Optional[str] = None) -> None:
    """
    Verify an API error response has the expected status code and message.
    
    Args:
        response: The API HTTP response to verify
        expected_status_code: The expected HTTP status code
        expected_message: The expected error message (or None to just verify status code)
    """
    # Always verify the status code
    assert response.status_code == expected_status_code, f"Expected status code {expected_status_code}, got {response.status_code}"
    
    # If no message is expected, we're done
    if expected_message is None:
        return
    
    # Verify the error message in the response
    try:
        response_json = response.json()
        if "detail" in response_json:
            detail = response_json["detail"]
            if isinstance(detail, list):
                # For validation errors, the detail is a list of errors
                found = False
                for error in detail:
                    if "msg" in error and expected_message in error["msg"]:
                        found = True
                        break
                assert found, f"Expected message '{expected_message}' not found in response detail: {detail}"
            else:
                # For simple error messages, the detail is a string
                assert expected_message in detail, f"Expected message '{expected_message}' not found in response detail: {detail}"
        else:
            # If there's no detail field but we expected a message, that's a problem
            assert False, f"Expected message '{expected_message}' but response has no 'detail' field: {response_json}"
    except (ValueError, KeyError) as e:
        # If we can't parse the JSON or find the detail, that's a problem if we expected a message
        assert False, f"Failed to verify error message '{expected_message}'. Error: {str(e)}, Response: {response.text}"


def verify_crud_error_response(exception: LIMSError, expected_status_code: int, expected_message: Optional[str] = None) -> None:
    """
    Verify an exception object has the expected status code and message.
    
    Args:
        exception: The exception object to verify
        expected_status_code: The expected HTTP status code
        expected_message: The expected error message (or None to just verify status code)
    """
    # Always verify the status code
    assert hasattr(exception, 'status_code'), f"Exception {type(exception)} does not have a status_code attribute"
    assert exception.status_code == expected_status_code, f"Expected status code {expected_status_code}, got {exception.status_code}"
    
    # If no message is expected, we're done
    if expected_message is None:
        return
    
    # For exception objects, the message is the string representation
    error_message = str(exception)
    assert expected_message in error_message, f"Expected message '{expected_message}' not found in exception message: {error_message}" 