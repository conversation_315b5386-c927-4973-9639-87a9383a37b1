# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*.pyd
*.pyo
*$py.class
*.py,cover

# C extensions
*.so

# Virtual environments
.venv/
venv/
ENV/
env/
env.bak/
venv.bak/

# Environment and dependency files
.env
!.env.example
.Python
Pipfile.lock
__pypackages__/

# Distribution / packaging
build/
dist/
downloads/
develop-eggs/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Logs
logs/
*.log
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.nosetests.xml
coverage.xml
.hypothesis/
.pytest_cache/
.cache
*.cover

# Documentation
docs/_build/

# IDE and editor-specific files
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Type checking and static analysis
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/

# PyInstaller
*.manifest
*.spec

# Flask and Django
instance/
.webassets-cache
*.sqlite3
*.sqlite3-journal
local_settings.py

# Celery
celerybeat-schedule
celerybeat.pid

# Temporary and backup files
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Docker-related files
docker-compose.override.yml

# Project-specific configurations
.spyderproject
.spyproject
.ropeproject
.pybuilder/
target/
.site
cython_debug/

# Exclude logs and translations
*.pot
*.mo

.vscode