/*
 * Test Configuration
 *
 * 1. FUNCTIONAL SETTINGS - Global technical configurations (form lengths, navigation delays, etc.)
 * 2. UI TEXT - Global UI text with bilingual support (common buttons, navigation, etc.)
 * 3. MESSAGES - Cross-cutting messages organized by type and category
 * 
 */

export const TEST_CONFIGS = {
    // ===== 1. FUNCTIONAL SETTINGS =====
    // Form lengths and navigation delays now use global standards

    // ===== 2. UI TEXT (Bilingual) =====
    // Test UI text organized by component/feature
    ui: {
        // Test type table configurations
        testTypeTable: {
            containerId: 'test-types-table-container',
            tableId: 'test-types-table',
            entityTerms: {
                en: 'test types',
                fr: 'types de tests'
            },
            manage: {
                addButton: {
                    text: { en: 'Add New Test', fr: 'Ajouter un nouveau test' },
                    urlPattern: '/{lang}/create-lab-test.html'
                }
            },

            // Table configuration
            columns: [
                {
                    key: 'name',
                    header: { en: 'Test Name', fr: 'Nom du test' }
                },
                {
                    key: 'description',
                    header: { en: 'Test Description', fr: 'Description du test' }
                },
                {
                    key: 'dateModified',
                    header: { en: 'Date Modified', fr: 'Date de modification' },
                    accessor: (row) => row.updated_at || row.created_at,
                    formatter: 'formatDate'
                },
                {
                    key: 'status',
                    header: { en: 'Status', fr: 'Statut' },
                    field: 'is_active',
                    formatter: 'testStatus'
                }
            ],
            tableOptions: {
                order: [[2, "desc"]]
            },
            // For bulk checkbox tables - replace status column
            bulkCheckboxColumn: {
                key: 'status',
                header: { en: 'Status', fr: 'Statut' },
                accessor: (row) => row,
                formatter: 'testStatusCheckbox'
            }
        },

        // Field terms for global template substitution
        fieldTerms: {
            name: {
                en: 'test name',
                fr: 'nom du test'
            },
            description: {
                en: 'test description',
                fr: 'description du test'
            }
        }
    },

    // ===== 3. MESSAGES (Bilingual) =====
    // Test-specific messages that cannot use global templates
    // Most messages now use global templates:
    // - Required fields: global.messages.errors.templates.fieldRequired (uses {field} placeholder)
    // - Length validation: global.messages.errors.templates.fieldTooLong (uses {field} placeholder)
    // - CRUD operations: global.messages.success/errors.templates.entity* (uses {entity} placeholder)
    // - Duplicate names: global.messages.warnings.templates.duplicateName (uses {entity} placeholder)
    // - Cancelled operations: global.messages.info.common.cancelled
};
