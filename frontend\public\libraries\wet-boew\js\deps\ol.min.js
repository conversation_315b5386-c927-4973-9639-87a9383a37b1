!function(t,e){"object"==typeof exports?module.exports=e():"function"==typeof define&&define.amd?define([],e):t.ol=e()}(this,function(){var k,O,D={},U=this;function t(t,e){var o,i=t.split("."),r=D||U;i[0]in r||!r.execScript||r.execScript("var "+i[0]);for(;i.length&&(o=i.shift());)i.length||void 0===e?r=r[o]||(r[o]={}):r[o]=e}function e(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t}function G(){}function B(t){return t.ao||(t.ao=++K)}var K=0;function X(t){this.message="Assertion failed. See https://openlayers.org/en/v3.20.1/doc/errors/#"+t+" for details.",this.code=t,this.name="AssertionError"}function V(t,e){if(!t)throw new X(e)}function W(t,e,o){return Math.min(Math.max(t,e),o)}e(X,Error);var z="cosh"in Math?Math.cosh:function(t){return((t=Math.exp(t))+1/t)/2};function H(t,e,o,i,r,n){var s,p=r-o,a=n-i;return 0==p&&0==a||(1<(s=((t-o)*p+(e-i)*a)/(p*p+a*a))?(o=r,i=n):0<s&&(o+=p*s,i+=a*s)),Y(t,e,o,i)}function Y(t,e,o,i){return(t=o-t)*t+(e=i-e)*e}function Z(t){return t*Math.PI/180}function q(t,e){t%=e;return t*e<0?t+e:t}function J(t,e,o){return t+o*(e-t)}function _(t){return t}function $(t,e,o){this.center=t,this.resolution=e,this.rotation=o}var Q="function"==typeof Object.assign?Object.assign:function(t,e){if(!t)throw new TypeError("Cannot convert undefined or null to object");for(var o=Object(t),i=1,r=arguments.length;i<r;++i){var n=arguments[i];if(null!=n)for(var s in n)n.hasOwnProperty(s)&&(o[s]=n[s])}return o};function tt(t){for(var e in t)delete t[e]}function et(t){var e,o=[];for(e in t)o.push(t[e]);return o}function ot(t){for(var e in t)return!1;return!e}function it(i){return i.yg=function(t){var e=i.listener,o=i.xg||i.target;return i.zg&&lt(i),e.call(o,t)}}function rt(t,e,o,i){for(var r,n=0,s=t.length;n<s;++n)if((r=t[n]).listener===e&&r.xg===o)return i&&(r.deleteIndex=n),r}function nt(t,e){t=t.eb;return t?t[e]:void 0}function st(t){return t.eb||(t.eb={})}function pt(t,e){var o=nt(t,e);if(o){for(var i=0,r=o.length;i<r;++i)t.removeEventListener(e,o[i].yg),tt(o[i]);o.length=0,(o=t.eb)&&(delete o[e],0===Object.keys(o).length)&&delete t.eb}}function c(t,e,o,i,r){var n=st(t),s=n[e];return(n=rt(s=s||(n[e]=[]),o,i,!1))?r||(n.zg=!1):(t.addEventListener(e,it(n={xg:i,zg:!!r,listener:o,target:t,type:e})),s.push(n)),n}function at(t,e,o,i){return c(t,e,o,i,!0)}function ht(t,e,o,i){(t=nt(t,e))&&(o=rt(t,o,i,!0))&&lt(o)}function lt(t){var e,o;t&&t.target&&(t.target.removeEventListener(t.type,t.yg),(e=nt(t.target,t.type))&&(-1!==(o="deleteIndex"in t?t.deleteIndex:e.indexOf(t))&&e.splice(o,1),0===e.length)&&pt(t.target,t.type),tt(t))}function ut(t){for(var e in st(t))pt(t,e)}function ct(){}function yt(t){t.zb||(t.zb=!0,t.oa())}function ft(t){this.type=t,this.target=null}function gt(t){t.stopPropagation()}function dt(){this.Qa={},this.va={},this.ra={}}function vt(t,e){return e?e in t.ra:0<Object.keys(t.ra).length}function bt(){dt.call(this),this.g=0}function mt(t){if(Array.isArray(t))for(var e=0,o=t.length;e<o;++e)lt(t[e]);else lt(t)}function wt(t){bt.call(this),B(this),this.H={},void 0!==t&&this.I(t)}ct.prototype.zb=!1,ct.prototype.oa=G,ft.prototype.preventDefault=ft.prototype.stopPropagation=function(){this.xo=!0},e(dt,ct),dt.prototype.addEventListener=function(t,e){var o=this.ra[t];-1===(o=o||(this.ra[t]=[])).indexOf(e)&&o.push(e)},dt.prototype.b=function(t){var e,o="string"==typeof t?new ft(t):t,i=(t=o.type,(o.target=this).ra[t]);if(i){t in this.va||(this.va[t]=0,this.Qa[t]=0),++this.va[t];for(var r=0,n=i.length;r<n;++r)if(!1===i[r].call(this,o)||o.xo){e=!1;break}if(--this.va[t],0===this.va[t]){for(o=this.Qa[t],delete this.Qa[t];o--;)this.removeEventListener(t,G);delete this.va[t]}return e}},dt.prototype.oa=function(){ut(this)},dt.prototype.removeEventListener=function(t,e){var o=this.ra[t];o&&(e=o.indexOf(e),t in this.Qa?(o[e]=G,++this.Qa[t]):(o.splice(e,1),0===o.length&&delete this.ra[t]))},e(bt,dt),(d=bt.prototype).s=function(){++this.g,this.b("change")},d.M=function(){return this.g},d.J=function(t,e,o){if(Array.isArray(t)){for(var i=t.length,r=Array(i),n=0;n<i;++n)r[n]=c(this,t[n],e,o);return r}return c(this,t,e,o)},d.N=function(t,e,o){if(Array.isArray(t)){for(var i=t.length,r=Array(i),n=0;n<i;++n)r[n]=at(this,t[n],e,o);return r}return at(this,t,e,o)},d.K=function(t,e,o){if(Array.isArray(t))for(var i=0,r=t.length;i<r;++i)ht(this,t[i],e,o);else ht(this,t,e,o)},d.O=mt,e(wt,bt);var xt={};function St(t){return xt.hasOwnProperty(t)?xt[t]:xt[t]="change:"+t}function Mt(t,e,o){var i=St(e);t.b(new Tt(i,e,o)),t.b(new Tt(Pt,e,o))}(d=wt.prototype).get=function(t){var e;return e=this.H.hasOwnProperty(t)?this.H[t]:e},d.S=function(){return Object.keys(this.H)},d.R=function(){return Q({},this.H)},d.set=function(t,e,o){o?this.H[t]=e:(o=this.H[t])!==(this.H[t]=e)&&Mt(this,t,o)},d.I=function(t,e){for(var o in t)this.set(o,t[o],e)},d.T=function(t,e){var o;t in this.H&&(o=this.H[t],delete this.H[t],e||Mt(this,t,o))};var Pt="propertychange";function Tt(t,e,o){ft.call(this,t),this.key=e,this.oldValue=o}function At(t,e){return e<t?1:t<e?-1:0}function Et(t,e){return 0<=t.indexOf(e)}function Ct(t,e,o){var i=t.length;if(t[0]<=e)return 0;if(!(e<=t[i-1]))if(0<o){for(o=1;o<i;++o)if(t[o]<e)return o-1}else if(o<0){for(o=1;o<i;++o)if(t[o]<=e)return o}else for(o=1;o<i;++o){if(t[o]==e)return o;if(t[o]<e)return t[o-1]-e<e-t[o]?o-1:o}return i-1}function jt(t,e){for(var o=Array.isArray(e)?e:[e],i=o.length,r=0;r<i;r++)t[t.length]=o[r]}function Lt(t,e){for(var o,i=t.length>>>0,r=0;r<i;r++)if(e(o=t[r],r,t))return o;return null}function Rt(t,e){var o=t.length;if(o===e.length){for(var i=0;i<o;i++)if(t[i]!==e[i])return;return 1}}function Nt(t){for(var o=qs,e=t.length,i=Array(t.length),r=0;r<e;r++)i[r]={index:r,value:t[r]};for(i.sort(function(t,e){return o(t.value,e.value)||t.index-e.index}),r=0;r<t.length;r++)t[r]=i[r].value}function It(o,i){var r;return o.every(function(t,e){return!i(t,r=e,o)})?-1:r}function Ft(t){if(void 0!==t)return 0}function kt(t,e){if(void 0!==t)return t+e}function Ot(t,e){e=void 0!==e?t.toFixed(e):""+t,t=e.indexOf(".");return 2<(t=-1===t?e.length:t)?e:Array(3-t).join("0")+e}function Dt(t){t=(""+t).split(".");for(var e=["1","3"],o=0;o<Math.max(t.length,e.length);o++){var i=parseInt(t[o]||"0",10),r=parseInt(e[o]||"0",10);if(r<i)return 1;if(i<r)return-1}return 0}function Bt(t,e){return t[0]+=e[0],t[1]+=e[1],t}function Ut(t,e){var o=t[0],t=t[1],i=e[0],e=e[1],r=i[0],i=i[1],n=e[0],s=n-r,p=(e=e[1])-i;return(o=0==s&&0==p?0:(s*(o-r)+p*(t-i))/(s*s+p*p||0))<=0||(1<=o?(r=n,i=e):(r+=o*s,i+=o*p)),[r,i]}function Gt(t,e,o){t=q(t+180,360)-180;var i=Math.abs(3600*t);return Math.floor(i/3600)+"° "+Ot(Math.floor(i/60%60))+"′ "+Ot(i%60,o||0)+"″ "+e.charAt(t<0?1:0)}function Kt(t,e,o){return t?e.replace("{x}",t[0].toFixed(o)).replace("{y}",t[1].toFixed(o)):""}function Xt(t,e){for(var o=!0,i=t.length-1;0<=i;--i)if(t[i]!=e[i]){o=!1;break}return o}function Vt(t,e){var o=Math.cos(e),e=Math.sin(e),i=t[1]*o+t[0]*e;return t[0]=t[0]*o-t[1]*e,t[1]=i,t}function Wt(t,e){t[0]*=e,t[1]*=e}function zt(t,e){var o=t[0]-e[0],t=t[1]-e[1];return o*o+t*t}function Ht(t,e){return zt(t,Ut(t,e))}function Yt(t,e){return Kt(t,"{x}, {y}",e)}function Zt(t){return Math.pow(t,3)}function qt(t){return 1-Zt(1-t)}function Jt(t){return 3*t*t-2*t*t*t}function _t(t){return t}function $t(t){return t<.5?Jt(2*t):1-Jt(2*(t-.5))}function Qt(t){for(var e=pe(),o=0,i=t.length;o<i;++o)ye(e,t[o]);return e}function te(t,e,o){return o?(o[0]=t[0]-e,o[1]=t[1]-e,o[2]=t[2]+e,o[3]=t[3]+e,o):[t[0]-e,t[1]-e,t[2]+e,t[3]+e]}function ee(t,e){return e?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e):t.slice()}function oe(t,e,o){return(e=e<t[0]?t[0]-e:t[2]<e?e-t[2]:0)*e+(t=o<t[1]?t[1]-o:t[3]<o?o-t[3]:0)*t}function ie(t,e){return ne(t,e[0],e[1])}function re(t,e){return t[0]<=e[0]&&e[2]<=t[2]&&t[1]<=e[1]&&e[3]<=t[3]}function ne(t,e,o){return t[0]<=e&&e<=t[2]&&t[1]<=o&&o<=t[3]}function se(t,e){var o=t[1],i=t[2],r=t[3],n=e[0],e=e[1],s=0;return n<t[0]?s|=16:i<n&&(s|=4),e<o?s|=8:r<e&&(s|=2),s=0===s?1:s}function pe(){return[1/0,1/0,-1/0,-1/0]}function ae(t,e,o,i,r){return r?(r[0]=t,r[1]=e,r[2]=o,r[3]=i,r):[t,e,o,i]}function he(t,e){var o=t[0],t=t[1];return ae(o,t,o,t,e)}function le(t,e,o,i,r){return fe(r=ae(1/0,1/0,-1/0,-1/0,r),t,e,o,i)}function ue(t,e){return t[0]==e[0]&&t[2]==e[2]&&t[1]==e[1]&&t[3]==e[3]}function ce(t,e){return e[0]<t[0]&&(t[0]=e[0]),e[2]>t[2]&&(t[2]=e[2]),e[1]<t[1]&&(t[1]=e[1]),e[3]>t[3]&&(t[3]=e[3]),t}function ye(t,e){e[0]<t[0]&&(t[0]=e[0]),e[0]>t[2]&&(t[2]=e[0]),e[1]<t[1]&&(t[1]=e[1]),e[1]>t[3]&&(t[3]=e[1])}function fe(t,e,o,i,r){for(;o<i;o+=r){var n=t,s=e[o],p=e[o+1];n[0]=Math.min(n[0],s),n[1]=Math.min(n[1],p),n[2]=Math.max(n[2],s),n[3]=Math.max(n[3],p)}return t}function ge(t,e,o){return e.call(o,ve(t))||e.call(o,be(t))||e.call(o,Pe(t))||e.call(o,Me(t))||!1}function de(t){var e=0;return e=Ee(t)?e:Te(t)*xe(t)}function ve(t){return[t[0],t[1]]}function be(t){return[t[2],t[1]]}function me(t){return[(t[0]+t[2])/2,(t[1]+t[3])/2]}function we(t,e,o,i,r){var n=e*i[0]/2,s=(i=e*i[1]/2,e=Math.cos(o),Math.sin(o)),p=(o=n*e,n*=s,e*=i,i*s),a=t[0],h=t[1],p=(t=a-o+p,i=a-o-p,s=a+o-p,o=a+o+p,h-n-e),a=h-n+e,l=h+n+e,n=h+n-e;return ae(Math.min(t,i,s,o),Math.min(p,a,l,n),Math.max(t,i,s,o),Math.max(p,a,l,n),r)}function xe(t){return t[3]-t[1]}function Se(t,e,o){return o=o||pe(),Ae(t,e)&&(o[0]=(t[0]>e[0]?t:e)[0],o[1]=(t[1]>e[1]?t:e)[1],o[2]=(t[2]<e[2]?t:e)[2],o[3]=(t[3]<e[3]?t:e)[3]),o}function Me(t){return[t[0],t[3]]}function Pe(t){return[t[2],t[3]]}function Te(t){return t[2]-t[0]}function Ae(t,e){return t[0]<=e[2]&&t[2]>=e[0]&&t[1]<=e[3]&&t[3]>=e[1]}function Ee(t){return t[2]<t[0]||t[3]<t[1]}function Ce(t,e){var o=(t[2]-t[0])/2*(e-1),e=(t[3]-t[1])/2*(e-1);t[0]-=o,t[2]+=o,t[1]-=e,t[3]+=e}function je(t,e,o){e(t=[t[0],t[1],t[0],t[3],t[2],t[1],t[2],t[3]],t,2);var i=[t[0],t[2],t[4],t[6]],r=[t[1],t[3],t[5],t[7]];return ae(e=Math.min.apply(null,i),t=Math.min.apply(null,r),Math.max.apply(null,i),Math.max.apply(null,r),o)}function Le(){return!0}function Re(){return!1}function Ne(t){this.radius=t}e(Tt,ft),Ne.prototype.a=function(t){for(var e=0,o=t.length,i=t[o-1][0],r=t[o-1][1],n=0;n<o;n++)var s=t[n][0],p=t[n][1],e=e+Z(s-i)*(2+Math.sin(Z(r))+Math.sin(Z(p))),i=s,r=p;return e*this.radius*this.radius/2},Ne.prototype.b=function(t,e){var o=Z(t[1]),i=Z(e[1]),r=(i-o)/2,e=Z(e[0]-t[0])/2,o=Math.sin(r)*Math.sin(r)+Math.sin(e)*Math.sin(e)*Math.cos(o)*Math.cos(i);return 2*this.radius*Math.atan2(Math.sqrt(o),Math.sqrt(1-o))},Ne.prototype.offset=function(t,e,o){var i=Z(t[1]),r=(e/=this.radius,Math.asin(Math.sin(i)*Math.cos(e)+Math.cos(i)*Math.sin(e)*Math.cos(o)));return[180*(Z(t[0])+Math.atan2(Math.sin(o)*Math.sin(e)*Math.cos(i),Math.cos(e)-Math.sin(i)*Math.sin(r)))/Math.PI,180*r/Math.PI]};var Ie=new Ne(6370997),Fe={},ke=(Fe.degrees=2*Math.PI*Ie.radius/360,Fe.ft=.3048,Fe.m=1,Fe["us-ft"]=1200/3937,null);function Oe(t){this.hb=t.code,this.c=t.units,this.f=void 0!==t.extent?t.extent:null,this.i=void 0!==t.worldExtent?t.worldExtent:null,this.b=void 0!==t.axisOrientation?t.axisOrientation:"enu",this.g=void 0!==t.global&&t.global,this.a=!(!this.g||!this.f),this.l=t.getPointResolution,this.j=null,this.o=t.metersPerUnit;var e=t.code,o=ke||window.proj4;"function"==typeof o&&void 0!==(e=o.defs(e))&&(void 0!==e.axis&&void 0===t.axisOrientation&&(this.b=e.axis),void 0===t.metersPerUnit&&(this.o=e.to_meter),void 0===t.units)&&(this.c=e.units)}(d=Oe.prototype).Zj=function(){return this.hb},d.G=function(){return this.f},d.Eb=function(){return this.c},d.ic=function(){return this.o||Fe[this.c]},d.Jk=function(){return this.i},d.rl=function(){return this.g},d.hp=function(t){this.g=t,this.a=!(!t||!this.f)},d.Sm=function(t){this.f=t,this.a=!(!this.g||!t)},d.op=function(t){this.i=t},d.gp=function(t){this.l=t};var De={},Be={};function Ue(t,e,o){t=t.hb,e=e.hb,t in Be||(Be[t]={}),Be[t][e]=o}function Ge(t,e){var o;return o=t in Be&&e in Be[t]?Be[t][e]:o}function Ke(t,e,o){var i=t.l;return i?e=i(e,o):"degrees"!=t.Eb()&&(e=(i=Je(t,Ye("EPSG:4326")))(e=[o[0]-e/2,o[1],o[0]+e/2,o[1],o[0],o[1]-e/2,o[0],o[1]+e/2],e,2),e=(Ie.b(e.slice(0,2),e.slice(2,4))+Ie.b(e.slice(4,6),e.slice(6,8)))/2,void 0!==(t=t.ic()))&&(e/=t),e}function Xe(t){var e;e=[],t.forEach(function(t){e.push(Ve(t))}),t.forEach(function(e){t.forEach(function(t){e!==t&&Ue(e,t,$e)})})}function Ve(t){Ue(De[t.hb]=t,t,$e)}function We(t){return t?"string"==typeof t?Ye(t):t:Ye("EPSG:3857")}function ze(t,e,o,i){Ue(t=Ye(t),e=Ye(e),He(o)),Ue(e,t,He(i))}function He(s){return function(t,e,o){var i,r,n=t.length;for(o=void 0!==o?o:2,e=void 0!==e?e:Array(n),r=0;r<n;r+=o)for(i=s([t[r],t[r+1]]),e[r]=i[0],e[r+1]=i[1],i=o-1;2<=i;--i)e[r+i]=t[r+i];return e}}function Ye(t){var e,o=null;return t instanceof Oe?o=t:"string"==typeof t&&(o=De[t]||null,e=ke||window.proj4,o||"function"!=typeof e||void 0===e.defs(t)||Ve(o=new Oe({code:t}))),o}function Ze(t,e){var o;return t===e||(o=t.Eb()===e.Eb(),(t.hb===e.hb||Je(t,e)===$e)&&o)}function qe(t,e){return Je(Ye(t),Ye(e))}function Je(t,e){var o,i,r,n=t.hb,s=e.hb,p=Ge(n,s);return p||"function"==typeof(o=ke||window.proj4)&&(i=o.defs(n),r=o.defs(s),void 0!==i)&&void 0!==r&&(i===r?Xe([e,t]):ze(e,t,(p=o(s,n)).forward,p.inverse),p=Ge(n,s)),p=p||_e}function _e(t,e){if(void 0!==e&&t!==e){for(var o=0,i=t.length;o<i;++o)e[o]=t[o];t=e}return t}function $e(t,e){if(void 0!==e){for(var o=0,i=t.length;o<i;++o)e[o]=t[o];o=e}else o=t.slice();return o}function Qe(t,e,o){return qe(e,o)(t,void 0,t.length)}function to(t,e,o){return je(t,e=qe(e,o))}function eo(){wt.call(this),this.v=pe(),this.u=-1,this.i={},this.o=this.j=0}function oo(t,e,o,i,r,n){for(var s=n||[],p=0;e<o;e+=i){var a=t[e],h=t[e+1];s[p++]=r[0]*a+r[2]*h+r[4],s[p++]=r[1]*a+r[3]*h+r[5]}return n&&s.length!=p&&(s.length=p),s}function io(t,e,o,i,r,n){for(var s,p=n||[],a=0,h=0;h<e;h+=o)for(p[a++]=t[h]+i,p[a++]=t[h+1]+r,s=h+2;s<h+o;++s)p[a++]=t[s];return n&&p.length!=a&&(p.length=a),p}function b(){eo.call(this),this.ka="XY",this.a=2,this.B=null}function ro(t){var e;return"XY"==t?e=2:"XYZ"==t||"XYM"==t?e=3:"XYZM"==t&&(e=4),e}function no(t,e,o){t.a=ro(e),t.ka=e,t.B=o}function so(t,e,o,i){if(e)o=ro(e);else{for(e=0;e<i;++e){if(0===o.length)return t.ka="XY",void(t.a=2);o=o[0]}var r;2==(o=o.length)?r="XY":3==o?r="XYZ":4==o&&(r="XYZM"),e=r}t.ka=e,t.a=o}function po(t,e,o,i){for(var r=0,n=t[o-i],s=t[o-i+1];e<o;e+=i)var p=t[e],a=t[e+1],r=r+(s*p-n*a),n=p,s=a;return r/2}function ao(t,e,o,i){for(var r=0,n=0,s=o.length;n<s;++n){var p=o[n],r=r+po(t,e,p,i);e=p}return r}function ho(t,e,o,i,r,n,s){var p=t[e],a=t[e+1],h=t[o]-p,l=t[o+1]-a;if(0!=h||0!=l)if(1<(n=((r-p)*h+(n-a)*l)/(h*h+l*l)))e=o;else if(0<n){for(r=0;r<i;++r)s[r]=J(t[e+r],t[o+r],n);return void(s.length=i)}for(r=0;r<i;++r)s[r]=t[e+r];s.length=i}function lo(t,e,o,i,r){var n=t[e],s=t[e+1];for(e+=i;e<o;e+=i){var p=t[e],a=t[e+1];r<(n=Y(n,s,p,a))&&(r=n),n=p,s=a}return r}function uo(t,e,o,i,r){for(var n=0,s=o.length;n<s;++n){var p=o[n];r=lo(t,e,p,i,r),e=p}return r}function co(t,e,o,i,r,n,s,p,a,h,l){var u;if(e!=o)if(0===r){if((u=Y(s,p,t[e],t[e+1]))<h){for(l=0;l<i;++l)a[l]=t[e+l];return a.length=i,u}}else{for(var c=l||[NaN,NaN],y=e+i;y<o;)if(ho(t,y-i,y,i,s,p,c),(u=Y(s,p,c[0],c[1]))<h){for(h=u,l=0;l<i;++l)a[l]=c[l];y+=a.length=i}else y+=i*Math.max((Math.sqrt(u)-Math.sqrt(h))/r|0,1);if(n&&(ho(t,o-i,e,i,s,p,c),(u=Y(s,p,c[0],c[1]))<h)){for(h=u,l=0;l<i;++l)a[l]=c[l];a.length=i}}return h}function yo(t,e,o,i,r,n,s,p,a,h,l){var u,c;for(l=l||[NaN,NaN],u=0,c=o.length;u<c;++u){var y=o[u];h=co(t,e,y,i,r,n,s,p,a,h,l),e=y}return h}function fo(t,e){for(var o=0,i=0,r=e.length;i<r;++i)t[o++]=e[i];return o}function go(t,e,o,i){for(var r=0,n=o.length;r<n;++r)for(var s=o[r],p=0;p<i;++p)t[e++]=s[p];return e}function vo(t,e,o,i,r){r=r||[];for(var n=0,s=0,p=o.length;s<p;++s)e=go(t,e,o[s],i),r[n++]=e;return r.length=n,r}function bo(t,e,o,i,r){r=void 0!==r?r:[];for(var n=0;e<o;e+=i)r[n++]=t.slice(e,e+i);return r.length=n,r}function mo(t,e,o,i,r){r=void 0!==r?r:[];for(var n=0,s=0,p=o.length;s<p;++s){var a=o[s];r[n++]=bo(t,e,a,i,r[n]),e=a}return r.length=n,r}function wo(t,e,o,i,r,n,s){var p=(o-e)/i;if(p<3)for(;e<o;e+=i)n[s++]=t[e],n[s++]=t[e+1];else{var a=Array(p);a[0]=1,a[p-1]=1,o=[e,o-i];for(var h=0;0<o.length;){for(var l=o.pop(),u=o.pop(),c=0,y=t[u],f=t[u+1],g=t[l],d=t[l+1],v=u+i;v<l;v+=i){var b=H(t[v],t[v+1],y,f,g,d);c<b&&(h=v,c=b)}r<c&&(a[(h-e)/i]=1,u+i<h&&o.push(u,h),h+i<l)&&o.push(h,l)}for(v=0;v<p;++v)a[v]&&(n[s++]=t[e+v*i],n[s++]=t[e+v*i+1])}return s}function xo(t,e,o,i,r,n,s,p){for(var a=0,h=o.length;a<h;++a){var l=o[a];t:{var u=t,c=l,y=i,f=r,g=n;if(e!=c){var d,v,b=f*Math.round(u[e]/f),m=f*Math.round(u[e+1]/f);e+=y,g[s++]=b,g[s++]=m;do{if(d=f*Math.round(u[e]/f),v=f*Math.round(u[e+1]/f),(e+=y)==c){g[s++]=d,g[s++]=v;break t}}while(d==b&&v==m);for(;e<c;){var w,x,S,M,P=f*Math.round(u[e]/f),T=f*Math.round(u[e+1]/f);e+=y,P==d&&T==v||((w=d-b)*(M=T-m)==(x=v-m)*(S=P-b)&&(w<0&&S<w||w==S||0<w&&w<S)&&(x<0&&M<x||x==M||0<x&&x<M)||(b=g[s++]=d,m=g[s++]=v),d=P,v=T)}g[s++]=d,g[s++]=v}}p.push(s),e=l}return s}function So(t,e){b.call(this),this.c=this.l=-1,this.qa(t,e)}function Mo(t,e,o){no(t,e,o),t.s()}function m(t,e){b.call(this),this.qa(t,e)}function Po(t,e,o,i,r,n){for(var s=0,p=t[o-i],a=t[o-i+1];e<o;e+=i){var h=t[e],l=t[e+1];a<=n?n<l&&0<(h-p)*(n-a)-(r-p)*(l-a)&&s++:l<=n&&(h-p)*(n-a)-(r-p)*(l-a)<0&&s--,p=h,a=l}return 0!==s}function To(t,e,o,i,r,n){if(0===o.length||!Po(t,e,o[0],i,r,n))return!1;var s;for(e=1,s=o.length;e<s;++e)if(Po(t,o[e-1],o[e],i,r,n))return!1;return!0}function Ao(t,e,o,i,r,n,s){for(var p,a=r[n+1],h=[],l=o[0],u=t[l-i],c=t[l-i+1],y=e;y<l;y+=i)f=t[y],p=t[y+1],(a<=c&&p<=a||c<=a&&a<=p)&&h.push(u=(a-c)/(p-c)*(f-u)+u),u=f,c=p;for(l=NaN,c=-1/0,h.sort(At),u=h[0],y=1,p=h.length;y<p;++y){var f=h[y],g=Math.abs(f-u);c<g&&To(t,e,o,i,u=(u+f)/2,a)&&(l=u,c=g),u=f}return isNaN(l)&&(l=r[n]),s?(s.push(l,a),s):[l,a]}function Eo(t,e,o,i,r,n){for(var s,p=[t[e],t[e+1]],a=[];e+i<o;e+=i){if(a[0]=t[e+i],a[1]=t[e+i+1],s=r.call(n,p,a))return s;p[0]=a[0],p[1]=a[1]}return!1}function Co(t,e,o,i,l){var r=fe(pe(),t,e,o,i);return!!Ae(l,r)&&(!!(re(l,r)||r[0]>=l[0]&&r[2]<=l[2]||r[1]>=l[1]&&r[3]<=l[3])||Eo(t,e,o,i,function(t,e){var o,i,r,n,s,p=!1,a=se(l,t),h=se(l,e);return 1===a||1===h?p=!0:(o=l[0],i=l[1],r=l[2],n=l[3],s=e[0],t=((e=e[1])-t[1])/(s-t[0]),(p=(p=(p=2&h&&!(2&a)?o<=(p=s-(e-n)/t)&&p<=r:p)||!(4&h)||4&a?p:i<=(p=e-(s-r)*t)&&p<=n)||!(8&h)||8&a?p:o<=(p=s-(e-i)/t)&&p<=r)||!(16&h)||16&a||(p=i<=(p=e-(s-o)*t)&&p<=n)),p}))}function jo(t,e,o,i,r){var n=o[0];if(!(Co(t,e,n,i,r)||Po(t,e,n,i,r[0],r[1])||Po(t,e,n,i,r[0],r[3])||Po(t,e,n,i,r[2],r[1])||Po(t,e,n,i,r[2],r[3])))return!1;if(1!==o.length)for(e=1,n=o.length;e<n;++e)if(function(e,o,i,r,t){return!ge(t,function(t){return!Po(e,o,i,r,t[0],t[1])})}(t,o[e-1],o[e],i,r))return!1;return!0}function Lo(t,e,o,i){for(var r=0,n=t[o-i],s=t[o-i+1];e<o;e+=i)var p=t[e],a=t[e+1],r=r+(p-n)*(a+s),n=p,s=a;return 0<r}function Ro(t,e,o,i){var r,n,s=0;for(i=void 0!==i&&i,r=0,n=e.length;r<n;++r){var p=e[r],s=Lo(t,s,p,o);if(0===r){if(i&&s||!i&&!s)return}else if(i&&!s||!i&&s)return;s=p}return 1}function No(t,e,o,i,r){var n,s;for(r=void 0!==r&&r,n=0,s=o.length;n<s;++n){var p=o[n],a=Lo(t,e,p,i);if(0===n?r&&a||!r&&!a:r&&!a||!r&&a)for(var a=t,h=p,l=i;e<h-l;){for(var u=0;u<l;++u){var c=a[e+u];a[e+u]=a[h-l+u],a[h-l+u]=c}e+=l,h-=l}e=p}return e}function Io(t,e,o,i){for(var r=0,n=0,s=e.length;n<s;++n)r=No(t,r,e[n],o,i);return r}function w(t,e){b.call(this),this.c=[],this.A=-1,this.C=null,this.P=this.D=this.L=-1,this.l=null,this.qa(t,e)}function Fo(t){var e;return t.A!=t.g&&(e=me(t.G()),t.C=Ao(t.Vb(),0,t.c,t.a,e,0),t.A=t.g),t.C}function ko(t,e,o,i){var r,n=i||32;for(i=[],r=0;r<n;++r)jt(i,t.offset(e,o,2*Math.PI*r/n));return i.push(i[0],i[1]),(t=new w(null)).da("XY",i,[i.length]),t}function Oo(t){var e=t[0],o=t[1],i=t[2],e=[e,o,e,t=t[3],i,t,i,o,e,o];return(o=new w(null)).da("XY",e,[e.length]),o}function Do(t,e,o){for(var i=e||32,r=t.pa(),n=new w(null,e=t.ka),i=r*(i+1),r=Array(i),s=0;s<i;s++)r[s]=0;return n.da(e,r,[r.length]),Bo(n,t.Fd(),t.qe(),o),n}function Bo(t,e,o,i){var r=t.ia(),n=t.ka,s=t.pa(),p=t.Kb(),a=r.length/s-1;i=i||0;for(var h,l,u=0;u<=a;++u)l=u*s,h=i+2*q(u,a)*Math.PI/a,r[l]=e[0]+o*Math.cos(h),r[1+l]=e[1]+o*Math.sin(h);t.da(n,r,p)}function h(t){wt.call(this),t=t||{},this.i=[0,0],this.f=[],this.Pe=this.Pe.bind(this);var e={};e[Ho]=void 0!==t.center?t.center:null,this.o=We(t.projection);var o,i,r,n,s,p,a,h,l,u,c,y=void 0!==t.minZoom?t.minZoom:0,f=void 0!==t.maxZoom?t.maxZoom:28,g=void 0!==t.zoomFactor?t.zoomFactor:2;f=void 0!==t.resolutions?(o=(f=t.resolutions)[0],i=f[f.length-1],a=f,function(t,e,o){if(void 0!==t)return(t=W((t=Ct(a,t,o))+e,0,a.length-1))!=(e=Math.floor(t))&&e<a.length-1?a[e]/Math.pow(a[e]/a[e+1],t-e):a[e]}):(r=(l=((i=(o=We(t.projection)).G())?Math.max(Te(i),xe(i)):360*Fe.degrees/o.ic())/256/Math.pow(2,0))/Math.pow(2,28),void 0!==(o=t.maxResolution)?y=0:o=l/Math.pow(g,y),void 0===(i=t.minResolution)&&(i=void 0!==t.maxZoom?void 0!==t.maxResolution?o/Math.pow(g,f):l/Math.pow(g,f):r),f=y+Math.floor(Math.log(o/i)/Math.log(g)),i=o/Math.pow(g,f-y),n=g,s=o,p=f-y,function(t,e,o){if(void 0!==t)return t=Math.max(Math.floor(Math.log(s/t)/Math.log(n)+(-o/2+.5))+e,0),void 0!==p&&(t=Math.min(t,p)),s/Math.pow(n,t)}),this.a=o,this.j=i,this.A=g,this.c=t.resolutions,this.l=y,y=void 0!==t.extent?(h=t.extent,function(t){if(t)return[W(t[0],h[0],h[2]),W(t[1],h[1],h[3])]}):_,g=void 0===t.enableRotation||t.enableRotation?void 0===(g=t.constrainRotation)||!0===g?(c=Z(5),function(t,e){if(void 0!==t)return Math.abs(t+e)<=c?0:t+e}):!1!==g&&"number"==typeof g?(l=g,u=2*Math.PI/l,function(t,e){if(void 0!==t)return Math.floor((t+e)/u+.5)*u}):kt:Ft,this.u=new $(y,f,g),void 0!==t.resolution?e[Yo]=t.resolution:void 0!==t.zoom&&(e[Yo]=this.constrainResolution(this.a,t.zoom-this.l)),e[Zo]=void 0!==t.rotation?t.rotation:0,this.I(e)}function Uo(t){zo(t,qo,-Xo(t)[qo]);for(var e=0,o=t.f.length;e<o;++e){var i=t.f[e];i[0].Vc&&i[0].Vc(!1)}t.f.length=0}function Go(t,e,o){var i,r=t.fb();return void 0!==r&&(Vt(i=[r[0]-o[0],r[1]-o[1]],e-t.Ra()),Bt(i,o)),i}function Ko(t,e,o){var i,r=t.fb();return t=t.Oa(),i=void 0!==r&&void 0!==t?[o[0]-e*(o[0]-r[0])/t,o[1]-e*(o[1]-r[1])/t]:i}function Xo(t,e){return void 0!==e?(e[0]=t.i[0],e[1]=t.i[1],e):t.i.slice()}function Vo(t,e){return Math.max(Te(t)/e[0],xe(t)/e[1])}function Wo(t){return t.fb()&&void 0!==t.Oa()}function zo(t,e,o){t.i[e]+=o,t.s()}e(eo,wt),(d=eo.prototype).Cb=function(t,e){e=e||[NaN,NaN];return this.Ab(t[0],t[1],e,1/0),e},d.mb=function(t){return this.Hc(t[0],t[1])},d.Hc=Re,d.G=function(t){this.u!=this.g&&(this.v=this.Yd(this.v),this.u=this.g);var e=this.v;return t?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3]):t=e,t},d.Jb=function(t){return this.Bd(t*t)},d.ob=function(t,e){return this.sc(qe(t,e)),this},e(b,eo),(d=b.prototype).Hc=Re,d.Yd=function(t){return le(this.B,0,this.B.length,this.a,t)},d.Rb=function(){return this.B.slice(0,this.a)},d.ia=function(){return this.B},d.Sb=function(){return this.B.slice(this.B.length-this.a)},d.Tb=function(){return this.ka},d.Bd=function(t){if(this.o!=this.g&&(tt(this.i),this.j=0,this.o=this.g),!(t<0||0!==this.j&&t<=this.j)){var e=t.toString();if(this.i.hasOwnProperty(e))return this.i[e];var o=this.$c(t);if(o.ia().length<this.B.length)return this.i[e]=o;this.j=t}return this},d.$c=function(){return this},d.pa=function(){return this.a},d.sc=function(t){this.B&&(t(this.B,this.B,this.a),this.s())},d.rotate=function(t,e){var o=this.ia();if(o){for(var i=o.length,r=this.pa(),n=o||[],s=Math.cos(t),p=Math.sin(t),a=e[0],h=e[1],l=0,u=0;u<i;u+=r){var c=o[u]-a,y=o[u+1]-h;for(n[l++]=a+c*s-y*p,n[l++]=h+c*p+y*s,c=u+2;c<u+r;++c)n[l++]=o[c]}o&&n.length!=l&&(n.length=l),this.s()}},d.scale=function(t,e,o){var i=void 0===e?t:e;if(p=(p=o)||me(this.G()),o=this.ia()){e=o.length;for(var r=this.pa(),n=o||[],s=p[0],p=p[1],a=0,h=0;h<e;h+=r){var l=o[h]-s,u=o[h+1]-p;for(n[a++]=s+t*l,n[a++]=p+i*u,l=h+2;l<h+r;++l)n[a++]=o[l]}o&&n.length!=a&&(n.length=a),this.s()}},d.translate=function(t,e){var o=this.ia();o&&(io(o,o.length,this.pa(),t,e,o),this.s())},e(So,b),(d=So.prototype).clone=function(){var t=new So(null);return Mo(t,this.ka,this.B.slice()),t},d.Ab=function(t,e,o,i){return i<oe(this.G(),t,e)?i:(this.c!=this.g&&(this.l=Math.sqrt(lo(this.B,0,this.B.length,this.a,0)),this.c=this.g),co(this.B,0,this.B.length,this.a,this.l,!0,t,e,o,i))},d.rm=function(){return po(this.B,0,this.B.length,this.a)},d.$=function(){return bo(this.B,0,this.B.length,this.a)},d.$c=function(t){var e=[];return e.length=wo(this.B,0,this.B.length,this.a,t,e,0),Mo(t=new So(null),"XY",e),t},d.Y=function(){return"LinearRing"},d.qa=function(t,e){t?(so(this,e,t,1),this.B||(this.B=[]),this.B.length=go(this.B,0,t,this.a),this.s()):Mo(this,"XY",null)},e(m,b),(d=m.prototype).clone=function(){var t=new m(null);return t.da(this.ka,this.B.slice()),t},d.Ab=function(t,e,o,i){var r=this.B;if((t=Y(t,e,r[0],r[1]))<i){for(i=this.a,e=0;e<i;++e)o[e]=r[e];return o.length=i,t}return i},d.$=function(){return this.B?this.B.slice():[]},d.Yd=function(t){return he(this.B,t)},d.Y=function(){return"Point"},d.Ta=function(t){return ne(t,this.B[0],this.B[1])},d.qa=function(t,e){t?(so(this,e,t,0),this.B||(this.B=[]),this.B.length=fo(this.B,t),this.s()):this.da("XY",null)},d.da=function(t,e){no(this,t,e),this.s()},e(w,b),(d=w.prototype).Fj=function(t){this.B?jt(this.B,t.ia()):this.B=t.ia().slice(),this.c.push(this.B.length),this.s()},d.clone=function(){var t=new w(null);return t.da(this.ka,this.B.slice(),this.c.slice()),t},d.Ab=function(t,e,o,i){return i<oe(this.G(),t,e)?i:(this.D!=this.g&&(this.L=Math.sqrt(uo(this.B,0,this.c,this.a,0)),this.D=this.g),yo(this.B,0,this.c,this.a,this.L,!0,t,e,o,i))},d.Hc=function(t,e){return To(this.Vb(),0,this.c,this.a,t,e)},d.um=function(){return ao(this.Vb(),0,this.c,this.a)},d.$=function(t){var e;return void 0!==t?No(e=this.Vb().slice(),0,this.c,this.a,t):e=this.B,mo(e,0,this.c,this.a)},d.Kb=function(){return this.c},d.ik=function(){return new m(Fo(this))},d.nk=function(){return this.c.length},d.Qg=function(t){var e;return t<0||this.c.length<=t?null:(Mo(e=new So(null),this.ka,this.B.slice(0===t?0:this.c[t-1],this.c[t])),e)},d.Zc=function(){for(var t=this.ka,e=this.B,o=this.c,i=[],r=0,n=0,s=o.length;n<s;++n){var p=o[n],a=new So(null);Mo(a,t,e.slice(r,p)),i.push(a),r=p}return i},d.Vb=function(){var t;return this.P!=this.g&&(Ro(t=this.B,this.c,this.a)?this.l=t:(this.l=t.slice(),this.l.length=No(this.l,0,this.c,this.a)),this.P=this.g),this.l},d.$c=function(t){var e=[],o=[];return e.length=xo(this.B,0,this.c,this.a,Math.sqrt(t),e,0,o),(t=new w(null)).da("XY",e,o),t},d.Y=function(){return"Polygon"},d.Ta=function(t){return jo(this.Vb(),0,this.c,this.a,t)},d.qa=function(t,e){t?(so(this,e,t,2),this.B||(this.B=[]),e=vo(this.B,0,t,this.a,this.c),this.B.length=0===e.length?0:e[e.length-1],this.s()):this.da("XY",null,this.c)},d.da=function(t,e,o){no(this,t,e),this.c=o,this.s()},e(h,wt),(d=h.prototype).animate=function(t){var e,o=Date.now(),i=this.fb().slice(),r=this.Oa(),n=this.Ra(),s=arguments.length;1<s&&"function"==typeof arguments[s-1]&&(e=arguments[s-1],--s);for(var p=[],a=0;a<s;++a){var h=arguments[a],l={start:o,complete:!1,anchor:h.anchor,duration:void 0!==h.duration?h.duration:1e3,easing:h.easing||Jt};h.center&&(l.fg=i,l.hg=h.center,i=l.hg),void 0!==h.zoom?(l.Me=r,l.Ne=this.constrainResolution(this.a,h.zoom-this.l,0),r=l.Ne):h.resolution&&(l.Me=r,l.Ne=h.resolution,r=l.Ne),void 0!==h.rotation&&(l.gg=n,l.Hi=h.rotation,n=l.Hi),l.Vc=e,o+=l.duration,p.push(l)}this.f.push(p),zo(this,qo,1),this.Pe()},d.Pe=function(){if(void 0!==this.v&&(cancelAnimationFrame(this.v),this.v=void 0),0<Xo(this)[qo]){for(var t=Date.now(),e=!1,o=this.f.length-1;0<=o;--o){for(var i=this.f[o],r=!0,n=0,s=i.length;n<s;++n){var p=i[n];if(!p.complete){var a,h,e=t-p.start;if(1<=(e=0<p.duration?e/p.duration:1)?(p.complete=!0,e=1):r=!1,e=p.easing(e),p.fg&&(a=p.fg[0],h=p.fg[1],this.set(Ho,[a+e*(p.hg[0]-a),h+e*(p.hg[1]-h)])),p.Me&&(a=p.Me+e*(p.Ne-p.Me),p.anchor&&this.set(Ho,Ko(this,a,p.anchor)),this.set(Yo,a)),void 0!==p.gg&&(e=p.gg+e*(p.Hi-p.gg),p.anchor&&this.set(Ho,Go(this,e,p.anchor)),this.set(Zo,e)),e=!0,!p.complete)break}}r&&(this.f[o]=null,zo(this,qo,-1),i=i[0].Vc)&&i(!0)}this.f=this.f.filter(Boolean),e&&void 0===this.v&&(this.v=requestAnimationFrame(this.Pe))}},d.Zd=function(t){return this.u.center(t)},d.constrainResolution=function(t,e,o){return this.u.resolution(t,e||0,o||0)},d.constrainRotation=function(t,e){return this.u.rotation(t,e||0)},d.fb=function(){return this.get(Ho)},d.Uc=function(t){var e=this.fb(),o=(V(e,1),this.Oa()),i=(V(void 0!==o,2),this.Ra());return V(void 0!==i,3),we(e,o,i,t)},d.Zl=function(){return this.a},d.$l=function(){return this.j},d.am=function(){return this.o},d.Oa=function(){return this.get(Yo)},d.bm=function(){return this.c},d.Ra=function(){return this.get(Zo)},d.W=function(){var t=this.fb(),e=this.o,o=this.Oa(),i=this.Ra();return{center:t.slice(),projection:void 0!==e?e:null,resolution:o,rotation:i}},d.Kk=function(){var t=this.Oa();if(void 0!==t&&t>=this.j&&t<=this.a){var e,o,i=this.l||0;if(this.c){if(i+=o=Ct(this.c,t,1),o==this.c.length-1)return i;o=(e=this.c[o])/this.c[o+1]}else e=this.a,o=this.A;i+=Math.log(e/t)/Math.log(o)}return i},d.lf=function(t,e,o){t instanceof b||(V(Array.isArray(t),24),V(!Ee(t),25),t=Oo(t));var i=void 0!==(o=o||{}).padding?o.padding:[0,0,0,0],r=void 0===o.constrainResolution||o.constrainResolution,n=void 0!==o.nearest&&o.nearest,s=void 0!==o.minResolution?o.minResolution:void 0!==o.maxZoom?this.constrainResolution(this.a,o.maxZoom-this.l,0):0,p=t.ia(),a=this.Ra(),h=Math.cos(-a),a=Math.sin(-a),l=1/0,u=1/0,c=-1/0,y=-1/0;t=t.pa();for(var f=0,g=p.length;f<g;f+=t)var d=p[f]*h-p[f+1]*a,v=p[f]*a+p[f+1]*h,l=Math.min(l,d),u=Math.min(u,v),c=Math.max(c,d),y=Math.max(y,v);e=Vo([l,u,c,y],[e[0]-i[1]-i[3],e[1]-i[0]-i[2]]),e=isNaN(e)?s:Math.max(e,s),r&&(s=this.constrainResolution(e,0,0),e=s=!n&&s<e?this.constrainResolution(s,-1,0):s),h=[(n=(l+c)/2+(i[1]-i[3])/2*e)*h-(i=(u+y)/2+(i[0]-i[2])/2*e)*(a=-a),i*h+n*a],void 0!==o.duration?this.animate({resolution:e,center:h,duration:o.duration,easing:o.easing}):(this.Oc(e),this.Mb(h))},d.Kj=function(t,e,o){var i=this.Ra(),r=Math.cos(-i),i=Math.sin(-i),n=t[0]*r-t[1]*i,s=(t=t[1]*r+t[0]*i,this.Oa()),n=n+(e[0]/2-o[0])*s;t+=(o[1]-e[1]/2)*s,this.Mb([n*r-t*(i=-i),t*r+n*i])},d.rotate=function(t,e){void 0!==e&&(e=Go(this,t,e),this.Mb(e)),this.pe(t)},d.Mb=function(t){this.set(Ho,t),0<Xo(this)[qo]&&Uo(this)},d.Oc=function(t){this.set(Yo,t),0<Xo(this)[qo]&&Uo(this)},d.pe=function(t){this.set(Zo,t),0<Xo(this)[qo]&&Uo(this)},d.pp=function(t){t=this.constrainResolution(this.a,t-this.l,0),this.Oc(t)};var Ho="center",Yo="resolution",Zo="rotation",qo=0;function Jo(t,e,o,i){this.ea=t,this.ca=e,this.ga=o,this.ja=i}function _o(t,e,o){return t.ea<=e&&e<=t.ca&&t.ga<=o&&o<=t.ja}function $o(t,e){return t.ea<=e.ca&&t.ca>=e.ea&&t.ga<=e.ja&&t.ja>=e.ga}function Qo(t,e,o){return(o=void 0===o?[0,0]:o)[0]=t[0]+2*e,o[1]=t[1]+2*e,o}function ti(t,e,o){return(o=void 0===o?[0,0]:o)[0]=t[0]*e+.5|0,o[1]=t[1]*e+.5|0,o}function ei(t,e){return Array.isArray(t)?t:(void 0===e?e=[t,t]:e[0]=e[1]=t,e)}function oi(t,e,o,i){return void 0!==i?(i[0]=t,i[1]=e,i[2]=o,i):[t,e,o]}function ii(t){this.minZoom=void 0!==t.minZoom?t.minZoom:0,this.b=t.resolutions,V((o=this.b,i=function(t,e){return e-t}||At,o.every(function(t,e){return 0===e||!(0<(e=i(o[e-1],t))||0===e)})),17),this.maxZoom=this.b.length-1,this.g=void 0!==t.origin?t.origin:null,this.f=null,void 0!==t.origins&&(this.f=t.origins,V(this.f.length==this.b.length,20));var o,i,e=t.extent;if(void 0===e||this.g||this.f||(this.g=Me(e)),V(!this.g&&this.f||this.g&&!this.f,18),this.c=null,void 0!==t.tileSizes&&(this.c=t.tileSizes,V(this.c.length==this.b.length,19)),this.i=void 0!==t.tileSize?t.tileSize:this.c?null:256,V(!this.i&&this.c||this.i&&!this.c,22),this.v=void 0!==e?e:null,this.a=null,this.j=[0,0],void 0!==t.sizes)this.a=t.sizes.map(function(t){return new Jo(Math.min(0,t[0]),Math.max(t[0]-1,-1),Math.min(0,t[1]),Math.max(t[1]-1,-1))},this);else if(e){for(var r=this,n=e,s=r.b.length,p=Array(s),a=r.minZoom;a<s;++a)p[a]=ai(r,n,a);r.a=p}}var ri=[0,0,0];function ni(t,e,o,i,r){for(r=t.Na(e,r),e=e[0]-1;e>=t.minZoom;){if(o.call(null,e,ai(t,r,e,i)))return!0;--e}return!1}function si(t,e,o,i){return e[0]<t.maxZoom?(i=t.Na(e,i),ai(t,i,e[0]+1,o)):null}function pi(t,e,o,i){li(t,e[0],e[1],o,!1,ri);var r=ri[1],n=ri[2];return li(t,e[2],e[3],o,!0,ri),t=ri[1],e=ri[2],void 0!==i?(i.ea=r,i.ca=t,i.ga=n,i.ja=e):i=new Jo(r,t,n,e),i}function ai(t,e,o,i){return o=t.Ha(o),pi(t,e,o,i)}function hi(t,e){var o=t.Kc(e[0]),i=t.Ha(e[0]),t=ei(t.Za(e[0]),t.j);return[o[0]+(e[1]+.5)*t[0]*i,o[1]+(e[2]+.5)*t[1]*i]}function li(t,e,o,i,r,n){var s=t.Ec(i),p=i/t.Ha(s),a=t.Kc(s);return t=ei(t.Za(s),t.j),e=p*Math.floor((e-a[0])/i+(r?.5:0))/t[0],o=p*Math.floor((o-a[1])/i+(r?0:.5))/t[1],o=r?(e=Math.ceil(e)-1,Math.ceil(o)-1):(e=Math.floor(e),Math.floor(o)),oi(s,e,o,n)}function ui(t){var e,o=t.j;return o||(e=yi(o=fi(t),void 0,void 0),o=new ii({extent:o,origin:Me(o),resolutions:e,tileSize:void 0}),t.j=o),o}function ci(t){var e={};return Q(e,void 0!==t?t:{}),void 0===e.extent&&(e.extent=Ye("EPSG:3857").G()),e.resolutions=yi(e.extent,e.maxZoom,e.tileSize),delete e.maxZoom,new ii(e)}function yi(t,e,o){e=void 0!==e?e:42;var i=xe(t);for(t=Te(t),o=ei(void 0!==o?o:256),o=Math.max(t/o[0],i/o[1]),e+=1,i=Array(e),t=0;t<e;++t)i[t]=o/Math.pow(2,t);return i}function fi(t){var e=(t=Ye(t)).G();return e=e?e:ae(-(t=180*Fe.degrees/t.ic()),-t,t,t)}function gi(t){this.b=t.html,this.a=t.tileRanges||null}function di(t){wt.call(this),this.a=t||[],vi(this)}function vi(t){t.set(bi,t.a.length)}(d=ii.prototype).Hg=function(t,e,o){for(var i=(t=ai(this,t,e)).ea,r=t.ca;i<=r;++i)for(var n=t.ga,s=t.ja;n<=s;++n)o([e,i,n])},d.G=function(){return this.v},d.Rg=function(){return this.maxZoom},d.Sg=function(){return this.minZoom},d.Kc=function(t){return this.g||this.f[t]},d.Ha=function(t){return this.b[t]},d.Wh=function(){return this.b},d.Na=function(t,e){var o=this.Kc(t[0]),i=this.Ha(t[0]),r=ei(this.Za(t[0]),this.j),n=o[0]+t[1]*r[0]*i;return ae(n,o=o[1]+t[2]*r[1]*i,n+r[0]*i,o+r[1]*i,e)},d.fe=function(t,e,o){return li(this,t[0],t[1],e,!1,o)},d.wf=function(t,e,o){return e=this.Ha(e),li(this,t[0],t[1],e,!1,o)},d.Za=function(t){return this.i||this.c[t]},d.Ec=function(t,e){return W(Ct(this.b,t,e||0),this.minZoom,this.maxZoom)},gi.prototype.g=function(){return this.b},e(di,wt),(d=di.prototype).clear=function(){for(;0<this.Ub();)this.pop()},d.Bf=function(t){for(var e=0,o=t.length;e<o;++e)this.push(t[e]);return this},d.forEach=function(t,e){this.a.forEach(t,e)},d.Il=function(){return this.a},d.item=function(t){return this.a[t]},d.Ub=function(){return this.get(bi)},d.ke=function(t,e){this.a.splice(t,0,e),vi(this),this.b(new xi(mi,e))},d.pop=function(){return this.Zf(this.Ub()-1)},d.push=function(t){var e=this.Ub();return this.ke(e,t),this.Ub()},d.remove=function(t){for(var e=this.a,o=0,i=e.length;o<i;++o)if(e[o]===t)return this.Zf(o)},d.Zf=function(t){var e=this.a[t];return this.a.splice(t,1),vi(this),this.b(new xi(wi,e)),e},d.ep=function(t,e){var o=this.Ub();if(t<o)o=this.a[t],this.a[t]=e,this.b(new xi(wi,o)),this.b(new xi(mi,e));else{for(;o<t;++o)this.ke(o,void 0);this.ke(t,e)}};var bi="length",mi="add",wi="remove";function xi(t,e){ft.call(this,t),this.element=e}e(xi,ft);var Si=/^#(?:[0-9a-f]{3}){1,2}$/i,Mi=/^([a-z]*)$/i;function Pi(t){return Array.isArray(t)?t:Ci(t)}function Ti(t){var e;return t="string"!=typeof t?"rgba("+(e=(e=t[0])!=(0|e)?e+.5|0:e)+","+(e=(e=t[1])!=(0|e)?e+.5|0:e)+","+(e=(e=t[2])!=(0|e)?e+.5|0:e)+","+(void 0===t[3]?1:t[3])+")":t}Ai={},Ei=0;var Ai,Ei,Ci=function(t){var e,o,i;if(Ai.hasOwnProperty(t))i=Ai[t];else{if(1024<=Ei)for(var r in i=0,Ai)0==(3&i++)&&(delete Ai[r],--Ei);Mi.exec(i=t)&&((r=document.createElement("div")).style.color=i,document.body.appendChild(r),i=getComputedStyle(r).color,document.body.removeChild(r)),Si.exec(i)?(V(3==(o=i.length-1)||6==o,54),e=3==o?1:2,o=parseInt(i.substr(1+0*e,e),16),r=parseInt(i.substr(1+e,e),16),i=parseInt(i.substr(1+2*e,e),16),1==e&&(o=(o<<4)+o,r=(r<<4)+r,i=(i<<4)+i),o=[o,r,i,1]):0==i.indexOf("rgba(")?o=ji(i=i.slice(5,-1).split(",").map(Number)):0==i.indexOf("rgb(")?((i=i.slice(4,-1).split(",").map(Number)).push(1),o=ji(i)):V(!1,14),i=o,Ai[t]=i,++Ei}return i};function ji(t){var e=[];return e[0]=W(t[0]+.5|0,0,255),e[1]=W(t[1]+.5|0,0,255),e[2]=W(t[2]+.5|0,0,255),e[3]=W(t[3],0,1),e}function Li(t){return"string"==typeof t||t instanceof CanvasPattern||t instanceof CanvasGradient?t:Ti(t)}function Ri(t,e){var o=document.createElement("CANVAS");return t&&(o.width=t),e&&(o.height=e),o.getContext("2d")}function Ni(t,e){var o=e.parentNode;o&&o.replaceChild(t,e)}function Ii(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function Fi(t,e,o){ft.call(this,t),this.map=e,this.frameState=void 0!==o?o:null}function ki(t){wt.call(this),this.element=t.element||null,this.a=this.P=null,this.v=[],this.render=t.render||G,t.target&&this.c(t.target)}function Oi(t){t=t||{},this.L=document.createElement("UL"),this.u=document.createElement("LI"),this.L.appendChild(this.u),this.u.style.display="none",this.f=void 0===t.collapsed||t.collapsed,this.l=void 0===t.collapsible||t.collapsible,this.l||(this.f=!1);var e=void 0!==t.className?t.className:"ol-attribution",o=void 0!==t.tipLabel?t.tipLabel:"Attributions",i=void 0!==t.collapseLabel?t.collapseLabel:"»",r=("string"==typeof i?(this.A=document.createElement("span"),this.A.textContent=i):this.A=i,"string"==typeof(i=void 0!==t.label?t.label:"i")?(this.C=document.createElement("span"),this.C.textContent=i):this.C=i,this.l&&!this.f?this.A:this.C);(i=document.createElement("button")).setAttribute("type","button"),i.title=o,i.appendChild(r),c(i,"click",this.em,this),(o=document.createElement("div")).className=e+" ol-unselectable ol-control"+(this.f&&this.l?" ol-collapsed":"")+(this.l?"":" ol-uncollapsible"),o.appendChild(this.L),o.appendChild(i),ki.call(this,{element:o,render:t.render||Di,target:t.target}),this.D=!0,this.o={},this.j={},this.U={}}function Di(t){if(t=t.frameState){for(var e,o,i,r,n,s,p,a,h,l,u,c=t.layerStatesArray,y=Q({},t.attributions),f={},g={},d=t.viewState.projection,v=0,b=c.length;v<b;v++)if((r=c[v].layer.la())&&(a=B(r).toString(),p=r.j))for(e=0,o=p.length;e<o;e++)if(!((s=B(n=p[e]).toString())in y)){if(i=t.usedTiles[a]){var m=r.Db(d);t:{var w,x=d;if((w=n).a){var S=void 0;for(S in i)if(S in w.a)for(var M,P=i[S],T=0,A=w.a[S].length;T<A;++T){if($o(M=w.a[S][T],P)){w=!0;break t}var E=ai(m,fi(x),parseInt(S,10)),C=E.ca-E.ea+1;if((P.ea<E.ea||P.ca>E.ca)&&($o(M,new Jo(q(P.ea,C),q(P.ca,C),P.ga,P.ja))||P.ca-P.ea+1>C&&$o(M,E))){w=!0;break t}}w=!1}else w=!0}}else w=!1;w?(s in f&&delete f[s],(w=n.b)in g||(g[w]=!0,y[s]=n)):f[s]=n}for(h in v=(b=[y,f])[0],b=b[1],this.o)h in v?(this.j[h]||(this.o[h].style.display="",this.j[h]=!0),delete v[h]):h in b?(this.j[h]&&(this.o[h].style.display="none",delete this.j[h]),delete b[h]):(Ii(this.o[h]),delete this.o[h],delete this.j[h]);for(h in v)e=document.createElement("LI"),e.innerHTML=v[h].b,this.L.appendChild(e),this.o[h]=e,this.j[h]=!0;for(h in b)e=document.createElement("LI"),e.innerHTML=b[h].b,e.style.display="none",this.L.appendChild(e),this.o[h]=e;for(l in h=!ot(this.j)||!ot(t.logos),this.D!=h&&(this.element.style.display=h?"":"none",this.D=h),h&&ot(this.j)?this.element.classList.add("ol-logo-only"):this.element.classList.remove("ol-logo-only"),t=t.logos,h=this.U)l in t||(Ii(h[l]),delete h[l]);for(u in t)b=t[u],b instanceof HTMLElement&&(this.u.appendChild(b),h[u]=b),u in h||(l=new Image,l.src=u,""===b?v=l:(v=document.createElement("a"),v.href=b,v.appendChild(l)),this.u.appendChild(v),h[u]=v);this.u.style.display=ot(t)?"none":""}else this.D&&(this.element.style.display="none",this.D=!1)}function Bi(t){t.element.classList.toggle("ol-collapsed"),t.f?Ni(t.A,t.C):Ni(t.C,t.A),t.f=!t.f}function Ui(t){this.f=void 0!==(t=t||{}).className?t.className:"ol-full-screen";var e=void 0!==t.label?t.label:"⤢",o=(this.l="string"==typeof e?document.createTextNode(e):e,e=void 0!==t.labelActive?t.labelActive:"×",this.o="string"==typeof e?document.createTextNode(e):e,t.tipLabel||"Toggle full-screen");(e=document.createElement("button")).className=this.f+"-"+Ki(),e.setAttribute("type","button"),e.title=o,e.appendChild(this.l),c(e,"click",this.C,this),(o=document.createElement("div")).className=this.f+" ol-unselectable ol-control "+(Gi()?"":"ol-unsupported"),o.appendChild(e),ki.call(this,{element:o,target:t.target}),this.A=void 0!==t.keys&&t.keys,this.j=t.source}function Gi(){var t=document.body;return t.webkitRequestFullscreen||t.mozRequestFullScreen&&document.mozFullScreenEnabled||t.msRequestFullscreen&&document.msFullscreenEnabled||t.requestFullscreen&&document.fullscreenEnabled}function Ki(){return!!(document.webkitIsFullScreen||document.mozFullScreen||document.msFullscreenElement||document.fullscreenElement)}function Xi(t){t.requestFullscreen?t.requestFullscreen():t.msRequestFullscreen?t.msRequestFullscreen():t.mozRequestFullScreen?t.mozRequestFullScreen():t.webkitRequestFullscreen&&t.webkitRequestFullscreen()}e(Fi,ft),e(ki,wt),ki.prototype.oa=function(){Ii(this.element),wt.prototype.oa.call(this)},ki.prototype.i=function(){return this.a},ki.prototype.setMap=function(t){this.a&&Ii(this.element);for(var e=0,o=this.v.length;e<o;++e)lt(this.v[e]);this.v.length=0,(this.a=t)&&((this.P||t.u).appendChild(this.element),this.render!==G&&this.v.push(c(t,"postrender",this.render,this)),t.render())},ki.prototype.c=function(t){this.P="string"==typeof t?document.getElementById(t):t},e(Oi,ki),(d=Oi.prototype).em=function(t){t.preventDefault(),Bi(this)},d.dm=function(){return this.l},d.gm=function(t){this.l!==t&&(this.l=t,this.element.classList.toggle("ol-uncollapsible"),!t)&&this.f&&Bi(this)},d.fm=function(t){this.l&&this.f!==t&&Bi(this)},d.cm=function(){return this.f},e(Ui,ki),Ui.prototype.C=function(t){t.preventDefault(),Gi()&&(t=this.a)&&(Ki()?document.exitFullscreen?document.exitFullscreen():document.msExitFullscreen?document.msExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitExitFullscreen&&document.webkitExitFullscreen():(t=this.j?"string"==typeof this.j?document.getElementById(this.j):this.j:t.Cc(),this.A?t.mozRequestFullScreenWithKeys?t.mozRequestFullScreenWithKeys():t.webkitRequestFullscreen?t.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):Xi(t):Xi(t)))},Ui.prototype.u=function(){var t=this.element.firstElementChild,e=this.a;Ki()?(t.className=this.f+"-true",Ni(this.o,this.l)):(t.className=this.f+"-false",Ni(this.l,this.o)),e&&e.ld()},Ui.prototype.setMap=function(t){ki.prototype.setMap.call(this,t),t&&this.v.push(c(document,Wi(),this.u,this))};var Vi,Wi=function(){var t;return Vi||((t=document.body).webkitRequestFullscreen?Vi="webkitfullscreenchange":t.mozRequestFullScreen?Vi="mozfullscreenchange":t.msRequestFullscreen?Vi="MSFullscreenChange":t.requestFullscreen&&(Vi="fullscreenchange")),Vi};function zi(t){var e=void 0!==(t=t||{}).className?t.className:"ol-rotate",o=void 0!==t.label?t.label:"⇧",i=(this.f=null,"string"==typeof o?(this.f=document.createElement("span"),this.f.className="ol-compass",this.f.textContent=o):(this.f=o,this.f.classList.add("ol-compass")),t.tipLabel||"Reset rotation");(o=document.createElement("button")).className=e+"-reset",o.setAttribute("type","button"),o.title=i,o.appendChild(this.f),c(o,"click",zi.prototype.A,this),(i=document.createElement("div")).className=e+" ol-unselectable ol-control",i.appendChild(o),e=t.render||Hi,this.l=t.resetNorth||void 0,ki.call(this,{element:i,render:e,target:t.target}),this.o=void 0!==t.duration?t.duration:250,this.j=void 0===t.autoHide||t.autoHide,this.u=void 0,this.j&&this.element.classList.add("ol-hidden")}function Hi(t){var e,o;(t=t.frameState)&&((t=t.viewState.rotation)!=this.u&&(e="rotate("+t+"rad)",this.j&&((o=this.element.classList.contains("ol-hidden"))||0!==t?o&&0!==t&&this.element.classList.remove("ol-hidden"):this.element.classList.add("ol-hidden")),this.f.style.msTransform=e,this.f.style.webkitTransform=e,this.f.style.transform=e),this.u=t)}function Yi(t){var e=void 0!==(t=t||{}).className?t.className:"ol-zoom",o=void 0!==t.delta?t.delta:1,i=void 0!==t.zoomInLabel?t.zoomInLabel:"+",r=void 0!==t.zoomOutLabel?t.zoomOutLabel:"−",n=void 0!==t.zoomInTipLabel?t.zoomInTipLabel:"Zoom in",s=void 0!==t.zoomOutTipLabel?t.zoomOutTipLabel:"Zoom out",p=document.createElement("button");p.className=e+"-in",p.setAttribute("type","button"),p.title=n,p.appendChild("string"==typeof i?document.createTextNode(i):i),c(p,"click",Yi.prototype.j.bind(this,o)),(i=document.createElement("button")).className=e+"-out",i.setAttribute("type","button"),i.title=s,i.appendChild("string"==typeof r?document.createTextNode(r):r),c(i,"click",Yi.prototype.j.bind(this,-o)),(o=document.createElement("div")).className=e+" ol-unselectable ol-control",o.appendChild(p),o.appendChild(i),ki.call(this,{element:o,target:t.target}),this.f=void 0!==t.duration?t.duration:250}function Zi(t){t=t||{};var e=new di;return void 0!==t.zoom&&!t.zoom||e.push(new Yi(t.zoomOptions)),void 0!==t.rotate&&!t.rotate||e.push(new zi(t.rotateOptions)),void 0!==t.attribution&&!t.attribution||e.push(new Oi(t.attributionOptions)),e}function qi(t){t=t||{};var e=document.createElement("DIV");e.className=void 0!==t.className?t.className:"ol-mouse-position",ki.call(this,{element:e,render:t.render||Ji,target:t.target}),c(this,St($i),this.hm,this),t.coordinateFormat&&this.oi(t.coordinateFormat),t.projection&&this.sh(Ye(t.projection)),this.u=void 0!==t.undefinedHTML?t.undefinedHTML:"",this.o=e.innerHTML,this.l=this.j=this.f=null}function Ji(t){(t=t.frameState)?this.f!=t.viewState.projection&&(this.f=t.viewState.projection,this.j=null):this.f=null,_i(this,this.l)}function _i(t,e){var o,i=t.u;e&&t.f&&(t.j||(o=t.rh(),t.j=o?Je(t.f,o):_e),o=t.a.Sa(e))&&(t.j(o,o),i=(i=t.Lg())?i(o):o.toString()),t.o&&i==t.o||(t.element.innerHTML=i,t.o=i)}e(zi,ki),zi.prototype.A=function(t){t.preventDefault(),void 0!==this.l?this.l():(t=this.a.aa())&&void 0!==t.Ra()&&(0<this.o?t.animate({rotation:0,duration:this.o,easing:qt}):t.pe(0))},e(Yi,ki),Yi.prototype.j=function(t,e){e.preventDefault();var o,e=this.a.aa();e&&(o=e.Oa())&&(o=e.constrainResolution(o,t),0<this.f?(0<Xo(e)[qo]&&Uo(e),e.animate({resolution:o,duration:this.f,easing:qt})):e.Oc(o))},e(qi,ki),(d=qi.prototype).hm=function(){this.j=null},d.Lg=function(){return this.get(Qi)},d.rh=function(){return this.get($i)},d.$k=function(t){this.l=this.a.ce(t),_i(this,this.l)},d.al=function(){_i(this,null),this.l=null},d.setMap=function(t){ki.prototype.setMap.call(this,t),t&&(t=t.f,this.v.push(c(t,"mousemove",this.$k,this),c(t,"mouseout",this.al,this)))},d.oi=function(t){this.set(Qi,t)},d.sh=function(t){this.set($i,t)};var $i="projection",Qi="coordinateFormat";function tr(t,e,o,i,r){Fi.call(this,t,e,r),this.originalEvent=o,this.pixel=e.ce(o),this.coordinate=e.Sa(this.pixel),this.dragging=void 0!==i&&i}e(tr,Fi),tr.prototype.preventDefault=function(){Fi.prototype.preventDefault.call(this),this.originalEvent.preventDefault()},tr.prototype.stopPropagation=function(){Fi.prototype.stopPropagation.call(this),this.originalEvent.stopPropagation()};var er={Np:"singleclick",Cp:"click",Dp:"dblclick",Gp:"pointerdrag",Jp:"pointermove",Fp:"pointerdown",Mp:"pointerup",Lp:"pointerover",Kp:"pointerout",Hp:"pointerenter",Ip:"pointerleave",Ep:"pointercancel"};function or(t,e,o,i,r){tr.call(this,t,e,o.b,i,r),this.b=o}e(or,tr);var ir=["experimental-webgl","webgl","webkit-3d","moz-webgl"];function rr(t,e){for(var o,i=ir.length,r=0;r<i;++r)try{if(o=t.getContext(ir[r],e))return o}catch(t){}return null}var nr,sr="undefined"!=typeof navigator?navigator.userAgent.toLowerCase():"",pr=-1!==sr.indexOf("firefox"),ar=-1!==sr.indexOf("safari")&&-1==sr.indexOf("chrom"),hr=-1!==sr.indexOf("webkit")&&-1==sr.indexOf("edge"),lr=-1!==sr.indexOf("macintosh"),ur=window.devicePixelRatio||1,cr=!1,yr=function(){if(!("HTMLCanvasElement"in window))return!1;try{var t=document.createElement("CANVAS").getContext("2d");return!!t&&(void 0!==t.setLineDash&&(cr=!0),!0)}catch(t){return!1}}(),fr="DeviceOrientationEvent"in window,gr="geolocation"in navigator,dr="ontouchstart"in window,vr="PointerEvent"in window,br=!!navigator.msPointerEnabled,sr=!1,mr=[];if("WebGLRenderingContext"in window)try{var wr=rr(document.createElement("CANVAS"),{failIfMajorPerformanceCaveat:!0});wr&&(sr=!0,p0=wr.getParameter(wr.MAX_TEXTURE_SIZE),mr=wr.getSupportedExtensions())}catch(t){}function xr(t,e){this.b=t,this.c=e}function Sr(t){xr.call(this,t,{mousedown:this.tl,mousemove:this.ul,mouseup:this.xl,mouseover:this.wl,mouseout:this.vl}),this.a=t.g,this.g=[]}function Mr(t,e){for(var o,i=t.g,r=e.clientX,n=e.clientY,s=0,p=i.length;s<p&&(o=i[s]);s++){var a=Math.abs(n-o[1]);if(Math.abs(r-o[0])<=25&&a<=25)return 1}}function Pr(t){var e=kr(t,t),o=e.preventDefault;return e.preventDefault=function(){t.preventDefault(),o()},e.pointerId=1,e.isPrimary=!0,e.pointerType="mouse",e}function Tr(t){xr.call(this,t,{MSPointerDown:this.Cl,MSPointerMove:this.Dl,MSPointerUp:this.Gl,MSPointerOut:this.El,MSPointerOver:this.Fl,MSPointerCancel:this.Bl,MSGotPointerCapture:this.zl,MSLostPointerCapture:this.Al}),this.a=t.g,this.g=["","unavailable","touch","pen","mouse"]}function Ar(t,e){var o=e;return"number"==typeof e.pointerType&&((o=kr(e,e)).pointerType=t.g[e.pointerType]),o}function Er(t){xr.call(this,t,{pointerdown:this.po,pointermove:this.qo,pointerup:this.to,pointerout:this.ro,pointerover:this.so,pointercancel:this.oo,gotpointercapture:this.Lk,lostpointercapture:this.sl})}function Cr(t,e,o){var i;ft.call(this,t),this.b=e,this.buttons=function(t){if(t.buttons||jr)t=t.buttons;else switch(t.which){case 1:t=1;break;case 2:t=4;break;case 3:t=2;break;default:t=0}return t}(t=o||{}),this.pressure=(o=t,i=this.buttons,o.pressure||(i?.5:0)),this.bubbles="bubbles"in t&&t.bubbles,this.cancelable="cancelable"in t&&t.cancelable,this.view="view"in t?t.view:null,this.detail="detail"in t?t.detail:null,this.screenX="screenX"in t?t.screenX:0,this.screenY="screenY"in t?t.screenY:0,this.clientX="clientX"in t?t.clientX:0,this.clientY="clientY"in t?t.clientY:0,this.button="button"in t?t.button:0,this.relatedTarget="relatedTarget"in t?t.relatedTarget:null,this.pointerId="pointerId"in t?t.pointerId:0,this.width="width"in t?t.width:0,this.height="height"in t?t.height:0,this.pointerType="pointerType"in t?t.pointerType:"",this.isPrimary="isPrimary"in t&&t.isPrimary,e.preventDefault&&(this.preventDefault=function(){e.preventDefault()})}nr=sr,O=mr,k=p0,e(Sr,xr),(d=Sr.prototype).tl=function(t){var e;Mr(this,t)||(1..toString()in this.a&&(e=Pr(t),Ur(this.b,"pointercancel",e,t),delete this.a[1..toString()]),e=Pr(t),this.a[1..toString()]=t,Ur(this.b,"pointerdown",e,t))},d.ul=function(t){var e;Mr(this,t)||(e=Pr(t),Ur(this.b,"pointermove",e,t))},d.xl=function(t){var e;Mr(this,t)||(e=this.a[1..toString()])&&e.button===t.button&&(e=Pr(t),Ur(this.b,"pointerup",e,t),delete this.a[1..toString()])},d.wl=function(t){var e;Mr(this,t)||(e=Pr(t),Br(this.b,e,t))},d.vl=function(t){var e;Mr(this,t)||(e=Pr(t),Dr(this.b,e,t))},e(Tr,xr),(d=Tr.prototype).Cl=function(t){this.a[t.pointerId.toString()]=t;var e=Ar(this,t);Ur(this.b,"pointerdown",e,t)},d.Dl=function(t){var e=Ar(this,t);Ur(this.b,"pointermove",e,t)},d.Gl=function(t){var e=Ar(this,t);Ur(this.b,"pointerup",e,t),delete this.a[t.pointerId.toString()]},d.El=function(t){var e=Ar(this,t);Dr(this.b,e,t)},d.Fl=function(t){var e=Ar(this,t);Br(this.b,e,t)},d.Bl=function(t){var e=Ar(this,t);Ur(this.b,"pointercancel",e,t),delete this.a[t.pointerId.toString()]},d.Al=function(t){this.b.b(new Cr("lostpointercapture",t,t))},d.zl=function(t){this.b.b(new Cr("gotpointercapture",t,t))},e(Er,xr),(d=Er.prototype).po=function(t){Gr(this.b,t)},d.qo=function(t){Gr(this.b,t)},d.to=function(t){Gr(this.b,t)},d.ro=function(t){Gr(this.b,t)},d.so=function(t){Gr(this.b,t)},d.oo=function(t){Gr(this.b,t)},d.sl=function(t){Gr(this.b,t)},d.Lk=function(t){Gr(this.b,t)},e(Cr,ft);var jr=!1;try{jr=1===new MouseEvent("click",{buttons:1}).buttons}catch(t){}function Lr(t,e){xr.call(this,t,{touchstart:this.up,touchmove:this.tp,touchend:this.sp,touchcancel:this.rp}),this.a=t.g,this.j=e,this.g=void 0,this.i=0,this.f=void 0}function Rr(t,e,o){function i(){e.preventDefault()}for(var r,n,s,p=Array.prototype.slice.call(e.changedTouches),a=p.length,h=0;h<a;++h)r=t,n=e,s=p[h],(n=kr(n,s)).pointerId=s.identifier+2,n.bubbles=!0,n.cancelable=!0,n.detail=r.i,n.button=0,n.buttons=1,n.width=s.webkitRadiusX||s.radiusX||0,n.height=s.webkitRadiusY||s.radiusY||0,n.pressure=s.webkitForce||s.force||.5,n.isPrimary=r.g===s.identifier,n.pointerType="touch",n.clientX=s.clientX,n.clientY=s.clientY,n.screenX=s.screenX,n.screenY=s.screenY,(r=n).preventDefault=i,o.call(t,e,r)}function Nr(t,e){var o,i=t.j.g,e=e.changedTouches[0];t.g===e.identifier&&(o=[e.clientX,e.clientY],i.push(o),setTimeout(function(){var t=i.indexOf(o);-1<t&&i.splice(t,1)},2500))}function Ir(t){dt.call(this),this.i=t,this.g={},this.c={},this.a=[],vr?Fr(this,new Er(this)):br?Fr(this,new Tr(this)):(Fr(this,t=new Sr(this)),dr&&Fr(this,new Lr(this,t))),t=this.a.length;for(var e,o,i=0;i<t;i++)e=this.a[i],o=this,Object.keys(e.c).forEach(function(t){c(this.i,t,this.f,this)},o)}function Fr(t,o){var e=Object.keys(o.c);e&&(e.forEach(function(t){var e=o.c[t];e&&(this.c[t]=e.bind(o))},t),t.a.push(o))}function kr(t,e){for(var o,i={},r=0,n=Kr.length;r<n;r++)i[o=Kr[r][0]]=t[o]||e[o]||Kr[r][1];return i}function Or(t,e,o){e.bubbles=!1,Ur(t,"pointerleave",e,o)}function Dr(t,e,o){t.out(e,o);var i=e.target,r=e.relatedTarget;i&&r&&i.contains(r)||Or(t,e,o)}function Br(t,e,o){e.bubbles=!0,Ur(t,"pointerover",e,o);var i=e.target,r=e.relatedTarget;i&&r&&i.contains(r)||(e.bubbles=!1,Ur(t,"pointerenter",e,o))}function Ur(t,e,o,i){t.b(new Cr(e,i,o))}function Gr(t,e){t.b(new Cr(e.type,e,e))}e(Lr,xr),(d=Lr.prototype).mi=function(){this.i=0,this.f=void 0},d.up=function(t){var e=t.touches,o=Object.keys(this.a),i=o.length;if(i>=e.length){for(var r=[],n=0;n<i;++n){var s=o[n],p=this.a[s];if(!(a=1==s))t:{for(var a=e.length,h=0;h<a;h++)if(e[h].identifier===s-2){a=!0;break t}a=!1}a||r.push(p.out)}for(n=0;n<r.length;++n)this.ef(t,r[n])}e=t.changedTouches[0],(0===(o=Object.keys(this.a).length)||1===o&&1..toString()in this.a)&&(this.g=e.identifier,void 0!==this.f)&&clearTimeout(this.f),Nr(this,t),this.i++,Rr(this,t,this.ko)},d.ko=function(t,e){this.a[e.pointerId]={target:e.target,out:e,Xh:e.target};var o=this.b;e.bubbles=!0,Ur(o,"pointerover",e,t),o=this.b,e.bubbles=!1,Ur(o,"pointerenter",e,t),Ur(this.b,"pointerdown",e,t)},d.tp=function(t){t.preventDefault(),Rr(this,t,this.yl)},d.yl=function(t,e){var o,i,r=this.a[e.pointerId];r&&(o=r.out,i=r.Xh,Ur(this.b,"pointermove",e,t),o&&i!==e.target&&(o.relatedTarget=e.target,e.relatedTarget=i,o.target=i,e.target?(Dr(this.b,o,t),Br(this.b,e,t)):(e.target=i,e.relatedTarget=null,this.ef(t,e))),r.out=e,r.Xh=e.target)},d.sp=function(t){Nr(this,t),Rr(this,t,this.vp)},d.vp=function(t,e){Ur(this.b,"pointerup",e,t),this.b.out(e,t),Or(this.b,e,t),delete this.a[e.pointerId],e.isPrimary&&(this.g=void 0,this.f=setTimeout(this.mi.bind(this),200))},d.rp=function(t){Rr(this,t,this.ef)},d.ef=function(t,e){Ur(this.b,"pointercancel",e,t),this.b.out(e,t),Or(this.b,e,t),delete this.a[e.pointerId],e.isPrimary&&(this.g=void 0,this.f=setTimeout(this.mi.bind(this),200))},e(Ir,dt),Ir.prototype.f=function(t){var e=this.c[t.type];e&&e(t)},Ir.prototype.out=function(t,e){t.bubbles=!0,Ur(this,"pointerout",t,e)};var Kr=[["bubbles",!(Ir.prototype.oa=function(){for(var t,e,o=this.a.length,i=0;i<o;i++)t=this.a[i],e=this,Object.keys(t.c).forEach(function(t){ht(this.i,t,this.f,this)},e);dt.prototype.oa.call(this)})],["cancelable",!1],["view",null],["detail",null],["screenX",0],["screenY",0],["clientX",0],["clientY",0],["ctrlKey",!1],["altKey",!1],["shiftKey",!1],["metaKey",!1],["button",0],["relatedTarget",null],["buttons",0],["pointerId",0],["width",0],["height",0],["pressure",0],["tiltX",0],["tiltY",0],["pointerType",""],["hwTimestamp",0],["isPrimary",!1],["type",""],["target",null],["currentTarget",null],["which",0]];function Xr(t){dt.call(this),this.f=t,this.j=0,this.l=!1,this.c=[],this.g=null,t=this.f.f,this.u=0,this.H={},this.i=new Ir(t),this.a=null,this.o=c(this.i,"pointerdown",this.cl,this),this.v=c(this.i,"pointermove",this.So,this)}function Vr(t,e){"pointerup"==e.type||"pointercancel"==e.type?delete t.H[e.pointerId]:"pointerdown"==e.type&&(t.H[e.pointerId]=!0),t.u=Object.keys(t.H).length}function Wr(t,e){dt.call(this),this.Ca=t,this.state=e,this.a=null,this.key=""}function zr(t){if(t.a){var e=t.a;do{if(e.W()==Hr)return e}while(e=e.a)}return t}e(Xr,dt),(d=Xr.prototype).$g=function(t){Vr(this,t);var e,o=new or("pointerup",this.f,t);this.b(o),this.l||0!==t.button||(e=(o=this).g,t=new or("click",o.f,e),o.b(t),0!==o.j?(clearTimeout(o.j),o.j=0,t=new or("dblclick",o.f,e),o.b(t)):o.j=setTimeout(function(){this.j=0;var t=new or("singleclick",this.f,e);this.b(t)}.bind(o),250)),0===this.u&&(this.c.forEach(lt),this.c.length=0,this.l=!1,this.g=null,yt(this.a),this.a=null)},d.cl=function(t){Vr(this,t);var e=new or("pointerdown",this.f,t);this.b(e),this.g=t,0===this.c.length&&(this.a=new Ir(document),this.c.push(c(this.a,"pointermove",this.Vl,this),c(this.a,"pointerup",this.$g,this),c(this.i,"pointercancel",this.$g,this)))},d.Vl=function(t){var e;t.clientX==this.g.clientX&&t.clientY==this.g.clientY||(this.l=!0,e=new or("pointerdrag",this.f,t,this.l),this.b(e)),t.preventDefault()},d.So=function(t){this.b(new or(t.type,this.f,t,!(!this.g||t.clientX==this.g.clientX&&t.clientY==this.g.clientY)))},d.oa=function(){this.v&&(lt(this.v),this.v=null),this.o&&(lt(this.o),this.o=null),this.c.forEach(lt),this.c.length=0,this.a&&(yt(this.a),this.a=null),this.i&&(yt(this.i),this.i=null),dt.prototype.oa.call(this)},e(Wr,dt),Wr.prototype.s=function(){this.b("change")},Wr.prototype.bb=function(){return this.key+"/"+this.Ca},Wr.prototype.c=function(){return this.Ca},Wr.prototype.W=function(){return this.state};var Hr=2;function Yr(t,e){this.o=t,this.f=e,this.b=[],this.g=[],this.a={}}function Zr(t){var e=t.b,o=t.g,i=e[0];return 1==e.length?(e.length=0,o.length=0):(e[0]=e.pop(),o[0]=o.pop(),qr(t,0)),e=t.f(i),delete t.a[e],i}function qr(t,e){for(var o=t.b,i=t.g,r=o.length,n=o[e],s=i[e],p=e;e<r>>1;){var a=2*e+1,h=2*e+2,a=h<r&&i[h]<i[a]?h:a;o[e]=o[a],i[e]=i[a],e=a}o[e]=n,i[e]=s,Jr(t,p,e)}function Jr(t,e,o){var i=t.b;t=t.g;for(var r=i[o],n=t[o];e<o;){var s=o-1>>1;if(!(t[s]>n))break;i[o]=i[s],t[o]=t[s],o=s}i[o]=r,t[o]=n}function _r(t){for(var e,o,i=t.o,r=t.b,n=t.g,s=0,p=r.length,a=0;a<p;++a)1/0==(o=i(e=r[a]))?delete t.a[t.f(e)]:(n[s]=o,r[s++]=e);for(r.length=s,n.length=s,i=(t.b.length>>1)-1;0<=i;i--)qr(t,i)}function $r(e,t){Yr.call(this,function(t){return e.apply(null,t)},function(t){return t[0].bb()}),this.v=t,this.j=0,this.i={}}function Qr(t,e,o){for(var i,r,n=0;t.j<e&&n<o&&0<t.b.length;)r=(i=Zr(t)[0]).bb(),0!==i.W()||r in t.i||(t.i[r]=!0,++t.j,++n,i.load())}function tn(t,e,o){this.c=t,this.f=e,this.i=o,this.b=[],this.a=this.g=0}function en(t){wt.call(this),this.v=null,this.Ea(!0),this.handleEvent=t.handleEvent}function on(t,e,o,i){var r,n;void 0!==e&&(r=t.Ra(),n=t.fb(),void 0!==r&&n&&0<i?t.animate({rotation:e,anchor:o,duration:i,easing:qt}):t.rotate(e,o))}function rn(t,e,o,i){var r=t.Oa();e=t.constrainResolution(r,e,0),nn(t,e,o,i)}function nn(t,e,o,i){var r,n;e&&(r=t.Oa(),n=t.fb(),void 0!==r&&n&&e!==r&&i?t.animate({resolution:e,anchor:o,duration:i,easing:qt}):(o&&(o=Ko(t,e,o),t.Mb(o)),t.Oc(e)))}Yr.prototype.clear=function(){this.b.length=0,this.g.length=0,tt(this.a)},Yr.prototype.c=function(t){V(!(this.f(t)in this.a),31);var e=this.o(t);return 1/0!=e&&(this.b.push(t),this.g.push(e),this.a[this.f(t)]=!0,Jr(this,0,this.b.length-1),!0)},e($r,Yr),$r.prototype.c=function(t){var e=Yr.prototype.c.call(this,t);return e&&c(t[0],"change",this.l,this),e},$r.prototype.l=function(t){var e=(t=t.target).W();e!==Hr&&3!==e&&4!==e&&5!==e||(ht(t,"change",this.l,this),(t=t.bb())in this.i&&(delete this.i[t],--this.j),this.v())},e(en,wt),en.prototype.f=function(){return this.get(sn)},en.prototype.c=function(){return this.v},en.prototype.Ea=function(t){this.set(sn,t)},en.prototype.setMap=function(t){this.v=t};var sn="active";function pn(t){this.a=(t=t||{}).delta||1,en.call(this,{handleEvent:an}),this.i=void 0!==t.duration?t.duration:250}function an(t){var e=!1,o=t.originalEvent;return"dblclick"==t.type&&(e=t.coordinate,o=o.shiftKey?-this.a:this.a,rn(t.map.aa(),o,e,this.i),t.preventDefault(),e=!0),!e}function hn(t){return(t=t.originalEvent).altKey&&!(t.metaKey||t.ctrlKey)&&t.shiftKey}function ln(t){return 0==(t=t.originalEvent).button&&!(hr&&lr&&t.ctrlKey)}function un(t){return"pointermove"==t.type}function cn(t){return"singleclick"==t.type}function yn(t){return!(t=t.originalEvent).altKey&&!(t.metaKey||t.ctrlKey)&&!t.shiftKey}function fn(t){return!(t=t.originalEvent).altKey&&!(t.metaKey||t.ctrlKey)&&t.shiftKey}function gn(t){return"INPUT"!==(t=t.originalEvent.target.tagName)&&"SELECT"!==t&&"TEXTAREA"!==t}function dn(t){return V(t.b,56),"mouse"==t.b.pointerType}function vn(t){return(t=t.b).isPrimary&&0===t.button}function bn(t){en.call(this,{handleEvent:(t=t||{}).handleEvent||wn}),this.Ve=t.handleDownEvent||Re,this.bf=t.handleDragEvent||G,this.cf=t.handleMoveEvent||G,this.df=t.handleUpEvent||Re,this.A=!1,this.Z={},this.l=[]}function mn(t){for(var e=t.length,o=0,i=0,r=0;r<e;r++)o+=t[r].clientX,i+=t[r].clientY;return[o/e,i/e]}function wn(t){var e,o;return!(t instanceof or&&(e=!1,"pointerdown"!==(o=t.type)&&"pointerdrag"!==o&&"pointerup"!==o||(o=t.b,"pointerup"==t.type?delete this.Z[o.pointerId]:("pointerdown"==t.type||o.pointerId in this.Z)&&(this.Z[o.pointerId]=o),this.l=et(this.Z)),this.A&&("pointerdrag"==t.type?this.bf(t):"pointerup"==t.type&&(this.A=this.df(t))),"pointerdown"==t.type?(this.A=t=this.Ve(t),e=this.Qc(t)):"pointermove"==t.type&&this.cf(t),e))}function xn(t){bn.call(this,{handleDownEvent:Pn,handleDragEvent:Sn,handleUpEvent:Mn}),this.a=(t=t||{}).kinetic,this.i=null,this.o=t.condition||yn,this.j=!1}function Sn(t){var e,o,i=mn(this.l);this.a&&this.a.b.push(i[0],i[1],Date.now()),this.i&&(Wt(o=[o=this.i[0]-i[0],i[1]-this.i[1]],(e=(t=t.map.aa()).W()).resolution),Vt(o,e.rotation),Bt(o,e.center),o=t.Zd(o),t.Mb(o)),this.i=i}function Mn(t){var e,o=t.map;if(t=o.aa(),0!==this.l.length)return!(this.i=null);if(e=!this.j&&this.a)if((e=this.a).b.length<6)e=!1;else{var i=Date.now()-e.i,r=e.b.length-3;if(e.b[r+2]<i)e=!1;else{for(var n=r-3;0<n&&e.b[n+2]>i;)n-=3;var i=e.b[r+2]-e.b[n+2],s=e.b[r]-e.b[n],r=e.b[r+1]-e.b[n+1];e.g=Math.atan2(r,s),e.a=Math.sqrt(s*s+r*r)/i,e=e.a>e.f}}return e&&(e=((e=this.a).f-e.a)/e.c,r=this.a.g,n=t.fb(),n=o.Ga(n),o=o.Sa([n[0]-e*Math.cos(r),n[1]-e*Math.sin(r)]),t.animate({center:t.Zd(o),duration:500,easing:qt})),zo(t,1,-1),!1}function Pn(t){var e;return!!(0<this.l.length&&this.o(t))&&(e=t.map.aa(),this.i=null,this.A||zo(e,1,1),e.Mb(t.frameState.viewState.center),this.a&&((t=this.a).b.length=0,t.g=0,t.a=0),this.j=1<this.l.length,!0)}function Tn(t){t=t||{},bn.call(this,{handleDownEvent:Cn,handleDragEvent:An,handleUpEvent:En}),this.i=t.condition||hn,this.a=void 0,this.j=void 0!==t.duration?t.duration:250}function An(t){var e,o,i;dn(t)&&(e=(o=t.map).nb(),t=t.pixel,e=Math.atan2(e[1]/2-t[1],t[0]-e[0]/2),void 0!==this.a&&(t=e-this.a,i=(o=o.aa()).Ra(),on(o,i-t)),this.a=e)}function En(t){if(!dn(t))return!0;zo(t=t.map.aa(),1,-1);var e=t.Ra(),o=this.j,e=t.constrainRotation(e,0);return on(t,e,void 0,o),!1}function Cn(t){return!!(dn(t)&&ln(t)&&this.i(t))&&(zo(t.map.aa(),1,1),!(this.a=void 0))}function jn(t){this.f=null,this.a=document.createElement("div"),this.a.style.position="absolute",this.a.className="ol-box "+t,this.g=this.c=this.b=null}function Ln(t){var e=t.c,o=t.g;(t=t.a.style).left=Math.min(e[0],o[0])+"px",t.top=Math.min(e[1],o[1])+"px",t.width=Math.abs(o[0]-e[0])+"px",t.height=Math.abs(o[1]-e[1])+"px"}function Rn(t){var e=t.c,o=t.g;(e=[e,[e[0],o[1]],o,[o[0],e[1]]].map(t.b.Sa,t.b))[4]=e[0].slice(),t.f?t.f.qa([e]):t.f=new w([e])}function Nn(t){bn.call(this,{handleDownEvent:On,handleDragEvent:Fn,handleUpEvent:kn}),this.a=new jn((t=t||{}).className||"ol-dragbox"),this.i=null,this.u=t.condition||Le,this.o=t.boxEndCondition||In}function In(t,e,o){return 64<=(t=o[0]-e[0])*t+(e=o[1]-e[1])*e}function Fn(t){var e,o;dn(t)&&(e=this.a,o=t.pixel,e.c=this.i,e.g=o,Rn(e),Ln(e),this.b(new Gn(Bn,t.coordinate,t)))}function kn(t){return!dn(t)||(this.a.setMap(null),this.o(t,this.i,t.pixel)&&(this.j(t),this.b(new Gn(Un,t.coordinate,t))),!1)}function On(t){var e,o;return!!(dn(t)&&ln(t)&&this.u(t))&&(this.i=t.pixel,this.a.setMap(t.map),e=this.a,o=this.i,e.c=this.i,e.g=o,Rn(e),Ln(e),this.b(new Gn(Dn,t.coordinate,t)),!0)}e(pn,en),e(bn,en),bn.prototype.Qc=function(t){return t},e(xn,bn),xn.prototype.Qc=Re,e(Tn,bn),Tn.prototype.Qc=Re,e(jn,ct),jn.prototype.oa=function(){this.setMap(null)},jn.prototype.setMap=function(t){var e;this.b&&(this.b.A.removeChild(this.a),(e=this.a.style).left=e.top=e.width=e.height="inherit"),(this.b=t)&&this.b.A.appendChild(this.a)},jn.prototype.V=function(){return this.f},e(Nn,bn),Nn.prototype.V=function(){return this.a.V()},Nn.prototype.j=G;var Dn="boxstart",Bn="boxdrag",Un="boxend";function Gn(t,e,o){ft.call(this,t),this.coordinate=e,this.mapBrowserEvent=o}function Kn(t){var e=(t=t||{}).condition||fn;this.C=void 0!==t.duration?t.duration:200,this.D=void 0!==t.out&&t.out,Nn.call(this,{condition:e,className:t.className||"ol-dragzoom"})}function Xn(t){en.call(this,{handleEvent:Vn}),t=t||{},this.a=function(t){return yn(t)&&gn(t)},this.i=void 0!==t.condition?t.condition:this.a,this.j=void 0!==t.duration?t.duration:100,this.l=void 0!==t.pixelDelta?t.pixelDelta:128}function Vn(t){var e,o,i,r,n=!1;return"keydown"==t.type&&(e=t.originalEvent.keyCode,!this.i(t)||40!=e&&37!=e&&39!=e&&38!=e||(o=(n=t.map.aa()).Oa()*this.l,r=i=0,40==e?r=-o:37==e?i=-o:39==e?i=o:r=o,Vt(o=[i,r],n.Ra()),e=this.j,(i=n.fb())&&(o=n.Zd([i[0]+o[0],i[1]+o[1]]),e?n.animate({duration:e,easing:_t,center:o}):n.Mb(o)),t.preventDefault(),n=!0)),!n}function Wn(t){en.call(this,{handleEvent:zn}),this.i=(t=t||{}).condition||gn,this.a=t.delta||1,this.j=void 0!==t.duration?t.duration:100}function zn(t){var e,o=!1;return"keydown"!=t.type&&"keypress"!=t.type||(e=t.originalEvent.charCode,!this.i(t))||43!=e&&45!=e||(o=43==e?this.a:-this.a,rn(e=t.map.aa(),o,void 0,this.j),t.preventDefault(),o=!0),!o}function Hn(t){en.call(this,{handleEvent:Yn}),t=t||{},this.j=0,this.L=void 0!==t.duration?t.duration:250,this.U=void 0!==t.timeout?t.timeout:80,this.A=void 0===t.useAnchor||t.useAnchor,this.a=null,this.o=this.l=this.u=this.i=void 0}function Yn(t){if("wheel"!==(n=t.type)&&"mousewheel"!==n)return!0;t.preventDefault();var e,o,i,r,n=t.map,s=t.originalEvent;return this.A&&(this.a=t.coordinate),"wheel"==t.type?(e=s.deltaY,pr&&s.deltaMode===WheelEvent.DOM_DELTA_PIXEL&&(e/=ur),s.deltaMode===WheelEvent.DOM_DELTA_LINE&&(e*=40)):"mousewheel"==t.type&&(e=-s.wheelDeltaY,ar)&&(e/=3),0!==e&&(t=Date.now(),void 0===this.i&&(this.i=t),(!this.l||400<t-this.i)&&(this.l=Math.abs(e)<4?Zn:qn),this.l===Zn?(n=n.aa(),this.o?clearTimeout(this.o):zo(n,1,1),this.o=setTimeout(this.C.bind(this),400),e=n.Oa()*Math.pow(2,e/300),s=n.j,o=n.a,i=0,e<s?(e=Math.max(e,s/1.5),i=1):o<e&&(e=Math.min(e,1.5*o),i=-1),this.a&&(r=Ko(n,e,this.a),n.Mb(r)),n.Oc(e),0<i?n.animate({resolution:s,easing:qt,anchor:this.a,duration:500}):i<0&&n.animate({resolution:o,easing:qt,anchor:this.a,duration:500}),this.i=t):(this.j+=e,t=Math.max(this.U-(t-this.i),0),clearTimeout(this.u),this.u=setTimeout(this.D.bind(this,n),t))),!1}e(Gn,ft),e(Kn,Nn),Kn.prototype.j=function(){var t=(r=this.v).aa(),e=r.nb(),o=this.V().G();if(this.D){for(var i=t.Uc(e),o=[r.Ga(ve(o)),r.Ga(Pe(o))],r=ae(1/0,1/0,-1/0,-1/0,void 0),n=0,s=o.length;n<s;++n)ye(r,o[n]);Ce(i,1/Vo(r,e)),o=i}e=t.constrainResolution(Vo(o,e)),t.animate({resolution:e,center:me(o),duration:this.C,easing:qt})},e(Xn,en),e(Wn,en),e(Hn,en),Hn.prototype.C=function(){this.o=void 0,zo(this.v.aa(),1,-1)},Hn.prototype.D=function(t){0<Xo(t=t.aa())[qo]&&Uo(t),rn(t,-W(this.j,-1,1),this.a,this.L),this.l=void 0,this.j=0,this.a=null,this.u=this.i=void 0},Hn.prototype.P=function(t){(this.A=t)||(this.a=null)};var Zn="trackpad",qn="wheel";function Jn(t){bn.call(this,{handleDownEvent:Qn,handleDragEvent:_n,handleUpEvent:$n}),t=t||{},this.i=null,this.j=void 0,this.a=!1,this.o=0,this.C=void 0!==t.threshold?t.threshold:.3,this.u=void 0!==t.duration?t.duration:250}function _n(t){var e=0,o=this.l[0],i=this.l[1],o=Math.atan2(i.clientY-o.clientY,i.clientX-o.clientX);void 0!==this.j&&(e=o-this.j,this.o+=e,!this.a)&&Math.abs(this.o)>this.C&&(this.a=!0),this.j=o,o=(t=t.map).f.getBoundingClientRect(),(i=mn(this.l))[0]-=o.left,i[1]-=o.top,this.i=t.Sa(i),this.a&&(i=(o=t.aa()).Ra(),t.render(),on(o,i+e,this.i))}function $n(t){var e,o,i;return!(this.l.length<2&&(zo(t=t.map.aa(),1,-1),this.a&&(i=t.Ra(),e=this.i,o=this.u,i=t.constrainRotation(i,0),on(t,i,e,o)),1))}function Qn(t){return 2<=this.l.length&&(t=t.map,this.i=null,this.j=void 0,this.a=!1,this.o=0,this.A||zo(t.aa(),1,1),!0)}function ts(t){bn.call(this,{handleDownEvent:is,handleDragEvent:es,handleUpEvent:os}),this.o=(t=t||{}).constrainResolution||!1,this.i=null,this.u=void 0!==t.duration?t.duration:400,this.a=void 0,this.j=1}function es(t){var e=1,o=this.l[0],i=this.l[1],r=o.clientX-i.clientX,o=o.clientY-i.clientY,r=Math.sqrt(r*r+o*o);void 0!==this.a&&(e=this.a/r),this.a=r,1!=e&&(this.j=e);var o=(r=(t=t.map).aa()).Oa(),i=t.f.getBoundingClientRect(),n=mn(this.l);n[0]-=i.left,n[1]-=i.top,this.i=t.Sa(n),t.render(),nn(r,o*e,this.i)}function os(t){var e,o,i;return!(this.l.length<2&&(zo(t=t.map.aa(),1,-1),this.o&&(i=t.Oa(),e=this.i,o=this.u,i=t.constrainResolution(i,0,this.j-1),nn(t,i,e,o)),1))}function is(t){return 2<=this.l.length&&(t=t.map,this.i=null,this.a=void 0,this.j=1,this.A||zo(t.aa(),1,1),!0)}function rs(t){t=t||{};var e=new di,o=new tn(-.005,.05,100);return void 0!==t.altShiftDragRotate&&!t.altShiftDragRotate||e.push(new Tn),void 0!==t.doubleClickZoom&&!t.doubleClickZoom||e.push(new pn({delta:t.zoomDelta,duration:t.zoomDuration})),void 0!==t.dragPan&&!t.dragPan||e.push(new xn({kinetic:o})),void 0!==t.pinchRotate&&!t.pinchRotate||e.push(new Jn),void 0!==t.pinchZoom&&!t.pinchZoom||e.push(new ts({duration:t.zoomDuration})),void 0!==t.keyboard&&!t.keyboard||(e.push(new Xn),e.push(new Wn({delta:t.zoomDelta,duration:t.zoomDuration}))),void 0!==t.mouseWheelZoom&&!t.mouseWheelZoom||e.push(new Hn({duration:t.zoomDuration})),void 0!==t.shiftDragZoom&&!t.shiftDragZoom||e.push(new Kn({duration:t.zoomDuration})),e}function ns(t){wt.call(this);var e=Q({},t);e[ps]=void 0!==t.opacity?t.opacity:1,e[as]=void 0===t.visible||t.visible,e[ls]=void 0!==t.zIndex?t.zIndex:0,e[us]=void 0!==t.maxResolution?t.maxResolution:1/0,e[cs]=void 0!==t.minResolution?t.minResolution:0,this.I(e),this.a={layer:this,me:!0}}function ss(t){return t.a.opacity=W(t.Yb(),0,1),t.a.Ei=t.uf(),t.a.visible=t.Fb(),t.a.extent=t.G(),t.a.zIndex=t.Zb(),t.a.maxResolution=t.Wb(),t.a.minResolution=Math.max(t.Xb(),0),t.a}e(Jn,bn),Jn.prototype.Qc=Re,e(ts,bn),ts.prototype.Qc=Re,e(ns,wt),(d=ns.prototype).G=function(){return this.get(hs)},d.Wb=function(){return this.get(us)},d.Xb=function(){return this.get(cs)},d.Yb=function(){return this.get(ps)},d.Fb=function(){return this.get(as)},d.Zb=function(){return this.get(ls)},d.kc=function(t){this.set(hs,t)},d.pc=function(t){this.set(us,t)},d.qc=function(t){this.set(cs,t)},d.lc=function(t){this.set(ps,t)},d.mc=function(t){this.set(as,t)},d.nc=function(t){this.set(ls,t)};var ps="opacity",as="visible",hs="extent",ls="zIndex",us="maxResolution",cs="minResolution";function ys(t){var e=t||{};delete(t=Q({},e)).layers,e=e.layers,ns.call(this,t),this.c=[],this.f={},c(this,St(fs),this.Wk,this),e?Array.isArray(e)?e=new di(e.slice()):V(e instanceof di,43):e=new di,this.yh(e)}e(ys,ns),(d=ys.prototype).ie=function(){this.Fb()&&this.s()},d.Wk=function(){this.c.forEach(lt),this.c.length=0;var t=this.cd();for(o in this.c.push(c(t,mi,this.Vk,this),c(t,wi,this.Xk,this)),this.f)this.f[o].forEach(lt);tt(this.f);for(var e,o=0,i=(t=t.a).length;o<i;o++)e=t[o],this.f[B(e).toString()]=[c(e,Pt,this.ie,this),c(e,"change",this.ie,this)];this.s()},d.Vk=function(t){var e=B(t=t.element).toString();this.f[e]=[c(t,Pt,this.ie,this),c(t,"change",this.ie,this)],this.s()},d.Xk=function(t){t=B(t.element).toString(),this.f[t].forEach(lt),delete this.f[t],this.s()},d.cd=function(){return this.get(fs)},d.yh=function(t){this.set(fs,t)},d.sf=function(t){var e,o,i=void 0!==t?t:[],r=i.length;for(this.cd().forEach(function(t){t.sf(i)}),t=ss(this),e=i.length;r<e;r++)(o=i[r]).opacity*=t.opacity,o.visible=o.visible&&t.visible,o.maxResolution=Math.min(o.maxResolution,t.maxResolution),o.minResolution=Math.max(o.minResolution,t.minResolution),void 0!==t.extent&&(o.extent=void 0!==o.extent?Se(o.extent,t.extent):t.extent);return i},d.uf=function(){return"ready"};var fs="layers";function gs(t){Oe.call(this,{code:t,units:"m",extent:vs,global:!0,worldExtent:bs,getPointResolution:function(t,e){return t/z(e[1]/6378137)}})}e(gs,Oe);var ds=6378137*Math.PI,vs=[-ds,-ds,ds,ds],bs=[-180,-85,180,85],ms="EPSG:3857 EPSG:102100 EPSG:102113 EPSG:900913 urn:ogc:def:crs:EPSG:6.18:3:3857 urn:ogc:def:crs:EPSG::3857 http://www.opengis.net/gml/srs/epsg.xml#3857".split(" ").map(function(t){return new gs(t)});function ws(t,e,o){var i=t.length;o=1<o?o:2,void 0===e&&(e=2<o?t.slice():Array(i));for(var r=0;r<i;r+=o){e[r]=ds*t[r]/180;var n=6378137*Math.log(Math.tan(Math.PI*(t[r+1]+90)/360));ds<n?n=ds:n<-ds&&(n=-ds),e[r+1]=n}return e}function xs(t,e,o){var i=t.length;o=1<o?o:2,void 0===e&&(e=2<o?t.slice():Array(i));for(var r=0;r<i;r+=o)e[r]=180*t[r]/ds,e[r+1]=360*Math.atan(Math.exp(t[r+1]/6378137))/Math.PI-90;return e}var Ss=new Ne(6378137);function Ms(t,e){Oe.call(this,{code:t,units:"degrees",extent:Ps,axisOrientation:e,global:!0,metersPerUnit:Ts,worldExtent:Ps})}e(Ms,Oe);var Ps=[-180,-90,180,90],Ts=Math.PI*Ss.radius/180,As=[new Ms("CRS:84"),new Ms("EPSG:4326","neu"),new Ms("urn:ogc:def:crs:EPSG::4326","neu"),new Ms("urn:ogc:def:crs:EPSG:6.6:4326","neu"),new Ms("urn:ogc:def:crs:OGC:1.3:CRS84"),new Ms("urn:ogc:def:crs:OGC:2:84"),new Ms("http://www.opengis.net/gml/srs/epsg.xml#4326","neu"),new Ms("urn:x-ogc:def:crs:EPSG:4326","neu")];function Es(){var t,o,i;Xe(ms),Xe(As),t=ms,o=ws,i=xs,As.forEach(function(e){t.forEach(function(t){Ue(e,t,o),Ue(t,e,i)})})}function Cs(t,e,o,i,r){ft.call(this,t),this.vectorContext=e,this.frameState=o,this.context=i,this.glContext=r}function o(t){var e=Q({},t);delete e.source,ns.call(this,e),this.A=this.v=this.o=null,t.map&&this.setMap(t.map),c(this,St("source"),this.il,this),this.Pc(t.source||null)}function js(t,e){return t.visible&&e>=t.minResolution&&e<t.maxResolution}function Ls(){this.b={},this.a=0}e(Cs,ft),e(o,ns),(d=o.prototype).sf=function(t){return(t=t||[]).push(ss(this)),t},d.la=function(){return this.get("source")||null},d.uf=function(){var t=this.la();return t?t.W():"undefined"},d.Rm=function(){this.s()},d.il=function(){this.A&&(lt(this.A),this.A=null);var t=this.la();t&&(this.A=c(t,"change",this.Rm,this)),this.s()},d.setMap=function(t){this.o&&(lt(this.o),this.o=null),t||this.s(),this.v&&(lt(this.v),this.v=null),t&&(this.o=c(t,"precompose",function(t){var e=ss(this);e.me=!1,e.zIndex=1/0,t.frameState.layerStatesArray.push(e),t.frameState.layerStates[B(this)]=e},this),this.v=c(this,"change",t.render,t),this.s())},d.Pc=function(t){this.set("source",t)},Ls.prototype.clear=function(){this.b={},this.a=0},Ls.prototype.get=function(t,e,o){return(t=e+":"+t+":"+(o?Ti(o):"null"))in this.b?this.b[t]:null},Ls.prototype.set=function(t,e,o,i){this.b[e+":"+t+":"+(o?Ti(o):"null")]=i,++this.a};var Rs=new Ls,Ns=Array(6);function Is(){return[1,0,0,1,0,0]}function Fs(t){return Os(t,1,0,0,1,0,0)}function ks(t,e){var o=t[0],i=t[1],r=t[2],n=t[3],s=t[4],p=t[5],a=e[0],h=e[1],l=e[2],u=e[3],c=e[4],e=e[5];return t[0]=o*a+r*h,t[1]=i*a+n*h,t[2]=o*l+r*u,t[3]=i*l+n*u,t[4]=o*c+r*e+s,t[5]=i*c+n*e+p,t}function Os(t,e,o,i,r,n,s){return t[0]=e,t[1]=o,t[2]=i,t[3]=r,t[4]=n,t[5]=s,t}function Ds(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Bs(t,e){var o=e[0],i=e[1];return e[0]=t[0]*o+t[2]*i+t[4],e[1]=t[1]*o+t[3]*i+t[5],e}function Us(t,e){var o=Math.cos(e),e=Math.sin(e);ks(t,Os(Ns,o,e,-e,o,0,0))}function Gs(t,e,o){return ks(t,Os(Ns,e,0,0,o,0,0))}function Ks(t,e,o){ks(t,Os(Ns,1,0,0,1,e,o))}function Xs(t,e,o,i,r,n,s,p){var a=Math.sin(n);return n=Math.cos(n),t[0]=i*n,t[1]=r*a,t[2]=-i*a,t[3]=r*n,t[4]=s*i*n-p*i*a+e,t[5]=s*r*a+p*r*n+o,t}function Vs(t){var e=t[0]*t[3]-t[1]*t[2],o=(V(0!=e,32),t[0]),i=t[1],r=t[2],n=t[3],s=t[4],p=t[5];return t[0]=n/e,t[1]=-i/e,t[2]=-r/e,t[3]=o/e,t[4]=(r*p-n*s)/e,t[5]=-(o*p-i*s)/e,t}function Ws(t,e){this.l=e,this.f={},this.v={}}function zs(t){var e=t.viewState,o=t.coordinateToPixelTransform,i=t.pixelToCoordinateTransform;Xs(o,t.size[0]/2,t.size[1]/2,1/e.resolution,-1/e.resolution,-e.rotation,-e.center[0],-e.center[1]),Vs(Ds(i,o))}function Hs(){if(32<Rs.a){var t,e,o=0;for(t in Rs.b)e=Rs.b[t],0!=(3&o++)||vt(e)||(delete Rs.b[t],--Rs.a)}}function Ys(t,e){var o=B(e).toString();return o in t.f?t.f[o]:(e=t.Ag(e),t.f[o]=e,t.v[o]=c(e,"change",t.Uk,t),e)}function Zs(t,e){for(var o in t.f)if(!(o in e.layerStates)){e.postRenderFunctions.push(t.Yo.bind(t));break}}function qs(t,e){return t.zIndex-e.zIndex}function Js(t){o.call(this,t||{})}function p(t){var e=Q({},t=t||{});delete e.preload,delete e.useInterimTilesOnError,o.call(this,e),this.l(void 0!==t.preload?t.preload:0),this.C(void 0===t.useInterimTilesOnError||t.useInterimTilesOnError)}e(Ws,ct),(d=Ws.prototype).oa=function(){for(var t in this.f)yt(this.f[t])},d.Ba=function(t,r,e,n,s,o,i){function p(t,e){var o=B(t).toString(),i=r.layerStates[B(e)].me;if(!(o in r.skippedFeatureUids)||i)return n.call(s,t,i?e:null)}var a,h,l=(c=r.viewState).resolution,u=c.projection,c=t;for(u.a&&(h=Te(u=u.G()),(f=t[0])<u[0]||f>u[2])&&(c=[f+h*Math.ceil((u[0]-f)/h),t[1]]),h=(u=r.layerStatesArray).length-1;0<=h;--h){var y=u[h],f=y.layer;if(js(y,l)&&o.call(i,f)&&(y=Ys(this,f),a=f.la()?y.Ba(f.la().D?c:t,r,e,p,s):a))return a}},d.Ch=function(t,e,o,i,r){return void 0!==this.Ba(t,e,o,Le,this,i,r)},d.Uk=function(){this.l.render()},d.ag=G,d.Yo=function(t,e){for(var o in this.f){var i;e&&o in e.layerStates||(o=o,i=this.f[o],delete this.f[o],lt(this.v[o]),delete this.v[o],yt(i))}},e(Js,o),e(p,o),p.prototype.f=function(){return this.get(_s)},p.prototype.l=function(t){this.set(_s,t)},p.prototype.c=function(){return this.get($s)},p.prototype.C=function(t){this.set($s,t)};var _s="preload",$s="useInterimTilesOnError";function Qs(t,e,o,i,r){dt.call(this),this.j=r,this.extent=t,this.f=o,this.resolution=e,this.state=i}function tp(t,e,o,i,r,n,s){Qs.call(this,t,e,o,ep,i),this.o=r,this.g=new Image,null!==n&&(this.g.crossOrigin=n),this.i={},this.c=null,this.state=ep,this.l=s}e(Qs,dt),Qs.prototype.s=function(){this.b("change")},Qs.prototype.G=function(){return this.extent},Qs.prototype.W=function(){return this.state},e(tp,Qs),tp.prototype.a=function(t){var e;return void 0!==t?(t=B(t))in this.i?this.i[t]:(e=ot(this.i)?this.g:this.g.cloneNode(!1),this.i[t]=e):this.g},tp.prototype.v=function(){this.state=rp,this.c.forEach(lt),this.c=null,this.s()},tp.prototype.H=function(){void 0===this.resolution&&(this.resolution=xe(this.extent)/this.g.height),this.state=ip,this.c.forEach(lt),this.c=null,this.s()},tp.prototype.load=function(){this.state!=ep&&this.state!=rp||(this.state=op,this.s(),this.c=[at(this.g,"error",this.v,this),at(this.g,"load",this.H,this)],this.l(this,this.o))};var ep=0,op=1,ip=2,rp=3,np=[0,0,0,1],sp=[],pp=[0,0,0,1];function ap(t,e,o,i){0!==e&&(t.translate(o,i),t.rotate(e),t.translate(-o,-i))}function hp(t){this.l=t.opacity,this.H=t.rotateWithView,this.o=t.rotation,this.i=t.scale,this.u=t.snapToPixel}function lp(t){this.C=this.A=this.j=null,this.f=void 0!==t.fill?t.fill:null,this.P=[0,0],this.b=t.points,this.a=void 0!==t.radius?t.radius:t.radius1,this.c=void 0!==t.radius2?t.radius2:this.a,this.v=void 0!==t.angle?t.angle:0,this.g=void 0!==t.stroke?t.stroke:null,this.L=this.va=this.U=this.ra=null,this.D=t.atlasManager,up(this,this.D),hp.call(this,{opacity:1,rotateWithView:void 0!==t.rotateWithView&&t.rotateWithView,rotation:void 0!==t.rotation?t.rotation:0,scale:1,snapToPixel:void 0===t.snapToPixel||t.snapToPixel})}function up(t,e){var o,i,r="",n="",s=0,p=null,a=0,h=(t.g&&(i=Li(t.g.b),void 0===(a=t.g.f)&&(a=1),p=t.g.g,cr||(p=null),void 0===(n=t.g.i)&&(n="round"),void 0===(r=t.g.c)&&(r="round"),void 0===(s=t.g.j))&&(s=10),2*(t.a+a)+1),r={strokeStyle:i,Fi:a,size:h,lineCap:r,lineDash:p,lineJoin:n,miterLimit:s};void 0===e?(n=Ri(h,h),t.A=n.canvas,o=h=t.A.width,t.Eg(r,n,0,0),t.L=[r.size,r.size],t.f?t.C=t.A:(n=Ri(r.size,r.size),t.C=n.canvas,t.Dg(r,n,0,0))):(h=Math.round(h),(n=!t.f)&&(o=t.Dg.bind(t,r)),s=t.g?(void 0===(s=t.g).a&&(s.a="s",s.a=s.b?"string"==typeof s.b?s.a+s.b:s.a+B(s.b).toString():s.a+"-",s.a+=","+(void 0!==s.c?s.c.toString():"-")+","+(s.g?s.g.toString():"-")+","+(void 0!==s.i?s.i:"-")+","+(void 0!==s.j?s.j.toString():"-")+","+(void 0!==s.f?s.f.toString():"-")),s.a):"-",p=t.f?(void 0===(p=t.f).a&&(p.a=p.b instanceof CanvasPattern||p.b instanceof CanvasGradient?B(p.b).toString():"f"+(p.b?Ti(p.b):"-")),p.a):"-",t.j&&s==t.j[1]&&p==t.j[2]&&t.a==t.j[3]&&t.c==t.j[4]&&t.v==t.j[5]&&t.b==t.j[6]||(t.j=["r"+s+p+(void 0!==t.a?t.a.toString():"-")+(void 0!==t.c?t.c.toString():"-")+(void 0!==t.v?t.v.toString():"-")+(void 0!==t.b?t.b.toString():"-"),s,p,t.a,t.c,t.v,t.b]),r=e.add(t.j[0],h,h,t.Eg.bind(t,r),o),t.A=r.image,t.P=[r.offsetX,r.offsetY],o=r.image.width,n?(t.C=r.yf,t.L=[r.yf.width,r.yf.height]):(t.C=t.A,t.L=[o,o])),t.ra=[h/2,h/2],t.U=[h,h],t.va=[o,o]}function cp(t){lp.call(this,{points:1/0,fill:(t=t||{}).fill,radius:t.radius,snapToPixel:t.snapToPixel,stroke:t.stroke,atlasManager:t.atlasManager})}function yp(t){this.b=void 0!==(t=t||{}).color?t.color:null,this.a=void 0}function fp(t){this.b=void 0!==(t=t||{}).color?t.color:null,this.c=t.lineCap,this.g=void 0!==t.lineDash?t.lineDash:null,this.i=t.lineJoin,this.j=t.miterLimit,this.f=t.width,this.a=void 0}function gp(t){t=t||{},this.i=null,this.c=wp,void 0!==t.geometry&&this.Pa(t.geometry),this.f=void 0!==t.fill?t.fill:null,this.a=void 0!==t.image?t.image:null,this.g=void 0!==t.stroke?t.stroke:null,this.j=void 0!==t.text?t.text:null,this.b=t.zIndex}function dp(t){var e;return"function"!=typeof t&&(e=Array.isArray(t)?t:(V(t instanceof gp,41),[t]),t=function(){return e}),t}(d=hp.prototype).ye=function(){return this.l},d.ze=function(){return this.H},d.Ae=function(){return this.o},d.Be=function(){return this.i},d.ee=function(){return this.u},d.dd=function(t){this.l=t},d.Ce=function(t){this.o=t},d.ed=function(t){this.i=t},e(lp,hp),(d=lp.prototype).clone=function(){var t=new lp({fill:this.f?this.f.clone():void 0,points:this.c!==this.a?this.b/2:this.b,radius:this.a,radius2:this.c,angle:this.v,snapToPixel:this.u,stroke:this.g?this.g.clone():void 0,rotation:this.o,rotateWithView:this.H,atlasManager:this.D});return t.dd(this.l),t.ed(this.i),t},d.Ac=function(){return this.ra},d.Nh=function(){return this.v},d.Oh=function(){return this.f},d.Lf=function(){return this.C},d.Ic=function(){return this.A},d.de=function(){return this.va},d.xe=function(){return ip},d.Jc=function(){return this.P},d.Ph=function(){return this.b},d.Qh=function(){return this.a},d.Vg=function(){return this.c},d.ac=function(){return this.U},d.Rh=function(){return this.g},d.eh=G,d.load=G,d.Ii=G,d.Eg=function(t,e,o,i){var r;if(e.setTransform(1,0,0,1,0,0),e.translate(o,i),e.beginPath(),1/0===this.b)e.arc(t.size/2,t.size/2,this.a,0,2*Math.PI,!0);else for(this.c!==this.a&&(this.b*=2),o=0;o<=this.b;o++)i=2*o*Math.PI/this.b-Math.PI/2+this.v,r=0==o%2?this.a:this.c,e.lineTo(t.size/2+r*Math.cos(i),t.size/2+r*Math.sin(i));this.f&&(e.fillStyle=Li(this.f.b),e.fill()),this.g&&(e.strokeStyle=t.strokeStyle,e.lineWidth=t.Fi,t.lineDash&&e.setLineDash(t.lineDash),e.lineCap=t.lineCap,e.lineJoin=t.lineJoin,e.miterLimit=t.miterLimit,e.stroke()),e.closePath()},d.Dg=function(t,e,o,i){var r;if(e.setTransform(1,0,0,1,0,0),e.translate(o,i),e.beginPath(),1/0===this.b)e.arc(t.size/2,t.size/2,this.a,0,2*Math.PI,!0);else for(this.c!==this.a&&(this.b*=2),o=0;o<=this.b;o++)r=2*o*Math.PI/this.b-Math.PI/2+this.v,i=0==o%2?this.a:this.c,e.lineTo(t.size/2+i*Math.cos(r),t.size/2+i*Math.sin(r));e.fillStyle=np,e.fill(),this.g&&(e.strokeStyle=t.strokeStyle,e.lineWidth=t.Fi,t.lineDash&&e.setLineDash(t.lineDash),e.stroke()),e.closePath()},e(cp,lp),cp.prototype.clone=function(){var t=new cp({fill:this.f?this.f.clone():void 0,stroke:this.g?this.g.clone():void 0,radius:this.a,snapToPixel:this.u,atlasManager:this.D});return t.dd(this.l),t.ed(this.i),t},cp.prototype.Qa=function(t){this.a=t,up(this,this.D)},yp.prototype.clone=function(){var t=this.b;return new yp({color:t&&t.slice?t.slice():t||void 0})},yp.prototype.g=function(){return this.b},yp.prototype.f=function(t){this.b=t,this.a=void 0},(d=fp.prototype).clone=function(){var t=this.b;return new fp({color:t&&t.slice?t.slice():t||void 0,lineCap:this.c,lineDash:this.g?this.g.slice():void 0,lineJoin:this.i,miterLimit:this.j,width:this.f})},d.In=function(){return this.b},d.kk=function(){return this.c},d.Jn=function(){return this.g},d.lk=function(){return this.i},d.rk=function(){return this.j},d.Kn=function(){return this.f},d.Ln=function(t){this.b=t,this.a=void 0},d.ip=function(t){this.c=t,this.a=void 0},d.setLineDash=function(t){this.g=t,this.a=void 0},d.jp=function(t){this.i=t,this.a=void 0},d.kp=function(t){this.j=t,this.a=void 0},d.np=function(t){this.f=t,this.a=void 0},(d=gp.prototype).clone=function(){var t=this.V();return new gp({geometry:t=t&&t.clone?t.clone():t,fill:this.f?this.f.clone():void 0,image:this.a?this.a.clone():void 0,stroke:this.g?this.g.clone():void 0,text:this.Ka()?this.Ka().clone():void 0,zIndex:this.b})},d.V=function(){return this.i},d.fk=function(){return this.c},d.Mn=function(){return this.f},d.Qn=function(t){this.f=t},d.Nn=function(){return this.a},d.Rn=function(t){this.a=t},d.On=function(){return this.g},d.Sn=function(t){this.g=t},d.Ka=function(){return this.j},d.Tn=function(t){this.j=t},d.Pn=function(){return this.b},d.Pa=function(e){this.c="function"==typeof e?e:"string"==typeof e?function(t){return t.get(e)}:e?function(){return e}:wp,this.i=e},d.Un=function(t){this.b=t};var vp=null;function bp(){var t,e;return vp||(t=new yp({color:"rgba(255,255,255,0.4)"}),e=new fp({color:"#3399CC",width:1.25}),vp=[new gp({image:new cp({fill:t,stroke:e,radius:5}),fill:t,stroke:e})]),vp}function mp(){var t={},e=[255,255,255,1],o=[0,153,255,1];return t.Polygon=[new gp({fill:new yp({color:[255,255,255,.5]})})],t.MultiPolygon=t.Polygon,t.LineString=[new gp({stroke:new fp({color:e,width:5})}),new gp({stroke:new fp({color:o,width:3})})],t.MultiLineString=t.LineString,t.Circle=t.Polygon.concat(t.LineString),t.Point=[new gp({image:new cp({radius:6,fill:new yp({color:o}),stroke:new fp({color:e,width:1.5})}),zIndex:1/0})],t.MultiPoint=t.Point,t.GeometryCollection=t.Polygon.concat(t.LineString,t.Point),t}function wp(t){return t.V()}function s(t){var e=Q({},t=t||{});delete e.style,delete e.renderBuffer,delete e.updateWhileAnimating,delete e.updateWhileInteracting,o.call(this,e),this.i=void 0!==t.renderBuffer?t.renderBuffer:100,this.C=null,this.j=void 0,this.l(t.style),this.Z=void 0!==t.updateWhileAnimating&&t.updateWhileAnimating,this.fa=void 0!==t.updateWhileInteracting&&t.updateWhileInteracting}function i(t){var e=Q({},t=t||{});delete e.preload,delete e.useInterimTilesOnError,s.call(this,e),this.P(t.preload||0),this.U(t.useInterimTilesOnError||!0),V(null==t.renderMode||t.renderMode==Mp||t.renderMode==Pp||t.renderMode==Tp,28),this.u=t.renderMode||Pp}e(s,o),s.prototype.D=function(){return this.C},s.prototype.L=function(){return this.j},s.prototype.l=function(t){this.C=void 0!==t?t:bp,this.j=null===t?void 0:dp(this.C),this.s()},e(i,s),i.prototype.f=function(){return this.get(xp)},i.prototype.c=function(){return this.get(Sp)},i.prototype.P=function(t){this.set(_s,t)},i.prototype.U=function(t){this.set($s,t)};var xp="preload",Sp="useInterimTilesOnError",Mp="image",Pp="hybrid",Tp="vector";function Ap(){}function Ep(t,e,o,i,r){this.f=t,this.A=e,this.c=o,this.C=i,this.fc=r,this.i=this.b=this.a=this.Z=this.Qa=this.U=null,this.fa=this.eb=this.H=this.ra=this.L=this.D=0,this.sa=!1,this.j=this.zb=0,this.na=!1,this.va=0,this.g="",this.xa=this.Fa=0,this.Ja=!1,this.o=this.Ua=0,this.P=this.v=this.l=null,this.u=[],this.Ob=Is()}function Cp(t,e,o){if(t.i){e=oo(e,0,o,2,t.C,t.u),o=t.f;var i,r,n=t.Ob,s=o.globalAlpha,p=(1!=t.H&&(o.globalAlpha=s*t.H),t.zb);for(t.sa&&(p+=t.fc),i=0,r=e.length;i<r;i+=2){var a,h,l=e[i]-t.D,u=e[i+1]-t.L;t.na&&(l=Math.round(l),u=Math.round(u)),0===p&&1==t.j||(Xs(n,a=l+t.D,h=u+t.L,t.j,t.j,p,-a,-h),o.setTransform.apply(o,n)),o.drawImage(t.i,t.eb,t.fa,t.va,t.ra,l,u,t.va,t.ra)}0===p&&1==t.j||o.setTransform(1,0,0,1,0,0),1!=t.H&&(o.globalAlpha=s)}}function jp(t,e,o,i){var r=0;if(t.P&&""!==t.g){t.l&&Np(t,t.l),t.v&&Ip(t,t.v);var n=t.P,s=t.f;for((a=t.Z)?(a.font!=n.font&&(a.font=s.font=n.font),a.textAlign!=n.textAlign&&(a.textAlign=s.textAlign=n.textAlign),a.textBaseline!=n.textBaseline&&(a.textBaseline=s.textBaseline=n.textBaseline)):(s.font=n.font,s.textAlign=n.textAlign,s.textBaseline=n.textBaseline,t.Z={font:n.font,textAlign:n.textAlign,textBaseline:n.textBaseline}),e=oo(e,r,o,i,t.C,t.u),n=t.f,s=t.Ua,t.Ja&&(s+=t.fc);r<o;r+=i){var p,a=e[r]+t.Fa,h=e[r+1]+t.xa;0===s&&1==t.o||(p=Xs(t.Ob,a,h,t.o,t.o,s,-a,-h),n.setTransform.apply(n,p)),t.v&&n.strokeText(t.g,a,h),t.l&&n.fillText(t.g,a,h)}0===s&&1==t.o||n.setTransform(1,0,0,1,0,0)}}function Lp(t,e,o,i,r,n){var s=t.f;for(t=oo(e,o,i,r,t.C,t.u),s.moveTo(t[0],t[1]),e=t.length,n&&(e-=2),o=2;o<e;o+=2)s.lineTo(t[o],t[o+1]);return n&&s.closePath(),i}function Rp(t,e,o,i,r){for(var n=0,s=i.length;n<s;++n)o=Lp(t,e,o,i[n],r,!0);return o}function Np(t,e){var o=t.f,i=t.U;i?i.fillStyle!=e.fillStyle&&(i.fillStyle=o.fillStyle=e.fillStyle):(o.fillStyle=e.fillStyle,t.U={fillStyle:e.fillStyle})}function Ip(t,e){var o=t.f,i=t.Qa;i?(i.lineCap!=e.lineCap&&(i.lineCap=o.lineCap=e.lineCap),cr&&!Rt(i.lineDash,e.lineDash)&&o.setLineDash(i.lineDash=e.lineDash),i.lineJoin!=e.lineJoin&&(i.lineJoin=o.lineJoin=e.lineJoin),i.lineWidth!=e.lineWidth&&(i.lineWidth=o.lineWidth=e.lineWidth),i.miterLimit!=e.miterLimit&&(i.miterLimit=o.miterLimit=e.miterLimit),i.strokeStyle!=e.strokeStyle&&(i.strokeStyle=o.strokeStyle=e.strokeStyle)):(o.lineCap=e.lineCap,cr&&o.setLineDash(e.lineDash),o.lineJoin=e.lineJoin,o.lineWidth=e.lineWidth,o.miterLimit=e.miterLimit,o.strokeStyle=e.strokeStyle,t.Qa={lineCap:e.lineCap,lineDash:e.lineDash,lineJoin:e.lineJoin,lineWidth:e.lineWidth,miterLimit:e.miterLimit,strokeStyle:e.strokeStyle})}function Fp(t){bt.call(this),this.a=t}function kp(t,e){var o=e.W();return o!=ip&&o!=rp&&c(e,"change",t.Fa,t),o==ep&&(e.load(),o=e.W()),o==ip}function Op(t){var e=t.a;e.Fb()&&"ready"==e.uf()&&t.s()}function Dp(t,e){e.Ih()&&t.postRenderFunctions.push(function(t,e,o){e=B(t).toString(),t.Wc(o.viewState.projection,o.usedTiles[e])}.bind(null,e))}function Bp(t,e){if(e)for(var o,i=0,r=e.length;i<r;++i)t[B(o=e[i]).toString()]=o}function Up(t,e){e=e.L;void 0!==e&&("string"==typeof e?t.logos[e]="":e&&(V("string"==typeof e.href,44),V("string"==typeof e.src,45),t.logos[e.src]=e.href))}function Gp(t,e,o,i){e=B(e).toString(),o=o.toString(),e in t?o in t[e]?(t=t[e][o],i.ea<t.ea&&(t.ea=i.ea),i.ca>t.ca&&(t.ca=i.ca),i.ga<t.ga&&(t.ga=i.ga),i.ja>t.ja&&(t.ja=i.ja)):t[e][o]=i:(t[e]={},t[e][o]=i)}function Kp(t,e,o,i,r,n,s,p,a,h){for(var l,u,c,y,f,g=B(e).toString(),d=(g in t.wantedTiles||(t.wantedTiles[g]={}),t.wantedTiles[g]),v=(t=t.tileQueue,o.minZoom),b=s;v<=b;--b)for(u=ai(o,n,b,u),c=o.Ha(b),y=u.ea;y<=u.ca;++y)for(f=u.ga;f<=u.ja;++f)s-b<=p?(0==(l=e.Dc(b,y,f,i,r)).W()&&(d[l.bb()]=!0,l.bb()in t.a||t.c([l,g,hi(o,l.Ca),c])),void 0!==a&&a.call(h,l)):e.ig(b,y,f,r)}function Xp(t){Fp.call(this,t),this.xa=Is()}function Vp(t,e,o){var i=e.pixelRatio,r=e.size[0]*i,n=e.size[1]*i,s=e.viewState.rotation,p=Me(o),a=Pe(o),h=be(o);o=ve(o),Bs(e.coordinateToPixelTransform,p),Bs(e.coordinateToPixelTransform,a),Bs(e.coordinateToPixelTransform,h),Bs(e.coordinateToPixelTransform,o),t.save(),ap(t,-s,r/2,n/2),t.beginPath(),t.moveTo(p[0]*i,p[1]*i),t.lineTo(a[0]*i,a[1]*i),t.lineTo(h[0]*i,h[1]*i),t.lineTo(o[0]*i,o[1]*i),t.clip(),ap(t,s,r/2,n/2)}function Wp(t,e,o,i,r){var n,s,p,a=t.a;vt(a,e)&&(n=i.size[0]*i.pixelRatio,s=i.size[1]*i.pixelRatio,ap(o,-(p=i.viewState.rotation),n/2,s/2),t=void 0!==r?r:zp(t,i,0),a.b(new Cs(e,new Ep(o,i.pixelRatio,i.extent,t,i.viewState.rotation),i,o,null)),ap(o,p,n/2,s/2))}function zp(t,e,o){var i=e.viewState,r=e.pixelRatio,n=r/i.resolution;return Xs(t.xa,r*e.size[0]/2,r*e.size[1]/2,n,-n,-i.rotation,-i.center[0]+o,-i.center[1])}function Hp(t){Xp.call(this,t),this.u=Is(),this.i=null}function Yp(t){Hp.call(this,t),this.f=null,this.j=Is()}function Zp(t){Hp.call(this,t),this.L=Ri(),this.j=null,this.f=[],this.H=pe(),this.Ua=new Jo(0,0,0,0),this.U=Is(),this.na=0}function qp(){}function Jp(t,e,o,i){this.zb=t,this.Qa=e,this.overlaps=i,this.c=0,this.resolution=o,this.va=this.ra=null,this.a=[],this.coordinates=[],this.Z=Is(),this.b=[],this.eb=[],this.sa=Is(),this.fa=Is()}function _p(t,e,o,i,r,n,s){for(var p,a,h=t.coordinates.length,l=t.mf(),u=(s&&(o+=r),s=[e[o],e[o+1]],[NaN,NaN]),c=!0,y=o+r;y<i;y+=r)u[0]=e[y],u[1]=e[y+1],c=(a=se(l,u))!==p?(c&&(t.coordinates[h++]=s[0],t.coordinates[h++]=s[1]),t.coordinates[h++]=u[0],t.coordinates[h++]=u[1],!1):1!==a||(t.coordinates[h++]=u[0],t.coordinates[h++]=u[1],!1),s[0]=u[0],s[1]=u[1],p=a;return(n&&c||y===o+r)&&(t.coordinates[h++]=s[0],t.coordinates[h++]=s[1]),h}function $p(t,e){t.ra=[0,e,0],t.a.push(t.ra),t.va=[0,e,0],t.b.push(t.va)}function Qp(t,e,o){var i;t.U&&(i=Bs(t.Z,t.U.slice()),e.translate(i[0],i[1]),e.rotate(o)),e.fill(),t.U&&e.setTransform.apply(e,t.fa)}function ta(t,e,o,i,r,n,s,p,a){var h;Rt(i,t.Z)?h=t.eb:(h=oo(t.coordinates,0,t.coordinates.length,2,i,t.eb),Ds(t.Z,i)),i=!ot(n);for(var l,u,c=0,O=s.length,y=0,f=t.sa,g=t.fa,d=0,v=0,b=t.a!=s||t.overlaps?0:200;c<O;)switch((L=s[c])[0]){case 0:y=L[1],i&&n[B(y).toString()]||!y.V()?c=L[2]:void 0===a||Ae(a,y.V().G())?++c:c=L[2]+1;break;case 1:b<d&&(Qp(t,e,r),d=0),b<v&&(e.stroke(),v=0),d||v||e.beginPath(),++c;break;case 2:m=h[y=L[1]],L=h[y+1],l=h[y+2]-m,y=h[y+3]-L,y=Math.sqrt(l*l+y*y),e.moveTo(m+y,L),e.arc(m,L,y,0,2*Math.PI,!0),++c;break;case 3:e.closePath(),++c;break;case 4:var y=L[1],m=L[2],w=L[3],x=L[4]*o,S=L[5]*o,M=L[6],P=L[7],T=L[8],A=L[9],E=(R=L[10],l=L[11],u=L[12],L[13]),C=L[14];for(R&&(l+=r);y<m;y+=2){var j,L=h[y]-x,R=h[y+1]-S,N=(E&&(L=Math.round(L),R=Math.round(R)),1==u&&0===l||(Xs(f,j=L+x,N=R+S,u,u,l,-j,-N),e.setTransform.apply(e,f)),j=e.globalAlpha,1!=P&&(e.globalAlpha=j*P),C+T>w.width?w.width-T:C),I=M+A>w.height?w.height-A:M;e.drawImage(w,T,A,N,I,L,R,N*o,I*o),1!=P&&(e.globalAlpha=j),1==u&&0===l||e.setTransform.apply(e,g)}++c;break;case 5:for(y=L[1],m=L[2],S=L[3],M=L[4]*o,P=L[5]*o,l=L[6],u=L[7]*o,w=L[8],x=L[9],(R=L[10])&&(l+=r);y<m;y+=2){for(L=h[y]+M,R=h[y+1]+P,1==u&&0===l||(Xs(f,L,R,u,u,l,-L,-R),e.setTransform.apply(e,f)),1<(A=(T=S.split("\n")).length)?R-=(A-1)/2*(E=Math.round(1.5*e.measureText("M").width)):E=0,C=0;C<A;C++)j=T[C],x&&e.strokeText(j,L,R),w&&e.fillText(j,L,R),R+=E;1==u&&0===l||e.setTransform.apply(e,g)}++c;break;case 6:if(void 0!==p&&(y=p(y=L[1])))return y;++c;break;case 7:b?d++:Qp(t,e,r),++c;break;case 8:for(y=L[1],m=L[2],L=h[y],u=(R=h[y+1])+.5|0,(l=L+.5|0)===F&&u===k||(e.moveTo(L,R),F=l,k=u),y+=2;y<m;y+=2)l=(L=h[y])+.5|0,u=(R=h[y+1])+.5|0,y!=m-2&&l===F&&u===k||(e.lineTo(L,R),F=l,k=u);++c;break;case 9:t.U=L[2],d&&(Qp(t,e,r),d=0),e.fillStyle=L[1],++c;break;case 10:var F=void 0===L[7]||L[7],D=L[8],k=L[2];v&&(e.stroke(),v=0),e.strokeStyle=L[1],e.lineWidth=F?k*o:k,e.lineCap=L[3],e.lineJoin=L[4],e.miterLimit=L[5],cr&&(k=L[6],F&&o!==D&&(k=k.map(function(t){return t*o/D}),L[6]=k,L[8]=o),e.setLineDash(k)),k=F=NaN,++c;break;case 11:e.font=L[1],e.textAlign=L[2],e.textBaseline=L[3],++c;break;case 12:b?v++:e.stroke(),++c;break;default:++c}d&&Qp(t,e,r),v&&e.stroke()}function ea(t){var e=t.b;e.reverse();for(var o,i,r=e.length,n=-1,s=0;s<r;++s)if(6==(i=(o=e[s])[0]))n=s;else if(0==i){for(o[2]=s,o=t.b,i=s;n<i;){var p=o[n];o[n]=o[i],o[i]=p,++n,--i}n=-1}}function oa(t,e){t.ra[2]=t.a.length,t.ra=null,t.va[2]=t.b.length,t.va=null;e=[6,e];t.a.push(e),t.b.push(e)}function ia(t,e,o,i){Jp.call(this,t,e,o,i),this.l=this.P=null,this.L=this.D=this.C=this.A=this.u=this.H=this.v=this.o=this.j=this.i=this.g=void 0}function ra(t,e,o,i){Jp.call(this,t,e,o,i),this.i=null,this.g={wd:void 0,rd:void 0,sd:null,td:void 0,ud:void 0,vd:void 0,Af:0,strokeStyle:void 0,lineCap:void 0,lineDash:null,lineJoin:void 0,lineWidth:void 0,miterLimit:void 0}}function na(t,e,o,i,r){var n=t.coordinates.length;return e=_p(t,e,o,i,r,!1,!1),t.a.push(n=[8,n,e]),t.b.push(n),i}function sa(t){var e=t.g,o=e.strokeStyle,i=e.lineCap,r=e.lineDash,n=e.lineJoin,s=e.lineWidth,p=e.miterLimit;e.wd==o&&e.rd==i&&Rt(e.sd,r)&&e.td==n&&e.ud==s&&e.vd==p||(e.Af!=t.coordinates.length&&(t.a.push([12]),e.Af=t.coordinates.length),t.a.push([10,o,s,i,n,p,r,!0,1],[1]),e.wd=o,e.rd=i,e.sd=r,e.td=n,e.ud=s,e.vd=p)}function pa(t,e,o,i){Jp.call(this,t,e,o,i),this.i=null,this.g={Bg:void 0,wd:void 0,rd:void 0,sd:null,td:void 0,ud:void 0,vd:void 0,fillStyle:void 0,strokeStyle:void 0,lineCap:void 0,lineDash:null,lineJoin:void 0,lineWidth:void 0,miterLimit:void 0}}function aa(t,e,o,i,r){var n=void 0!==(s=t.g).fillStyle,s=null!=s.strokeStyle,p=i.length,a=[1];for(t.a.push(a),t.b.push(a),a=0;a<p;++a){var h=i[a],l=t.coordinates.length;o=_p(t,e,o,h,r,!0,!s),t.a.push(o=[8,l,o]),t.b.push(o),s&&(t.a.push(o=[3]),t.b.push(o)),o=h}return t.b.push(e=[7]),n&&t.a.push(e),s&&(t.a.push(n=[12]),t.b.push(n)),o}function ha(t,e){var o,i=t.g,r=i.fillStyle,n=i.strokeStyle,s=i.lineCap,p=i.lineDash,a=i.lineJoin,h=i.lineWidth,l=i.miterLimit;void 0===r||"string"==typeof r&&i.Bg==r||(o=[9,r],"string"!=typeof r&&(r=e.G(),o.push([r[0],r[3]])),t.a.push(o),i.Bg=i.fillStyle),void 0===n||i.wd==n&&i.rd==s&&Rt(i.sd,p)&&i.td==a&&i.ud==h&&i.vd==l||(t.a.push([10,n,h,s,a,l,p,!0,1]),i.wd=n,i.rd=s,i.sd=p,i.td=a,i.ud=h,i.vd=l)}function la(t,e,o,i){Jp.call(this,t,e,o,i),this.L=this.D=this.C=null,this.l="",this.v=this.o=0,this.H=void 0,this.A=this.u=0,this.j=this.i=this.g=null}function ua(t,e,o,i,r){var n,s,p;""!==t.l&&t.j&&(t.g||t.i)&&(t.g&&(n=t.g,(s=t.C)&&s.fillStyle==n.fillStyle||(p=[9,n.fillStyle],t.a.push(p),t.b.push(p),s?s.fillStyle=n.fillStyle:t.C={fillStyle:n.fillStyle})),t.i&&(n=t.i,(s=t.D)&&s.lineCap==n.lineCap&&s.lineDash==n.lineDash&&s.lineJoin==n.lineJoin&&s.lineWidth==n.lineWidth&&s.miterLimit==n.miterLimit&&s.strokeStyle==n.strokeStyle||(p=[10,n.strokeStyle,n.lineWidth,n.lineCap,n.lineJoin,n.miterLimit,n.lineDash,!1,1],t.a.push(p),t.b.push(p),s?(s.lineCap=n.lineCap,s.lineDash=n.lineDash,s.lineJoin=n.lineJoin,s.lineWidth=n.lineWidth,s.miterLimit=n.miterLimit,s.strokeStyle=n.strokeStyle):t.D={lineCap:n.lineCap,lineDash:n.lineDash,lineJoin:n.lineJoin,lineWidth:n.lineWidth,miterLimit:n.miterLimit,strokeStyle:n.strokeStyle})),n=t.j,(s=t.L)&&s.font==n.font&&s.textAlign==n.textAlign&&s.textBaseline==n.textBaseline||(p=[11,n.font,n.textAlign,n.textBaseline],t.a.push(p),t.b.push(p),s?(s.font=n.font,s.textAlign=n.textAlign,s.textBaseline=n.textBaseline):t.L={font:n.font,textAlign:n.textAlign,textBaseline:n.textBaseline}),$p(t,r),e=[5,n=t.coordinates.length,e=_p(t,e,0,o,i,!1,!1),t.l,t.o,t.v,t.u,t.A,!!t.g,!!t.i,t.H],t.a.push(e),t.b.push(e),oa(t,r))}e(Ep,Ap),(d=Ep.prototype).hc=function(t){var e,o,i;Ae(this.c,t.G())&&((this.a||this.b)&&(this.a&&Np(this,this.a),this.b&&Ip(this,this.b),e=this.C,o=this.u,o=(e=(i=t.ia())?oo(i,0,i.length,t.pa(),e,o):null)[2]-e[0],i=e[3]-e[1],o=Math.sqrt(o*o+i*i),(i=this.f).beginPath(),i.arc(e[0],e[1],o,0,2*Math.PI),this.a&&i.fill(),this.b)&&i.stroke(),""!==this.g)&&jp(this,t.Fd(),2,2)},d.Gd=function(t){this.Ma(t.f,t.g),this.dc(t.a),this.$b(t.Ka())},d.tc=function(t){switch(t.Y()){case"Point":this.xc(t);break;case"LineString":this.Pb(t);break;case"Polygon":this.yc(t);break;case"MultiPoint":this.vc(t);break;case"MultiLineString":this.uc(t);break;case"MultiPolygon":this.wc(t);break;case"GeometryCollection":this.kf(t);break;case"Circle":this.hc(t)}},d.jf=function(t,e){t=(0,e.c)(t);t&&Ae(this.c,t.G())&&(this.Gd(e),this.tc(t))},d.kf=function(t){for(var e=0,o=(t=t.f).length;e<o;++e)this.tc(t[e])},d.xc=function(t){var e=t.ia();t=t.pa(),this.i&&Cp(this,e,e.length),""!==this.g&&jp(this,e,e.length,t)},d.vc=function(t){var e=t.ia();t=t.pa(),this.i&&Cp(this,e,e.length),""!==this.g&&jp(this,e,e.length,t)},d.Pb=function(t){var e,o;Ae(this.c,t.G())&&(this.b&&(Ip(this,this.b),e=this.f,o=t.ia(),e.beginPath(),Lp(this,o,0,o.length,t.pa(),!1),e.stroke()),""!==this.g)&&jp(this,t=Ec(t),2,2)},d.uc=function(t){var e=t.G();if(Ae(this.c,e)){if(this.b){Ip(this,this.b);var o,i,e=this.f,r=t.ia(),n=0,s=t.Kb(),p=t.pa();for(e.beginPath(),o=0,i=s.length;o<i;++o)n=Lp(this,r,n,s[o],p,!1);e.stroke()}""!==this.g&&jp(this,t=Cc(t),t.length,2)}},d.yc=function(t){var e;Ae(this.c,t.G())&&((this.b||this.a)&&(this.a&&Np(this,this.a),this.b&&Ip(this,this.b),(e=this.f).beginPath(),Rp(this,t.Vb(),0,t.Kb(),t.pa()),this.a&&e.fill(),this.b)&&e.stroke(),""!==this.g)&&jp(this,t=Fo(t),2,2)},d.wc=function(t){if(Ae(this.c,t.G())){if(this.b||this.a){this.a&&Np(this,this.a),this.b&&Ip(this,this.b);var e,o,i=this.f,r=Rc(t),n=0,s=t.c,p=t.pa();for(i.beginPath(),e=0,o=s.length;e<o;++e)n=Rp(this,r,n,s[e],p);this.a&&i.fill(),this.b&&i.stroke()}""!==this.g&&jp(this,t=Lc(t),t.length,2)}},d.Ma=function(t,e){var o,i,r,n;t?(o=t.b,this.a={fillStyle:Li(o||np)}):this.a=null,e?(o=e.b,t=e.c,i=e.g,r=e.i,n=e.f,e=e.j,this.b={lineCap:void 0!==t?t:"round",lineDash:i||sp,lineJoin:void 0!==r?r:"round",lineWidth:this.A*(void 0!==n?n:1),miterLimit:void 0!==e?e:10,strokeStyle:Li(o||pp)}):this.b=null},d.dc=function(t){var e,o,i,r;t?(e=t.Ac(),o=t.Ic(1),i=t.Jc(),r=t.ac(),this.D=e[0],this.L=e[1],this.ra=r[1],this.i=o,this.H=t.l,this.eb=i[0],this.fa=i[1],this.sa=t.H,this.zb=t.o,this.j=t.i,this.na=t.u,this.va=r[0]):this.i=null},d.$b=function(t){var e,o,i,r,n,s,p,a;t?((e=t.b)?(e=e.b,this.l={fillStyle:Li(e||np)}):this.l=null,(s=t.f)?(e=s.b,o=s.c,i=s.g,r=s.i,n=s.f,s=s.j,this.v={lineCap:void 0!==o?o:"round",lineDash:i||sp,lineJoin:void 0!==r?r:"round",lineWidth:void 0!==n?n:1,miterLimit:void 0!==s?s:10,strokeStyle:Li(e||pp)}):this.v=null,e=t.g,o=t.c,i=t.i,r=t.v,n=t.j,s=t.a,p=t.Ka(),a=t.l,t=t.o,this.P={font:void 0!==e?e:"10px sans-serif",textAlign:void 0!==a?a:"center",textBaseline:void 0!==t?t:"middle"},this.g=void 0!==p?p:"",this.Fa=void 0!==o?this.A*o:0,this.xa=void 0!==i?this.A*i:0,this.Ja=void 0!==r&&r,this.Ua=void 0!==n?n:0,this.o=this.A*(void 0!==s?s:1)):this.g=""},e(Fp,bt),Fp.prototype.Ba=G,Fp.prototype.te=Re,Fp.prototype.hf=function(o,i,r){return function(e,t){return t1(o,i,e,t,function(t){r[e]||(r[e]={}),r[e][t.Ca.toString()]=t})}},Fp.prototype.Fa=function(t){t.target.W()===ip&&Op(this)},e(Xp,Fp),Xp.prototype.C=function(t,e,o,i){if(this.Ba(t,e,0,Le,this))return o.call(i,this.a,null)},Xp.prototype.v=function(t,e,o,i){Wp(this,"postcompose",t,e,i)},e(Hp,Xp),Hp.prototype.D=function(t,e,o){Wp(this,"precompose",o,t,void 0);var i,r,n,s=this.l();s&&((i=void 0!==(r=e.extent))&&Vp(o,t,r),r=this.P(),n=o.globalAlpha,o.globalAlpha=e.opacity,o.drawImage(s,0,0,+s.width,+s.height,Math.round(r[4]),Math.round(r[5]),Math.round(s.width*r[0]),Math.round(s.height*r[3])),o.globalAlpha=n,i)&&o.restore(),this.v(o,t,e)},Hp.prototype.Ba=function(t,e,o,i,r){var n=this.a;return n.la().Ba(t,e.viewState.resolution,e.viewState.rotation,o,e.skippedFeatureUids,function(t){return i.call(r,t,n)})},Hp.prototype.C=function(t,e,o,i){var r;if(this.l())return this.a.la().Ba!==G?Xp.prototype.C.apply(this,arguments):(Wt(r=Bs(this.u,t.slice()),e.viewState.resolution/this.c),this.i||(this.i=Ri(1,1)),this.i.clearRect(0,0,1,1),this.i.drawImage(this.l(),r[0],r[1],1,1,0,0,1,1),0<(r=this.i.getImageData(0,0,1,1).data)[3]?o.call(i,this.a,r):void 0)},e(Yp,Hp),Yp.prototype.l=function(){return this.f?this.f.a():null},Yp.prototype.P=function(){return this.j},Yp.prototype.o=function(t,e){var o,i=t.pixelRatio,r=t.size,n=t.viewState,s=n.center,p=n.resolution,a=this.a.la(),h=t.viewHints,l=t.extent;return void 0!==e.extent&&(l=Se(l,e.extent)),h[qo]||h[1]||Ee(l)||(n=a.U(l,p,i,n.projection))&&kp(this,n)&&(this.f=n,this.c=p),this.f&&(h=(n=this.f).G(),l=n.resolution,e=n.f,h=Xs(this.j,i*r[0]/2,i*r[1]/2,o=i*l/(p*e),o,0,e*(h[0]-s[0])/l,e*(s[1]-h[3])/l),Xs(this.u,i*r[0]/2-h[4],i*r[1]/2-h[5],i/p,-i/p,0,-s[0],-s[1]),Bp(t.attributions,n.j),Up(t,a)),!!this.f},e(Zp,Hp),Zp.prototype.o=function(t,e){var o=t.pixelRatio,i=t.size,r=(s=t.viewState).projection,n=s.resolution,s=s.center,p=this.a,a=p.la(),h=a.g,l=a.Db(r),u=l.Ec(n,this.na),c=l.Ha(u),y=t.extent;if(Ee(y=void 0!==e.extent?Se(y,e.extent):y))return!1;for(var f,g,d=pi(l,y,c),v=l.Kc(u),b=l.Ha(u),m=ei(l.Za(u),l.j),w=(b=ae(v[0]+d.ea*m[0]*b,v[1]+d.ga*m[1]*b,v[0]+(d.ca+1)*m[0]*b,v[1]+(d.ja+1)*m[1]*b,void 0),v=a.jb(o),(m={})[u]={},this.hf(a,r,m)),x=p.c(),S=this.H,M=this.Ua,P=!1,T=d.ea;T<=d.ca;++T)for(f=d.ga;f<=d.ja;++f)(R=(g=a.Dc(u,T,f,o,r)).W())==Hr||4==R||3==R&&!x?R==Hr&&(m[u][g.Ca.toString()]=g,P||-1!=this.f.indexOf(g)||(P=!0)):ni(l,(g=zr(g)).Ca,w,M,S)||(g=si(l,g.Ca,M,S))&&w(u+1,g);if(T=t.viewHints,!(this.c&&16<Date.now()-t.time&&(T[qo]||T[1])||!P&&this.j&&ue(this.j,b)&&this.Ja==h)){g=a.Dd(u,o,r),T=(d.ca-d.ea+1)*g[0],g=(d.ja-d.ga+1)*g[0],f=(P=this.L).canvas,w=a.tf(r),f.width!=T||f.height!=g?(f.width=T,f.height=g):P.clearRect(0,0,T,g),this.f.length=0,(x=Object.keys(m).map(Number)).sort(At);for(var A,E,C,j,L,R=0,N=x.length;R<N;++R)for(var I in T=x[R],M=a.Dd(T,o,r),A=(g=l.Ha(T))/c,E=v*a.qf(r),C=m[T])g=C[I],f=l.Na(g.Ca,S),T=(f[0]-b[0])/c*v,f=(b[3]-f[3])/c*v,j=M[0]*A,L=M[1]*A,w||P.clearRect(T,f,j,L),this.A(g,t,e,T,f,j,L,E),this.f.push(g);this.Ja=h,this.c=c,this.j=b}return I=o/v*this.c/n,I=Xs(this.U,o*i[0]/2,o*i[1]/2,I,I,0,v*(this.j[0]-s[0])/this.c,v*(s[1]-this.j[3])/this.c),Xs(this.u,o*i[0]/2-I[4],o*i[1]/2-I[5],o/n,-o/n,0,-s[0],-s[1]),Gp(t.usedTiles,a,u,d),Kp(t,a,l,o,r,y,u,p.f()),Dp(t,a),Up(t,a),0<this.f.length},Zp.prototype.A=function(t,e,o,i,r,n,s,p){(t=t.ub())&&this.L.drawImage(t,p,p,t.width-2*p,t.height-2*p,i,r,n,s)},Zp.prototype.l=function(){return this.L.canvas},Zp.prototype.P=function(){return this.U},e(Jp,Ap),Jp.prototype.f=function(t,e,o,i,r){ta(this,t,e,o,i,r,this.a,void 0,void 0)},Jp.prototype.se=G,Jp.prototype.mf=function(){return this.Qa},e(ia,Jp),ia.prototype.xc=function(t,e){var o,i;this.l&&($p(this,e),i=t.ia(),o=this.coordinates.length,i=_p(this,i,0,i.length,t.pa(),!1,!1),this.a.push([4,o,i,this.l,this.g,this.i,this.j,this.o,this.v,this.H,this.u,this.A,this.C,this.D,this.L]),this.b.push([4,o,i,this.P,this.g,this.i,this.j,this.o,this.v,this.H,this.u,this.A,this.C,this.D,this.L]),oa(this,e))},ia.prototype.vc=function(t,e){var o,i;this.l&&($p(this,e),i=t.ia(),o=this.coordinates.length,i=_p(this,i,0,i.length,t.pa(),!1,!1),this.a.push([4,o,i,this.l,this.g,this.i,this.j,this.o,this.v,this.H,this.u,this.A,this.C,this.D,this.L]),this.b.push([4,o,i,this.P,this.g,this.i,this.j,this.o,this.v,this.H,this.u,this.A,this.C,this.D,this.L]),oa(this,e))},ia.prototype.se=function(){ea(this),this.i=this.g=void 0,this.l=this.P=null,this.L=this.D=this.A=this.u=this.H=this.v=this.o=this.C=this.j=void 0},ia.prototype.dc=function(t){var e=t.Ac(),o=t.ac(),i=t.Lf(1),r=t.Ic(1),n=t.Jc();this.g=e[0],this.i=e[1],this.P=i,this.l=r,this.j=o[1],this.o=t.l,this.v=n[0],this.H=n[1],this.u=t.H,this.A=t.o,this.C=t.i,this.D=t.u,this.L=o[0]},e(ra,Jp),(d=ra.prototype).mf=function(){return this.i||(this.i=ee(this.Qa),0<this.c&&te(this.i,this.resolution*(this.c+1)/2,this.i)),this.i},d.Pb=function(t,e){var o=this.g,i=o.lineWidth;void 0!==o.strokeStyle&&void 0!==i&&(sa(this),$p(this,e),this.b.push([10,o.strokeStyle,o.lineWidth,o.lineCap,o.lineJoin,o.miterLimit,o.lineDash,!0,1],[1]),na(this,o=t.ia(),0,o.length,t.pa()),this.b.push([12]),oa(this,e))},d.uc=function(t,e){var o=(i=this.g).lineWidth;if(void 0!==i.strokeStyle&&void 0!==o){sa(this),$p(this,e),this.b.push([10,i.strokeStyle,i.lineWidth,i.lineCap,i.lineJoin,i.miterLimit,i.lineDash,!0,1],[1]);for(var i=t.Kb(),o=t.ia(),r=t.pa(),n=0,s=0,p=i.length;s<p;++s)n=na(this,o,n,i[s],r);this.b.push([12]),oa(this,e)}},d.se=function(){this.g.Af!=this.coordinates.length&&this.a.push([12]),ea(this),this.g=null},d.Ma=function(t,e){var o=e.b;this.g.strokeStyle=Li(o||pp),o=e.c,this.g.lineCap=void 0!==o?o:"round",o=e.g,this.g.lineDash=o||sp,o=e.i,this.g.lineJoin=void 0!==o?o:"round",o=e.f,this.g.lineWidth=void 0!==o?o:1,o=e.j,this.g.miterLimit=void 0!==o?o:10,this.g.lineWidth>this.c&&(this.c=this.g.lineWidth,this.i=null)},e(pa,Jp),(d=pa.prototype).hc=function(t,e){var o,i=this.g,r=i.strokeStyle;void 0===i.fillStyle&&void 0===r||(ha(this,t),$p(this,e),this.b.push([9,Ti(np)]),void 0!==i.strokeStyle&&this.b.push([10,i.strokeStyle,i.lineWidth,i.lineCap,i.lineJoin,i.miterLimit,i.lineDash,!0,1]),o=t.ia(),r=this.coordinates.length,_p(this,o,0,o.length,t.pa(),!1,!1),this.a.push(o=[1],r=[2,r]),this.b.push(o,r),this.b.push(r=[7]),void 0!==i.fillStyle&&this.a.push(r),void 0!==i.strokeStyle&&(this.a.push(i=[12]),this.b.push(i)),oa(this,e))},d.yc=function(t,e){var o=this.g,o=(ha(this,t),$p(this,e),this.b.push([9,Ti(np)]),void 0!==o.strokeStyle&&this.b.push([10,o.strokeStyle,o.lineWidth,o.lineCap,o.lineJoin,o.miterLimit,o.lineDash,!0,1]),t.Kb());aa(this,t.Vb(),0,o,t.pa()),oa(this,e)},d.wc=function(t,e){var o=(i=this.g).strokeStyle;if(void 0!==i.fillStyle||void 0!==o){ha(this,t),$p(this,e),this.b.push([9,Ti(np)]),void 0!==i.strokeStyle&&this.b.push([10,i.strokeStyle,i.lineWidth,i.lineCap,i.lineJoin,i.miterLimit,i.lineDash,!0,1]);for(var i=t.c,o=Rc(t),r=t.pa(),n=0,s=0,p=i.length;s<p;++s)n=aa(this,o,n,i[s],r);oa(this,e)}},d.se=function(){ea(this),this.g=null;var t=this.zb;if(0!==t)for(var e=this.coordinates,o=0,i=e.length;o<i;++o)e[o]=t*Math.round(e[o]/t)},d.mf=function(){return this.i||(this.i=ee(this.Qa),0<this.c&&te(this.i,this.resolution*(this.c+1)/2,this.i)),this.i},d.Ma=function(t,e){var o,i=this.g;t?(o=t.b,i.fillStyle=Li(o||np)):i.fillStyle=void 0,e?(o=e.b,i.strokeStyle=Li(o||pp),o=e.c,i.lineCap=void 0!==o?o:"round",o=e.g,i.lineDash=o?o.slice():sp,o=e.i,i.lineJoin=void 0!==o?o:"round",o=e.f,i.lineWidth=void 0!==o?o:1,o=e.j,i.miterLimit=void 0!==o?o:10,i.lineWidth>this.c&&(this.c=i.lineWidth,this.i=null)):(i.strokeStyle=void 0,i.lineCap=void 0,i.lineDash=null,i.lineJoin=void 0,i.lineWidth=void 0,i.miterLimit=void 0)},e(la,Jp),la.prototype.$b=function(t){var e,o,i,r,n,s,p,a,h;t?((o=t.b)?(o=Li((o=o.b)||np),this.g?this.g.fillStyle=o:this.g={fillStyle:o}):this.g=null,(s=t.f)?(o=s.b,i=s.c,r=s.g,p=s.i,n=s.f,s=s.j,i=void 0!==i?i:"round",r=r?r.slice():sp,p=void 0!==p?p:"round",n=void 0!==n?n:1,s=void 0!==s?s:10,o=Li(o||pp),this.i?((a=this.i).lineCap=i,a.lineDash=r,a.lineJoin=p,a.lineWidth=n,a.miterLimit=s,a.strokeStyle=o):this.i={lineCap:i,lineDash:r,lineJoin:p,lineWidth:n,miterLimit:s,strokeStyle:o}):this.i=null,e=t.g,o=t.c,i=t.i,r=t.v,n=t.j,s=t.a,p=t.Ka(),a=t.l,h=t.o,t=void 0!==e?e:"10px sans-serif",a=void 0!==a?a:"center",h=void 0!==h?h:"middle",this.j?((e=this.j).font=t,e.textAlign=a,e.textBaseline=h):this.j={font:t,textAlign:a,textBaseline:h},this.l=void 0!==p?p:"",this.o=void 0!==o?o:0,this.v=void 0!==i?i:0,this.H=void 0!==r&&r,this.u=void 0!==n?n:0,this.A=void 0!==s?s:1):this.l=""};var ca=["Polygon","Circle","LineString","Image","Text"];function ya(t,e,o,i,r){this.H=t,this.c=e,this.o=i,this.v=o,this.i=r,this.a={},this.j=Ri(1,1),this.l=Is()}e(ya,qp);var fa={0:[[!0]]};function ga(t,e,o){var i,r=Math.floor(t.length/2);if(r<=e)for(i=r;i<e;i++)t[i][o]=!0;else if(e<r)for(i=e+1;i<r;i++)t[i][o]=!0}function da(t){for(var e in t.a){var o,i=t.a[e];for(o in i)i[o].se()}}function va(t,e){var t=t.c,o=t[0],i=t[1],r=t[2];return oo(o=[o,i,o,t=t[3],r,t,r,i],0,8,2,e,o),o}ya.prototype.Ba=function(t,e,o,i,r,n){var s,p,a,h,l,u,c=2*(i=Math.round(i))+1,y=Xs(this.l,i+.5,i+.5,1/e,-1/e,-o,-t[0],-t[1]),f=this.j,g=(f.canvas.width!==c||f.canvas.height!==c?(f.canvas.width=c,f.canvas.height=c):f.clearRect(0,0,c,c),void 0!==this.i&&(ye(s=pe(),t),te(s,e*(this.i+i),s)),function(t){if(void 0!==fa[t])return fa[t];for(var e=2*t+1,o=Array(e),i=0;i<e;i++)o[i]=Array(e);for(var e=t,r=i=0;i<=e;)ga(o,t+e,t+i),ga(o,t+i,t+e),ga(o,t-i,t+e),ga(o,t-e,t+i),ga(o,t-e,t-i),ga(o,t-i,t-e),ga(o,t+i,t-e),ga(o,t+e,t-i),0<2*((r+=1+2*++i)-e)+1&&(r+=1-2*--e);return fa[t]=o}(i)),d=this,v=f,b=y,m=o,w=r,x=function(t){for(var e=f.getImageData(0,0,c,c).data,o=0;o<c;o++)for(var i=0;i<c;i++)if(g[o][i]&&0<e[4*(i*c+o)+3])return(t=n(t))||void f.clearRect(0,0,c,c)},S=s,M=Object.keys(d.a).map(Number);for(M.sort(function(t,e){return e-t}),p=0,a=M.length;p<a;++p)for(l=d.a[M[p].toString()],h=ca.length-1;0<=h;--h)if(void 0!==(u=l[ca[h]])&&(u=ta(u,v,1,b,m,w,u.b,x,S)))return u},ya.prototype.b=function(t,e){var t=void 0!==t?t.toString():"0",o=this.a[t];return void 0===o&&(this.a[t]=o={}),void 0===(t=o[e])&&(t=new ba[e](this.H,this.c,this.v,this.o),o[e]=t),t},ya.prototype.g=function(){return ot(this.a)},ya.prototype.f=function(t,e,o,i,r,n){var s=Object.keys(this.a).map(Number),p=(s.sort(At),va(this,o));t.save(),t.beginPath(),t.moveTo(p[0],p[1]),t.lineTo(p[2],p[3]),t.lineTo(p[4],p[5]),t.lineTo(p[6],p[7]),t.clip(),n=n||ca;for(var a,h,l,u,p=0,c=s.length;p<c;++p)for(l=this.a[s[p].toString()],a=0,h=n.length;a<h;++a)void 0!==(u=l[n[a]])&&u.f(t,e,o,i,r);t.restore()};var ba={Circle:pa,Image:ia,LineString:ra,Polygon:pa,Text:la};function ma(t,e){return B(t)-B(e)}function wa(t,e){t=.5*t/e;return t*t}function xa(t,e,o,i,r,n){var s,p,a=!1;return(s=o.a)&&((p=s.xe())==ip||p==rp?s.Ii(r,n):(p==ep&&s.load(),s.eh(r,n),a=!0)),(r=(0,o.c)(e))&&(i=r.Bd(i),(0,Sa[i.Y()])(t,i,o,e)),a}var Sa={Point:function(t,e,o,i){var r=o.a;if(r){if(r.xe()!=ip)return;var n=t.b(o.b,"Image");n.dc(r),n.xc(e,i)}(r=o.Ka())&&((t=t.b(o.b,"Text")).$b(r),ua(t,e.ia(),2,2,i))},LineString:function(t,e,o,i){var r,n=o.g;n&&((r=t.b(o.b,"LineString")).Ma(null,n),r.Pb(e,i)),(n=o.Ka())&&((t=t.b(o.b,"Text")).$b(n),ua(t,Ec(e),2,2,i))},Polygon:function(t,e,o,i){var r,n=o.f,s=o.g;(n||s)&&((r=t.b(o.b,"Polygon")).Ma(n,s),r.yc(e,i)),(n=o.Ka())&&((t=t.b(o.b,"Text")).$b(n),ua(t,Fo(e),2,2,i))},MultiPoint:function(t,e,o,i){var r=o.a;if(r){if(r.xe()!=ip)return;var n=t.b(o.b,"Image");n.dc(r),n.vc(e,i)}(r=o.Ka())&&((t=t.b(o.b,"Text")).$b(r),ua(t,o=e.ia(),o.length,e.pa(),i))},MultiLineString:function(t,e,o,i){var r,n=o.g;n&&((r=t.b(o.b,"LineString")).Ma(null,n),r.uc(e,i)),(n=o.Ka())&&((t=t.b(o.b,"Text")).$b(n),ua(t,e=Cc(e),e.length,2,i))},MultiPolygon:function(t,e,o,i){var r,n=o.f,s=o.g;(s||n)&&((r=t.b(o.b,"Polygon")).Ma(n,s),r.wc(e,i)),(n=o.Ka())&&((t=t.b(o.b,"Text")).$b(n),ua(t,e=Lc(e),e.length,2,i))},GeometryCollection:function(t,e,o,i){for(var r=0,n=(e=e.f).length;r<n;++r)(0,Sa[e[r].Y()])(t,e[r],o,i)},Circle:function(t,e,o,i){var r,n=o.f,s=o.g;(n||s)&&((r=t.b(o.b,"Circle")).Ma(n,s),r.hc(e,i)),(n=o.Ka())&&((t=t.b(o.b,"Text")).$b(n),ua(t,e.Fd(),2,2,i))}};function Ma(t){Xp.call(this,t),this.f=!1,this.u=-1,this.H=NaN,this.j=pe(),this.c=this.l=null,this.i=Ri()}function Pa(t){Zp.call(this,t),this.Z=!1,this.sa=Is(),this.na=t.u==Tp?1:0}e(Ma,Xp),Ma.prototype.D=function(t,e,o){var i=t.extent,r=t.pixelRatio,n=e.me?t.skippedFeatureUids:{},s=(p=t.viewState).projection,p=p.rotation,a=s.G(),h=this.a.la(),l=zp(this,t,0),u=(Wp(this,"precompose",o,t,l),e.extent),c=void 0!==u;if(c&&Vp(o,t,u),(u=this.c)&&!u.g()){var y,f,g=0,d=0,v=(y=(f=vt(this.a,"render")?(f=o.canvas.width,y=o.canvas.height,p&&(g=((v=Math.round(Math.sqrt(f*f+y*y)))-f)/2,d=(v-y)/2,f=y=v),this.i.canvas.width=f,this.i.canvas.height=y,this.i):o).globalAlpha,f.globalAlpha=e.opacity,f!=o&&f.translate(g,d),t.size[0]*r),b=t.size[1]*r;if(ap(f,-p,v/2,b/2),u.f(f,r,l,p,n),h.D&&s.a&&!re(a,i)){for(var s=i[0],h=Te(a),m=0;s<a[0];)l=zp(this,t,l=h*--m),u.f(f,r,l,p,n),s+=h;for(m=0,s=i[2];s>a[2];)l=zp(this,t,l=h*++m),u.f(f,r,l,p,n),s-=h;l=zp(this,t,0)}ap(f,p,v/2,b/2),f!=o&&(Wp(this,"render",f,t,l),o.drawImage(f.canvas,-g,-d),f.translate(-g,-d)),f.globalAlpha=y}c&&o.restore(),this.v(o,t,e,l)},Ma.prototype.Ba=function(t,e,o,i,r){var n,s;if(this.c)return n=this.a,s={},this.c.Ba(t,e.viewState.resolution,e.viewState.rotation,o,{},function(t){var e=B(t).toString();if(!(e in s))return s[e]=!0,i.call(r,t,n)})},Ma.prototype.A=function(){Op(this)},Ma.prototype.o=function(t){function e(t){var e,o=t.Gc();if(o?e=o.call(t,n):(o=a.j)&&(e=o(t,n)),e){if(o=!1,Array.isArray(e))for(var i=0,r=e.length;i<r;++i)o=xa(p,t,e[i],wa(n,s),this.A,this)||o;else o=xa(p,t,e,wa(n,s),this.A,this)||o;t=o,this.f=this.f||t}}var o,n,s,i,p,r,a=this.a,h=a.la(),l=(Bp(t.attributions,h.j),Up(t,h),t.viewHints[qo]),u=t.viewHints[1],c=a.Z,y=a.fa;return!this.f&&!c&&l||!y&&u||(o=t.extent,l=(y=t.viewState).projection,n=y.resolution,s=t.pixelRatio,u=a.g,i=a.i,void 0===(c=a.get("renderOrder"))&&(c=ma),o=te(o,i*n),i=y.projection.G(),h.D&&y.projection.a&&!re(i,t.extent)&&(t=Math.max(Te(o)/2,Te(i)),o[0]=i[0]-t,o[2]=i[2]+t),!this.f&&this.H==n&&this.u==u&&this.l==c&&re(this.j,o))||(this.c=null,this.f=!1,p=new ya(.5*n/s,o,n,h.xa,a.i),h.Ed(o,n,l),c?(r=[],h.Qb(o,function(t){r.push(t)},this),r.sort(c),r.forEach(e,this)):h.Qb(o,e,this),da(p),this.H=n,this.u=u,this.l=c,this.j=o,this.c=p),!0},e(Pa,Zp);var Ta={image:ca,hybrid:["Polygon","LineString"]},Aa={hybrid:["Image","Text"],vector:ca};function Ea(t,e){Ws.call(this,0,e),this.g=Ri(),this.b=this.g.canvas,this.b.style.width="100%",this.b.style.height="100%",this.b.className="ol-unselectable",t.insertBefore(this.b,t.childNodes[0]||null),this.a=!0,this.c=Is()}function Ca(t,e,o){var i,r,n,s,p,a=t.l,h=t.g;vt(a,e)&&(i=o.extent,r=o.pixelRatio,n=o.viewState.rotation,s=o.viewState,p=o.pixelRatio/s.resolution,t=Xs(t.c,t.b.width/2,t.b.height/2,p,-p,-s.rotation,-s.center[0],-s.center[1]),a.b(new Cs(e,new Ep(h,r,i,t,n),o,h,null)))}Pa.prototype.A=function(t,e,o,i,r,n,s,p){var a,h,l,u,c,y=t,f=this,g=y,d=e;function v(t){var e;if((o=t.Gc())?e=o.call(t,L):(o=b.j)&&(e=o(t,L)),e){Array.isArray(e)||(e=[e]);var o=N,i=R;if(e){var r=!1;if(Array.isArray(e))for(var n=0,s=e.length;n<s;++n)r=xa(i,t,e[n],o,this.fa,this)||r;else r=xa(i,t,e,o,this.fa,this)||r;t=r}else t=!1;this.Z=this.Z||t,S.xd=S.xd||t}}var b=f.a,m=d.pixelRatio,w=(d=d.viewState.projection,b.g),x=b.get("renderOrder")||null,S=g.g;if(S.xd||S.li!=w||S.bg!=x){S.jd=null,S.xd=!1;var M,P,T,A=b.la(),E=A.tileGrid,C=g.Ca,j=g.j,L=E.Ha(C[0]),R=("tile-pixels"==j.Eb()?(M=T=A.jb(),M=[0,0,(E=ei(E.Za(C[0])))[0]*M,E[1]*M]):(T=L,M=E.Na(C),Ze(d,j)||(P=!0,g.Ff(d))),S.xd=!1,new ya(0,M,T,A.i,b.i)),N=wa(T,m);for(g=g.i,x&&x!==S.bg&&g.sort(x),A=0,T=g.length;A<T;++A)m=g[A],P&&m.V().ob(j,d),v.call(f,m);da(R),S.li=w,S.bg=x,S.jd=R,S.resolution=NaN}this.a.u!=Tp&&(E=y,C=e,M=this.a,y=E.g,w=M.g,x=Ta[M.u])&&y.cg!==w&&(y.cg=w,a=E.Ca,h=E.Ca[0],w=C.pixelRatio,u=(l=M.la()).tileGrid,c=l.jb(),M=Fs(this.sa),"tile-pixels"==E.j.Eb()?Gs(M,a=w/c,a):(c=w/(c=u.Ha(h)),a=u.Na(a,this.H),Gs(M,c,-c),Ks(M,-a[0],-a[3])),E=E.f,C=l.Dd(h,w,C.viewState.projection),E.canvas.width=C[0],E.canvas.height=C[1],y.jd.f(E,w,M,0,{},x)),Zp.prototype.A.apply(this,arguments)},Pa.prototype.Ba=function(t,e,o,i,r){for(var n,s,p,a,h=e.viewState.resolution,l=(e=e.viewState.rotation,o=null==o?0:o,this.a),u={},c=this.f,y=l.la(),f=y.tileGrid,g=0,d=c.length;g<d;++g)s=(a=c[g]).Ca,ie(te(p=y.tileGrid.Na(s,this.H),o*h),t)&&(s="tile-pixels"===a.j.Eb()?(p=Me(p),h=y.jb(),s=f.Ha(s[0])/h,[(t[0]-p[0])/s,(p[1]-t[1])/s]):t,a=a.g.jd,n=n||a.Ba(s,h,e,o,{},function(t){var e=B(t).toString();if(!(e in u))return u[e]=!0,i.call(r,t,l)}));return n},Pa.prototype.fa=function(){Op(this)},Pa.prototype.v=function(t,e,o){var i=Aa[this.a.u];if(i)for(var r=e.pixelRatio,n=e.viewState.rotation,s=e.size,p=Math.round(r*s[0]/2),s=Math.round(r*s[1]/2),a=this.f,h=[],l=[],u=a.length-1;0<=u;--u){var c,y,f,g,d,v,b=a[u],m=b;for(v=e,v="tile-pixels"==m.j.Eb()?(g=(c=this.a.la()).tileGrid,d=m.Ca,c=g.Ha(d[0])/c.jb(),m=v.viewState,y=v.pixelRatio,f=m.resolution/y,d=g.Na(d,this.H),g=m.center,d=Me(d),v=v.size,Xs(this.sa,Math.round(y*v[0]/2),Math.round(y*v[1]/2),c/f,c/f,m.rotation,(d[0]-g[0])/c,(g[1]-d[1])/c)):zp(this,v,0),c=va(b.g.jd,v),m=b.Ca[0],t.save(),t.globalAlpha=o.opacity,ap(t,-n,p,s),y=0,f=h.length;y<f;++y)g=h[y],m<l[y]&&(t.beginPath(),t.moveTo(c[0],c[1]),t.lineTo(c[2],c[3]),t.lineTo(c[4],c[5]),t.lineTo(c[6],c[7]),t.moveTo(g[6],g[7]),t.lineTo(g[4],g[5]),t.lineTo(g[2],g[3]),t.lineTo(g[0],g[1]),t.clip());b.g.jd.f(t,r,v,n,{},i),t.restore(),h.push(c),l.push(m)}Zp.prototype.v.apply(this,arguments)},e(Ea,Ws),Ea.prototype.Ag=function(t){return t instanceof Js?new Yp(t):t instanceof p?new Zp(t):t instanceof i?new Pa(t):t instanceof s?new Ma(t):null},Ea.prototype.Y=function(){return"canvas"},Ea.prototype.ag=function(t){if(t){for(var e,o,i=this.g,r=t.pixelRatio,n=Math.round(t.size[0]*r),r=Math.round(t.size[1]*r),s=(this.b.width!=n||this.b.height!=r?(this.b.width=n,this.b.height=r):i.clearRect(0,0,n,r),t.viewState.rotation),p=(zs(t),Ca(this,"precompose",t),t.layerStatesArray),a=(Nt(p),ap(i,s,n/2,r/2),t.viewState.resolution),h=0,l=p.length;h<l;++h)e=Ys(this,e=(o=p[h]).layer),js(o,a)&&"ready"==o.Ei&&e.o(t,o)&&e.D(t,o,i);ap(i,-s,n/2,r/2),Ca(this,"postcompose",t),this.a||(this.b.style.display="",this.a=!0),Zs(this,t),t.postRenderFunctions.push(Hs)}else this.a&&(this.b.style.display="none",this.a=!1)},Ea.prototype.Bh=function(t,e,o,i,r,n){var s=e.viewState.resolution,p=e.layerStatesArray,a=p.length;for(t=Bs(e.pixelToCoordinateTransform,t.slice()),--a;0<=a;--a){var h,l=(h=p[a]).layer;if(js(h,s)&&r.call(n,l)&&(h=Ys(this,l).C(t,e,o,i)))return h}};var ja=[0,0,0,1],La=[],Ra=[0,0,0,1];function Na(t,e,o,i,r,n){return(t=(o-t)*(n-e)-(r-t)*(i-e))<=Ia&&-Ia<=t?void 0:0<t}var Ia=Number.EPSILON||2220446049250313e-31;function Fa(t){this.b=t}function ka(t){this.b=t}function Oa(t){this.b=t}function Da(){this.b="precision mediump float;varying vec2 a;varying vec2 b;varying float c;varying float d;uniform float m;uniform vec4 n;uniform vec4 o;uniform vec2 p;void main(void){vec2 windowCenter=vec2((a.x+1.0)/2.0*p.x*d,(a.y+1.0)/2.0*p.y*d);vec2 windowOffset=vec2((b.x+1.0)/2.0*p.x*d,(b.y+1.0)/2.0*p.y*d);float radius=length(windowCenter-windowOffset);float dist=length(windowCenter-gl_FragCoord.xy);if(dist>radius+c){if(o.a==0.0){gl_FragColor=n;}else{gl_FragColor=o;}gl_FragColor.a=gl_FragColor.a-(dist-(radius+c));}else if(n.a==0.0){gl_FragColor=o;if(dist<radius-c){gl_FragColor.a=gl_FragColor.a-(radius-c-dist);}} else{gl_FragColor=n;float strokeDist=radius-c;float antialias=2.0*d;if(dist>strokeDist){gl_FragColor=o;}else if(dist>=strokeDist-antialias){float step=smoothstep(strokeDist-antialias,strokeDist,dist);gl_FragColor=mix(n,o,step);}} gl_FragColor.a=gl_FragColor.a*m;if(gl_FragColor.a<=0.0){discard;}}"}e(ka,Fa),ka.prototype.Y=function(){return 35632},e(Oa,Fa),Oa.prototype.Y=function(){return 35633},e(Da,ka);var Ba=new Da;function Ua(){this.b="varying vec2 a;varying vec2 b;varying float c;varying float d;attribute vec2 e;attribute float f;attribute float g;uniform mat4 h;uniform mat4 i;uniform mat4 j;uniform float k;uniform float l;void main(void){mat4 offsetMatrix=i*j;a=vec4(h*vec4(e,0.0,1.0)).xy;d=l;float lineWidth=k*l;c=lineWidth/2.0;if(lineWidth==0.0){lineWidth=2.0*l;}vec2 offset;float radius=g+3.0*l;if(f==0.0){offset=vec2(-1.0,1.0);}else if(f==1.0){offset=vec2(-1.0,-1.0);}else if(f==2.0){offset=vec2(1.0,-1.0);}else{offset=vec2(1.0,1.0);}gl_Position=h*vec4(e+offset*radius,0.0,1.0)+offsetMatrix*vec4(offset*lineWidth,0.0,0.0);b=vec4(h*vec4(e.x+g,e.y,0.0,1.0)).xy;if(distance(a,b)>20000.0){gl_Position=vec4(a,0.0,1.0);}}"}e(Ua,Oa);var Ga=new Ua;function Ka(t,e){this.D=t.getUniformLocation(e,"n"),this.L=t.getUniformLocation(e,"k"),this.f=t.getUniformLocation(e,"j"),this.c=t.getUniformLocation(e,"i"),this.a=t.getUniformLocation(e,"m"),this.ra=t.getUniformLocation(e,"l"),this.g=t.getUniformLocation(e,"h"),this.va=t.getUniformLocation(e,"p"),this.P=t.getUniformLocation(e,"o"),this.j=t.getAttribLocation(e,"f"),this.b=t.getAttribLocation(e,"e"),this.u=t.getAttribLocation(e,"g")}function Xa(){return[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]}function Va(t,e){return t[0]=e[0],t[1]=e[1],t[4]=e[2],t[5]=e[3],t[12]=e[4],t[13]=e[5],t}function Wa(t,e){this.origin=me(e),this.Ua=Is(),this.xa=Is(),this.Ja=Is(),this.na=Xa(),this.b=[],this.o=null,this.g=[],this.i=[],this.a=[],this.v=null,this.j=void 0}function za(t,e,o,i){t.drawElements(4,i-o,e.i?5125:5123,o*(e.i?4:2))}function Ha(t){this.b=void 0!==t?t:[],this.a=Ya}e(Wa,Ap),Wa.prototype.f=function(t,e,o,i,r,n,s,p,a,h,l){var u,c,y,f,g,d,v,b,m,w=t.b,x=(this.j&&(u=w.isEnabled(w.STENCIL_TEST),c=w.getParameter(w.STENCIL_FUNC),y=w.getParameter(w.STENCIL_VALUE_MASK),f=w.getParameter(w.STENCIL_REF),g=w.getParameter(w.STENCIL_WRITEMASK),d=w.getParameter(w.STENCIL_FAIL),v=w.getParameter(w.STENCIL_PASS_DEPTH_PASS),b=w.getParameter(w.STENCIL_PASS_DEPTH_FAIL),w.enable(w.STENCIL_TEST),w.clear(w.STENCIL_BUFFER_BIT),w.stencilMask(255),w.stencilFunc(w.ALWAYS,1,255),w.stencilOp(w.KEEP,w.KEEP,w.REPLACE),this.j.f(t,e,o,i,r,n,s,p,a,h,l),w.stencilMask(0),w.stencilFunc(w.NOTEQUAL,1,255)),oh(t,34962,this.v),oh(t,34963,this.o),n=this.Ke(w,t,r,n),Fs(this.Ua));return Gs(x,2/(o*r[0]),2/(o*r[1])),Us(x,-i),Ks(x,-(e[0]-this.origin[0]),-(e[1]-this.origin[1])),Gs(e=Fs(this.Ja),2/r[0],2/r[1]),r=Fs(this.xa),0!==i&&Us(r,-i),w.uniformMatrix4fv(n.g,!1,Va(this.na,x)),w.uniformMatrix4fv(n.c,!1,Va(this.na,e)),w.uniformMatrix4fv(n.f,!1,Va(this.na,r)),w.uniform1f(n.a,s),void 0===a?this.yd(w,t,p,!1):m=t=h?this.$d(w,t,p,a,l):(w.clear(w.COLOR_BUFFER_BIT|w.DEPTH_BUFFER_BIT),this.yd(w,t,p,!0),(t=a(null))||void 0),this.Le(w,n),this.j&&(u||w.disable(w.STENCIL_TEST),w.clear(w.STENCIL_BUFFER_BIT),w.stencilFunc(c,f,y),w.stencilMask(g),w.stencilOp(d,b,v)),m};var Ya=35044;function Za(t,e){Wa.call(this,0,e),this.H=null,this.l=[],this.u=[],this.A=0,this.c={fillColor:null,strokeColor:null,lineDash:null,lineWidth:void 0,s:!1}}function qa(t,e,o,i){e.uniform4fv(t.H.P,o),e.uniform1f(t.H.L,i)}function Ja(){this.b="precision mediump float;varying vec2 a;varying float b;uniform float k;uniform sampler2D l;void main(void){vec4 texColor=texture2D(l,a);gl_FragColor.rgb=texColor.rgb;float alpha=texColor.a*b*k;if(alpha==0.0){discard;}gl_FragColor.a=alpha;}"}e(Za,Wa),(d=Za.prototype).hc=function(t,e){var o=t.qe(),i=t.pa();if(o){this.g.push(this.b.length),this.i.push(e),this.c.s&&(this.u.push(this.b.length),this.c.s=!1),this.A=o;for(var o=io(t.ia(),2,i,-this.origin[0],-this.origin[1]),r=this.a.length,n=this.b.length,s=r/4,p=0;p<2;p+=i)this.a[r++]=o[p],this.a[r++]=o[p+1],this.a[r++]=0,this.a[r++]=this.A,this.a[r++]=o[p],this.a[r++]=o[p+1],this.a[r++]=1,this.a[r++]=this.A,this.a[r++]=o[p],this.a[r++]=o[p+1],this.a[r++]=2,this.a[r++]=this.A,this.a[r++]=o[p],this.a[r++]=o[p+1],this.a[r++]=3,this.a[r++]=this.A,this.b[n++]=s,this.b[n++]=s+1,this.b[n++]=s+2,this.b[n++]=s+2,this.b[n++]=s+3,this.b[n++]=s,s+=4}else this.c.s&&(this.l.pop(),this.l.length)&&(i=this.l[this.l.length-1],this.c.fillColor=i[0],this.c.strokeColor=i[1],this.c.lineWidth=i[2],this.c.s=!1)},d.vb=function(){this.v=new Ha(this.a),this.o=new Ha(this.b),this.g.push(this.b.length),0===this.u.length&&0<this.l.length&&(this.l=[]),this.b=this.a=null},d.wb=function(t){var e=this.v,o=this.o;return function(){ih(t,e),ih(t,o)}},d.Ke=function(t,e,o,i){var r,n=sh(e,Ba,Ga);return this.H?r=this.H:this.H=r=new Ka(t,n),e.Lc(n),t.enableVertexAttribArray(r.b),t.vertexAttribPointer(r.b,2,5126,!1,16,0),t.enableVertexAttribArray(r.j),t.vertexAttribPointer(r.j,1,5126,!1,16,8),t.enableVertexAttribArray(r.u),t.vertexAttribPointer(r.u,1,5126,!1,16,12),t.uniform2fv(r.va,o),t.uniform1f(r.ra,i),r},d.Le=function(t,e){t.disableVertexAttribArray(e.b),t.disableVertexAttribArray(e.j),t.disableVertexAttribArray(e.u)},d.yd=function(t,e,o){if(ot(o)){var i=this.g[this.g.length-1];for(o=this.u.length-1;0<=o;--o)a=this.u[o],p=this.l[o],t.uniform4fv(this.H.D,p[0]),qa(this,t,p[1],p[2]),za(t,e,a,i),i=a}else for(var r,n,s=this.g.length-2,p=i=this.g[s+1],a=this.u.length-1;0<=a;--a){for(r=this.l[a],t.uniform4fv(this.H.D,r[0]),qa(this,t,r[1],r[2]),r=this.u[a];0<=s&&this.g[s]>=r;)n=this.g[s],o[B(this.i[s]).toString()]&&(i!==p&&za(t,e,i,p),p=n),s--,i=n;i!==p&&za(t,e,i,p),i=p=r}},d.$d=function(t,e,o,i,r){for(var n,s,p,a=this.g.length-2,h=this.g[a+1],l=this.u.length-1;0<=l;--l)for(n=this.l[l],t.uniform4fv(this.H.D,n[0]),qa(this,t,n[1],n[2]),s=this.u[l];0<=a&&this.g[a]>=s;){if(n=this.g[a],void 0===o[B(p=this.i[a]).toString()]&&p.V()&&(void 0===r||Ae(r,p.V().G()))&&(t.clear(t.COLOR_BUFFER_BIT|t.DEPTH_BUFFER_BIT),za(t,e,n,h),h=i(p)))return h;a--,h=n}},d.Ma=function(t,e){var o,i=e?(o=e.g,this.c.lineDash=o||La,o=!((o=e.b)instanceof CanvasGradient||o instanceof CanvasPattern)&&Pi(o).map(function(t,e){return 3!=e?t/255:t})||Ra,void 0!==(i=e.f)?i:1):(o=[0,0,0,0],0),e=!((e=t?t.b:[0,0,0,0])instanceof CanvasGradient||e instanceof CanvasPattern)&&Pi(e).map(function(t,e){return 3!=e?t/255:t})||ja;this.c.strokeColor&&Rt(this.c.strokeColor,o)&&this.c.fillColor&&Rt(this.c.fillColor,e)&&this.c.lineWidth===i||(this.c.s=!0,this.c.fillColor=e,this.c.strokeColor=o,this.c.lineWidth=i,this.l.push([e,o,i]))},e(Ja,ka);var _a=new Ja;function $a(){this.b="varying vec2 a;varying float b;attribute vec2 c;attribute vec2 d;attribute vec2 e;attribute float f;attribute float g;uniform mat4 h;uniform mat4 i;uniform mat4 j;void main(void){mat4 offsetMatrix=i;if(g==1.0){offsetMatrix=i*j;}vec4 offsets=offsetMatrix*vec4(e,0.0,0.0);gl_Position=h*vec4(c,0.0,1.0)+offsets;a=d;b=f;}"}e($a,Oa);var Qa=new $a;function th(t,e){this.f=t.getUniformLocation(e,"j"),this.c=t.getUniformLocation(e,"i"),this.a=t.getUniformLocation(e,"k"),this.g=t.getUniformLocation(e,"h"),this.v=t.getAttribLocation(e,"e"),this.H=t.getAttribLocation(e,"f"),this.b=t.getAttribLocation(e,"c"),this.A=t.getAttribLocation(e,"g"),this.C=t.getAttribLocation(e,"d")}function eh(t,e){this.j=t,this.b=e,this.a={},this.f={},this.g={},this.o=this.v=this.c=this.l=null,(this.i=Et(O,"OES_element_index_uint"))&&e.getExtension("OES_element_index_uint"),c(this.j,"webglcontextlost",this.co,this),c(this.j,"webglcontextrestored",this.eo,this)}function oh(t,e,o){var i,r,n=t.b,s=o.b,p=String(B(o));p in t.a?n.bindBuffer(e,t.a[p].buffer):(i=n.createBuffer(),n.bindBuffer(e,i),34962==e?r=new Float32Array(s):34963==e&&(r=new(t.i?Uint32Array:Uint16Array)(s)),n.bufferData(e,r,o.a),t.a[p]={gc:o,buffer:i})}function ih(t,e){var o=t.b,e=String(B(e)),i=t.a[e];o.isContextLost()||o.deleteBuffer(i.buffer),delete t.a[e]}function rh(t){var e,o,i,r;return t.c||(o=(e=t.b).createFramebuffer(),e.bindFramebuffer(e.FRAMEBUFFER,o),i=ah(e,1,1),r=e.createRenderbuffer(),e.bindRenderbuffer(e.RENDERBUFFER,r),e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_COMPONENT16,1,1),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,i,0),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_ATTACHMENT,e.RENDERBUFFER,r),e.bindTexture(e.TEXTURE_2D,null),e.bindRenderbuffer(e.RENDERBUFFER,null),e.bindFramebuffer(e.FRAMEBUFFER,null),t.c=o,t.v=i,t.o=r),t.c}function nh(t,e){var o,i,r=String(B(e));return r in t.f?t.f[r]:(i=(o=t.b).createShader(e.Y()),o.shaderSource(i,e.b),o.compileShader(i),t.f[r]=i)}function sh(t,e,o){var i,r,n=B(e)+"/"+B(o);return n in t.g?t.g[n]:(r=(i=t.b).createProgram(),i.attachShader(r,nh(t,e)),i.attachShader(r,nh(t,o)),i.linkProgram(r),t.g[n]=r)}function ph(t,e,o){var i=t.createTexture();return t.bindTexture(t.TEXTURE_2D,i),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.LINEAR),void 0!==e&&t.texParameteri(3553,10242,e),void 0!==o&&t.texParameteri(3553,10243,o),i}function ah(t,e,o){var i=ph(t,void 0,void 0);return t.texImage2D(t.TEXTURE_2D,0,t.RGBA,e,o,0,t.RGBA,t.UNSIGNED_BYTE,null),i}function hh(t,e){var o=ph(t,33071,33071);return t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,e),o}function lh(t,e){Wa.call(this,0,e),this.D=this.C=void 0,this.A=[],this.H=[],this.ra=void 0,this.l=[],this.c=[],this.P=this.va=void 0,this.L=null,this.sa=this.fa=this.eb=this.Z=this.Qa=this.U=void 0,this.Fa=[],this.u=[],this.zb=void 0}function uh(t,e,o,i){for(var r,n,s,p,a,h=t.C,l=t.D,u=t.ra,c=t.va,y=t.P,f=t.U,g=t.Qa,d=t.Z,v=t.eb?1:0,b=-t.fa,m=t.sa,w=t.zb,x=Math.cos(b),b=Math.sin(b),S=t.b.length,M=t.a.length,P=0;P<o;P+=i)p=e[P]-t.origin[0],a=e[P+1]-t.origin[1],r=M/8,n=-m*h,s=-m*(u-l),t.a[M++]=p,t.a[M++]=a,t.a[M++]=n*x-s*b,t.a[M++]=n*b+s*x,t.a[M++]=g/y,t.a[M++]=(d+u)/c,t.a[M++]=f,t.a[M++]=v,n=m*(w-h),s=-m*(u-l),t.a[M++]=p,t.a[M++]=a,t.a[M++]=n*x-s*b,t.a[M++]=n*b+s*x,t.a[M++]=(g+w)/y,t.a[M++]=(d+u)/c,t.a[M++]=f,t.a[M++]=v,n=m*(w-h),s=m*l,t.a[M++]=p,t.a[M++]=a,t.a[M++]=n*x-s*b,t.a[M++]=n*b+s*x,t.a[M++]=(g+w)/y,t.a[M++]=d/c,t.a[M++]=f,t.a[M++]=v,n=-m*h,s=m*l,t.a[M++]=p,t.a[M++]=a,t.a[M++]=n*x-s*b,t.a[M++]=n*b+s*x,t.a[M++]=g/y,t.a[M++]=d/c,t.a[M++]=f,t.a[M++]=v,t.b[S++]=r,t.b[S++]=1+r,t.b[S++]=2+r,t.b[S++]=r,t.b[S++]=2+r,t.b[S++]=3+r}function ch(t,e,o,i){for(var r,n,s=e.length,p=0;p<s;++p)(n=B(r=e[p]).toString())in o?r=o[n]:(r=hh(i,r),o[n]=r),t[p]=r}function yh(t,e,o){var i=e-o;return t[0]===t[i]&&t[1]===t[1+i]&&3<+e/o&&!!po(t,0,e,o)}function fh(){this.b="precision mediump float;varying float a;varying vec2 b;varying float c;uniform float m;uniform vec4 n;uniform vec2 o;uniform float p;void main(void){if(a>0.0){vec2 windowCoords=vec2((b.x+1.0)/2.0*o.x*p,(b.y+1.0)/2.0*o.y*p);if(length(windowCoords-gl_FragCoord.xy)>c*p){discard;}} gl_FragColor=n;float alpha=n.a*m;if(alpha==0.0){discard;}gl_FragColor.a=alpha;}"}e(eh,ct),(d=eh.prototype).oa=function(){ut(this.j);var t=this.b;if(!t.isContextLost()){for(var e in this.a)t.deleteBuffer(this.a[e].buffer);for(e in this.g)t.deleteProgram(this.g[e]);for(e in this.f)t.deleteShader(this.f[e]);t.deleteFramebuffer(this.c),t.deleteRenderbuffer(this.o),t.deleteTexture(this.v)}},d.bo=function(){return this.b},d.co=function(){tt(this.a),tt(this.f),tt(this.g),this.o=this.v=this.c=this.l=null},d.eo=function(){},d.Lc=function(t){return t!=this.l&&(this.b.useProgram(t),this.l=t,!0)},e(lh,Wa),(d=lh.prototype).wb=function(o){var i=this.v,r=this.o,n=this.Fa,s=this.u,p=o.b;return function(){if(!p.isContextLost()){for(var t=0,e=n.length;t<e;++t)p.deleteTexture(n[t]);for(t=0,e=s.length;t<e;++t)p.deleteTexture(s[t])}ih(o,i),ih(o,r)}},d.vc=function(t,e){this.g.push(this.b.length),this.i.push(e);e=t.ia();uh(this,e,e.length,t.pa())},d.xc=function(t,e){this.g.push(this.b.length),this.i.push(e);e=t.ia();uh(this,e,e.length,t.pa())},d.vb=function(t){t=t.b,this.A.push(this.b.length),this.H.push(this.b.length),this.v=new Ha(this.a),this.o=new Ha(this.b);var e={};ch(this.Fa,this.l,e,t),ch(this.u,this.c,e,t),this.ra=this.D=this.C=void 0,this.c=this.l=null,this.P=this.va=void 0,this.b=null,this.sa=this.fa=this.eb=this.Z=this.Qa=this.U=void 0,this.a=null,this.zb=void 0},d.Ke=function(t,e){var o,i=sh(e,_a,Qa);return this.L?o=this.L:this.L=o=new th(t,i),e.Lc(i),t.enableVertexAttribArray(o.b),t.vertexAttribPointer(o.b,2,5126,!1,32,0),t.enableVertexAttribArray(o.v),t.vertexAttribPointer(o.v,2,5126,!1,32,8),t.enableVertexAttribArray(o.C),t.vertexAttribPointer(o.C,2,5126,!1,32,16),t.enableVertexAttribArray(o.H),t.vertexAttribPointer(o.H,1,5126,!1,32,24),t.enableVertexAttribArray(o.A),t.vertexAttribPointer(o.A,1,5126,!1,32,28),o},d.Le=function(t,e){t.disableVertexAttribArray(e.b),t.disableVertexAttribArray(e.v),t.disableVertexAttribArray(e.C),t.disableVertexAttribArray(e.H),t.disableVertexAttribArray(e.A)},d.yd=function(t,e,o,i){var r=i?this.u:this.Fa;if(i=i?this.H:this.A,ot(o))for(var n=r.length,s=o=0;o<n;++o){t.bindTexture(3553,r[o]);var p=i[o];za(t,e,s,p),s=p}else for(s=n=0,p=r.length;s<p;++s){t.bindTexture(3553,r[s]);for(var a=0<s?i[s-1]:0,h=i[s],l=a;n<this.g.length&&this.g[n]<=h;){a=void 0!==o[B(this.i[n]).toString()]?(l!==a&&za(t,e,l,a),l=n===this.g.length-1?h:this.g[n+1]):n===this.g.length-1?h:this.g[n+1];n++}l!==a&&za(t,e,l,a)}},d.$d=function(t,e,o,i,r){for(var n,s,p,a,h=this.g.length-1,l=this.u.length-1;0<=l;--l)for(t.bindTexture(3553,this.u[l]),n=0<l?this.H[l-1]:0,p=this.H[l];0<=h&&this.g[h]>=n;){if(s=this.g[h],void 0===o[B(a=this.i[h]).toString()]&&a.V()&&(void 0===r||Ae(r,a.V().G()))&&(t.clear(t.COLOR_BUFFER_BIT|t.DEPTH_BUFFER_BIT),za(t,e,s,p),p=i(a)))return p;p=s,h--}},d.dc=function(t){var e=t.Ac(),o=t.Ic(1),i=t.de(),r=t.Lf(1),n=t.l,s=t.Jc(),p=t.H,a=t.o,h=t.ac();t=t.i,0===this.l.length?this.l.push(o):B(this.l[this.l.length-1])!=B(o)&&(this.A.push(this.b.length),this.l.push(o)),0===this.c.length?this.c.push(r):B(this.c[this.c.length-1])!=B(r)&&(this.H.push(this.b.length),this.c.push(r)),this.C=e[0],this.D=e[1],this.ra=h[1],this.va=i[1],this.P=i[0],this.U=n,this.Qa=s[0],this.Z=s[1],this.fa=a,this.eb=p,this.sa=t,this.zb=h[0]},e(fh,ka);var gh=new fh;function dh(){this.b="varying float a;varying vec2 b;varying float c;attribute vec2 d;attribute vec2 e;attribute vec2 f;attribute float g;uniform mat4 h;uniform mat4 i;uniform mat4 j;uniform float k;uniform float l;bool nearlyEquals(in float value,in float ref){float epsilon=0.000000000001;return value>=ref-epsilon&&value<=ref+epsilon;}void alongNormal(out vec2 offset,in vec2 nextP,in float turnDir,in float direction){vec2 dirVect=nextP-e;vec2 normal=normalize(vec2(-turnDir*dirVect.y,turnDir*dirVect.x));offset=k/2.0*normal*direction;}void miterUp(out vec2 offset,out float round,in bool isRound,in float direction){float halfWidth=k/2.0;vec2 tangent=normalize(normalize(f-e)+normalize(e-d));vec2 normal=vec2(-tangent.y,tangent.x);vec2 dirVect=f-e;vec2 tmpNormal=normalize(vec2(-dirVect.y,dirVect.x));float miterLength=abs(halfWidth/dot(normal,tmpNormal));offset=normal*direction*miterLength;round=0.0;if(isRound){round=1.0;}else if(miterLength>l+k){offset=halfWidth*tmpNormal*direction;}} bool miterDown(out vec2 offset,in vec4 projPos,in mat4 offsetMatrix,in float direction){bool degenerate=false;vec2 tangent=normalize(normalize(f-e)+normalize(e-d));vec2 normal=vec2(-tangent.y,tangent.x);vec2 dirVect=d-e;vec2 tmpNormal=normalize(vec2(-dirVect.y,dirVect.x));vec2 longOffset,shortOffset,longVertex;vec4 shortProjVertex;float halfWidth=k/2.0;if(length(f-e)>length(d-e)){longOffset=tmpNormal*direction*halfWidth;shortOffset=normalize(vec2(dirVect.y,-dirVect.x))*direction*halfWidth;longVertex=f;shortProjVertex=h*vec4(d,0.0,1.0);}else{shortOffset=tmpNormal*direction*halfWidth;longOffset=normalize(vec2(dirVect.y,-dirVect.x))*direction*halfWidth;longVertex=d;shortProjVertex=h*vec4(f,0.0,1.0);}vec4 p1=h*vec4(longVertex,0.0,1.0)+offsetMatrix*vec4(longOffset,0.0,0.0);vec4 p2=projPos+offsetMatrix*vec4(longOffset,0.0,0.0);vec4 p3=shortProjVertex+offsetMatrix*vec4(-shortOffset,0.0,0.0);vec4 p4=shortProjVertex+offsetMatrix*vec4(shortOffset,0.0,0.0);float denom=(p4.y-p3.y)*(p2.x-p1.x)-(p4.x-p3.x)*(p2.y-p1.y);float firstU=((p4.x-p3.x)*(p1.y-p3.y)-(p4.y-p3.y)*(p1.x-p3.x))/denom;float secondU=((p2.x-p1.x)*(p1.y-p3.y)-(p2.y-p1.y)*(p1.x-p3.x))/denom;float epsilon=0.000000000001;if(firstU>epsilon&&firstU<1.0-epsilon&&secondU>epsilon&&secondU<1.0-epsilon){shortProjVertex.x=p1.x+firstU*(p2.x-p1.x);shortProjVertex.y=p1.y+firstU*(p2.y-p1.y);offset=shortProjVertex.xy;degenerate=true;}else{float miterLength=abs(halfWidth/dot(normal,tmpNormal));offset=normal*direction*miterLength;}return degenerate;}void squareCap(out vec2 offset,out float round,in bool isRound,in vec2 nextP,in float turnDir,in float direction){round=0.0;vec2 dirVect=e-nextP;vec2 firstNormal=normalize(dirVect);vec2 secondNormal=vec2(turnDir*firstNormal.y*direction,-turnDir*firstNormal.x*direction);vec2 hypotenuse=normalize(firstNormal-secondNormal);vec2 normal=vec2(turnDir*hypotenuse.y*direction,-turnDir*hypotenuse.x*direction);float length=sqrt(c*c*2.0);offset=normal*length;if(isRound){round=1.0;}} void main(void){bool degenerate=false;float direction=float(sign(g));mat4 offsetMatrix=i*j;vec2 offset;vec4 projPos=h*vec4(e,0.0,1.0);bool round=nearlyEquals(mod(g,2.0),0.0);a=0.0;c=k/2.0;b=projPos.xy;if(nearlyEquals(mod(g,3.0),0.0)||nearlyEquals(mod(g,17.0),0.0)){alongNormal(offset,f,1.0,direction);}else if(nearlyEquals(mod(g,5.0),0.0)||nearlyEquals(mod(g,13.0),0.0)){alongNormal(offset,d,-1.0,direction);}else if(nearlyEquals(mod(g,23.0),0.0)){miterUp(offset,a,round,direction);}else if(nearlyEquals(mod(g,19.0),0.0)){degenerate=miterDown(offset,projPos,offsetMatrix,direction);}else if(nearlyEquals(mod(g,7.0),0.0)){squareCap(offset,a,round,f,1.0,direction);}else if(nearlyEquals(mod(g,11.0),0.0)){squareCap(offset,a,round,d,-1.0,direction);}if(!degenerate){vec4 offsets=offsetMatrix*vec4(offset,0.0,0.0);gl_Position=projPos+offsets;}else{gl_Position=vec4(offset,0.0,1.0);}}"}e(dh,Oa);var vh=new dh;function bh(t,e){this.D=t.getUniformLocation(e,"n"),this.L=t.getUniformLocation(e,"k"),this.P=t.getUniformLocation(e,"l"),this.f=t.getUniformLocation(e,"j"),this.c=t.getUniformLocation(e,"i"),this.a=t.getUniformLocation(e,"m"),this.ra=t.getUniformLocation(e,"p"),this.g=t.getUniformLocation(e,"h"),this.va=t.getUniformLocation(e,"o"),this.i=t.getAttribLocation(e,"g"),this.l=t.getAttribLocation(e,"d"),this.o=t.getAttribLocation(e,"f"),this.b=t.getAttribLocation(e,"e")}function mh(t,e){Wa.call(this,0,e),this.H=null,this.u=[],this.l=[],this.c={strokeColor:null,lineCap:void 0,lineDash:null,lineJoin:void 0,lineWidth:void 0,miterLimit:void 0,s:!1}}function wh(t,e,o,i){for(var r,n,s,p,a,h,l=t.a.length,u=t.b.length,c="bevel"===t.c.lineJoin?0:"miter"===t.c.lineJoin?1:2,y="butt"===t.c.lineCap?0:"square"===t.c.lineCap?1:2,f=yh(e,o,i),g=u,d=1,v=0;v<o;v+=i){if(s=l/7,p=a,a=h||[e[v],e[v+1]],0===v){if(h=[e[v+i],e[v+i+1]],+o==2*i&&Rt(a,h))break;if(!f){y&&(l=xh(t,[0,0],a,h,7*d*y,l),l=xh(t,[0,0],a,h,7*-d*y,l),t.b[u++]=s+2,t.b[u++]=s,t.b[u++]=s+1,t.b[u++]=s+1,t.b[u++]=s+3,t.b[u++]=s+2),l=xh(t,[0,0],a,h,3*d*(y||1),l),g=(l=xh(t,[0,0],a,h,3*-d*(y||1),l))/7-1;continue}p=[e[o-2*i],e[o-2*i+1]],r=h}else{if(v===o-i){f?h=r:(l=xh(t,p=p||[0,0],a,[0,0],5*d*(y||1),l),l=xh(t,p,a,[0,0],5*-d*(y||1),l),t.b[u++]=s,t.b[u++]=g-1,t.b[u++]=g,t.b[u++]=g,t.b[u++]=s+1,t.b[u++]=s,y&&(l=xh(t,p,a,[0,0],11*d*y,l),l=xh(t,p,a,[0,0],11*-d*y,l),t.b[u++]=s+2,t.b[u++]=s,t.b[u++]=s+1,t.b[u++]=s+1,t.b[u++]=s+3,t.b[u++]=s+2));break}h=[e[v+i],e[v+i+1]]}l=xh(t,p,a,h,13*(n=Na(p[0],p[1],a[0],a[1],h[0],h[1])?-1:1)*(c||1),l),l=xh(t,p,a,h,17*n*(c||1),l),l=xh(t,p,a,h,19*-n*(c||1),l),0<v&&(t.b[u++]=s,t.b[u++]=g-1,t.b[u++]=g,t.b[u++]=s+2,t.b[u++]=s,t.b[u++]=0<d*n?g:g-1),t.b[u++]=s,t.b[u++]=s+2,t.b[u++]=s+1,g=s+2,d=n,c&&(l=xh(t,p,a,h,23*n*c,l),t.b[u++]=s+1,t.b[u++]=s+3,t.b[u++]=s)}f&&(s=s||l/7,l=xh(t,p,a,h,13*(n=Lo([p[0],p[1],a[0],a[1],h[0],h[1]],0,6,2)?1:-1)*(c||1),l),xh(t,p,a,h,19*-n*(c||1),l),t.b[u++]=s,t.b[u++]=g-1,t.b[u++]=g,t.b[u++]=s+1,t.b[u++]=s,t.b[u++]=0<d*n?g:g-1)}function xh(t,e,o,i,r,n){return t.a[n++]=e[0],t.a[n++]=e[1],t.a[n++]=o[0],t.a[n++]=o[1],t.a[n++]=i[0],t.a[n++]=i[1],t.a[n++]=r,n}function Sh(t,e,o){return!((e-=0)<2*o||e===2*o&&Rt([t[0],t[1]],[t[0+o],t[o+1]]))}function Mh(t,e,o,i){var r;if(yh(e,e.length,i)||(e.push(e[0]),e.push(e[1])),wh(t,e,e.length,i),o.length)for(e=0,r=o.length;e<r;++e)yh(o[e],o[e].length,i)||(o[e].push(o[e][0]),o[e].push(o[e][1])),wh(t,o[e],o[e].length,i)}function Ph(t,e,o){o=void 0===o?t.b.length:o,t.g.push(o),t.i.push(e),t.c.s&&(t.l.push(o),t.c.s=!1)}function Th(t,e,o,i,r){e.uniform4fv(t.H.D,o),e.uniform1f(t.H.L,i),e.uniform1f(t.H.P,r)}function Ah(){this.b="precision mediump float;uniform vec4 e;uniform float f;void main(void){gl_FragColor=e;float alpha=e.a*f;if(alpha==0.0){discard;}gl_FragColor.a=alpha;}"}e(mh,Wa),(d=mh.prototype).Pb=function(t,e){var o=t.ia(),t=t.pa();Sh(o,o.length,t)&&(o=io(o,o.length,t,-this.origin[0],-this.origin[1]),this.c.s&&(this.l.push(this.b.length),this.c.s=!1),this.g.push(this.b.length),this.i.push(e),wh(this,o,o.length,t))},d.uc=function(t,e){for(var o=this.b.length,i=t.Yc(),r=0,n=i.length;r<n;++r){var s=i[r].ia(),p=i[r].pa();Sh(s,s.length,p)&&wh(this,s=io(s,s.length,p,-this.origin[0],-this.origin[1]),s.length,p)}this.b.length>o&&(this.g.push(o),this.i.push(e),this.c.s)&&(this.l.push(o),this.c.s=!1)},d.vb=function(){this.v=new Ha(this.a),this.o=new Ha(this.b),this.g.push(this.b.length),0===this.l.length&&0<this.u.length&&(this.u=[]),this.b=this.a=null},d.wb=function(t){var e=this.v,o=this.o;return function(){ih(t,e),ih(t,o)}},d.Ke=function(t,e,o,i){var r,n=sh(e,gh,vh);return this.H?r=this.H:this.H=r=new bh(t,n),e.Lc(n),t.enableVertexAttribArray(r.l),t.vertexAttribPointer(r.l,2,5126,!1,28,0),t.enableVertexAttribArray(r.b),t.vertexAttribPointer(r.b,2,5126,!1,28,8),t.enableVertexAttribArray(r.o),t.vertexAttribPointer(r.o,2,5126,!1,28,16),t.enableVertexAttribArray(r.i),t.vertexAttribPointer(r.i,1,5126,!1,28,24),t.uniform2fv(r.va,o),t.uniform1f(r.ra,i),r},d.Le=function(t,e){t.disableVertexAttribArray(e.l),t.disableVertexAttribArray(e.b),t.disableVertexAttribArray(e.o),t.disableVertexAttribArray(e.i)},d.yd=function(t,e,o,i){var r=t.getParameter(t.DEPTH_FUNC),n=t.getParameter(t.DEPTH_WRITEMASK);if(i||(t.enable(t.DEPTH_TEST),t.depthMask(!0),t.depthFunc(t.NOTEQUAL)),ot(o)){var s=this.g[this.g.length-1];for(o=this.l.length-1;0<=o;--o)u=this.l[o],Th(this,t,(l=this.u[o])[0],l[1],l[2]),za(t,e,u,s),t.clear(t.DEPTH_BUFFER_BIT),s=u}else for(var p,a,h=this.g.length-2,l=s=this.g[h+1],u=this.l.length-1;0<=u;--u){for(Th(this,t,(p=this.u[u])[0],p[1],p[2]),p=this.l[u];0<=h&&this.g[h]>=p;)a=this.g[h],o[B(this.i[h]).toString()]&&(s!==l&&(za(t,e,s,l),t.clear(t.DEPTH_BUFFER_BIT)),l=a),h--,s=a;s!==l&&(za(t,e,s,l),t.clear(t.DEPTH_BUFFER_BIT)),s=l=p}i||(t.disable(t.DEPTH_TEST),t.clear(t.DEPTH_BUFFER_BIT),t.depthMask(n),t.depthFunc(r))},d.$d=function(t,e,o,i,r){for(var n,s,p,a=this.g.length-2,h=this.g[a+1],l=this.l.length-1;0<=l;--l)for(Th(this,t,(n=this.u[l])[0],n[1],n[2]),s=this.l[l];0<=a&&this.g[a]>=s;){if(n=this.g[a],void 0===o[B(p=this.i[a]).toString()]&&p.V()&&(void 0===r||Ae(r,p.V().G()))&&(t.clear(t.COLOR_BUFFER_BIT|t.DEPTH_BUFFER_BIT),za(t,e,n,h),h=i(p)))return h;a--,h=n}},d.Ma=function(t,e){var o=e.c,o=(this.c.lineCap=void 0!==o?o:"round",o=e.g,this.c.lineDash=o||La,o=e.i,this.c.lineJoin=void 0!==o?o:"round",!((o=e.b)instanceof CanvasGradient||o instanceof CanvasPattern)&&Pi(o).map(function(t,e){return 3!=e?t/255:t})||Ra),i=void 0!==(i=e.f)?i:1,e=void 0!==(e=e.j)?e:10;this.c.strokeColor&&Rt(this.c.strokeColor,o)&&this.c.lineWidth===i&&this.c.miterLimit===e||(this.c.s=!0,this.c.strokeColor=o,this.c.lineWidth=i,this.c.miterLimit=e,this.u.push([o,i,e]))},e(Ah,ka);var Eh=new Ah;function Ch(){this.b="attribute vec2 a;uniform mat4 b;uniform mat4 c;uniform mat4 d;void main(void){gl_Position=b*vec4(a,0.0,1.0);}"}e(Ch,Oa);var jh,Lh,Rh,Nh,Ih,Fh,kh=new Ch;function Oh(t,e){this.D=t.getUniformLocation(e,"e"),this.f=t.getUniformLocation(e,"d"),this.c=t.getUniformLocation(e,"c"),this.a=t.getUniformLocation(e,"f"),this.g=t.getUniformLocation(e,"b"),this.b=t.getAttribLocation(e,"a")}function Dh(t){this.b=this.a=this.g=void 0,this.c=void 0===t||t,this.f=0}function Bh(t){var e,o,i=t.b;i&&(e=i.next,o=i.pb,e&&(e.pb=o),o&&(o.next=e),t.b=e||o,t.g===t.a?(t.b=void 0,t.g=void 0,t.a=void 0):t.g===i?t.g=t.b:t.a===i&&(t.a=o?t.b.pb:t.b),t.f--)}function Uh(t){if(t.b=t.g,t.b)return t.b.data}function Gh(t){if(t.b&&t.b.next)return t.b=t.b.next,t.b.data}function Kh(t){if(t.b&&t.b.next)return t.b.next.data}function Xh(t){if(t.b&&t.b.pb)return t.b=t.b.pb,t.b.data}function Vh(t){if(t.b&&t.b.pb)return t.b.pb.data}function Wh(t){if(t.b)return t.b.data}function zh(t){this.b=jh(t),this.a={}}function Hh(t,e,o){var i=t.a[B(o)];ue([i.ea,i.ga,i.ca,i.ja],e)||(t.remove(o),t.Da(e,o))}function Yh(t){return t.b.all().map(function(t){return t.value})}function Zh(t,e){return t.b.search({ea:e[0],ga:e[1],ca:e[2],ja:e[3]}).map(function(t){return t.value})}function qh(t,e,o,i){return Jh(Zh(t,e),o,i)}function Jh(t,e,o){for(var i,r=0,n=t.length;r<n&&!(i=e.call(o,t[r]));r++);return i}function _h(t,e){Wa.call(this,0,e),this.j=new mh(0,e),this.H=null,this.u=[],this.c=[],this.l={fillColor:null,s:!1}}function $h(t,e,o,i){var r=new Dh,n=new zh;if(e=Qh(t,e,i,r,n,!0),o.length){for(var s=[],p=0,a=o.length;p<a;++p){var h={list:new Dh,ca:void 0};s.push(h),h.ca=Qh(t,o[p],i,h.list,n,!1)}for(s.sort(function(t,e){return e.ca-t.ca}),p=0;p<s.length;++p){l=void 0;u=void 0;c=void 0;y=void 0;f=void 0;g=void 0;w=void 0;d=void 0;v=void 0;b=void 0;m=void 0;M=void 0;x=void 0;S=void 0;var l=s[p].list;var u=s[p].ca;var c=r;var y=e;var f=n;tl(l,f,!0);for(var g=Uh(l);g.X.x!==u;)g=Gh(l);u=g.X,y={x:y,y:u.y,$a:-1};var d,v,b,m,w=1/0;for(b=al({ba:u,X:y},f,!0),d=0,v=b.length;d<v;++d){var x,S,M=b[d];void 0===M.ba.qb&&(x=hl(u,y,M.ba,M.X,!0),(S=Math.abs(u.x-x[0]))<w)&&(w=S,m={x:x[0],y:x[1],$a:-1},g=M)}if(1/0!==w){if(b=g.X,0<w&&(g=pl(u,m,g.X,f)).length)for(m=1/0,d=0,v=g.length;d<v;++d)w=g[d],((M=Math.atan2(u.y-w.y,y.x-w.x))<m||M===m&&w.x<b.x)&&(m=M,b=w);for(g=Uh(c);g.X!==b;)g=Gh(c);y={x:u.x,y:u.y,$a:u.$a,qb:void 0},d={x:g.X.x,y:g.X.y,$a:g.X.$a,qb:void 0},Kh(l).ba=y,nl(u,g.X,l,f),nl(d,y,l,f),g.X=d,l.c&&l.b&&(l.g=l.b,l.a=l.b.pb),c.concat(l)}}}tl(r,n,!1),function t(e,o,i){for(var r=!1,n=il(o,i);3<o.f;)if(n){if(!el(e,o,i,n,r)&&!tl(o,i,r)&&!ol(e,o,i,!0))break}else if(!el(e,o,i,n,r)&&!tl(o,i,r)&&!ol(e,o,i)){if(!(n=il(o,i))){n=e,s=p=Uh(r=o);do{if((a=al(s,i)).length){for(p=a[0],a=hl(s.ba,s.X,p.ba,p.X),a=rl(n,a[0],a[1],n.a.length/2),h=new Dh,l=new zh,nl(a,s.X,h,l),s.X=a,Hh(i,[Math.min(s.ba.x,a.x),Math.min(s.ba.y,a.y),Math.max(s.ba.x,a.x),Math.max(s.ba.y,a.y)],s),s=Gh(r);s!==p;)nl(s.ba,s.X,h,l),i.remove(s),Bh(r),s=Wh(r);nl(p.ba,a,h,l),p.ba=a,Hh(i,[Math.min(p.X.x,a.x),Math.min(p.X.y,a.y),Math.max(p.X.x,a.x),Math.max(p.X.y,a.y)],p),tl(r,i,!1),t(n,r,i),tl(h,l,!1),t(n,h,l);break}}while((s=Gh(r))!==p);break}for(var s=2*(r=o).f,p=Array(s),a=Uh(r),h=a,l=0;p[l++]=h.ba.x,p[l++]=h.ba.y,(h=Gh(r))!==a;);r=!Lo(p,0,s,2),tl(o,i,r)}3===o.f&&(n=e.b.length,e.b[n++]=Vh(o).ba.$a,e.b[n++]=Wh(o).ba.$a,e.b[n++]=Kh(o).ba.$a)}(t,r,n)}function Qh(t,e,o,i,r,n){var s,p,a,h,l,u=t.a.length/2,c=[],y=[];if(n===Lo(e,0,e.length,o))for(h=a=rl(t,e[0],e[1],u++),n=e[0],s=o,p=e.length;s<p;s+=o)l=rl(t,e[s],e[s+1],u++),y.push(nl(h,l,i)),c.push([Math.min(h.x,l.x),Math.min(h.y,l.y),Math.max(h.x,l.x),Math.max(h.y,l.y)]),n=e[s]>n?e[s]:n,h=l;else for(h=a=rl(t,e[s=e.length-o],e[s+1],u++),n=e[s],s-=o,p=0;p<=s;s-=o)l=rl(t,e[s],e[s+1],u++),y.push(nl(h,l,i)),c.push([Math.min(h.x,l.x),Math.min(h.y,l.y),Math.max(h.x,l.x),Math.max(h.y,l.y)]),n=e[s]>n?e[s]:n,h=l;return y.push(nl(l,a,i)),c.push([Math.min(h.x,l.x),Math.min(h.y,l.y),Math.max(h.x,l.x),Math.max(h.y,l.y)]),r.load(c,y),n}function tl(t,e,o){var i=Uh(t),r=i,n=Gh(t),s=!1;do{var p=o?Na(n.X.x,n.X.y,r.X.x,r.X.y,r.ba.x,r.ba.y):Na(r.ba.x,r.ba.y,r.X.x,r.X.y,n.X.x,n.X.y)}while(void 0===p?(sl(r,n,t,e),s=!0,n===i&&(i=Kh(t)),n=r,Xh(t)):r.X.qb!==p&&(r.X.qb=p,s=!0),r=n,n=Gh(t),r!==i);return s}function el(t,e,o,i,r){var n=t.b.length,s=Uh(e),p=Vh(e),a=s,h=Gh(e),l=Kh(e),u=!1;do{var c,y=a.ba,f=a.X,g=h.X}while(!1===f.qb&&(c=r?ll(l.X,g,f,y,p.ba):ll(p.ba,y,f,g,l.X),!i&&0!==al({ba:y,X:g},o).length||!c||0!==pl(y,f,g,o,!0).length||!i&&!1!==y.qb&&!1!==g.qb&&Lo([p.ba.x,p.ba.y,y.x,y.y,f.x,f.y,g.x,g.y,l.X.x,l.X.y],0,10,2)!==!r||(t.b[n++]=y.$a,t.b[n++]=f.$a,t.b[n++]=g.$a,sl(a,h,e,o),h===s&&(s=l),u=!0)),p=Vh(e),a=Wh(e),h=Gh(e),l=Kh(e),a!==s&&3<e.f);return u}function ol(t,e,o,i){var r=Uh(e),n=(Gh(e),r),s=Gh(e),p=!1;do{var a=hl(n.ba,n.X,s.ba,s.X,i);if(a){var h,p=t.b.length,l=t.a.length/2,u=Xh(e);if(Bh(e),o.remove(u),h=u===r,i?(h=a[0]===n.ba.x&&a[1]===n.ba.y?(Xh(e),a=n.ba,s.ba=a,o.remove(n),h||n===r):(a=s.X,n.X=a,o.remove(s),h||s===r),Bh(e)):(a=rl(t,a[0],a[1],l),n.X=a,s.ba=a,Hh(o,[Math.min(n.ba.x,n.X.x),Math.min(n.ba.y,n.X.y),Math.max(n.ba.x,n.X.x),Math.max(n.ba.y,n.X.y)],n),Hh(o,[Math.min(s.ba.x,s.X.x),Math.min(s.ba.y,s.X.y),Math.max(s.ba.x,s.X.x),Math.max(s.ba.y,s.X.y)],s)),t.b[p++]=u.ba.$a,t.b[p++]=u.X.$a,t.b[p++]=a.$a,p=!0,h)break}}while(n=Vh(e),s=Gh(e),n!==r);return p}function il(t,e){var o=Uh(t),i=o;do{if(al(i,e).length)return!1}while((i=Gh(t))!==o);return!0}function rl(t,e,o,i){var r=t.a.length;return{x:t.a[r++]=e,y:t.a[+r]=o,$a:i,qb:void 0}}function nl(t,e,o,i){var r,n={ba:t,X:e},s={pb:void 0,next:void 0,data:n},p=o.b;return p?(r=p.next,s.pb=p,s.next=r,p.next=s,r&&(r.pb=s),p===o.a&&(o.a=s)):(o.g=s,o.a=s,o.c&&((s.next=s).pb=s)),o.b=s,o.f++,i&&i.Da([Math.min(t.x,e.x),Math.min(t.y,e.y),Math.max(t.x,e.x),Math.max(t.y,e.y)],n),n}function sl(t,e,o,i){Wh(o)===e&&(Bh(o),t.X=e.X,i.remove(e),Hh(i,[Math.min(t.ba.x,t.X.x),Math.min(t.ba.y,t.X.y),Math.max(t.ba.x,t.X.x),Math.max(t.ba.y,t.X.y)],t))}function pl(t,e,o,i,r){var n,s,p,a=[],h=Zh(i,[Math.min(t.x,e.x,o.x),Math.min(t.y,e.y,o.y),Math.max(t.x,e.x,o.x),Math.max(t.y,e.y,o.y)]);for(i=0,n=h.length;i<n;++i)for(s in h[i])p=h[i][s],"object"!=typeof p||r&&!p.qb||p.x===t.x&&p.y===t.y||p.x===e.x&&p.y===e.y||p.x===o.x&&p.y===o.y||-1!==a.indexOf(p)||!Po([t.x,t.y,e.x,e.y,o.x,o.y],0,6,2,p.x,p.y)||a.push(p);return a}function al(t,e,o){for(var i=t.ba,r=t.X,n=[],s=0,p=(e=Zh(e,[Math.min(i.x,r.x),Math.min(i.y,r.y),Math.max(i.x,r.x),Math.max(i.y,r.y)])).length;s<p;++s){var a=e[s];t!==a&&(o||a.ba!==r||a.X!==i)&&hl(i,r,a.ba,a.X,o)&&n.push(a)}return n}function hl(t,e,o,i,r){var n=(i.y-o.y)*(e.x-t.x)-(i.x-o.x)*(e.y-t.y);if(0!=n&&(i=((i.x-o.x)*(t.y-o.y)-(i.y-o.y)*(t.x-o.x))/n,o=((e.x-t.x)*(t.y-o.y)-(e.y-t.y)*(t.x-o.x))/n,!r&&Ia<i&&i<1-Ia&&Ia<o&&o<1-Ia||r&&0<=i&&i<=1&&0<=o&&o<=1))return[t.x+i*(e.x-t.x),t.y+i*(e.y-t.y)]}function ll(t,e,o,i,r){var n;return void 0!==e.qb&&void 0!==i.qb&&(n=(o.x-i.x)*(e.y-i.y)>(o.y-i.y)*(e.x-i.x),r=(r.x-i.x)*(e.y-i.y)<(r.y-i.y)*(e.x-i.x),t=(t.x-e.x)*(i.y-e.y)>(t.y-e.y)*(i.x-e.x),o=(o.x-e.x)*(i.y-e.y)<(o.y-e.y)*(i.x-e.x),e=e.qb?o||t:o&&t,i.qb?r||n:r&&n)&&e}function ul(){}function cl(t,e,o){this.i=e,this.j=t,this.c=o,this.a={}}function yl(t,e){var o,i=[];for(o in t.a){var r,n=t.a[o];for(r in n)i.push(n[r].wb(e))}return function(){for(var t,e=i.length,o=0;o<e;o++)t=i[o].apply(this,arguments);return t}}function fl(t,e,o,i,r,n,s,p,a,h,l){var u,c,y,f,g,d=gl,v=Object.keys(t.a).map(Number);for(v.sort(function(t,e){return e-t}),u=0,c=v.length;u<c;++u)for(f=t.a[v[u].toString()],y=ca.length-1;0<=y;--y)if(void 0!==(g=f[ca[y]])&&(g=g.f(e,o,i,r,d,n,s,p,a,h,l)))return g}Dh.prototype.concat=function(t){var e;t.b&&(this.b?(e=this.b.next,this.b.next=t.g,t.g.pb=this.b,e.pb=t.a,t.a.next=e,this.f+=t.f):(this.b=t.b,this.g=t.g,this.a=t.a,this.f=t.f),t.b=void 0,t.g=void 0,t.a=void 0,t.f=0)},Fh={ma:Ih={}},function(t){"object"==typeof Ih&&void 0!==Fh?Fh.ma=t():("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Tp=t()}(function(){return function i(r,n,s){function p(o,t){if(!n[o]){if(!r[o]){var e="function"==typeof require&&require;if(!t&&e)return e(o,!0);if(a)return a(o,!0);throw(e=Error("Cannot find module '"+o+"'")).code="MODULE_NOT_FOUND",e}e=n[o]={ma:{}},r[o][0].call(e.ma,function(t){var e=r[o][1][t];return p(e||t)},e,e.ma,i,r,n,s)}return n[o].ma}for(var a="function"==typeof require&&require,t=0;t<s.length;t++)p(s[t]);return p}({1:[function(t,e){function l(t,e,o){var i=t[e];t[e]=t[o],t[o]=i}function u(t,e){return t<e?-1:e<t?1:0}e.ma=function t(e,o,i,r,n){for(i=i||0,r=r||e.length-1,n=n||u;i<r;){var s,p,a,h;for(600<r-i&&(s=r-i+1,p=o-i+1,h=Math.log(s),a=.5*Math.exp(2*h/3),h=.5*Math.sqrt(h*a*(s-a)/s)*(p-s/2<0?-1:1),t(e,o,Math.max(i,Math.floor(o-p*a/s+h)),Math.min(r,Math.floor(o+(s-p)*a/s+h)),n)),s=e[o],a=r,l(e,p=i,o),0<n(e[r],s)&&l(e,i,r);p<a;){for(l(e,p,a),p++,a--;n(e[p],s)<0;)p++;for(;0<n(e[a],s);)a--}0===n(e[i],s)?l(e,i,a):l(e,++a,r),a<=o&&(i=a+1),o<=a&&(r=a-1)}}},{}],2:[function(t,e){function o(t,e){if(!(this instanceof o))return new o(t,e);this.af=Math.max(4,t||9),this.rg=Math.max(2,Math.ceil(.4*this.af)),e&&this.vj(e),this.clear()}function l(t,e){u(t,0,t.children.length,e,t)}function u(t,e,o,i,r){(r=r||d(null)).ea=1/0,r.ga=1/0,r.ca=-1/0,r.ja=-1/0;for(var n;e<o;e++)n=t.children[e],h(r,t.ab?i(n):n);return r}function h(t,e){t.ea=Math.min(t.ea,e.ea),t.ga=Math.min(t.ga,e.ga),t.ca=Math.max(t.ca,e.ca),t.ja=Math.max(t.ja,e.ja)}function n(t,e){return t.ea-e.ea}function s(t,e){return t.ga-e.ga}function c(t){return(t.ca-t.ea)*(t.ja-t.ga)}function y(t){return t.ca-t.ea+(t.ja-t.ga)}function f(t,e){return t.ea<=e.ea&&t.ga<=e.ga&&e.ca<=t.ca&&e.ja<=t.ja}function g(t,e){return e.ea<=t.ca&&e.ga<=t.ja&&e.ca>=t.ea&&e.ja>=t.ga}function d(t){return{children:t,height:1,ab:!0,ea:1/0,ga:1/0,ca:-1/0,ja:-1/0}}function v(t,e,o,i,r){for(var n,s=[e,o];s.length;)(o=s.pop())-(e=s.pop())<=i||(n=e+Math.ceil((o-e)/i/2)*i,p(t,n,e,o,r),s.push(e,n,n,o))}e.ma=o;var p=t("quickselect");o.prototype={all:function(){return this.mg(this.data,[])},search:function(t){var e=this.data,o=[],i=this.tb;if(g(t,e))for(var r,n,s,p,a=[];e;){for(r=0,n=e.children.length;r<n;r++)s=e.children[r],g(t,p=e.ab?i(s):s)&&(e.ab?o.push(s):f(t,p)?this.mg(s,o):a.push(s));e=a.pop()}return o},load:function(t){if(t&&t.length)if(t.length<this.rg)for(var e=0,o=t.length;e<o;e++)this.Da(t[e]);else t=this.og(t.slice(),0,t.length-1,0),this.data.children.length?this.data.height===t.height?this.tg(this.data,t):(this.data.height<t.height&&(e=this.data,this.data=t,t=e),this.qg(t,this.data.height-t.height-1,!0)):this.data=t;return this},Da:function(t){return t&&this.qg(t,this.data.height-1),this},clear:function(){return this.data=d([]),this},remove:function(t,e){if(t)for(var o,i,r,n=this.data,s=this.tb(t),p=[],a=[];n||p.length;){if(n||(n=p.pop(),i=p[p.length-1],o=a.pop(),r=!0),n.ab){t:{var h=t,l=n.children,u=e;if(u){for(var c=0;c<l.length;c++)if(u(h,l[c])){h=c;break t}h=-1}else h=l.indexOf(h)}if(-1!==h){n.children.splice(h,1),p.push(n),this.tj(p);break}}r||n.ab||!f(n,s)?i?(o++,n=i.children[o],r=!1):n=null:(p.push(n),a.push(o),n=(i=n).children[o=0])}return this},tb:function(t){return t},ff:n,gf:s,toJSON:function(){return this.data},mg:function(t,e){for(var o=[];t;)t.ab?e.push.apply(e,t.children):o.push.apply(o,t.children),t=o.pop();return e},og:function(t,e,o,i){var r,n,s,p,a=o-e+1,h=this.af;if(a<=h)l(r=d(t.slice(e,o+1)),this.tb);else{for(i||(i=Math.ceil(Math.log(a)/Math.log(h)),h=Math.ceil(a/Math.pow(h,i-1))),(r=d([])).ab=!1,r.height=i,v(t,e,o,h=(a=Math.ceil(a/h))*Math.ceil(Math.sqrt(h)),this.ff);e<=o;e+=h)for(v(t,e,s=Math.min(e+h-1,o),a,this.gf),n=e;n<=s;n+=a)p=Math.min(n+a-1,s),r.children.push(this.og(t,n,p,i-1));l(r,this.tb)}return r},sj:function(t,e,o,i){for(var r,n,s,p,a,h,l,u;i.push(e),!e.ab&&i.length-1!==o;){for(l=u=1/0,r=0,n=e.children.length;r<n;r++)a=c(s=e.children[r]),(h=(Math.max(s.ca,t.ca)-Math.min(s.ea,t.ea))*(Math.max(s.ja,t.ja)-Math.min(s.ga,t.ga))-a)<u?(u=h,l=a<l?a:l,p=s):h===u&&a<l&&(l=a,p=s);e=p||e.children[0]}return e},qg:function(t,e,o){var i=this.tb,i=(o=o?t:i(t),[]),r=this.sj(o,this.data,e,i);for(r.children.push(t),h(r,o);0<=e&&i[e].children.length>this.af;)this.Aj(i,e),e--;this.pj(o,i,e)},Aj:function(t,e){var o=t[e],i=o.children.length,r=this.rg;this.qj(o,r,i),i=this.rj(o,r,i),(i=d(o.children.splice(i,o.children.length-i))).height=o.height,i.ab=o.ab,l(o,this.tb),l(i,this.tb),e?t[e-1].children.push(i):this.tg(o,i)},tg:function(t,e){this.data=d([t,e]),this.data.height=t.height+1,this.data.ab=!1,l(this.data,this.tb)},rj:function(t,e,o){for(var i,r,n,s,p,a=s=1/0,h=e;h<=o-e;h++)i=u(t,0,h,this.tb),r=u(t,h,o,this.tb),n=Math.max(0,Math.min(i.ca,r.ca)-Math.max(i.ea,r.ea))*Math.max(0,Math.min(i.ja,r.ja)-Math.max(i.ga,r.ga)),i=c(i)+c(r),n<a?(a=n,p=h,s=i<s?i:s):n===a&&i<s&&(s=i,p=h);return p},qj:function(t,e,o){var i=t.ab?this.ff:n,r=t.ab?this.gf:s;this.ng(t,e,o,i)<(e=this.ng(t,e,o,r))&&t.children.sort(i)},ng:function(t,e,o,i){t.children.sort(i);for(var r,n=u(t,0,e,i=this.tb),s=u(t,o-e,o,i),p=y(n)+y(s),a=e;a<o-e;a++)r=t.children[a],h(n,t.ab?i(r):r),p+=y(n);for(a=o-e-1;e<=a;a--)r=t.children[a],h(s,t.ab?i(r):r),p+=y(s);return p},pj:function(t,e,o){for(;0<=o;o--)h(e[o],t)},tj:function(t){for(var e,o=t.length-1;0<=o;o--)0===t[o].children.length?0<o?(e=t[o-1].children).splice(e.indexOf(t[o]),1):this.clear():l(t[o],this.tb)},vj:function(t){var e=["return a"," - b",";"];this.ff=new Function("a","b",e.join(t[0])),this.gf=new Function("a","b",e.join(t[1])),this.tb=new Function("a","return {minX: a"+t[0]+", minY: a"+t[1]+", maxX: a"+t[2]+", maxY: a"+t[3]+"};")}}},{quickselect:1}]},{},[2])(2)}),jh=Fh.ma,(d=zh.prototype).Da=function(t,e){t={ea:t[0],ga:t[1],ca:t[2],ja:t[3],value:e};this.b.Da(t),this.a[B(e)]=t},d.load=function(t,e){for(var o=Array(e.length),i=0,r=e.length;i<r;i++){var n=t[i],s=e[i],n={ea:n[0],ga:n[1],ca:n[2],ja:n[3],value:s};o[i]=n,this.a[B(s)]=n}this.b.load(o)},d.remove=function(t){t=B(t);var e=this.a[t];return delete this.a[t],null!==this.b.remove(e)},d.forEach=function(t,e){return Jh(Yh(this),t,e)},d.clear=function(){this.b.clear(),this.a={}},d.G=function(){var t=this.b.data;return[t.ea,t.ga,t.ca,t.ja]},e(_h,Wa),(d=_h.prototype).wc=function(t,e){for(var o=t.Ad(),i=t.pa(),t=this.b.length,r=this.j.b.length,n=0,s=o.length;n<s;++n){var p=o[n].Zc();if(0<p.length){for(var a,h=io(h=p[0].ia(),h.length,i,-this.origin[0],-this.origin[1]),l=[],u=1,c=p.length;u<c;++u)a=io(a=p[u].ia(),a.length,i,-this.origin[0],-this.origin[1]),l.push(a);Mh(this.j,h,l,i),$h(this,h,l,i)}}this.b.length>t&&(this.g.push(t),this.i.push(e),this.l.s)&&(this.c.push(t),this.l.s=!1),this.j.b.length>r&&Ph(this.j,e,r)},d.yc=function(t,e){var o=t.Zc(),i=t.pa();if(0<o.length){this.g.push(this.b.length),this.i.push(e),this.l.s&&(this.c.push(this.b.length),this.l.s=!1),Ph(this.j,e);for(var r,t=io(t=o[0].ia(),t.length,i,-this.origin[0],-this.origin[1]),n=[],s=1,p=o.length;s<p;++s)r=io(r=o[s].ia(),r.length,i,-this.origin[0],-this.origin[1]),n.push(r);Mh(this.j,t,n,i),$h(this,t,n,i)}},d.vb=function(t){this.v=new Ha(this.a),this.o=new Ha(this.b),this.g.push(this.b.length),this.j.vb(t),0===this.c.length&&0<this.u.length&&(this.u=[]),this.b=this.a=null},d.wb=function(t){var e=this.v,o=this.o,i=this.j.wb(t);return function(){ih(t,e),ih(t,o),i()}},d.Ke=function(t,e){var o,i=sh(e,Eh,kh);return this.H?o=this.H:this.H=o=new Oh(t,i),e.Lc(i),t.enableVertexAttribArray(o.b),t.vertexAttribPointer(o.b,2,5126,!1,8,0),o},d.Le=function(t,e){t.disableVertexAttribArray(e.b)},d.yd=function(t,e,o,i){var r=t.getParameter(t.DEPTH_FUNC),n=t.getParameter(t.DEPTH_WRITEMASK);if(i||(t.enable(t.DEPTH_TEST),t.depthMask(!0),t.depthFunc(t.NOTEQUAL)),ot(o)){var s=this.g[this.g.length-1];for(o=this.c.length-1;0<=o;--o)u=this.c[o],l=this.u[o],t.uniform4fv(this.H.D,l),za(t,e,u,s),s=u}else for(var p,a,h=this.g.length-2,l=s=this.g[h+1],u=this.c.length-1;0<=u;--u){for(p=this.u[u],t.uniform4fv(this.H.D,p),p=this.c[u];0<=h&&this.g[h]>=p;)a=this.g[h],o[B(this.i[h]).toString()]&&(s!==l&&(za(t,e,s,l),t.clear(t.DEPTH_BUFFER_BIT)),l=a),h--,s=a;s!==l&&(za(t,e,s,l),t.clear(t.DEPTH_BUFFER_BIT)),s=l=p}i||(t.disable(t.DEPTH_TEST),t.clear(t.DEPTH_BUFFER_BIT),t.depthMask(n),t.depthFunc(r))},d.$d=function(t,e,o,i,r){for(var n,s,p,a=this.g.length-2,h=this.g[a+1],l=this.c.length-1;0<=l;--l)for(n=this.u[l],t.uniform4fv(this.H.D,n),s=this.c[l];0<=a&&this.g[a]>=s;){if(n=this.g[a],void 0===o[B(p=this.i[a]).toString()]&&p.V()&&(void 0===r||Ae(r,p.V().G()))&&(t.clear(t.COLOR_BUFFER_BIT|t.DEPTH_BUFFER_BIT),za(t,e,n,h),h=i(p)))return h;a--,h=n}},d.Ma=function(t,e){t=!((t=t?t.b:[0,0,0,0])instanceof CanvasGradient||t instanceof CanvasPattern)&&Pi(t).map(function(t,e){return 3!=e?t/255:t})||ja;this.l.fillColor&&Rt(t,this.l.fillColor)||(this.l.fillColor=t,this.l.s=!0,this.u.push(t)),e?this.j.Ma(null,e):this.j.Ma(null,new fp({color:[0,0,0,0],lineWidth:0}))},ul.prototype.f=function(){},e(cl,qp),cl.prototype.b=function(t,e){var t=void 0!==t?t.toString():"0",o=this.a[t];return void 0===o&&(this.a[t]=o={}),void 0===(t=o[e])&&(t=new dl[e](this.j,this.i),o[e]=t),t},cl.prototype.g=function(){return ot(this.a)},cl.prototype.f=function(t,e,o,i,r,n,s,p){var a,h,l,u,c,y,f=Object.keys(this.a).map(Number);for(f.sort(At),a=0,h=f.length;a<h;++a)for(c=this.a[f[a].toString()],l=0,u=ca.length;l<u;++l)void 0!==(y=c[ca[l]])&&y.f(t,e,o,i,r,n,s,p,void 0,!1)},cl.prototype.Ba=function(t,e,o,i,r,n,s,p,a,h){var l,u=e.b;return u.bindFramebuffer(u.FRAMEBUFFER,rh(e)),fl(this,e,t,i,r,s,p,a,function(t){var e=new Uint8Array(4);if(u.readPixels(0,0,1,1,u.RGBA,u.UNSIGNED_BYTE,e),0<e[3]&&(t=h(t)))return t},!0,l=void 0!==this.c?te(he(t),i*this.c):l)};var gl=[1,1],dl={Circle:Za,Image:lh,LineString:mh,Polygon:_h,Text:ul};function vl(t,e,o,i,r,n,s){this.b=t,this.g=e,this.a=n,this.f=s,this.j=r,this.i=i,this.c=o,this.l=this.o=this.v=null}function bl(){this.b="precision mediump float;varying vec2 a;uniform float f;uniform sampler2D g;void main(void){vec4 texColor=texture2D(g,a);gl_FragColor.rgb=texColor.rgb;gl_FragColor.a=texColor.a*f;}"}e(vl,Ap),(d=vl.prototype).Gd=function(t){this.Ma(t.f,t.g),this.dc(t.a)},d.tc=function(t){switch(t.Y()){case"Point":this.xc(t,null);break;case"LineString":this.Pb(t,null);break;case"Polygon":this.yc(t,null);break;case"MultiPoint":this.vc(t,null);break;case"MultiLineString":this.uc(t,null);break;case"MultiPolygon":this.wc(t,null);break;case"GeometryCollection":this.kf(t,null);break;case"Circle":this.hc(t,null)}},d.jf=function(t,e){t=(0,e.c)(t);t&&Ae(this.a,t.G())&&(this.Gd(e),this.tc(t))},d.kf=function(t){for(var e=0,o=(t=t.f).length;e<o;++e)this.tc(t[e])},d.xc=function(t,e){var o=this.b,i=new cl(1,this.a).b(0,"Image");i.dc(this.v),i.xc(t,e),i.vb(o),i.f(this.b,this.g,this.c,this.i,this.j,this.f,1,{},void 0,!1),i.wb(o)()},d.vc=function(t,e){var o=this.b,i=new cl(1,this.a).b(0,"Image");i.dc(this.v),i.vc(t,e),i.vb(o),i.f(this.b,this.g,this.c,this.i,this.j,this.f,1,{},void 0,!1),i.wb(o)()},d.Pb=function(t,e){var o=this.b,i=new cl(1,this.a).b(0,"LineString");i.Ma(null,this.l),i.Pb(t,e),i.vb(o),i.f(this.b,this.g,this.c,this.i,this.j,this.f,1,{},void 0,!1),i.wb(o)()},d.uc=function(t,e){var o=this.b,i=new cl(1,this.a).b(0,"LineString");i.Ma(null,this.l),i.uc(t,e),i.vb(o),i.f(this.b,this.g,this.c,this.i,this.j,this.f,1,{},void 0,!1),i.wb(o)()},d.yc=function(t,e){var o=this.b,i=new cl(1,this.a).b(0,"Polygon");i.Ma(this.o,this.l),i.yc(t,e),i.vb(o),i.f(this.b,this.g,this.c,this.i,this.j,this.f,1,{},void 0,!1),i.wb(o)()},d.wc=function(t,e){var o=this.b,i=new cl(1,this.a).b(0,"Polygon");i.Ma(this.o,this.l),i.wc(t,e),i.vb(o),i.f(this.b,this.g,this.c,this.i,this.j,this.f,1,{},void 0,!1),i.wb(o)()},d.hc=function(t,e){var o=this.b,i=new cl(1,this.a).b(0,"Circle");i.Ma(this.o,this.l),i.hc(t,e),i.vb(o),i.f(this.b,this.g,this.c,this.i,this.j,this.f,1,{},void 0,!1),i.wb(o)()},d.dc=function(t){this.v=t},d.Ma=function(t,e){this.o=t,this.l=e},e(bl,ka);var ml=new bl;function wl(){this.b="varying vec2 a;attribute vec2 b;attribute vec2 c;uniform mat4 d;uniform mat4 e;void main(void){gl_Position=e*vec4(b,0.,1.);a=(d*vec4(c,0.,1.)).st;}"}e(wl,Oa);var xl=new wl;function Sl(t,e){this.g=t.getUniformLocation(e,"f"),this.f=t.getUniformLocation(e,"e"),this.i=t.getUniformLocation(e,"d"),this.c=t.getUniformLocation(e,"g"),this.b=t.getAttribLocation(e,"b"),this.a=t.getAttribLocation(e,"c")}function Ml(t,e){Fp.call(this,e),this.f=t,this.U=new Ha([-1,-1,0,0,1,-1,1,0,-1,1,0,1,1,1,1,1]),this.i=this.yb=null,this.j=void 0,this.v=Is(),this.u=Is(),this.C=Xa(),this.H=null}function Pl(t,e,o,i){var r;vt(t=t.a,e)&&(r=i.viewState,t.b(new Cs(e,new vl(o,r.center,r.resolution,r.rotation,i.size,i.extent,i.pixelRatio),i,null,o)))}function Tl(t,e,o,i,r,n){this.c=void 0!==n?n:null,Qs.call(this,t,e,o,void 0!==n?ep:ip,i),this.g=r}e(Ml,Fp),Ml.prototype.Dh=function(t,e,o){Pl(this,"precompose",o,t),oh(o,34962,this.U);var i,r=o.b,n=sh(o,ml,xl);this.H?i=this.H:this.H=i=new Sl(r,n),o.Lc(n)&&(r.enableVertexAttribArray(i.b),r.vertexAttribPointer(i.b,2,5126,!1,16,0),r.enableVertexAttribArray(i.a),r.vertexAttribPointer(i.a,2,5126,!1,16,8),r.uniform1i(i.c,0)),r.uniformMatrix4fv(i.i,!1,Va(this.C,this.v)),r.uniformMatrix4fv(i.f,!1,Va(this.C,this.u)),r.uniform1f(i.g,e.opacity),r.bindTexture(3553,this.yb),r.drawArrays(5,0,4),Pl(this,"postcompose",o,t)},Ml.prototype.If=function(){this.i=this.yb=null,this.j=void 0},e(Tl,Qs),Tl.prototype.i=function(t){this.state=t?rp:ip,this.s()},Tl.prototype.load=function(){this.state==ep&&(this.state=op,this.s(),this.c(this.i.bind(this)))},Tl.prototype.a=function(){return this.g};var Al,wr=-1<navigator.userAgent.indexOf("OPR"),sr=-1<navigator.userAgent.indexOf("Edge");function El(t,e,o,i){var r=Qe(o,e,t);return o=Ke(e,i,o),void 0!==(e=e.ic())&&(o*=e),void 0!==(e=t.ic())&&(o/=e),t=Ke(t,o,r)/o,isFinite(t)&&0<t&&(o/=t),o}function Cl(t,e,o,i){t=o-t,e=i-e;var r=Math.sqrt(t*t+e*e);return[Math.round(o+t/r),Math.round(i+e/r)]}function jl(t,e,g,d,o,v,i,r,n,s,p){var b,m,a,w,x=Ri(Math.round(g*t),Math.round(g*e));return 0!==n.length&&(x.scale(g,g),b=pe(),n.forEach(function(t){ce(b,t.extent)}),m=Ri(Math.round(g*Te(b)/d),Math.round(g*xe(b)/d)),a=g/d,n.forEach(function(t){m.drawImage(t.image,s,s,t.image.width-2*s,t.image.height-2*s,(t.extent[0]-b[0])*a,-(t.extent[3]-b[3])*a,Te(t.extent)*a,xe(t.extent)*a)}),w=Me(i),r.f.forEach(function(t){var e=t.source,o=t.target,i=e[1][0],r=e[1][1],n=e[2][0],s=e[2][1],p=(t=(o[0][0]-w[0])/v,-(o[0][1]-w[1])/v),a=(o[1][0]-w[0])/v,h=-(o[1][1]-w[1])/v,l=(o[2][0]-w[0])/v,u=-(o[2][1]-w[1])/v;t:{for(r=(i=[[i=i-(o=e[0][0]),r=r-(e=e[0][1]),0,0,a-t],[n=n-o,s=s-e,0,0,l-t],[0,0,i,r,h-p],[0,0,n,s,u-p]]).length,n=0;n<r;n++){for(var s=n,c=Math.abs(i[n][n]),y=n+1;y<r;y++){var f=Math.abs(i[y][n]);c<f&&(c=f,s=y)}if(0===c){i=null;break t}for(c=i[s],i[s]=i[n],i[n]=c,s=n+1;s<r;s++)for(c=-i[s][n]/i[n][n],y=n;y<r+1;y++)i[s][y]=n==y?0:i[s][y]+c*i[n][y]}for(n=Array(r),s=r-1;0<=s;s--)for(n[s]=i[s][r]/i[s][s],c=s-1;0<=c;c--)i[c][r]-=i[c][s]*n[s];i=n}i&&(x.save(),x.beginPath(),Al?(r=Cl(n=(t+a+l)/3,s=(p+h+u)/3,t,p),a=Cl(n,s,a,h),l=Cl(n,s,l,u),x.moveTo(a[0],a[1]),x.lineTo(r[0],r[1]),x.lineTo(l[0],l[1])):(x.moveTo(a,h),x.lineTo(t,p),x.lineTo(l,u)),x.clip(),x.transform(i[0],i[2],i[1],i[3],t,p),x.translate(b[0]-o,b[3]-e),x.scale(d/g,-d/g),x.drawImage(m.canvas,0,0),x.restore())}),p)&&(x.save(),x.strokeStyle="black",x.lineWidth=1,r.f.forEach(function(t){t=((n=t.target)[0][0]-w[0])/v;var e=-(n[0][1]-w[1])/v,o=(n[1][0]-w[0])/v,i=-(n[1][1]-w[1])/v,r=(n[2][0]-w[0])/v,n=-(n[2][1]-w[1])/v;x.beginPath(),x.moveTo(o,i),x.lineTo(t,e),x.lineTo(r,n),x.closePath(),x.stroke()}),x.restore()),x.canvas}function Ll(t,e,o,i,r){this.g=t,this.c=e;var n,s={},p=qe(this.c,this.g),a=(this.a=function(t){var e=t[0]+"/"+t[1];return s[e]||(s[e]=p(t)),s[e]},this.i=i,this.v=r*r,this.f=[],this.l=!1,this.o=this.g.a&&!!i&&!!this.g.G()&&Te(i)==Te(this.g.G()),this.b=this.g.G()?Te(this.g.G()):null,this.j=this.c.G()?Te(this.c.G()):null,t=Me(o),e=Pe(o),i=be(o),o=ve(o),r=this.a(t),this.a(e)),h=this.a(i),l=this.a(o);!function t(e,o,i,r,n,s,p,a,h,l){var u=Qt([s,p,a,h]),c=e.b?Te(u)/e.b:null,y=e.b,f=e.g.a&&.5<c&&c<1,g=!1;{var d;0<l&&(e.c.g&&e.j&&(d=Qt([o,i,r,n]),g|=.25<Te(d)/e.j),!f)&&e.g.g&&c&&(g|=.25<c)}if(g||!e.i||Ae(u,e.i)){if(!(g||isFinite(s[0])&&isFinite(s[1])&&isFinite(p[0])&&isFinite(p[1])&&isFinite(a[0])&&isFinite(a[1])&&isFinite(h[0])&&isFinite(h[1]))){if(!(0<l))return;g=!0}if(0<l&&(g||(u=e.a([(o[0]+r[0])/2,(o[1]+r[1])/2]),y=f?(q(s[0],y)+q(a[0],y))/2-q(u[0],y):(s[0]+a[0])/2-u[0],u=(s[1]+a[1])/2-u[1],g=y*y+u*u>e.v),g))Math.abs(o[0]-r[0])<=Math.abs(o[1]-r[1])?(f=[(i[0]+r[0])/2,(i[1]+r[1])/2],y=e.a(f),u=[(n[0]+o[0])/2,(n[1]+o[1])/2],c=e.a(u),t(e,o,i,f,u,s,p,y,c,l-1),t(e,u,f,r,n,c,y,a,h,l-1)):(f=[(o[0]+i[0])/2,(o[1]+i[1])/2],y=e.a(f),u=[(r[0]+n[0])/2,(r[1]+n[1])/2],c=e.a(u),t(e,o,f,u,n,s,y,c,h,l-1),t(e,f,i,r,u,y,p,a,c,l-1));else{if(f){if(!e.o)return;e.l=!0}e.f.push({source:[s,a,h],target:[o,r,n]}),e.f.push({source:[s,p,a],target:[o,i,r]})}}}(this,t,e,i,o,r,a,h,l,10),this.l&&(n=1/0,this.f.forEach(function(t){n=Math.min(n,t.source[0][0],t.source[1][0],t.source[2][0])}),this.f.forEach(function(t){var e;Math.max(t.source[0][0],t.source[1][0],t.source[2][0])-n>this.b/2&&((e=[[t.source[0][0],t.source[0][1]],[t.source[1][0],t.source[1][1]],[t.source[2][0],t.source[2][1]]])[0][0]-n>this.b/2&&(e[0][0]-=this.b),e[1][0]-n>this.b/2&&(e[1][0]-=this.b),e[2][0]-n>this.b/2&&(e[2][0]-=this.b),Math.max(e[0][0],e[1][0],e[2][0])-Math.min(e[0][0],e[1][0],e[2][0])<this.b/2)&&(t.source=e)},this)),s={}}function Rl(t){var e=pe();return t.f.forEach(function(t){t=t.source,ye(e,t[0]),ye(e,t[1]),ye(e,t[2])}),e}function Nl(t,e,o,i,r,n){this.H=e,this.v=t.G();var s=(p=e.G())?Se(o,p):o,p=El(t,e,me(s),i);this.l=new Ll(t,e,s,this.v,.5*p),this.c=i,this.g=o,t=Rl(this.l),this.o=(this.xb=n(t,p,r))?this.xb.f:1,this.Md=this.i=null,r=ip,n=[],this.xb&&(r=ep,n=this.xb.j),Qs.call(this,o,i,this.o,r,n)}function Il(t){wt.call(this),this.f=Ye(t.projection),this.j=Fl(t.attributions),this.L=t.logo,this.Fa=void 0!==t.state?t.state:"ready",this.D=void 0!==t.wrapX&&t.wrapX}function Fl(t){if("string"==typeof t)return[new gi({html:t})];if(t instanceof gi)return[t];if(Array.isArray(t)){for(var e=t.length,o=Array(e),i=0;i<e;i++){var r=t[i];o[i]="string"==typeof r?new gi({html:r}):r}return o}return null}function kl(t,e){t.Fa=e,t.s()}function Ol(t){Il.call(this,{attributions:t.attributions,extent:t.extent,logo:t.logo,projection:t.projection,state:t.state}),this.A=void 0!==t.resolutions?t.resolutions:null,this.a=null,this.sa=0}function Dl(t,e){return e=t.A?t.A[Ct(t.A,e,0)]:e}function Bl(t,e){t.a().src=e}function Ul(t,e){ft.call(this,t),this.image=e}Al=!(!navigator.userAgent.match("CriOS")&&"chrome"in window&&"Google Inc."===navigator.vendor&&0==wr&&0==sr),e(Nl,Qs),Nl.prototype.oa=function(){this.state==op&&(lt(this.Md),this.Md=null),Qs.prototype.oa.call(this)},Nl.prototype.a=function(){return this.i},Nl.prototype.Ld=function(){var t=this.xb.W();t==ip&&(this.i=jl(Te(this.g)/this.c,xe(this.g)/this.c,this.o,this.xb.resolution,0,this.c,this.g,this.l,[{extent:this.xb.G(),image:this.xb.a()}],0)),this.state=t,this.s()},Nl.prototype.load=function(){var t;this.state==ep&&(this.state=op,this.s(),(t=this.xb.W())==ip||t==rp?this.Ld():(this.Md=c(this.xb,"change",function(){var t=this.xb.W();t!=ip&&t!=rp||(lt(this.Md),this.Md=null,this.Ld())},this),this.xb.load()))},e(Il,wt),(d=Il.prototype).Ba=G,d.za=function(){return this.j},d.ya=function(){return this.L},d.Aa=function(){return this.f},d.W=function(){return this.Fa},d.wa=function(){this.s()},d.ua=function(t){this.j=Fl(t),this.s()},e(Ol,Il),Ol.prototype.U=function(t,e,o,i){var r=this.f;if(r&&i&&!Ze(r,i)){if(this.a){if(this.sa==this.g&&Ze(this.a.H,i)&&this.a.resolution==e&&this.a.f==o&&ue(this.a.G(),t))return this.a;yt(this.a),this.a=null}return this.a=new Nl(r,i,t,e,o,function(t,e,o){return this.Xc(t,e,o,r)}.bind(this)),this.sa=this.g,this.a}return this.Xc(t,e,o,i=r?r:i)},Ol.prototype.o=function(t){switch((t=t.target).W()){case op:this.b(new Ul(Gl,t));break;case ip:this.b(new Ul(Kl,t));break;case rp:this.b(new Ul(Xl,t))}},e(Ul,ft);var Gl="imageloadstart",Kl="imageloadend",Xl="imageloaderror";function Vl(t){Ol.call(this,{attributions:t.attributions,logo:t.logo,projection:t.projection,resolutions:t.resolutions,state:t.state}),this.fa=t.canvasFunction,this.P=null,this.Z=0,this.na=void 0!==t.ratio?t.ratio:1.5}function Wl(t){this.c=t.source,this.Ja=Is(),this.i=Ri(),this.l=[0,0],this.xa=null==t.renderBuffer?100:t.renderBuffer,this.u=null,Vl.call(this,{attributions:t.attributions,canvasFunction:this.Jj.bind(this),logo:t.logo,projection:t.projection,ratio:t.ratio,resolutions:t.resolutions,state:this.c.W()}),this.C=null,this.v=void 0,this.Fh(t.style),c(this.c,"change",this.kn,this)}function zl(t,e){Ml.call(this,t,e),this.o=this.l=this.c=null}function Hl(){this.b="precision mediump float;varying vec2 a;uniform sampler2D e;void main(void){gl_FragColor=texture2D(e,a);}"}e(Vl,Ol),Vl.prototype.Xc=function(t,e,o,i){e=Dl(this,e);var r=this.P;return r&&this.Z==this.g&&r.resolution==e&&r.f==o&&re(r.G(),t)||(Ce(t=t.slice(),this.na),(i=this.fa(t,e,o,[Te(t)/e*o,xe(t)/e*o],i))&&(r=new Tl(t,e,o,this.j,i)),this.P=r,this.Z=this.g),r},e(Wl,Vl),(d=Wl.prototype).Jj=function(t,n,s,e,o){var i,r,p=new ya(.5*n/s,t,n,this.c.xa,this.xa),a=(this.c.Ed(t,n,o),!1);return this.c.Qb(t,function(t){var e;if(!(i=a))if((i=t.Gc())?e=i.call(t,n):this.v&&(e=this.v(t,n)),e){for(var o=!1,i=0,r=(e=Array.isArray(e)?e:[e]).length;i<r;++i)o=xa(p,t,e[i],wa(n,s),this.jn,this)||o;i=o}else i=!1;a=i},this),da(p),a?null:(this.l[0]!=e[0]||this.l[1]!=e[1]?(this.i.canvas.width=e[0],this.i.canvas.height=e[1],this.l[0]=e[0],this.l[1]=e[1]):this.i.clearRect(0,0,e[0],e[1]),o=this,i=me(t),r=s/(r=n),t=Xs(o.Ja,e[0]/2,e[1]/2,r,-r,0,-i[0],-i[1]),p.f(this.i,s,t,0,{}),this.u=p,this.i.canvas)},d.Ba=function(t,e,o,i,r,n){var s;if(this.u)return s={},this.u.Ba(t,e,0,i,r,function(t){var e=B(t).toString();if(!(e in s))return s[e]=!0,n(t)})},d.fn=function(){return this.c},d.gn=function(){return this.C},d.hn=function(){return this.v},d.jn=function(){this.s()},d.kn=function(){kl(this,this.c.W())},d.Fh=function(t){this.C=void 0!==t?t:bp,this.v=t?dp(this.C):void 0,this.s()},e(zl,Ml),zl.prototype.Ba=function(t,e,o,i,r){var n=this.a;return n.la().Ba(t,e.viewState.resolution,e.viewState.rotation,o,e.skippedFeatureUids,function(t){return i.call(r,t,n)})},zl.prototype.Jf=function(t,e){var o=this.f.g,i=t.pixelRatio,r=t.viewState,n=r.center,s=r.resolution,p=r.rotation,a=this.c,h=this.yb,l=this.a.la(),u=t.viewHints,c=t.extent;return void 0!==e.extent&&(c=Se(c,e.extent)),u[qo]||u[1]||Ee(c)||(r=l.U(c,s,i,r.projection))&&kp(this,r)&&(e=this,u=(u=a=r).a(),h=hh(e.f.g,u),this.yb)&&t.postRenderFunctions.push(function(t,e){t.isContextLost()||t.deleteTexture(e)}.bind(null,o,this.yb)),a&&(o=this.f.c.j,c=this,r=o.width,e=o.height,u=i,o=n,n=s,s=p,p=a.G(),r*=n,e*=n,Fs(c=c.u),Gs(c,2*u/r,2*u/e),Us(c,-s),Ks(c,p[0]-o[0],p[1]-o[1]),Gs(c,(p[2]-p[0])/2,(p[3]-p[1])/2),Ks(c,1,1),this.o=null,Fs(i=this.v),Gs(i,1,-1),Ks(i,0,-1),this.c=a,this.yb=h,Bp(t.attributions,a.j),Up(t,l)),!!a},zl.prototype.te=function(t,e){return void 0!==this.Ba(t,e,0,Le,this)},zl.prototype.Hf=function(t,e,o,i){if(this.c&&this.c.a())if(this.a.la()instanceof Wl){var r=Bs(e.pixelToCoordinateTransform,t.slice());if(this.Ba(r,e,0,Le,this))return o.call(i,this.a,null)}else{var n,s,r=[this.c.a().width,this.c.a().height];if(this.o||(n=e.size,Ks(e=Is(),-1,-1),Gs(e,2/n[0],2/n[1]),Ks(e,0,n[1]),Gs(e,1,-1),n=Vs(this.u.slice()),Ks(s=Is(),0,r[1]),Gs(s,1,-1),Gs(s,r[0]/2,r[1]/2),Ks(s,1,1),ks(s,n),ks(s,e),this.o=s),!((t=Bs(this.o,t.slice()))[0]<0||t[0]>r[0]||t[1]<0||t[1]>r[1])&&(this.l||(this.l=Ri(1,1)),this.l.clearRect(0,0,1,1),this.l.drawImage(this.c.a(),t[0],t[1],1,1,0,0,1,1),0<(r=this.l.getImageData(0,0,1,1).data)[3]))return o.call(i,this.a,r)}},e(Hl,ka);var Yl=new Hl;function Zl(){this.b="varying vec2 a;attribute vec2 b;attribute vec2 c;uniform vec4 d;void main(void){gl_Position=vec4(b*d.xy+d.zw,0.,1.);a=c;}"}e(Zl,Oa);var ql=new Zl;function Jl(t,e){this.g=t.getUniformLocation(e,"e"),this.f=t.getUniformLocation(e,"d"),this.b=t.getAttribLocation(e,"b"),this.a=t.getAttribLocation(e,"c")}function _l(t,e){Ml.call(this,t,e),this.L=Yl,this.Z=ql,this.c=null,this.D=new Ha([0,0,0,1,1,0,1,1,0,1,0,0,1,1,1,0]),this.A=this.l=null,this.o=-1,this.P=[0,0]}function $l(t,e){Ml.call(this,t,e),this.o=!1,this.P=-1,this.L=NaN,this.A=pe(),this.l=this.c=this.D=null}function Ql(){this.f=0,this.b={},this.g=this.a=null}function tu(t,e){Ws.call(this,0,e),this.b=document.createElement("CANVAS"),this.b.style.width="100%",this.b.style.height="100%",this.b.className="ol-unselectable",t.insertBefore(this.b,t.childNodes[0]||null),this.u=this.A=0,this.C=Ri(),this.o=!0,this.g=rr(this.b,{antialias:!0,depth:!0,failIfMajorPerformanceCaveat:!0,preserveDrawingBuffer:!1,stencil:!0}),this.c=new eh(this.b,this.g),c(this.b,"webglcontextlost",this.Um,this),c(this.b,"webglcontextrestored",this.Vm,this),this.a=new Ql,this.H=null,this.j=new Yr(function(t){var e=t[1],o=(t=t[2],e[0]-this.H[0]),e=e[1]-this.H[1];return 65536*Math.log(t)+Math.sqrt(o*o+e*e)/t}.bind(this),function(t){return t[0].bb()}),this.D=function(){var t;return 0!==this.j.b.length&&(_r(this.j),eu(this,(t=Zr(this.j))[0],t[3],t[4])),!1}.bind(this),this.i=0,iu(this)}function eu(t,e,o,i){var r,n,s,p=t.g,a=e.bb();t.a.b.hasOwnProperty(a)?(t=t.a.get(a),p.bindTexture(3553,t.yb),9729!=t.gh&&(p.texParameteri(3553,10240,9729),t.gh=9729),9729!=t.ih&&(p.texParameteri(3553,10241,9729),t.ih=9729)):(r=p.createTexture(),p.bindTexture(3553,r),0<i?(n=t.C.canvas,s=t.C,t.A!==o[0]||t.u!==o[1]?(n.width=o[0],n.height=o[1],t.A=o[0],t.u=o[1]):s.clearRect(0,0,o[0],o[1]),s.drawImage(e.ub(),i,i,o[0],o[1],0,0,o[0],o[1]),p.texImage2D(3553,0,6408,6408,5121,n)):p.texImage2D(3553,0,6408,6408,5121,e.ub()),p.texParameteri(3553,10240,9729),p.texParameteri(3553,10241,9729),p.texParameteri(3553,10242,33071),p.texParameteri(3553,10243,33071),t.a.set(a,{yb:r,gh:9729,ih:9729}))}function ou(t,e,o){var i,r=t.l;vt(r,e)&&(t=t.c,i=o.viewState,r.b(new Cs(e,new vl(t,i.center,i.resolution,i.rotation,o.size,o.extent,o.pixelRatio),o,null,t)))}function iu(t){(t=t.g).activeTexture(33984),t.blendFuncSeparate(770,771,1,771),t.disable(2884),t.disable(2929),t.disable(3089),t.disable(2960)}e(_l,Ml),(d=_l.prototype).oa=function(){ih(this.f.c,this.D),Ml.prototype.oa.call(this)},d.hf=function(e,i,r){var n=this.f;return function(o,t){return t1(e,i,o,t,function(t){var e=n.a.b.hasOwnProperty(t.bb());return e&&(r[o]||(r[o]={}),r[o][t.Ca.toString()]=t),e})}},d.If=function(){Ml.prototype.If.call(this),this.c=null},d.Jf=function(t,e,o){var i,r,n,s,p,a,h=this.f,l=o.b,u=t.viewState,c=u.projection,y=this.a,f=y.la(),g=f.Db(c),d=g.Ec(u.resolution),v=g.Ha(d),b=f.Dd(d,t.pixelRatio,c),m=b[0]/ei(g.Za(d),this.P)[0],w=v/m,x=f.jb(m)*f.qf(c),S=u.center,M=t.extent,P=pi(g,M,v);if(this.l&&(p=this.l,a=P,p.ea==a.ea)&&p.ga==a.ga&&p.ca==a.ca&&p.ja==a.ja&&this.o==f.g)w=this.A;else{var T=[P.ca-P.ea+1,P.ja-P.ga+1];V(0<(p=Math.max(T[0]*b[0],T[1]*b[1])),29);for(var A,E,T=w*(R=Math.pow(2,Math.ceil(Math.log(p)/Math.LN2))),C=g.Kc(d),w=[N=C[0]+P.ea*b[0]*w,w=C[1]+P.ga*b[1]*w,N+T,w+T],j=(a=t,r=R,s=(i=this).f.g,void 0===i.j||i.j!=r?(a.postRenderFunctions.push(function(t,e,o){t.isContextLost()||(t.deleteFramebuffer(e),t.deleteTexture(o))}.bind(null,s,i.i,i.yb)),a=ah(s,r,r),n=s.createFramebuffer(),s.bindFramebuffer(36160,n),s.framebufferTexture2D(36160,36064,3553,a,0),i.yb=a,i.i=n,i.j=r):s.bindFramebuffer(36160,i.i),l.viewport(0,0,R,R),l.clearColor(0,0,0,0),l.clear(16384),l.disable(3042),R=sh(o,this.L,this.Z),o.Lc(R),this.c||(this.c=new Jl(l,R)),oh(o,34962,this.D),l.enableVertexAttribArray(this.c.b),l.vertexAttribPointer(this.c.b,2,5126,!1,16,0),l.enableVertexAttribArray(this.c.a),l.vertexAttribPointer(this.c.a,2,5126,!1,16,8),l.uniform1i(this.c.g,0),(o={})[d]={},this.hf(f,c,o)),L=y.c(),R=!0,N=pe(),I=new Jo(0,0,0,0),F=P.ea;F<=P.ca;++F)for(E=P.ga;E<=P.ja;++E)if(C=f.Dc(d,F,E,m,c),void 0===e.extent||Ae(A=g.Na(C.Ca,N),e.extent)){if((A=(C=(A=(A=C.W())==Hr||4==A||3==A&&!L)?C:zr(C)).W())==Hr){if(h.a.b.hasOwnProperty(C.bb())){o[d][C.Ca.toString()]=C;continue}}else if(4==A||3==A&&!L)continue;R=!1,(A=ni(g,C.Ca,j,I,N))||(C=si(g,C.Ca,I,N))&&j(d+1,C)}(e=Object.keys(o).map(Number)).sort(At);for(var k,j=new Float32Array(4),L=0,I=e.length;L<I;++L)for(k in F=o[e[L]])C=F[k],A=g.Na(C.Ca,N),j[0]=2*(A[2]-A[0])/T,j[1]=2*(A[3]-A[1])/T,j[2]=2*(A[0]-w[0])/T-1,j[3]=2*(A[1]-w[1])/T-1,l.uniform4fv(this.c.f,j),eu(h,C,b,x*m),l.drawArrays(5,0,4);R?(this.l=P,this.A=w,this.o=f.g):(this.A=this.l=null,this.o=-1,t.animate=!0)}Gp(t.usedTiles,f,d,P);var O=h.j;return Kp(t,f,g,m,c,M,d,y.f(),function(t){t.W()!=Hr||h.a.b.hasOwnProperty(t.bb())||t.bb()in O.a||O.c([t,hi(g,t.Ca),g.Ha(t.Ca[0]),b,x*m])},this),Dp(t,f),Up(t,f),Fs(l=this.v),Ks(l,(Math.round(S[0]/v)*v-w[0])/(w[2]-w[0]),(Math.round(S[1]/v)*v-w[1])/(w[3]-w[1])),0!==u.rotation&&Us(l,u.rotation),Gs(l,t.size[0]*u.resolution/(w[2]-w[0]),t.size[1]*u.resolution/(w[3]-w[1])),Ks(l,-.5,-.5),!0},d.Hf=function(t,e,o,i){if(this.i){t=[(t=Bs(this.v,[t[0]/e.size[0],(e.size[1]-t[1])/e.size[1]].slice()))[0]*this.j,t[1]*this.j],(e=this.f.c.b).bindFramebuffer(e.FRAMEBUFFER,this.i);var r=new Uint8Array(4);if(e.readPixels(t[0],t[1],1,1,e.RGBA,e.UNSIGNED_BYTE,r),0<r[3])return o.call(i,this.a,r)}},e($l,Ml),(d=$l.prototype).Dh=function(t,e,o){this.l=e;var i=t.viewState,r=this.c,n=t.size,s=t.pixelRatio,p=this.f.g;r&&!r.g()&&(p.enable(p.SCISSOR_TEST),p.scissor(0,0,n[0]*s,n[1]*s),r.f(o,i.center,i.resolution,i.rotation,n,s,e.opacity,e.me?t.skippedFeatureUids:{}),p.disable(p.SCISSOR_TEST))},d.oa=function(){var t=this.c;t&&(yl(t,this.f.c)(),this.c=null),Ml.prototype.oa.call(this)},d.Ba=function(t,e,o,i,r){var n,s;if(this.c&&this.l)return o=e.viewState,n=this.a,s={},this.c.Ba(t,this.f.c,o.center,o.resolution,o.rotation,e.size,e.pixelRatio,this.l.opacity,{},function(t){var e=B(t).toString();if(!(e in s))return s[e]=!0,i.call(r,t,n)})},d.te=function(t,e){var o,i,r,n,s,p,a;return!(!this.c||!this.l)&&(n=e.viewState,o=this.c,t=t,i=this.f.c,r=n.resolution,n=n.rotation,s=e.pixelRatio,p=this.l.opacity,e=e.skippedFeatureUids,(a=i.b).bindFramebuffer(a.FRAMEBUFFER,rh(i)),void 0!==fl(o,i,t,r,n,s,p,e,function(){var t=new Uint8Array(4);return a.readPixels(0,0,1,1,a.RGBA,a.UNSIGNED_BYTE,t),0<t[3]},!1))},d.Hf=function(t,e,o,i){if(t=Bs(e.pixelToCoordinateTransform,t.slice()),this.te(t,e))return o.call(i,this.a,null)},d.Eh=function(){Op(this)},d.Jf=function(t,e,o){function i(t){var e,o=t.Gc();if(o?e=o.call(t,h):(o=r.j)&&(e=o(t,h)),e){if(o=!1,Array.isArray(e))for(var i=e.length-1;0<=i;--i)o=xa(f,t,e[i],wa(h,l),this.Eh,this)||o;else o=xa(f,t,e,wa(h,l),this.Eh,this)||o;t=o,this.o=this.o||t}}var r=this.a,n=(e=r.la(),Bp(t.attributions,e.j),Up(t,e),t.viewHints[qo]),s=t.viewHints[1],p=r.Z,a=r.fa;if(!(!this.o&&!p&&n||!a&&s)){var s=t.extent,n=(p=t.viewState).projection,h=p.resolution,l=t.pixelRatio,p=r.g,u=r.i,a=r.get("renderOrder");if(void 0===a&&(a=ma),s=te(s,u*h),this.o||this.L!=h||this.P!=p||this.D!=a||!re(this.A,s)){this.c&&t.postRenderFunctions.push(yl(this.c,o)),this.o=!1;var c,y,f=new cl(.5*h/l,s,r.i),g=(e.Ed(s,h,n),a?(c=[],e.Qb(s,function(t){c.push(t)},this),c.sort(a),c.forEach(i,this)):e.Qb(s,i,this),f),d=o;for(y in g.a){var v,b=g.a[y];for(v in b)b[v].vb(d)}this.L=h,this.P=p,this.D=a,this.A=s,this.c=f}}return!0},(d=Ql.prototype).clear=function(){this.f=0,this.b={},this.g=this.a=null},d.forEach=function(t,e){for(var o=this.a;o;)t.call(e,o.Rc,o.jc,this),o=o.Gb},d.get=function(t){return V(void 0!==(t=this.b[t]),15),t!==this.g&&(t===this.a?(this.a=this.a.Gb,this.a.fd=null):(t.Gb.fd=t.fd,t.fd.Gb=t.Gb),t.Gb=null,t.fd=this.g,this.g=this.g.Gb=t),t.Rc},d.pop=function(){var t=this.a;return delete this.b[t.jc],t.Gb&&(t.Gb.fd=null),this.a=t.Gb,this.a||(this.g=null),--this.f,t.Rc},d.replace=function(t,e){this.get(t),this.b[t].Rc=e},d.set=function(t,e){V(!(t in this.b),16);e={jc:t,Gb:null,fd:this.g,Rc:e};this.g?this.g.Gb=e:this.a=e,this.g=e,this.b[t]=e,++this.f},e(tu,Ws),(d=tu.prototype).Ag=function(t){return t instanceof Js?new zl(this,t):t instanceof p?new _l(this,t):t instanceof s?new $l(this,t):null},d.oa=function(){var e=this.g;e.isContextLost()||this.a.forEach(function(t){t&&e.deleteTexture(t.yb)}),yt(this.c),Ws.prototype.oa.call(this)},d.Nj=function(t,e){for(var o,i=this.g;1024<this.a.f-this.i;){if(o=this.a.a.Rc)i.deleteTexture(o.yb);else{if(+this.a.a.jc==e.index)break;--this.i}this.a.pop()}},d.Y=function(){return"webgl"},d.Um=function(t){for(var e in t.preventDefault(),this.a.clear(),this.i=0,t=this.f)t[e].If()},d.Vm=function(){iu(this),this.l.render()},d.ag=function(t){var e=this.c,o=this.g;if(o.isContextLost())return!1;if(!t)return this.o&&(this.b.style.display="none",this.o=!1),!1;this.H=t.focus,this.a.set((-t.index).toString(),null),++this.i,ou(this,"precompose",t);for(var i,r=[],n=t.layerStatesArray,s=(Nt(n),t.viewState.resolution),p=0,a=n.length;p<a;++p)js(i=n[p],s)&&"ready"==i.Ei&&Ys(this,i.layer).Jf(t,i,e)&&r.push(i);for(n=t.size[0]*t.pixelRatio,s=t.size[1]*t.pixelRatio,this.b.width==n&&this.b.height==s||(this.b.width=n,this.b.height=s),o.bindFramebuffer(36160,null),o.clearColor(0,0,0,0),o.clear(16384),o.enable(3042),o.viewport(0,0,this.b.width,this.b.height),p=0,a=r.length;p<a;++p)Ys(this,(i=r[p]).layer).Dh(t,i,e);this.o||(this.b.style.display="",this.o=!0),zs(t),1024<this.a.f-this.i&&t.postRenderFunctions.push(this.Nj.bind(this)),0!==this.j.b.length&&(t.postRenderFunctions.push(this.D),t.animate=!0),ou(this,"postcompose",t),Zs(this,t),t.postRenderFunctions.push(Hs)},d.Ba=function(t,e,o,i,r,n,s){if(this.g.isContextLost())return!1;for(var p=e.viewState,a=e.layerStatesArray,h=a.length-1;0<=h;--h){var l,u=(l=a[h]).layer;if(js(l,p.resolution)&&n.call(s,u)&&(l=Ys(this,u).Ba(t,e,o,i,r)))return l}},d.Ch=function(t,e,o,i,r){if(o=!1,this.g.isContextLost())return!1;for(var n=e.viewState,s=e.layerStatesArray,p=s.length-1;0<=p;--p){var a=s[p],h=a.layer;if(js(a,n.resolution)&&i.call(r,h)&&(o=Ys(this,h).te(t,e)))return!0}return o},d.Bh=function(t,e,o,i,r){if(this.g.isContextLost())return!1;for(var n=e.viewState,s=e.layerStatesArray,p=s.length-1;0<=p;--p){var a,h=(a=s[p]).layer;if(js(a,n.resolution)&&r.call(i,h)&&(a=Ys(this,h).Hf(t,e,o,i)))return a}};var ru=["canvas","webgl"];function a(t){wt.call(this);var e=function(t){var e=null,o=(void 0!==t.keyboardEventTarget&&(e="string"==typeof t.keyboardEventTarget?document.getElementById(t.keyboardEventTarget):t.keyboardEventTarget),{}),i={};void 0===t.logo||"boolean"==typeof t.logo&&t.logo?i["data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAA3NCSVQICAjb4U/gAAAACXBIWXMAAAHGAAABxgEXwfpGAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAAhNQTFRF////AP//AICAgP//AFVVQECA////K1VVSbbbYL/fJ05idsTYJFtbbcjbJllmZszWWMTOIFhoHlNiZszTa9DdUcHNHlNlV8XRIVdiasrUHlZjIVZjaMnVH1RlIFRkH1RkH1ZlasvYasvXVsPQH1VkacnVa8vWIVZjIFRjVMPQa8rXIVVkXsXRsNveIFVkIFZlIVVj3eDeh6GmbMvXH1ZkIFRka8rWbMvXIFVkIFVjIFVkbMvWH1VjbMvWIFVlbcvWIFVla8vVIFVkbMvWbMvVH1VkbMvWIFVlbcvWIFVkbcvVbMvWjNPbIFVkU8LPwMzNIFVkbczWIFVkbsvWbMvXIFVkRnB8bcvW2+TkW8XRIFVkIlZlJVloJlpoKlxrLl9tMmJwOWd0Omh1RXF8TneCT3iDUHiDU8LPVMLPVcLPVcPQVsPPVsPQV8PQWMTQWsTQW8TQXMXSXsXRX4SNX8bSYMfTYcfTYsfTY8jUZcfSZsnUaIqTacrVasrVa8jTa8rWbI2VbMvWbcvWdJObdcvUdszUd8vVeJaee87Yfc3WgJyjhqGnitDYjaarldPZnrK2oNbborW5o9bbo9fbpLa6q9ndrL3ArtndscDDutzfu8fJwN7gwt7gxc/QyuHhy+HizeHi0NfX0+Pj19zb1+Tj2uXk29/e3uLg3+Lh3+bl4uXj4ufl4+fl5Ofl5ufl5ujm5+jmySDnBAAAAFp0Uk5TAAECAgMEBAYHCA0NDg4UGRogIiMmKSssLzU7PkJJT1JTVFliY2hrdHZ3foSFhYeJjY2QkpugqbG1tre5w8zQ09XY3uXn6+zx8vT09vf4+Pj5+fr6/P39/f3+gz7SsAAAAVVJREFUOMtjYKA7EBDnwCPLrObS1BRiLoJLnte6CQy8FLHLCzs2QUG4FjZ5GbcmBDDjxJBXDWxCBrb8aM4zbkIDzpLYnAcE9VXlJSWlZRU13koIeW57mGx5XjoMZEUqwxWYQaQbSzLSkYGfKFSe0QMsX5WbjgY0YS4MBplemI4BdGBW+DQ11eZiymfqQuXZIjqwyadPNoSZ4L+0FVM6e+oGI6g8a9iKNT3o8kVzNkzRg5lgl7p4wyRUL9Yt2jAxVh6mQCogae6GmflI8p0r13VFWTHBQ0rWPW7ahgWVcPm+9cuLoyy4kCJDzCm6d8PSFoh0zvQNC5OjDJhQopPPJqph1doJBUD5tnkbZiUEqaCnB3bTqLTFG1bPn71kw4b+GFdpLElKIzRxxgYgWNYc5SCENVHKeUaltHdXx0dZ8uBI1hJ2UUDgq82CM2MwKeibqAvSO7MCABq0wXEPiqWEAAAAAElFTkSuQmCC"]="https://openlayers.org/":"string"==typeof(p=t.logo)?i[p]="":p instanceof HTMLElement?i[B(p).toString()]=p:p&&(V("string"==typeof p.href,44),V("string"==typeof p.src,45),i[p.src]=p.href);p=t.layers instanceof ys?t.layers:new ys({layers:t.layers}),o[nu]=p,o[pu]=t.target,o[au]=void 0!==t.view?t.view:new h;var r,n,s,p=Ws;for(void 0!==t.renderer?(Array.isArray(t.renderer)?r=t.renderer:"string"==typeof t.renderer?r=[t.renderer]:V(!1,46),0<=r.indexOf("dom")&&(r=r.concat(ru))):r=ru,n=0,s=r.length;n<s;++n){var a=r[n];if("canvas"==a){if(yr){p=Ea;break}}else if("webgl"==a&&nr){p=tu;break}}return r=void 0!==t.controls?Array.isArray(t.controls)?new di(t.controls.slice()):(V(t.controls instanceof di,47),t.controls):Zi(),n=void 0!==t.interactions?Array.isArray(t.interactions)?new di(t.interactions.slice()):(V(t.interactions instanceof di,48),t.interactions):rs(),t=void 0!==t.overlays?Array.isArray(t.overlays)?new di(t.overlays.slice()):(V(t.overlays instanceof di,49),t.overlays):new di,{controls:r,interactions:n,keyboardEventTarget:e,logos:i,overlays:t,ap:p,values:o}}(t);this.Xe=void 0!==t.loadTilesWhileAnimating&&t.loadTilesWhileAnimating,this.Ve=void 0!==t.loadTilesWhileInteracting&&t.loadTilesWhileInteracting,this.cf=void 0!==t.pixelRatio?t.pixelRatio:ur,this.bf=e.logos,this.sa=function(){this.i=void 0,this.Zo.call(this,Date.now())}.bind(this),this.Ob=Is(),this.df=Is(),this.fc=0,this.a=null,this.Ua=pe(),this.D=this.L=this.P=null,this.f=document.createElement("DIV"),this.f.className="ol-viewport"+(dr?" ol-touch":""),this.f.style.position="relative",this.f.style.overflow="hidden",this.f.style.width="100%",this.f.style.height="100%",this.f.style.msTouchAction="none",this.f.style.touchAction="none",this.A=document.createElement("DIV"),this.A.className="ol-overlaycontainer",this.f.appendChild(this.A),this.u=document.createElement("DIV"),this.u.className="ol-overlaycontainer-stopevent";for(var o,i=0,r=(t="click dblclick mousedown touchstart mspointerdown pointerdown mousewheel wheel".split(" ")).length;i<r;++i)c(this.u,t[i],gt);for(o in this.f.appendChild(this.u),this.Fa=new Xr(this),er)c(this.Fa,er[o],this.Zg,this);this.na=e.keyboardEventTarget,this.v=null,c(this.f,"wheel",this.ad,this),c(this.f,"mousewheel",this.ad,this),this.l=e.controls,this.j=e.interactions,this.o=e.overlays,this.Mf={},this.C=new e.ap(this.f,this),this.U=null,this.Z=[],this.Ja=[],this.xa=new $r(this.Gk.bind(this),this.kl.bind(this)),this.fa={},c(this,St(nu),this.Tk,this),c(this,St(au),this.ll,this),c(this,St(su),this.hl,this),c(this,St(pu),this.jl,this),this.I(e.values),this.l.forEach(function(t){t.setMap(this)},this),c(this.l,mi,function(t){t.element.setMap(this)},this),c(this.l,wi,function(t){t.element.setMap(null)},this),this.j.forEach(function(t){t.setMap(this)},this),c(this.j,mi,function(t){t.element.setMap(this)},this),c(this.j,wi,function(t){t.element.setMap(null)},this),this.o.forEach(this.wg,this),c(this.o,mi,function(t){this.wg(t.element)},this),c(this.o,wi,function(t){var e=t.element.i;void 0!==e&&delete this.Mf[e.toString()],t.element.setMap(null)},this)}e(a,wt),(d=a.prototype).Bj=function(t){this.l.push(t)},d.Cj=function(t){this.j.push(t)},d.ug=function(t){this.Bc().cd().push(t)},d.vg=function(t){this.o.push(t)},d.wg=function(t){var e=t.i;void 0!==e&&(this.Mf[e.toString()]=t),t.setMap(this)},d.Ij=function(t){this.render(),Array.prototype.push.apply(this.Z,arguments)},d.oa=function(){yt(this.Fa),yt(this.C),ht(this.f,"wheel",this.ad,this),ht(this.f,"mousewheel",this.ad,this),void 0!==this.c&&(window.removeEventListener("resize",this.c,!1),this.c=void 0),this.i&&(cancelAnimationFrame(this.i),this.i=void 0),this.ph(null),wt.prototype.oa.call(this)},d.ae=function(t,e,o){if(this.a)return t=this.Sa(t),this.C.Ba(t,this.a,void 0!==(o=void 0!==o?o:{}).hitTolerance?o.hitTolerance*this.a.pixelRatio:0,e,null,void 0!==o.layerFilter?o.layerFilter:Le,null)},d.Wl=function(t,e,o,i,r){if(this.a)return this.C.Bh(t,this.a,e,void 0!==o?o:null,void 0!==i?i:Le,void 0!==r?r:null)},d.ml=function(t,e){return!!this.a&&(t=this.Sa(t),this.C.Ch(t,this.a,void 0!==(e=void 0!==e?e:{}).hitTolerance?e.hitTolerance*this.a.pixelRatio:0,void 0!==e.layerFilter?e.layerFilter:Le,null))},d.ck=function(t){return this.Sa(this.ce(t))},d.ce=function(t){var e=this.f.getBoundingClientRect();return[(t=t.changedTouches?t.changedTouches[0]:t).clientX-e.left,t.clientY-e.top]},d.vf=function(){return this.get(pu)},d.Cc=function(){var t=this.vf();return void 0!==t?"string"==typeof t?document.getElementById(t):t:null},d.Sa=function(t){var e=this.a;return e?Bs(e.pixelToCoordinateTransform,t.slice()):null},d.ak=function(){return this.l},d.vk=function(){return this.o},d.uk=function(t){return void 0!==(t=this.Mf[t.toString()])?t:null},d.hk=function(){return this.j},d.Bc=function(){return this.get(nu)},d.oh=function(){return this.Bc().cd()},d.Ga=function(t){var e=this.a;return e?Bs(e.coordinateToPixelTransform,t.slice(0,2)):null},d.nb=function(){return this.get(su)},d.aa=function(){return this.get(au)},d.Ik=function(){return this.f},d.Gk=function(t,e,o,i){var r=this.a;return r&&e in r.wantedTiles&&r.wantedTiles[e][t.bb()]?(t=o[0]-r.focus[0],o=o[1]-r.focus[1],65536*Math.log(i)+Math.sqrt(t*t+o*o)/i):1/0},d.ad=function(t,e){e=new tr(e||t.type,this,t);this.Zg(e)},d.Zg=function(t){if(this.a){this.U=t.coordinate,t.frameState=this.a;var e,o=this.j.a;if(!1!==this.b(t))for(e=o.length-1;0<=e;e--){var i=o[e];if(i.f()&&!i.handleEvent(t))break}}},d.fl=function(){var t,e,o,i=this.a,r=this.xa;for(0!==r.b.length&&(e=t=16,i&&((o=i.viewHints)[qo]&&(t=this.Xe?8:0,e=2),o[1])&&(t=this.Ve?8:0,e=2),r.j<t)&&(_r(r),Qr(r,t,e)),t=0,e=(r=this.Ja).length;t<e;++t)r[t](this,i);r.length=0},d.hl=function(){this.render()},d.jl=function(){var t;if(this.vf()&&(t=this.Cc()),this.v){for(var e=0,o=this.v.length;e<o;++e)lt(this.v[e]);this.v=null}t?(t.appendChild(this.f),t=this.na||t,this.v=[c(t,"keydown",this.ad,this),c(t,"keypress",this.ad,this)],this.c||(this.c=this.ld.bind(this),window.addEventListener("resize",this.c,!1))):(Ii(this.f),void 0!==this.c&&(window.removeEventListener("resize",this.c,!1),this.c=void 0)),this.ld()},d.kl=function(){this.render()},d.bh=function(){this.render()},d.ll=function(){this.P&&(lt(this.P),this.P=null),this.L&&(lt(this.L),this.L=null);var t=this.aa();t&&(this.P=c(t,Pt,this.bh,this),this.L=c(t,"change",this.bh,this)),this.render()},d.Tk=function(){this.D&&(this.D.forEach(lt),this.D=null);var t=this.Bc();t&&(this.D=[c(t,Pt,this.render,this),c(t,"change",this.render,this)]),this.render()},d.$o=function(){this.i&&cancelAnimationFrame(this.i),this.sa()},d.render=function(){void 0===this.i&&(this.i=requestAnimationFrame(this.sa))},d.To=function(t){return this.l.remove(t)},d.Uo=function(t){return this.j.remove(t)},d.Wo=function(t){return this.Bc().cd().remove(t)},d.Xo=function(t){return this.o.remove(t)},d.Zo=function(t){var e,o=this.nb(),i=this.aa(),r=pe(),n=null;if(void 0!==o&&0<o[0]&&0<o[1]&&i&&Wo(i)){for(var n=Xo(i,this.a?this.a.viewHints:void 0),s=this.Bc().sf(),p={},a=0,h=s.length;a<h;++a)p[B(s[a].layer)]=s[a];e=i.W(),n={animate:!1,attributions:{},coordinateToPixelTransform:this.Ob,extent:r,focus:this.U||e.center,index:this.fc++,layerStates:p,layerStatesArray:s,logos:Q({},this.bf),pixelRatio:this.cf,pixelToCoordinateTransform:this.df,postRenderFunctions:[],size:o,skippedFeatureUids:this.fa,tileQueue:this.xa,time:t,usedTiles:{},viewState:e,viewHints:n,wantedTiles:{}}}if(n){for(a=o=0,h=(t=this.Z).length;a<h;++a)(i=t[a])(this,n)&&(t[o++]=i);t.length=o,n.extent=we(e.center,e.resolution,e.rotation,n.size,r)}this.a=n,this.C.ag(n),n&&(n.animate&&this.render(),Array.prototype.push.apply(this.Ja,n.postRenderFunctions),0!==this.Z.length||n.viewHints[qo]||n.viewHints[1]||ue(n.extent,this.Ua)||(this.b(new Fi("moveend",this,n)),ee(n.extent,this.Ua))),this.b(new Fi("postrender",this,n)),setTimeout(this.fl.bind(this),0)},d.vi=function(t){this.set(nu,t)},d.eg=function(t){this.set(su,t)},d.ph=function(t){this.set(pu,t)},d.mp=function(t){this.set(au,t)},d.Di=function(t){t=B(t).toString(),this.fa[t]=!0,this.render()},d.ld=function(){var t,e=this.Cc();e?(t=getComputedStyle(e),this.eg([e.offsetWidth-parseFloat(t.borderLeftWidth)-parseFloat(t.paddingLeft)-parseFloat(t.paddingRight)-parseFloat(t.borderRightWidth),e.offsetHeight-parseFloat(t.borderTopWidth)-parseFloat(t.paddingTop)-parseFloat(t.paddingBottom)-parseFloat(t.borderBottomWidth)])):this.eg(void 0)},d.Ji=function(t){t=B(t).toString(),delete this.fa[t],this.render()};var nu="layergroup",su="size",pu="target",au="view";function hu(t){wt.call(this),this.i=t.id,this.o=void 0===t.insertFirst||t.insertFirst,this.v=void 0===t.stopEvent||t.stopEvent,this.f=document.createElement("DIV"),this.f.className="ol-overlay-container",this.f.style.position="absolute",this.autoPan=void 0!==t.autoPan&&t.autoPan,this.j=t.autoPanAnimation||{},this.l=void 0!==t.autoPanMargin?t.autoPanMargin:20,this.a={Xd:"",le:"",Je:"",Oe:"",visible:!0},this.c=null,c(this,St(Su),this.Ok,this),c(this,St(Mu),this.Yk,this),c(this,St(Pu),this.bl,this),c(this,St(Tu),this.dl,this),c(this,St(Au),this.el,this),void 0!==t.element&&this.pi(t.element),this.xi(void 0!==t.offset?t.offset:[0,0]),this.Ai(void 0!==t.positioning?t.positioning:mu),void 0!==t.position&&this.Ef(t.position)}function lu(t,e){var t=t.getBoundingClientRect(),o=t.left+window.pageXOffset;return[o,t=t.top+window.pageYOffset,o+e[0],t+e[1]]}function uu(t,e){t.a.visible!==e&&(t.f.style.display=e?"":"none",t.a.visible=e)}function cu(t){var e,o,i,r,n=t.oe(),s=t.qh();void 0!==n&&n.a&&void 0!==s?(s=n.Ga(s),e=n.nb(),n=t.f.style,r=t.Tg(),o=t.Ug(),i=r[0],r=r[1],o==gu||o==bu||o==xu?(""!==t.a.le&&(t.a.le=n.left=""),i=Math.round(e[0]-s[0]-i)+"px",t.a.Je!=i&&(t.a.Je=n.right=i)):(""!==t.a.Je&&(t.a.Je=n.right=""),o!=fu&&o!=vu&&o!=wu||(i-=t.f.offsetWidth/2),i=Math.round(s[0]+i)+"px",t.a.le!=i&&(t.a.le=n.left=i)),o==yu||o==fu||o==gu?(""!==t.a.Oe&&(t.a.Oe=n.top=""),s=Math.round(e[1]-s[1]-r)+"px",t.a.Xd!=s&&(t.a.Xd=n.bottom=s)):(""!==t.a.Xd&&(t.a.Xd=n.bottom=""),o!=du&&o!=vu&&o!=bu||(r-=t.f.offsetHeight/2),s=Math.round(s[1]+r)+"px",t.a.Oe!=s&&(t.a.Oe=n.top=s)),uu(t,!0)):uu(t,!1)}Es(),e(hu,wt),(d=hu.prototype).be=function(){return this.get(Su)},d.Xl=function(){return this.i},d.oe=function(){return this.get(Mu)},d.Tg=function(){return this.get(Pu)},d.qh=function(){return this.get(Tu)},d.Ug=function(){return this.get(Au)},d.Ok=function(){for(var t=this.f;t.lastChild;)t.removeChild(t.lastChild);(t=this.be())&&this.f.appendChild(t)},d.Yk=function(){this.c&&(Ii(this.f),lt(this.c),this.c=null);var t=this.oe();t&&(this.c=c(t,"postrender",this.render,this),cu(this),t=this.v?t.u:t.A,this.o?t.insertBefore(this.f,t.childNodes[0]||null):t.appendChild(this.f))},d.render=function(){cu(this)},d.bl=function(){cu(this)},d.dl=function(){var t,e,o,i,r,n,s;cu(this),void 0!==this.get(Tu)&&this.autoPan&&void 0!==(t=this.oe())&&t.Cc()&&(e=lu(t.Cc(),t.nb()),o=(s=this.be()).offsetWidth,r=s.currentStyle||getComputedStyle(s),o+=parseInt(r.marginLeft,10)+parseInt(r.marginRight,10),r=s.offsetHeight,i=s.currentStyle||getComputedStyle(s),n=lu(s,[o,r=r+(parseInt(i.marginTop,10)+parseInt(i.marginBottom,10))]),s=this.l,re(e,n)||(o=n[0]-e[0],r=e[2]-n[2],i=n[1]-e[1],n=e[3]-n[3],e=[0,0],o<0?e[0]=o-s:r<0&&(e[0]=Math.abs(r)+s),i<0?e[1]=i-s:n<0&&(e[1]=Math.abs(n)+s),0===e[0]&&0===e[1])||(s=t.aa().fb(),e=[(s=t.Ga(s))[0]+e[0],s[1]+e[1]],t.aa().animate({center:t.Sa(e),duration:this.j.duration,easing:this.j.easing})))},d.el=function(){cu(this)},d.pi=function(t){this.set(Su,t)},d.setMap=function(t){this.set(Mu,t)},d.xi=function(t){this.set(Pu,t)},d.Ef=function(t){this.set(Tu,t)},d.Ai=function(t){this.set(Au,t)};var yu="bottom-left",fu="bottom-center",gu="bottom-right",du="center-left",vu="center-center",bu="center-right",mu="top-left",wu="top-center",xu="top-right",Su="element",Mu="map",Pu="offset",Tu="position",Au="positioning";function Eu(t){this.j=void 0===(t=t||{}).collapsed||t.collapsed,this.l=void 0===t.collapsible||t.collapsible,this.l||(this.j=!1);var e=void 0!==t.className?t.className:"ol-overviewmap",o=void 0!==t.tipLabel?t.tipLabel:"Overview map",i=void 0!==t.collapseLabel?t.collapseLabel:"«",r=("string"==typeof i?(this.o=document.createElement("span"),this.o.textContent=i):this.o=i,"string"==typeof(i=void 0!==t.label?t.label:"»")?(this.u=document.createElement("span"),this.u.textContent=i):this.u=i,this.l&&!this.j?this.o:this.u),n=((i=document.createElement("button")).setAttribute("type","button"),i.title=o,i.appendChild(r),c(i,"click",this.km,this),(o=document.createElement("DIV")).className="ol-overviewmap-map",this.f=new a({controls:new di,interactions:new di,target:o,view:t.view}));t.layers&&t.layers.forEach(function(t){n.ug(t)},this),(r=document.createElement("DIV")).className="ol-overviewmap-box",r.style.boxSizing="border-box",this.A=new hu({position:[0,0],positioning:yu,element:r}),this.f.vg(this.A),(r=document.createElement("div")).className=e+" ol-unselectable ol-control"+(this.j&&this.l?" ol-collapsed":"")+(this.l?"":" ol-uncollapsible"),r.appendChild(o),r.appendChild(i),ki.call(this,{element:r,render:t.render||Cu,target:t.target})}function Cu(){var t,e,o,i,r=this.a,n=this.f;r.a&&n.a&&(t=r.nb(),r=r.aa().Uc(t),i=n.nb(),t=n.aa().Uc(i),e=n.Ga(Me(r)),o=n.Ga(be(r)),n=Math.abs(e[0]-o[0]),e=Math.abs(e[1]-o[1]),o=i[0],i=i[1],n<.1*o||e<.1*i||.75*o<n||.75*i<e?ju(this):re(t,r)||(r=this.f,t=this.a.aa(),r.aa().Mb(t.fb()))),Lu(this)}function ju(t){var e=t.a,o=(t=t.f,e.nb()),e=e.aa().Uc(o),o=t.nb();t=t.aa(),Ce(e,1/(.1*Math.pow(2,Math.log(7.5)/Math.LN2/2))),t.lf(e,o)}function Lu(t){var e,o,i,r,n,s,p=t.a,a=t.f;p.a&&a.a&&(i=p.nb(),r=p.aa(),n=a.aa(),a=r.Ra(),p=t.A,e=t.A.be(),o=r.Uc(i),i=n.Oa(),r=ve(o),n=Pe(o),(t=t.a.aa().fb())&&(Vt(s=[r[0]-t[0],r[1]-t[1]],a),Bt(s,t)),p.Ef(s),e)&&(e.style.width=Math.abs((r[0]-n[0])/i)+"px",e.style.height=Math.abs((n[1]-r[1])/i)+"px")}function Ru(t){t.element.classList.toggle("ol-collapsed"),t.j?Ni(t.o,t.u):Ni(t.u,t.o),t.j=!t.j;var e=t.f;t.j||e.a||(e.ld(),ju(t),at(e,"postrender",function(){Lu(this)},t))}function Nu(t){var e=void 0!==(t=t||{}).className?t.className:"ol-scale-line";this.l=document.createElement("DIV"),this.l.className=e+"-inner",this.f=document.createElement("DIV"),this.f.className=e+" ol-unselectable",this.f.appendChild(this.l),this.u=null,this.o=void 0!==t.minWidth?t.minWidth:64,this.j=!1,this.C=void 0,this.A="",ki.call(this,{element:this.f,render:t.render||Fu,target:t.target}),c(this,St(Ou),this.L,this),this.D(t.units||Gu)}e(Eu,ki),(d=Eu.prototype).setMap=function(t){var e=this.a;t!==e&&((e=e&&e.aa())&&ht(e,St(Zo),this.je,this),ki.prototype.setMap.call(this,t),t)&&(this.v.push(c(t,Pt,this.Zk,this)),0===this.f.oh().Ub()&&this.f.vi(t.Bc()),t=t.aa())&&(c(t,St(Zo),this.je,this),Wo(t)&&(this.f.ld(),ju(this)))},d.Zk=function(t){t.key===au&&((t=t.oldValue)&&ht(t,St(Zo),this.je,this),c(t=this.a.aa(),St(Zo),this.je,this))},d.je=function(){this.f.aa().pe(this.a.aa().Ra())},d.km=function(t){t.preventDefault(),Ru(this)},d.jm=function(){return this.l},d.mm=function(t){this.l!==t&&(this.l=t,this.element.classList.toggle("ol-uncollapsible"),!t)&&this.j&&Ru(this)},d.lm=function(t){this.l&&this.j!==t&&Ru(this)},d.im=function(){return this.j},d.wk=function(){return this.f},e(Nu,ki);var Iu=[1,2,5];function Fu(t){(t=t.frameState)?this.u=t.viewState:this.u=null,ku(this)}function ku(t){if(o=t.u){var e=(i=o.projection).ic(),o=Ke(i,o.resolution,o.center)*e,e=t.o*o,i="";(n=t.Eb())==Du?(o/=i=Fe.degrees,e<i/60?(i="″",o*=3600):e<i?(i="′",o*=60):i="°"):n==Bu?e<.9144?(i="in",o/=.0254):e<1609.344?(i="ft",o/=.3048):(i="mi",o/=1609.344):n==Uu?(o/=1852,i="nm"):n==Gu?e<1?(i="mm",o*=1e3):e<1e3?i="m":(i="km",o/=1e3):n==Ku?e<.9144?(i="in",o*=39.37):e<1609.344?(i="ft",o/=.30480061):(i="mi",o/=1609.3472):V(!1,33);for(var r,n=3*Math.floor(Math.log(t.o*o)/Math.log(10));;){if(r=Iu[(n%3+3)%3]*Math.pow(10,Math.floor(n/3)),e=Math.round(r/o),isNaN(e))return t.f.style.display="none",void(t.j=!1);if(e>=t.o)break;++n}t.A!=(o=r+" "+i)&&(t.l.innerHTML=o,t.A=o),t.C!=e&&(t.l.style.width=e+"px",t.C=e),t.j||(t.f.style.display="",t.j=!0)}else t.j&&(t.f.style.display="none",t.j=!1)}Nu.prototype.Eb=function(){return this.get(Ou)},Nu.prototype.L=function(){ku(this)},Nu.prototype.D=function(t){this.set(Ou,t)};var Ou="units",Du="degrees",Bu="imperial",Uu="nautical",Gu="metric",Ku="us";function Xu(t){t=t||{},this.f=void 0,this.j=Vu,this.u=[],this.C=this.o=0,this.U=null,this.fa=!1,this.Z=void 0!==t.duration?t.duration:200;var e=void 0!==t.className?t.className:"ol-zoomslider",o=document.createElement("button"),i=(o.setAttribute("type","button"),o.className=e+"-thumb ol-unselectable",document.createElement("div"));i.className=e+" ol-unselectable ol-control",i.appendChild(o),this.l=new Ir(i),c(this.l,"pointerdown",this.Nk,this),c(this.l,"pointermove",this.Xg,this),c(this.l,"pointerup",this.Yg,this),c(i,"click",this.Mk,this),c(o,"click",gt),ki.call(this,{element:i,render:t.render||Wu})}e(Xu,ki),Xu.prototype.oa=function(){yt(this.l),ki.prototype.oa.call(this)};var Vu=0;function Wu(t){var e,o,i,r,n;t.frameState&&(this.fa||(e=(r=this.element).offsetWidth,o=r.offsetHeight,n=r.firstElementChild,i=getComputedStyle(n),r=n.offsetWidth+parseFloat(i.marginRight)+parseFloat(i.marginLeft),n=n.offsetHeight+parseFloat(i.marginTop)+parseFloat(i.marginBottom),this.U=[r,n],o<e?(this.j=1,this.C=e-r):(this.j=Vu,this.o=o-n),this.fa=!0),(t=t.frameState.viewState.resolution)!==this.f)&&(this.f=t,zu(this,t))}function zu(t,e){var o,i,r=1-(r=t.a.aa(),o=r.a,i=Math.log(o/r.j)/Math.log(2),function(t){return Math.log(o/t)/Math.log(2)/i}(e)),e=t.element.firstElementChild;1==t.j?e.style.left=t.C*r+"px":e.style.top=t.o*r+"px"}function Hu(t,e){t=t.a.aa(),o=t.a,t=Math.log(o/t.j)/Math.log(2);var o,e=1-e;return o/Math.pow(2,e*t)}function Yu(t){this.f=(t=t||{}).extent||null;var e=void 0!==t.className?t.className:"ol-zoom-extent",o=void 0!==t.label?t.label:"E",i=void 0!==t.tipLabel?t.tipLabel:"Fit to extent",r=document.createElement("button");r.setAttribute("type","button"),r.title=i,r.appendChild("string"==typeof o?document.createTextNode(o):o),c(r,"click",this.j,this),(o=document.createElement("div")).className=e+" ol-unselectable ol-control",o.appendChild(r),ki.call(this,{element:o,target:t.target})}function Zu(t){wt.call(this),t=t||{},this.a=null,c(this,St(Qu),this.Kl,this),this.Cf(void 0!==t.tracking&&t.tracking)}(d=Xu.prototype).setMap=function(t){ki.prototype.setMap.call(this,t),t&&t.render()},d.Mk=function(t){var e=this.a.aa();t=Hu(this,W(1===this.j?(t.offsetX-this.U[0]/2)/this.C:(t.offsetY-this.U[1]/2)/this.o,0,1)),e.animate({resolution:e.constrainResolution(t),duration:this.Z,easing:qt})},d.Nk=function(t){var e;this.A||t.b.target!==this.element.firstElementChild||(zo(this.a.aa(),1,1),this.D=t.clientX,this.L=t.clientY,this.A=!0,0!==this.u.length)||(t=this.Xg,e=this.Yg,this.u.push(c(document,"mousemove",t,this),c(document,"touchmove",t,this),c(document,"pointermove",t,this),c(document,"mouseup",e,this),c(document,"touchend",e,this),c(document,"pointerup",e,this)))},d.Xg=function(t){var e;this.A&&(e=this.element.firstElementChild,this.f=Hu(this,W(1===this.j?(t.clientX-this.D+parseInt(e.style.left,10))/this.C:(t.clientY-this.L+parseInt(e.style.top,10))/this.o,0,1)),this.a.aa().Oc(this.f),zu(this,this.f),this.D=t.clientX,this.L=t.clientY)},d.Yg=function(){var t;this.A&&(zo(t=this.a.aa(),1,-1),t.animate({resolution:t.constrainResolution(this.f),duration:this.Z,easing:qt}),this.A=!1,this.L=this.D=void 0,this.u.forEach(lt),this.u.length=0)},e(Yu,ki),Yu.prototype.j=function(t){t.preventDefault();t=(o=this.a).aa();var e=this.f||t.o.G(),o=o.nb();t.lf(e,o)},e(Zu,wt),(d=Zu.prototype).oa=function(){this.Cf(!1),wt.prototype.oa.call(this)},d.io=function(t){var e;null!==t.alpha&&(e=Z(t.alpha),this.set(qu,e),"boolean"==typeof t.absolute&&t.absolute?this.set($u,e):"number"==typeof t.webkitCompassHeading&&-1!=t.webkitCompassAccuracy&&this.set($u,Z(t.webkitCompassHeading))),null!==t.beta&&this.set(Ju,Z(t.beta)),null!==t.gamma&&this.set(_u,Z(t.gamma)),this.s()},d.Vj=function(){return this.get(qu)},d.Yj=function(){return this.get(Ju)},d.ek=function(){return this.get(_u)},d.Jl=function(){return this.get($u)},d.kh=function(){return this.get(Qu)},d.Kl=function(){var t;fr&&((t=this.kh())&&!this.a?this.a=c(window,"deviceorientation",this.io,this):t||null===this.a||(lt(this.a),this.a=null))},d.Cf=function(t){this.set(Qu,t)};var qu="alpha",Ju="beta",_u="gamma",$u="heading",Qu="tracking";function x(t){wt.call(this),this.f=void 0,this.a="geometry",this.i=null,this.j=void 0,this.c=null,c(this,St(this.a),this.he,this),void 0!==t&&(t instanceof eo||!t?this.Pa(t):this.I(t))}e(x,wt),(d=x.prototype).clone=function(){var t=new x(this.R()),e=(t.Nc(this.a),this.V());return e&&t.Pa(e.clone()),(e=this.i)&&t.Df(e),t},d.V=function(){return this.get(this.a)},d.Ll=function(){return this.f},d.gk=function(){return this.a},d.Ml=function(){return this.i},d.Gc=function(){return this.j},d.Nl=function(){this.s()},d.he=function(){this.c&&(lt(this.c),this.c=null);var t=this.V();t&&(this.c=c(t,"change",this.Nl,this)),this.s()},d.Pa=function(t){this.set(this.a,t)},d.Df=function(t){this.j=(this.i=t)?function(t){{var e;"function"!=typeof t&&(e=Array.isArray(t)?t:(V(t instanceof gp,41),[t]),t=function(){return e})}return t}(t):void 0,this.s()},d.cc=function(t){this.f=t,this.s()},d.Nc=function(t){ht(this,St(this.a),this.he,this),this.a=t,c(this,St(this.a),this.he,this),this.he()};var tc=document.implementation.createDocument("","",null);function ec(t,e){return tc.createElementNS(t,e)}function oc(t,e){return function t(e,o,i){if(e.nodeType==Node.CDATA_SECTION_NODE||e.nodeType==Node.TEXT_NODE)o?i.push(String(e.nodeValue).replace(/(\r\n|\r|\n)/g,"")):i.push(e.nodeValue);else for(e=e.firstChild;e;e=e.nextSibling)t(e,o,i);return i}(t,e,[]).join("")}function ic(t){return t instanceof Document}function rc(t){return t instanceof Node}function nc(t){return(new DOMParser).parseFromString(t,"application/xml")}function sc(o,i){return function(t,e){t=o.call(i,t,e);void 0!==t&&jt(e[e.length-1],t)}}function pc(o,i){return function(t,e){t=o.call(void 0!==i?i:this,t,e);void 0!==t&&e[e.length-1].push(t)}}function ac(o,i){return function(t,e){t=o.call(void 0!==i?i:this,t,e);void 0!==t&&(e[e.length-1]=t)}}function hc(i){return function(t,e){var o=i.call(this,t,e);void 0!==o&&(e=e[e.length-1],((t=t.localName)in e?e[t]:e[t]=[]).push(o))}}function r(i,r){return function(t,e){var o=i.call(this,t,e);void 0!==o&&(e[e.length-1][void 0!==r?r:t.localName]=o)}}function l(i,r){return function(t,e,o){i.call(void 0!==r?r:this,t,e,o),o[o.length-1].node.appendChild(t)}}function lc(r){var n,s;return function(t,e,o){var i;n||(n={},(i={})[t.localName]=r,n[t.namespaceURI]=i,s=uc(t.localName)),gc(n,s,e,o)}}function uc(i,r){return function(t,e,o){return t=e[e.length-1].node,void 0===(e=i)&&(e=o),ec(o=void 0===(o=r)?t.namespaceURI:r,e)}}var cc=uc();function yc(t,e){for(var o=e.length,i=Array(o),r=0;r<o;++r)i[r]=t[e[r]];return i}function u(t,e,o){var i,r;for(o=void 0!==o?o:{},i=0,r=t.length;i<r;++i)o[t[i]]=e;return o}function fc(t,e,o,i){for(e=e.firstElementChild;e;e=e.nextElementSibling){var r=t[e.namespaceURI];void 0!==r&&(r=r[e.localName])&&r.call(i,e,o)}}function g(t,e,o,i,r){return i.push(t),fc(e,o,i,r),i.pop()}function gc(t,e,o,i,r,n){for(var s,p,a=(void 0!==r?r:o).length,h=0;h<a;++h)void 0!==(s=o[h])&&void 0!==(p=e.call(n,s,i,void 0!==r?r[h]:void 0))&&t[p.namespaceURI][p.localName].call(n,p,s,i)}function dc(t,e,o,i,r,n,s){r.push(t),gc(e,o,i,r,n,s),r.pop()}function vc(r,n,s,p){return function(t,e,o){var i=new XMLHttpRequest;i.open("GET","function"==typeof r?r(t,e,o):r,!0),"arraybuffer"==n.Y()&&(i.responseType="arraybuffer"),i.onload=function(){var t,e;(!i.status||200<=i.status&&i.status<300)&&("json"==(t=n.Y())||"text"==t?e=i.responseText:"xml"==t?e=(e=i.responseXML)||nc(i.responseText):"arraybuffer"==t&&(e=i.response),e)?s.call(this,n.La(e,{featureProjection:o}),n.Wa(e)):p.call(this)}.bind(this),i.send()}}function bc(t,e){return vc(t,e,function(t){this.Tc(t)},G)}function mc(){this.j=this.defaultDataProjection=null}function wc(t,e,o){var i;return o&&(i={dataProjection:o.dataProjection||t.Wa(e),featureProjection:o.featureProjection}),xc(t,i)}function xc(t,e){return Q({dataProjection:t.defaultDataProjection,featureProjection:t.j},e)}function Sc(t,e,o){var i,r=o?Ye(o.featureProjection):null,n=o?Ye(o.dataProjection):null,n=r&&n&&!Ze(r,n)?t instanceof eo?(e?t.clone():t).ob(e?r:n,e?n:r):to(e?t.slice():t,e?r:n,e?n:r):t;return e&&o&&o.decimals&&(i=Math.pow(10,o.decimals),t=function(t){for(var e=0,o=t.length;e<o;++e)t[e]=Math.round(t[e]*i)/i;return t},Array.isArray(n)?t(n):n.sc(t)),n}function Mc(){mc.call(this)}function Pc(t){return"string"==typeof t?(t=JSON.parse(t))||null:null!==t?t:null}function Tc(t,e,o,i,r,n){var s=NaN,p=NaN;if(0!==(a=(o-e)/i))if(1==a)s=t[e],p=t[e+1];else if(2==a)s=(1-r)*t[e]+r*t[e+i],p=(1-r)*t[e+1]+r*t[e+i+1];else{for(var p=t[e],a=t[e+1],h=0,s=[0],l=e+i;l<o;l+=i){var u=t[l],c=t[l+1],h=h+Math.sqrt((u-p)*(u-p)+(c-a)*(c-a));s.push(h),p=u,a=c}for(o=r*h,a=0,h=s.length,l=!1;a<h;)(p=+At(s[r=a+(h-a>>1)],o))<0?a=r+1:(h=r,l=!p);p=(r=l?a:~a)<0?(o=(o-s[-r-2])/(s[-r-1]-s[-r-2]),s=J(t[e+=(-r-2)*i],t[e+i],o),J(t[e+1],t[e+i+1],o)):(s=t[e+r*i],t[e+r*i+1])}return n?(n[0]=s,n[1]=p,n):[s,p]}function Ac(t,e,o,i,r,n){if(o==e)return null;if(r<t[e+i-1])return n?((o=t.slice(e,e+i))[i-1]=r,o):null;if(t[o-1]<r)return n?((o=t.slice(o-i,o))[i-1]=r,o):null;if(r==t[e+i-1])return t.slice(e,e+i);for(e/=i,o/=i;e<o;)r<t[((n=e+o>>1)+1)*i-1]?o=n:e=n+1;if(r==(o=t[e*i-1]))return t.slice((e-1)*i,(e-1)*i+i);var s;for(n=(r-o)/(t[(e+1)*i-1]-o),o=[],s=0;s<i-1;++s)o.push(J(t[(e-1)*i+s],t[e*i+s],n));return o.push(r),o}function S(t,e){b.call(this),this.c=null,this.A=this.C=this.l=-1,this.qa(t,e)}function Ec(t){return t.l!=t.g&&(t.c=t.Kg(.5,t.c),t.l=t.g),t.c}function M(t,e){b.call(this),this.c=[],this.l=this.A=-1,this.qa(t,e)}function Cc(t){var e,o,i=[],r=t.B,n=0,s=t.c;for(t=t.a,e=0,o=s.length;e<o;++e){var p=s[e];jt(i,n=Tc(r,n,p,t,.5)),n=p}return i}function jc(t,e){for(var o=t.ka,i=[],r=[],n=0,s=e.length;n<s;++n){var p=e[n];0===n&&(o=p.ka),jt(i,p.ia()),r.push(i.length)}t.da(o,i,r)}function P(t,e){b.call(this),this.qa(t,e)}function y(t,e){b.call(this),this.c=[],this.A=-1,this.C=null,this.P=this.D=this.L=-1,this.l=null,this.qa(t,e)}function Lc(t){if(t.A!=t.g){var e=t.B,o=t.c,i=t.a,r=0,n=[];for(h=0,a=o.length;h<a;++h){var s=o[h],r=le(e,r,s[0],i);n.push((r[0]+r[2])/2,(r[1]+r[3])/2),r=s[s.length-1]}for(e=Rc(t),o=t.c,i=t.a,a=[],s=h=0,r=o.length;s<r;++s)var p=o[s],a=Ao(e,h,p,i,n,2*s,a),h=p[p.length-1];t.C=a,t.A=t.g}return t.C}function Rc(t){if(t.P!=t.g){var e=t.B;t:{for(var o,i=0,r=(o=t.c).length;i<r;++i)if(!Ro(e,o[i],t.a,void 0)){o=!1;break t}o=!0}o?t.l=e:(t.l=e.slice(),t.l.length=Io(t.l,t.c,t.a)),t.P=t.g}return t.l}function Nc(t,e,o,i){no(t,e,o),t.c=i,t.s()}function Ic(t,e){for(var o=t.ka,i=[],r=[],n=0,s=e.length;n<s;++n){for(var p,a=e[n],h=(0===n&&(o=a.ka),i.length),l=0,u=(p=a.Kb()).length;l<u;++l)p[l]+=h;jt(i,a.ia()),r.push(p)}Nc(t,o,i,r)}function Fc(t){t=t||{},mc.call(this),this.b=t.geometryName}function kc(t,e){if(!t)return null;if("number"==typeof t.x&&"number"==typeof t.y)n="Point";else if(t.points)n="MultiPoint";else if(t.paths)n=1===t.paths.length?"LineString":"MultiLineString";else if(t.rings){for(var o=t.rings,i=Oc(t),r=[],n=[],s=0,p=o.length;s<p;++s){var a=function o(t){return t.reduce(function(t,e){return Array.isArray(e)?t.concat(o(e)):t.concat(e)},[])}(o[s]);Lo(a,0,a.length,i.length)?r.push([o[s]]):n.push(o[s])}for(;n.length;){for(o=n.shift(),i=!1,s=r.length-1;0<=s;s--)if(re(new So(r[s][0]).G(),new So(o).G())){r[s].push(o),i=!0;break}i||r.push([o.reverse()])}t=Q({},t),1===r.length?(n="Polygon",t.rings=r[0]):(n="MultiPolygon",t.rings=r)}return Sc((0,Bc[n])(t),!1,e)}function Oc(t){var e="XY";return!0===t.hasZ&&!0===t.hasM?e="XYZM":!0===t.hasZ?e="XYZ":!0===t.hasM&&(e="XYM"),e}function Dc(t){return{hasZ:"XYZ"===(t=t.ka)||"XYZM"===t,hasM:"XYM"===t||"XYZM"===t}}e(Mc,mc),(d=Mc.prototype).Y=function(){return"json"},d.bc=function(t,e){return this.gd(Pc(t),wc(this,t,e))},d.La=function(t,e){return this.Sf(Pc(t),wc(this,t,e))},d.hd=function(t,e){return this.ci(Pc(t),wc(this,t,e))},d.Wa=function(t){return this.ii(Pc(t))},d.Od=function(t,e){return JSON.stringify(this.md(t,e))},d.ec=function(t,e){return JSON.stringify(this.Se(t,e))},d.od=function(t,e){return JSON.stringify(this.Te(t,e))},e(S,b),(d=S.prototype).Dj=function(t){this.B?jt(this.B,t):this.B=t.slice(),this.s()},d.clone=function(){var t=new S(null);return t.da(this.ka,this.B.slice()),t},d.Ab=function(t,e,o,i){return i<oe(this.G(),t,e)?i:(this.A!=this.g&&(this.C=Math.sqrt(lo(this.B,0,this.B.length,this.a,0)),this.A=this.g),co(this.B,0,this.B.length,this.a,this.C,!1,t,e,o,i))},d.Sj=function(t,e){return Eo(this.B,0,this.B.length,this.a,t,e)},d.pm=function(t,e){return"XYM"!=this.ka&&"XYZM"!=this.ka?null:Ac(this.B,0,this.B.length,this.a,t,void 0!==e&&e)},d.$=function(){return bo(this.B,0,this.B.length,this.a)},d.Kg=function(t,e){return Tc(this.B,0,this.B.length,this.a,t,e)},d.qm=function(){for(var t=this.B,e=this.a,o=t[0],i=t[1],r=0,n=0+e;n<this.B.length;n+=e)var s=t[n],p=t[n+1],r=r+Math.sqrt((s-o)*(s-o)+(p-i)*(p-i)),o=s,i=p;return r},d.$c=function(t){var e=[];return e.length=wo(this.B,0,this.B.length,this.a,t,e,0),(t=new S(null)).da("XY",e),t},d.Y=function(){return"LineString"},d.Ta=function(t){return Co(this.B,0,this.B.length,this.a,t)},d.qa=function(t,e){t?(so(this,e,t,1),this.B||(this.B=[]),this.B.length=go(this.B,0,t,this.a),this.s()):this.da("XY",null)},d.da=function(t,e){no(this,t,e),this.s()},e(M,b),(d=M.prototype).Ej=function(t){this.B?jt(this.B,t.ia().slice()):this.B=t.ia().slice(),this.c.push(this.B.length),this.s()},d.clone=function(){var t=new M(null);return t.da(this.ka,this.B.slice(),this.c.slice()),t},d.Ab=function(t,e,o,i){return i<oe(this.G(),t,e)?i:(this.l!=this.g&&(this.A=Math.sqrt(uo(this.B,0,this.c,this.a,0)),this.l=this.g),yo(this.B,0,this.c,this.a,this.A,!1,t,e,o,i))},d.sm=function(t,e,o){if(("XYM"==this.ka||"XYZM"==this.ka)&&0!==this.B.length){var i=this.B,r=this.c,n=this.a,s=t,p=void 0!==e&&e,a=void 0!==o&&o,h=0;if(a)return Ac(i,h,r[r.length-1],n,s,p);if(s<i[n-1])return p?((i=i.slice(0,n))[n-1]=s,i):null;if(i[i.length-1]<s)return p?((i=i.slice(i.length-n))[n-1]=s,i):null;for(p=0,a=r.length;p<a;++p){var l=r[p];if(h!=l){if(s<i[h+n-1])break;if(s<=i[l-1])return Ac(i,h,l,n,s,!1);h=l}}}return null},d.$=function(){return mo(this.B,0,this.c,this.a)},d.Kb=function(){return this.c},d.mk=function(t){var e;return t<0||this.c.length<=t?null:((e=new S(null)).da(this.ka,this.B.slice(0===t?0:this.c[t-1],this.c[t])),e)},d.Yc=function(){for(var t=this.B,e=this.c,o=this.ka,i=[],r=0,n=0,s=e.length;n<s;++n){var p=e[n],a=new S(null);a.da(o,t.slice(r,p)),i.push(a),r=p}return i},d.$c=function(t){for(var e=[],o=[],i=this.B,r=this.c,n=this.a,s=0,p=0,a=0,h=r.length;a<h;++a){var l=r[a],p=wo(i,s,l,n,t,e,p);o.push(p),s=l}return e.length=p,(t=new M(null)).da("XY",e,o),t},d.Y=function(){return"MultiLineString"},d.Ta=function(t){t:{for(var e=this.B,o=this.c,i=this.a,r=0,n=0,s=o.length;n<s;++n){if(Co(e,r,o[n],i,t)){t=!0;break t}r=o[n]}t=!1}return t},d.qa=function(t,e){t?(so(this,e,t,2),this.B||(this.B=[]),e=vo(this.B,0,t,this.a,this.c),this.B.length=0===e.length?0:e[e.length-1],this.s()):this.da("XY",null,this.c)},d.da=function(t,e,o){no(this,t,e),this.c=o,this.s()},e(P,b),(d=P.prototype).Gj=function(t){this.B?jt(this.B,t.ia()):this.B=t.ia().slice(),this.s()},d.clone=function(){var t=new P(null);return t.da(this.ka,this.B.slice()),t},d.Ab=function(t,e,o,i){if(!(i<oe(this.G(),t,e)))for(var r,n=this.B,s=this.a,p=0,a=n.length;p<a;p+=s)if((r=Y(t,e,n[p],n[p+1]))<i){for(i=r,r=0;r<s;++r)o[r]=n[p+r];o.length=s}return i},d.$=function(){return bo(this.B,0,this.B.length,this.a)},d.yk=function(t){var e=this.B?this.B.length/this.a:0;return t<0||e<=t?null:((e=new m(null)).da(this.ka,this.B.slice(t*this.a,(t+1)*this.a)),e)},d.re=function(){for(var t=this.B,e=this.ka,o=this.a,i=[],r=0,n=t.length;r<n;r+=o){var s=new m(null);s.da(e,t.slice(r,r+o)),i.push(s)}return i},d.Y=function(){return"MultiPoint"},d.Ta=function(t){for(var e=this.B,o=this.a,i=0,r=e.length;i<r;i+=o)if(ne(t,e[i],e[i+1]))return!0;return!1},d.qa=function(t,e){t?(so(this,e,t,1),this.B||(this.B=[]),this.B.length=go(this.B,0,t,this.a),this.s()):this.da("XY",null)},d.da=function(t,e){no(this,t,e),this.s()},e(y,b),(d=y.prototype).Hj=function(t){if(this.B){var e,o,i=this.B.length;for(jt(this.B,t.ia()),e=0,o=(t=t.Kb().slice()).length;e<o;++e)t[e]+=i}else this.B=t.ia().slice(),t=t.Kb().slice(),this.c.push();this.c.push(t),this.s()},d.clone=function(){for(var t=new y(null),e=this.c.length,o=Array(e),i=0;i<e;++i)o[i]=this.c[i].slice();return Nc(t,this.ka,this.B.slice(),o),t},d.Ab=function(t,e,o,i){if(!(i<oe(this.G(),t,e))){if(this.D!=this.g){for(var r=this.c,n=0,s=0,p=0,a=r.length;p<a;++p)var h=r[p],s=uo(this.B,n,h,this.a,s),n=h[h.length-1];this.L=Math.sqrt(s),this.D=this.g}r=Rc(this),n=this.c,s=this.a,p=this.L;for(var h=[NaN,NaN],l=a=0,u=n.length;l<u;++l){var c=n[l];i=yo(r,a,c,s,p,!0,t,e,o,i,h),a=c[c.length-1]}}return i},d.Hc=function(t,e){t:{var o=Rc(this),i=this.c,r=this.a,n=0;if(0!==i.length)for(var s=0,p=i.length;s<p;++s){var a=i[s];if(To(o,n,a,r,t,e)){o=!0;break t}n=a[a.length-1]}o=!1}return o},d.tm=function(){for(var t=Rc(this),e=this.c,o=0,i=0,r=0,n=e.length;r<n;++r)var s=e[r],i=i+ao(t,o,s,this.a),o=s[s.length-1];return i},d.$=function(t){void 0!==t?Io(e=Rc(this).slice(),this.c,this.a,t):e=this.B,t=e,e=this.c;for(var e,o=this.a,i=0,r=[],n=0,s=0,p=e.length;s<p;++s){var a=e[s];r[n++]=mo(t,i,a,o,r[n]),i=a[a.length-1]}return r.length=n,r},d.jk=function(){var t=new P(null);return t.da("XY",Lc(this).slice()),t},d.$c=function(t){for(var e=[],o=[],i=this.B,r=this.c,n=this.a,s=(t=Math.sqrt(t),0),p=0,a=0,h=r.length;a<h;++a){var l=r[a],u=[],p=xo(i,s,l,n,t,e,p,u);o.push(u),s=l[l.length-1]}return e.length=p,Nc(i=new y(null),"XY",e,o),i},d.zk=function(t){if(t<0||this.c.length<=t)return null;var e=0===t?0:(e=this.c[t-1])[e.length-1],o=(t=this.c[t].slice())[t.length-1];if(0!==e)for(var i=0,r=t.length;i<r;++i)t[i]-=e;return(i=new w(null)).da(this.ka,this.B.slice(e,o),t),i},d.Ad=function(){for(var t,e,o=this.ka,i=this.B,r=this.c,n=[],s=0,p=0,a=r.length;p<a;++p){var h=r[p].slice(),l=h[h.length-1];if(0!==s)for(t=0,e=h.length;t<e;++t)h[t]-=s;(t=new w(null)).da(o,i.slice(s,l),h),n.push(t),s=l}return n},d.Y=function(){return"MultiPolygon"},d.Ta=function(t){t:{for(var e=Rc(this),o=this.c,i=this.a,r=0,n=0,s=o.length;n<s;++n){var p=o[n];if(jo(e,r,p,i,t)){t=!0;break t}r=p[p.length-1]}t=!1}return t},d.qa=function(t,e){if(t){so(this,e,t,3),this.B||(this.B=[]);for(var o=this.B,i=this.a,r=0,n=(n=this.c)||[],s=0,p=0,a=t.length;p<a;++p)r=vo(o,r,t[p],i,n[s]),r=(n[s++]=r)[r.length-1];n.length=s,0===n.length?this.B.length=0:(o=n[n.length-1],this.B.length=0===o.length?0:o[o.length-1]),this.s()}else Nc(this,"XY",null,this.c)},e(Fc,Mc);var Bc={Point:function(t){return void 0!==t.m&&void 0!==t.z?new m([t.x,t.y,t.z,t.m],"XYZM"):void 0!==t.z?new m([t.x,t.y,t.z],"XYZ"):void 0!==t.m?new m([t.x,t.y,t.m],"XYM"):new m([t.x,t.y])},LineString:function(t){return new S(t.paths[0],Oc(t))},Polygon:function(t){return new w(t.rings,Oc(t))},MultiPoint:function(t){return new P(t.points,Oc(t))},MultiLineString:function(t){return new M(t.paths,Oc(t))},MultiPolygon:function(t){return new y(t.rings,Oc(t))}},Uc={Point:function(t){var e,o=t.$();return"XYZ"===(t=t.ka)?e={x:o[0],y:o[1],z:o[2]}:"XYM"===t?e={x:o[0],y:o[1],m:o[2]}:"XYZM"===t?e={x:o[0],y:o[1],z:o[2],m:o[3]}:"XY"===t?e={x:o[0],y:o[1]}:V(!1,34),e},LineString:function(t){var e=Dc(t);return{hasZ:e.hasZ,hasM:e.hasM,paths:[t.$()]}},Polygon:function(t){var e=Dc(t);return{hasZ:e.hasZ,hasM:e.hasM,rings:t.$(!1)}},MultiPoint:function(t){var e=Dc(t);return{hasZ:e.hasZ,hasM:e.hasM,points:t.$()}},MultiLineString:function(t){var e=Dc(t);return{hasZ:e.hasZ,hasM:e.hasM,paths:t.$()}},MultiPolygon:function(t){var e=Dc(t);t=t.$(!1);for(var o=[],i=0;i<t.length;i++)for(var r=t[i].length-1;0<=r;r--)o.push(t[i][r]);return{hasZ:e.hasZ,hasM:e.hasM,rings:o}}};function Gc(t,e){return(0,Uc[t.Y()])(Sc(t,!0,e),e)}function Kc(t){this.Nb=t}function Xc(t){this.Nb=t}function Vc(t,e,o){this.Nb=t,this.b=e,this.a=o}function Wc(t,e){Vc.call(this,"And",t,e)}function zc(t,e,o){this.Nb="BBOX",this.geometryName=t,this.extent=e,this.srsName=o}function Hc(t,e){this.Nb=t,this.b=e}function Yc(t,e,o,i){Hc.call(this,t,e),this.g=o,this.a=i}function Zc(t,e,o){Yc.call(this,"PropertyIsEqualTo",t,e,o)}function qc(t,e){Yc.call(this,"PropertyIsGreaterThan",t,e)}function Jc(t,e){Yc.call(this,"PropertyIsGreaterThanOrEqualTo",t,e)}function _c(t,e,o,i){this.Nb=t,this.geometryName=e||"the_geom",this.geometry=o,this.srsName=i}function $c(t,e,o){_c.call(this,"Intersects",t,e,o)}function Qc(t,e,o){Hc.call(this,"PropertyIsBetween",t),this.a=e,this.g=o}function ty(t,e,o,i,r,n){Hc.call(this,"PropertyIsLike",t),this.f=e,this.i=void 0!==o?o:"*",this.c=void 0!==i?i:".",this.g=void 0!==r?r:"!",this.a=n}function ey(t){Hc.call(this,"PropertyIsNull",t)}function oy(t,e){Yc.call(this,"PropertyIsLessThan",t,e)}function iy(t,e){Yc.call(this,"PropertyIsLessThanOrEqualTo",t,e)}function ry(t){this.Nb="Not",this.condition=t}function ny(t,e,o){Yc.call(this,"PropertyIsNotEqualTo",t,e,o)}function sy(t,e){Vc.call(this,"Or",t,e)}function py(t,e,o){_c.call(this,"Within",t,e,o)}function ay(t,e){return new Wc(t,e)}function hy(t,e,o){return new zc(t,e,o)}function ly(t){eo.call(this),this.f=t||null,yy(this)}function uy(t){for(var e=[],o=0,i=t.length;o<i;++o)e.push(t[o].clone());return e}function cy(t){var e,o;if(t.f)for(e=0,o=t.f.length;e<o;++e)ht(t.f[e],"change",t.s,t)}function yy(t){var e,o;if(t.f)for(e=0,o=t.f.length;e<o;++e)c(t.f[e],"change",t.s,t)}function fy(t){t=t||{},mc.call(this),this.defaultDataProjection=Ye(t.defaultDataProjection||"EPSG:4326"),t.featureProjection&&(this.j=Ye(t.featureProjection)),this.b=t.geometryName}function gy(t,e){return t?Sc((0,vy[t.type])(t),!1,e):null}function dy(t,e){return(0,by[t.Y()])(Sc(t,!0,e),e)}(d=Fc.prototype).gd=function(t,e){var o=kc(t.geometry,e),i=new x;return this.b&&i.Nc(this.b),i.Pa(o),e&&e.zf&&t.attributes[e.zf]&&i.cc(t.attributes[e.zf]),t.attributes&&i.I(t.attributes),i},d.Sf=function(t,e){var o=e||{};if(t.features){var i,r,n=[],s=t.features;for(o.zf=t.objectIdFieldName,i=0,r=s.length;i<r;++i)n.push(this.gd(s[i],o));return n}return[this.gd(t,o)]},d.ci=kc,d.ii=function(t){return t.spatialReference&&t.spatialReference.wkid?Ye("EPSG:"+t.spatialReference.wkid):null},d.Te=function(t,e){return Gc(t,xc(this,e))},d.md=function(t,e){e=xc(this,e);var o={},i=t.V();return i&&(o.geometry=Gc(i,e)),delete(i=t.R())[t.a],o.attributes=ot(i)?{}:i,e&&e.featureProjection&&(o.spatialReference={wkid:Ye(e.featureProjection).hb.split(":").pop()}),o},d.Se=function(t,e){e=xc(this,e);for(var o=[],i=0,r=t.length;i<r;++i)o.push(this.md(t[i],e));return{features:o}},e(Xc,Kc),e(Vc,Xc),e(Wc,Vc),e(zc,Kc),e(Hc,Kc),e(Yc,Hc),e(Zc,Yc),e(qc,Yc),e(Jc,Yc),e(_c,Kc),e($c,_c),e(Qc,Hc),e(ty,Hc),e(ey,Hc),e(oy,Yc),e(iy,Yc),e(ry,Xc),e(ny,Yc),e(sy,Vc),e(py,_c),e(ly,eo),(d=ly.prototype).clone=function(){var t=new ly(null);return t.ti(this.f),t},d.Ab=function(t,e,o,i){if(!(i<oe(this.G(),t,e)))for(var r=this.f,n=0,s=r.length;n<s;++n)i=r[n].Ab(t,e,o,i);return i},d.Hc=function(t,e){for(var o=this.f,i=0,r=o.length;i<r;++i)if(o[i].Hc(t,e))return!0;return!1},d.Yd=function(t){ae(1/0,1/0,-1/0,-1/0,t);for(var e=this.f,o=0,i=e.length;o<i;++o)ce(t,e[o].G());return t},d.pf=function(){return uy(this.f)},d.Bd=function(t){if(this.o!=this.g&&(tt(this.i),this.j=0,this.o=this.g),!(t<0||0!==this.j&&t<this.j)){var e=t.toString();if(this.i.hasOwnProperty(e))return this.i[e];for(var o=[],i=this.f,r=!1,n=0,s=i.length;n<s;++n){var p=i[n],a=p.Bd(t);o.push(a),a!==p&&(r=!0)}if(r)return cy(t=new ly(null)),t.f=o,yy(t),t.s(),this.i[e]=t;this.j=t}return this},d.Y=function(){return"GeometryCollection"},d.Ta=function(t){for(var e=this.f,o=0,i=e.length;o<i;++o)if(e[o].Ta(t))return!0;return!1},d.rotate=function(t,e){for(var o=this.f,i=0,r=o.length;i<r;++i)o[i].rotate(t,e);this.s()},d.scale=function(t,e,o){o=o||me(this.G());for(var i=this.f,r=0,n=i.length;r<n;++r)i[r].scale(t,e,o);this.s()},d.ti=function(t){t=uy(t),cy(this),this.f=t,yy(this),this.s()},d.sc=function(t){for(var e=this.f,o=0,i=e.length;o<i;++o)e[o].sc(t);this.s()},d.translate=function(t,e){for(var o=this.f,i=0,r=o.length;i<r;++i)o[i].translate(t,e);this.s()},d.oa=function(){cy(this),eo.prototype.oa.call(this)},e(fy,Mc);var vy={Point:function(t){return new m(t.coordinates)},LineString:function(t){return new S(t.coordinates)},Polygon:function(t){return new w(t.coordinates)},MultiPoint:function(t){return new P(t.coordinates)},MultiLineString:function(t){return new M(t.coordinates)},MultiPolygon:function(t){return new y(t.coordinates)},GeometryCollection:function(t,e){return new ly(t.geometries.map(function(t){return gy(t,e)}))}},by={Point:function(t){return{type:"Point",coordinates:t.$()}},LineString:function(t){return{type:"LineString",coordinates:t.$()}},Polygon:function(t,e){var o;return e&&(o=e.rightHanded),{type:"Polygon",coordinates:t.$(o)}},MultiPoint:function(t){return{type:"MultiPoint",coordinates:t.$()}},MultiLineString:function(t){return{type:"MultiLineString",coordinates:t.$()}},MultiPolygon:function(t,e){var o;return e&&(o=e.rightHanded),{type:"MultiPolygon",coordinates:t.$(o)}},GeometryCollection:function(t,o){return{type:"GeometryCollection",geometries:t.f.map(function(t){var e=Q({},o);return delete e.featureProjection,dy(t,e)})}},Circle:function(){return{type:"GeometryCollection",geometries:[]}}};function my(){this.f=new XMLSerializer,mc.call(this)}function wy(t,e,o){return 0<(t=xy(t,e,o)).length?t[0]:null}function xy(t,e,o){var i=[];for(e=e.firstChild;e;e=e.nextSibling)e.nodeType==Node.ELEMENT_NODE&&jt(i,t.oc(e,o));return i}function Sy(t){this.featureType=(t=t||{}).featureType,this.featureNS=t.featureNS,this.srsName=t.srsName,this.schemaLocation="",this.b={},this.b["http://www.opengis.net/gml"]={featureMember:ac(Sy.prototype.Id),featureMembers:ac(Sy.prototype.Id)},my.call(this)}(d=fy.prototype).gd=function(t,e){var t="Feature"===t.type?t:{type:"Feature",geometry:t},e=gy(t.geometry,e),o=new x;return this.b&&o.Nc(this.b),o.Pa(e),void 0!==t.id&&o.cc(t.id),t.properties&&o.I(t.properties),o},d.Sf=function(t,e){if("FeatureCollection"===t.type)for(var o=[],i=t.features,r=0,n=i.length;r<n;++r)o.push(this.gd(i[r],e));else o=[this.gd(t,e)];return o},d.ci=gy,d.ii=function(t){var e;return(t=t.crs)?"name"==t.type?e=Ye(t.properties.name):"EPSG"==t.type?e=Ye("EPSG:"+t.properties.code):V(!1,36):e=this.defaultDataProjection,e},d.md=function(t,e){e=xc(this,e);var o={type:"Feature"},i=t.f;return void 0!==i&&(o.id=i),(i=t.V())?o.geometry=dy(i,e):o.geometry=null,delete(i=t.R())[t.a],ot(i)?o.properties=null:o.properties=i,o},d.Se=function(t,e){e=xc(this,e);for(var o=[],i=0,r=t.length;i<r;++i)o.push(this.md(t[i],e));return{type:"FeatureCollection",features:o}},d.Te=function(t,e){return dy(t,xc(this,e))},e(my,mc),(d=my.prototype).Y=function(){return"xml"},d.bc=function(t,e){return ic(t)?wy(this,t,e):rc(t)?this.ai(t,e):"string"==typeof t?wy(this,nc(t),e):null},d.La=function(t,e){return ic(t)?xy(this,t,e):rc(t)?this.oc(t,e):"string"==typeof t?xy(this,nc(t),e):[]},d.hd=function(t,e){var o;return ic(t)?this.u(t,e):rc(t)?(o=this.Ee(t,[wc(this,t,e||{})]))||null:"string"==typeof t?(o=nc(t),this.u(o,e)):null},d.Wa=function(t){return ic(t)?this.Xf(t):rc(t)?this.He(t):"string"==typeof t?(t=nc(t),this.Xf(t)):null},d.Xf=function(){return this.defaultDataProjection},d.He=function(){return this.defaultDataProjection},d.Od=function(t,e){t=this.C(t,e);return this.f.serializeToString(t)},d.ec=function(t,e){t=this.a(t,e);return this.f.serializeToString(t)},d.od=function(t,e){t=this.H(t,e);return this.f.serializeToString(t)},e(Sy,my);var My=/^[\s\xa0]*$/;function Py(t){return Ty(t=oc(t,!1))}function Ty(t){if(t=/^\s*(true|1)|(false|0)\s*$/.exec(t))return void 0!==t[1]||!1}function Ay(t){return t=oc(t,!1),t=Date.parse(t),isNaN(t)?void 0:t/1e3}function Ey(t){return Cy(t=oc(t,!1))}function Cy(t){if(t=/^\s*([+\-]?\d*\.?\d+(?:e[+\-]?\d+)?)\s*$/i.exec(t))return parseFloat(t[1])}function jy(t){return Ly(t=oc(t,!1))}function Ly(t){if(t=/^\s*(\d+)\s*$/.exec(t))return parseInt(t[1],10)}function n(t){return oc(t,!1).trim()}function Ry(t,e){Fy(t,e?"1":"0")}function Ny(t,e){t.appendChild(tc.createTextNode(e.toPrecision()))}function Iy(t,e){t.appendChild(tc.createTextNode(e.toString()))}function Fy(t,e){t.appendChild(tc.createTextNode(e))}function f(t){Sy.call(this,t=t||{}),this.v=void 0!==t.surface&&t.surface,this.i=void 0!==t.curve&&t.curve,this.l=void 0===t.multiCurve||t.multiCurve,this.o=void 0===t.multiSurface||t.multiSurface,this.schemaLocation=t.schemaLocation||"http://www.opengis.net/gml http://schemas.opengis.net/gml/3.1.1/profiles/gmlsfProfile/1.0.0/gmlsf.xsd"}function ky(t,e,o){o=o[o.length-1].srsName;for(var i=(e=e.$()).length,r=Array(i),n=0;n<i;++n){var s=e[n],p=n,a="enu";o&&(a=Ye(o).b),r[p]="en"===a.substr(0,2)?s[0]+" "+s[1]:s[1]+" "+s[0]}Fy(t,r.join(" "))}(d=Sy.prototype).Id=function(t,e){var o=t.localName,i=null;if("FeatureCollection"==o)i="http://www.opengis.net/wfs"===t.namespaceURI?g([],this.b,t,e,this):g(null,this.b,t,e,this);else if("featureMembers"==o||"featureMember"==o){var r,n,s=(f=e[0]).featureType,p=f.featureNS;if(!s&&t.childNodes){for(s=[],p={},r=0,n=t.childNodes.length;r<n;++r)if(1===(c=t.childNodes[r]).nodeType){var a=c.nodeName.split(":").pop();if(-1===s.indexOf(a)){var h,l="",u=0,c=c.namespaceURI;for(h in p){if(p[h]===c){l=h;break}++u}l||(p[l="p"+u]=c),s.push(l+":"+a)}}"featureMember"!=o&&(f.featureType=s,f.featureNS=p)}"string"==typeof p&&(r=p,(p={}).p0=r);var y,f={},s=Array.isArray(s)?s:[s];for(y in p){for(a={},r=0,n=s.length;r<n;++r)(-1===s[r].indexOf(":")?"p0":s[r].split(":")[0])===y&&(a[s[r].split(":").pop()]=("featureMembers"==o?pc:ac)(this.Rf,this));f[p[y]]=a}i=g("featureMember"==o?void 0:[],f,t,e)}return i=null===i?[]:i},d.Ee=function(t,e){var o=e[0],t=(o.srsName=t.firstElementChild.getAttribute("srsName"),g(null,this.lg,t,e,this));if(t)return Sc(t,!1,o)},d.Rf=function(t,e){for(var o,i=(i=t.getAttribute("fid"))||t.getAttributeNS("http://www.opengis.net/gml","id")||"",r={},n=t.firstElementChild;n;n=n.nextElementSibling){var s,p=n.localName;0===n.childNodes.length||1===n.childNodes.length&&(3===n.firstChild.nodeType||4===n.firstChild.nodeType)?(s=oc(n,!1),My.test(s)&&(s=void 0),r[p]=s):("boundedBy"!==p&&(o=p),r[p]=this.Ee(n,e))}return n=new x(r),o&&n.Nc(o),i&&n.cc(i),n},d.hi=function(t,e){t=this.De(t,e);if(t)return(e=new m(null)).da("XYZ",t),e},d.fi=function(t,e){t=g([],this.cj,t,e,this);if(t)return new P(t)},d.ei=function(t,e){t=g([],this.bj,t,e,this);if(t)return jc(e=new M(null),t),e},d.gi=function(t,e){t=g([],this.dj,t,e,this);if(t)return Ic(e=new y(null),t),e},d.Yh=function(t,e){fc(this.gj,t,e,this)},d.dh=function(t,e){fc(this.$i,t,e,this)},d.Zh=function(t,e){fc(this.hj,t,e,this)},d.Fe=function(t,e){t=this.De(t,e);if(t)return(e=new S(null)).da("XYZ",t),e},d.Do=function(t,e){t=g(null,this.Qd,t,e,this);if(t)return t},d.di=function(t,e){t=this.De(t,e);if(t)return Mo(e=new So(null),"XYZ",t),e},d.Ge=function(t,e){var o=g([null],this.We,t,e,this);if(o&&o[0]){for(var t=new w(null),i=o[0],r=[i.length],n=1,s=o.length;n<s;++n)jt(i,o[n]),r.push(i.length);return t.da("XYZ",i,r),t}},d.De=function(t,e){return g(null,this.Qd,t,e,this)},d.cj={"http://www.opengis.net/gml":{pointMember:pc(Sy.prototype.Yh),pointMembers:pc(Sy.prototype.Yh)}},d.bj={"http://www.opengis.net/gml":{lineStringMember:pc(Sy.prototype.dh),lineStringMembers:pc(Sy.prototype.dh)}},d.dj={"http://www.opengis.net/gml":{polygonMember:pc(Sy.prototype.Zh),polygonMembers:pc(Sy.prototype.Zh)}},d.gj={"http://www.opengis.net/gml":{Point:pc(Sy.prototype.De)}},d.$i={"http://www.opengis.net/gml":{LineString:pc(Sy.prototype.Fe)}},d.hj={"http://www.opengis.net/gml":{Polygon:pc(Sy.prototype.Ge)}},d.Rd={"http://www.opengis.net/gml":{LinearRing:ac(Sy.prototype.Do)}},d.oc=function(t,e){var o={featureType:this.featureType,featureNS:this.featureNS};return e&&Q(o,wc(this,t,e)),this.Id(t,[o])||[]},d.He=function(t){return Ye(this.srsName||t.firstElementChild.getAttribute("srsName"))},e(f,Sy),(d=f.prototype).Ho=function(t,e){t=g([],this.aj,t,e,this);if(t)return jc(e=new M(null),t),e},d.Io=function(t,e){t=g([],this.ej,t,e,this);if(t)return Ic(e=new y(null),t),e},d.Cg=function(t,e){fc(this.Xi,t,e,this)},d.Gi=function(t,e){fc(this.lj,t,e,this)},d.Lo=function(t,e){return g([null],this.fj,t,e,this)},d.Oo=function(t,e){return g([null],this.kj,t,e,this)},d.Mo=function(t,e){return g([null],this.We,t,e,this)},d.Go=function(t,e){return g([null],this.Qd,t,e,this)},d.ql=function(t,e){t=g(void 0,this.Rd,t,e,this);t&&e[e.length-1].push(t)},d.Oj=function(t,e){t=g(void 0,this.Rd,t,e,this);t&&(e[e.length-1][0]=t)},d.ji=function(t,e){var o=g([null],this.mj,t,e,this);if(o&&o[0]){for(var t=new w(null),i=o[0],r=[i.length],n=1,s=o.length;n<s;++n)jt(i,o[n]),r.push(i.length);return t.da("XYZ",i,r),t}},d.$h=function(t,e){t=g([null],this.Yi,t,e,this);if(t)return(e=new S(null)).da("XYZ",t),e},d.Co=function(t,e){t=g([null],this.Zi,t,e,this);return ae(t[1][0],t[1][1],t[2][0],t[2][1])},d.Eo=function(t,e){for(var o,i=oc(t,!1),r=/^\s*([+\-]?\d*\.?\d+(?:[eE][+\-]?\d+)?)\s*/,n=[];o=r.exec(i);)n.push(parseFloat(o[1])),i=i.substr(o[0].length);if(""===i){if(r="enu","neu"===(r=(i=e[0].srsName)?Ye(i).b:r))for(i=0,r=n.length;i<r;i+=3)o=n[i],n[i]=n[i+1],n[i+1]=o;return 2==(i=n.length)&&n.push(0),0===i?void 0:n}},d.Vf=function(t,e){var o=oc(t,!1).replace(/^\s*|\s*$/g,""),i=e[0].srsName,r=t.parentNode.getAttribute("srsDimension"),n="enu";i&&(n=Ye(i).b),o=o.split(/\s+/),i=2,t.getAttribute("srsDimension")?i=Ly(t.getAttribute("srsDimension")):t.getAttribute("dimension")?i=Ly(t.getAttribute("dimension")):r&&(i=Ly(r));for(var s,p,a=[],h=0,l=o.length;h<l;h+=i)r=parseFloat(o[h]),s=parseFloat(o[h+1]),p=3===i?parseFloat(o[h+2]):0,"en"===n.substr(0,2)?a.push(r,s,p):a.push(s,r,p);return a},d.Qd={"http://www.opengis.net/gml":{pos:ac(f.prototype.Eo),posList:ac(f.prototype.Vf)}},d.We={"http://www.opengis.net/gml":{interior:f.prototype.ql,exterior:f.prototype.Oj}},d.lg={"http://www.opengis.net/gml":{Point:ac(Sy.prototype.hi),MultiPoint:ac(Sy.prototype.fi),LineString:ac(Sy.prototype.Fe),MultiLineString:ac(Sy.prototype.ei),LinearRing:ac(Sy.prototype.di),Polygon:ac(Sy.prototype.Ge),MultiPolygon:ac(Sy.prototype.gi),Surface:ac(f.prototype.ji),MultiSurface:ac(f.prototype.Io),Curve:ac(f.prototype.$h),MultiCurve:ac(f.prototype.Ho),Envelope:ac(f.prototype.Co)}},d.aj={"http://www.opengis.net/gml":{curveMember:pc(f.prototype.Cg),curveMembers:pc(f.prototype.Cg)}},d.ej={"http://www.opengis.net/gml":{surfaceMember:pc(f.prototype.Gi),surfaceMembers:pc(f.prototype.Gi)}},d.Xi={"http://www.opengis.net/gml":{LineString:pc(Sy.prototype.Fe),Curve:pc(f.prototype.$h)}},d.lj={"http://www.opengis.net/gml":{Polygon:pc(Sy.prototype.Ge),Surface:pc(f.prototype.ji)}},d.mj={"http://www.opengis.net/gml":{patches:ac(f.prototype.Lo)}},d.Yi={"http://www.opengis.net/gml":{segments:ac(f.prototype.Oo)}},d.Zi={"http://www.opengis.net/gml":{lowerCorner:pc(f.prototype.Vf),upperCorner:pc(f.prototype.Vf)}},d.fj={"http://www.opengis.net/gml":{PolygonPatch:ac(f.prototype.Mo)}},d.kj={"http://www.opengis.net/gml":{LineStringSegment:ac(f.prototype.Go)}},d.Ti=function(t,e,o){var i=o[o.length-1].srsName;i&&t.setAttribute("srsName",i),i=ec(t.namespaceURI,"pos"),t.appendChild(i),t="enu",(o=o[o.length-1].srsName)&&(t=Ye(o).b),e=e.$(),Fy(i,"en"===t.substr(0,2)?e[0]+" "+e[1]:e[1]+" "+e[0])};var d,Oy={"http://www.opengis.net/gml":{lowerCorner:l(Fy),upperCorner:l(Fy)}},Dy=((d=f.prototype).zp=function(t,e,o){var i=o[o.length-1].srsName;i&&t.setAttribute("srsName",i),dc({node:t},Oy,cc,[e[0]+" "+e[1],e[2]+" "+e[3]],o,["lowerCorner","upperCorner"],this)},d.Qi=function(t,e,o){var i=o[o.length-1].srsName;i&&t.setAttribute("srsName",i),i=ec(t.namespaceURI,"posList"),t.appendChild(i),ky(i,e,o)},d.jj=function(t,e){var e=e[e.length-1],o=e.node,i=e.exteriorWritten;return void 0===i&&(e.exteriorWritten=!0),ec(o.namespaceURI,void 0!==i?"interior":"exterior")},d.Ue=function(t,e,o){var i=o[o.length-1].srsName;"PolygonPatch"!==t.nodeName&&i&&t.setAttribute("srsName",i),"Polygon"===t.nodeName||"PolygonPatch"===t.nodeName?(e=e.Zc(),dc({node:t,srsName:i},Gy,this.jj,e,o,void 0,this)):"Surface"===t.nodeName&&(i=ec(t.namespaceURI,"patches"),t.appendChild(i),t=ec(i.namespaceURI,"PolygonPatch"),i.appendChild(t),this.Ue(t,e,o))},d.Qe=function(t,e,o){var i=o[o.length-1].srsName;"LineStringSegment"!==t.nodeName&&i&&t.setAttribute("srsName",i),"LineString"===t.nodeName||"LineStringSegment"===t.nodeName?(i=ec(t.namespaceURI,"posList"),t.appendChild(i),ky(i,e,o)):"Curve"===t.nodeName&&(i=ec(t.namespaceURI,"segments"),t.appendChild(i),t=ec(i.namespaceURI,"LineStringSegment"),i.appendChild(t),this.Qe(t,e,o))},d.Si=function(t,e,o){var i=(r=o[o.length-1]).srsName,r=r.surface;i&&t.setAttribute("srsName",i),e=e.Ad(),dc({node:t,srsName:i,surface:r},Dy,this.c,e,o,void 0,this)},d.Ap=function(t,e,o){var i=o[o.length-1].srsName;i&&t.setAttribute("srsName",i),e=e.re(),dc({node:t,srsName:i},By,uc("pointMember"),e,o,void 0,this)},d.Ri=function(t,e,o){var i=(r=o[o.length-1]).srsName,r=r.curve;i&&t.setAttribute("srsName",i),e=e.Yc(),dc({node:t,srsName:i,curve:r},Uy,this.c,e,o,void 0,this)},d.Ui=function(t,e,o){var i=ec(t.namespaceURI,"LinearRing");t.appendChild(i),this.Qi(i,e,o)},d.Vi=function(t,e,o){var i=this.g(e,o);i&&(t.appendChild(i),this.Ue(i,e,o))},d.Bp=function(t,e,o){var i=ec(t.namespaceURI,"Point");t.appendChild(i),this.Ti(i,e,o)},d.Pi=function(t,e,o){var i=this.g(e,o);i&&(t.appendChild(i),this.Qe(i,e,o))},d.pd=function(t,e,o){var i=o[o.length-1],r=Q({},i);r.node=t,t=Array.isArray(e)?i.dataProjection?to(e,i.featureProjection,i.dataProjection):e:Sc(e,!0,i),dc(r,Ky,this.g,[t],o,void 0,this)},d.Ni=function(t,e,o){var i=e.f;i&&t.setAttribute("fid",i);var r,n=(i=o[o.length-1]).featureNS,s=e.a,p=(i.Mc||(i.Mc={},i.Mc[n]={}),e.R()),a=(e=[],[]);for(r in p){var h=p[r];null!==h&&(e.push(r),a.push(h),r==s||h instanceof eo?r in i.Mc[n]||(i.Mc[n][r]=l(this.pd,this)):r in i.Mc[n]||(i.Mc[n][r]=l(Fy)))}(r=Q({},i)).node=t,dc(r,i.Mc,uc(void 0,n),a,o,e)},{"http://www.opengis.net/gml":{surfaceMember:l(f.prototype.Vi),polygonMember:l(f.prototype.Vi)}}),By={"http://www.opengis.net/gml":{pointMember:l(f.prototype.Bp)}},Uy={"http://www.opengis.net/gml":{lineStringMember:l(f.prototype.Pi),curveMember:l(f.prototype.Pi)}},Gy={"http://www.opengis.net/gml":{exterior:l(f.prototype.Ui),interior:l(f.prototype.Ui)}},Ky={"http://www.opengis.net/gml":{Curve:l(f.prototype.Qe),MultiCurve:l(f.prototype.Ri),Point:l(f.prototype.Ti),MultiPoint:l(f.prototype.Ap),LineString:l(f.prototype.Qe),MultiLineString:l(f.prototype.Ri),LinearRing:l(f.prototype.Qi),Polygon:l(f.prototype.Ue),MultiPolygon:l(f.prototype.Si),Surface:l(f.prototype.Ue),MultiSurface:l(f.prototype.Si),Envelope:l(f.prototype.zp)}},Xy={MultiLineString:"lineStringMember",MultiCurve:"curveMember",MultiPolygon:"polygonMember",MultiSurface:"surfaceMember"};function Vy(t){Sy.call(this,t=t||{}),this.b["http://www.opengis.net/gml"].featureMember=pc(Sy.prototype.Id),this.schemaLocation=t.schemaLocation||"http://www.opengis.net/gml http://schemas.opengis.net/gml/2.1.2/feature.xsd"}function Wy(t){t=t||{},my.call(this),this.defaultDataProjection=Ye("EPSG:4326"),this.b=t.readExtensions}f.prototype.c=function(t,e){return ec("http://www.opengis.net/gml",Xy[e[e.length-1].node.nodeName])},f.prototype.g=function(t,e){var o,i=(e=e[e.length-1]).multiSurface,r=e.surface,n=e.curve,e=e.multiCurve;return Array.isArray(t)?o="Envelope":"MultiPolygon"===(o=t.Y())&&!0===i?o="MultiSurface":"Polygon"===o&&!0===r?o="Surface":"LineString"===o&&!0===n?o="Curve":"MultiLineString"===o&&!0===e&&(o="MultiCurve"),ec("http://www.opengis.net/gml",o)},f.prototype.H=function(t,e){e=xc(this,e);var o=ec("http://www.opengis.net/gml","geom"),i={node:o,srsName:this.srsName,curve:this.i,surface:this.v,multiSurface:this.o,multiCurve:this.l};return e&&Q(i,e),this.pd(o,t,[i]),o},f.prototype.a=function(t,e){e=xc(this,e);var o=ec("http://www.opengis.net/gml","featureMembers"),i=(o.setAttributeNS("http://www.w3.org/2001/XMLSchema-instance","xsi:schemaLocation",this.schemaLocation),{srsName:this.srsName,curve:this.i,surface:this.v,multiSurface:this.o,multiCurve:this.l,featureNS:this.featureNS,featureType:this.featureType});e&&Q(i,e);var e=(i=[i])[i.length-1],r=e.featureType,n=e.featureNS,s={};return s[n]={},s[n][r]=l(this.Ni,this),(e=Q({},e)).node=o,dc(e,s,uc(r,n),t,i),o},e(Vy,Sy),(d=Vy.prototype).bi=function(t,e){var o=oc(t,!1).replace(/^\s*|\s*$/g,""),i=e[0].srsName,r=t.parentNode.getAttribute("srsDimension"),n="enu";(i=i&&Ye(i))&&(n=i.b),o=o.split(/[\s,]+/),i=2,t.getAttribute("srsDimension")?i=Ly(t.getAttribute("srsDimension")):t.getAttribute("dimension")?i=Ly(t.getAttribute("dimension")):r&&(i=Ly(r));for(var s,p,a=[],h=0,l=o.length;h<l;h+=i)r=parseFloat(o[h]),s=parseFloat(o[h+1]),p=3===i?parseFloat(o[h+2]):0,"en"===n.substr(0,2)?a.push(r,s,p):a.push(s,r,p);return a},d.Ao=function(t,e){t=g([null],this.Wi,t,e,this);return ae(t[1][0],t[1][1],t[1][3],t[1][4])},d.ol=function(t,e){t=g(void 0,this.Rd,t,e,this);t&&e[e.length-1].push(t)},d.jo=function(t,e){t=g(void 0,this.Rd,t,e,this);t&&(e[e.length-1][0]=t)},d.Qd={"http://www.opengis.net/gml":{coordinates:ac(Vy.prototype.bi)}},d.We={"http://www.opengis.net/gml":{innerBoundaryIs:Vy.prototype.ol,outerBoundaryIs:Vy.prototype.jo}},d.Wi={"http://www.opengis.net/gml":{coordinates:pc(Vy.prototype.bi)}},d.lg={"http://www.opengis.net/gml":{Point:ac(Sy.prototype.hi),MultiPoint:ac(Sy.prototype.fi),LineString:ac(Sy.prototype.Fe),MultiLineString:ac(Sy.prototype.ei),LinearRing:ac(Sy.prototype.di),Polygon:ac(Sy.prototype.Ge),MultiPolygon:ac(Sy.prototype.gi),Box:ac(Vy.prototype.Ao)}},e(Wy,my);var zy=[null,"http://www.topografix.com/GPX/1/0","http://www.topografix.com/GPX/1/1"];function Hy(t,e,o,i){return t.push(parseFloat(o.getAttribute("lon")),parseFloat(o.getAttribute("lat"))),"ele"in i?(t.push(i.ele),delete i.ele,e.hasZ=!0):t.push(0),"time"in i?(t.push(i.time),delete i.time,e.hasM=!0):t.push(0),t}function Yy(t,e,o){var i="XY",r=2;if(t.hasZ&&t.hasM?(i="XYZM",r=4):t.hasZ?(i="XYZ",r=3):t.hasM&&(i="XYM",r=3),4!==r){for(var n=0,s=e.length/4;n<s;n++)e[n*r]=e[4*n],e[n*r+1]=e[4*n+1],t.hasZ&&(e[n*r+2]=e[4*n+2]),t.hasM&&(e[n*r+2]=e[4*n+3]);if(e.length=e.length/4*r,o)for(n=0,s=o.length;n<s;n++)o[n]=o[n]/4*r}return i}function Zy(t,e){var o=e[e.length-1],i=t.getAttribute("href");null!==i&&(o.link=i),fc(ef,t,e)}function qy(t,e){e[e.length-1].extensionsNode_=t}function Jy(t,e){var o,i,r=e[0],t=g({flatCoordinates:[],layoutOptions:{}},of,t,e);if(t)return e=t.flatCoordinates,delete t.flatCoordinates,o=t.layoutOptions,delete t.layoutOptions,o=Yy(o,e),(i=new S(null)).da(o,e),Sc(i,!1,r),(r=new x(i)).I(t),r}function _y(t,e){var o,i,r,n=e[0],t=g({flatCoordinates:[],ends:[],layoutOptions:{}},nf,t,e);if(t)return e=t.flatCoordinates,delete t.flatCoordinates,o=t.ends,delete t.ends,i=t.layoutOptions,delete t.layoutOptions,i=Yy(i,e,o),(r=new M(null)).da(i,e,o),Sc(r,!1,n),(n=new x(r)).I(t),n}function $y(t,e){var o,i=e[0],e=g({},af,t,e);if(e)return Sc(t=new m(t=Hy([],o={},t,e),o=Yy(o,t)),!1,i),(i=new x(t)).I(e),i}var Qy={rte:Jy,trk:_y,wpt:$y},tf=u(zy,{rte:pc(Jy),trk:pc(_y),wpt:pc($y)}),ef=u(zy,{text:r(n,"linkText"),type:r(n,"linkType")}),of=u(zy,{name:r(n),cmt:r(n),desc:r(n),src:r(n),link:Zy,number:r(jy),extensions:qy,type:r(n),rtept:function(t,e){var o=g({},rf,t,e);o&&Hy((e=e[e.length-1]).flatCoordinates,e.layoutOptions,t,o)}}),rf=u(zy,{ele:r(Ey),time:r(Ay)}),nf=u(zy,{name:r(n),cmt:r(n),desc:r(n),src:r(n),link:Zy,number:r(jy),type:r(n),extensions:qy,trkseg:function(t,e){var o=e[e.length-1];fc(sf,t,e),o.ends.push(o.flatCoordinates.length)}}),sf=u(zy,{trkpt:function(t,e){var o=g({},pf,t,e);o&&Hy((e=e[e.length-1]).flatCoordinates,e.layoutOptions,t,o)}}),pf=u(zy,{ele:r(Ey),time:r(Ay)}),af=u(zy,{ele:r(Ey),time:r(Ay),magvar:r(Ey),geoidheight:r(Ey),name:r(n),cmt:r(n),desc:r(n),src:r(n),link:Zy,sym:r(n),type:r(n),fix:r(n),sat:r(jy),hdop:r(Ey),vdop:r(Ey),pdop:r(Ey),ageofdgpsdata:r(Ey),dgpsid:r(jy),extensions:qy});function hf(t,e){for(var o=0,i=(e=e||[]).length;o<i;++o){var r,n=e[o];t.b&&(r=n.get("extensionsNode_")||null,t.b(n,r)),n.set("extensionsNode_",void 0)}}function lf(t,e,o){t.setAttribute("href",e),e=o[o.length-1].properties,dc({node:t},yf,cc,[e.linkText,e.linkType],o,cf)}function uf(t,e,o){var i=o[o.length-1],r=i.node.namespaceURI,n=i.properties;switch(t.setAttributeNS(null,"lat",e[1]),t.setAttributeNS(null,"lon",e[0]),i.geometryLayout){case"XYZM":0!==e[3]&&(n.time=e[3]);case"XYZ":0!==e[2]&&(n.ele=e[2]);break;case"XYM":0!==e[2]&&(n.time=e[2])}i=yc(n,e=("rtept"==t.nodeName?df:xf)[r]),dc({node:t,properties:n},Sf,cc,i,o,e)}Wy.prototype.ai=function(t,e){var o;return Et(zy,t.namespaceURI)&&(o=(o=Qy[t.localName])&&o(t,[wc(this,t,e)]))?(hf(this,[o]),o):null},Wy.prototype.oc=function(t,e){if(Et(zy,t.namespaceURI)&&"gpx"==t.localName){t=g([],tf,t,[wc(this,t,e)]);if(t)return hf(this,t),t}return[]};var cf=["text","type"],yf=u(zy,{text:l(Fy),type:l(Fy)}),ff=u(zy,"name cmt desc src link number type rtept".split(" ")),gf=u(zy,{name:l(Fy),cmt:l(Fy),desc:l(Fy),src:l(Fy),link:l(lf),number:l(Iy),type:l(Fy),rtept:lc(l(uf))}),df=u(zy,["ele","time"]),vf=u(zy,"name cmt desc src link number type trkseg".split(" ")),bf=u(zy,{name:l(Fy),cmt:l(Fy),desc:l(Fy),src:l(Fy),link:l(lf),number:l(Iy),type:l(Fy),trkseg:lc(l(function(t,e,o){dc({node:t,geometryLayout:e.ka,properties:{}},wf,mf,e.$(),o)}))}),mf=uc("trkpt"),wf=u(zy,{trkpt:l(uf)}),xf=u(zy,"ele time magvar geoidheight name cmt desc src link sym type fix sat hdop vdop pdop ageofdgpsdata dgpsid".split(" ")),Sf=u(zy,{ele:l(Ny),time:l(function(t,e){e=new Date(1e3*e);t.appendChild(tc.createTextNode(e.getUTCFullYear()+"-"+Ot(e.getUTCMonth()+1)+"-"+Ot(e.getUTCDate())+"T"+Ot(e.getUTCHours())+":"+Ot(e.getUTCMinutes())+":"+Ot(e.getUTCSeconds())+"Z"))}),magvar:l(Ny),geoidheight:l(Ny),name:l(Fy),cmt:l(Fy),desc:l(Fy),src:l(Fy),link:l(lf),sym:l(Fy),type:l(Fy),fix:l(Fy),sat:l(Iy),hdop:l(Ny),vdop:l(Ny),pdop:l(Ny),ageofdgpsdata:l(Ny),dgpsid:l(Iy)}),Mf={Point:"wpt",LineString:"rte",MultiLineString:"trk"};function Pf(t,e){t=t.V();if(t=t&&Mf[t.Y()])return ec(e[e.length-1].node.namespaceURI,t)}var Tf=u(zy,{rte:l(function(t,e,o){var i=o[0],r=e.R();t={node:t,properties:r},(e=e.V())&&(e=Sc(e,!0,i),t.geometryLayout=e.ka,r.rtept=e.$()),r=yc(r,i=ff[o[o.length-1].node.namespaceURI]),dc(t,gf,cc,r,o,i)}),trk:l(function(t,e,o){var i=o[0],r=e.R();t={node:t,properties:r},(e=e.V())&&(e=Sc(e,!0,i),r.trkseg=e.Yc()),r=yc(r,i=vf[o[o.length-1].node.namespaceURI]),dc(t,bf,cc,r,o,i)}),wpt:l(function(t,e,o){var i=o[0],r=o[o.length-1];r.properties=e.R(),(e=e.V())&&(e=Sc(e,!0,i),r.geometryLayout=e.ka,uf(t,e.$(),o))})});function Af(){mc.call(this)}function Ef(t){return"string"==typeof t?t:""}function Cf(t){t=t||{},mc.call(this),this.defaultDataProjection=Ye("EPSG:4326"),this.b=t.altitudeMode||kf}Wy.prototype.a=function(t,e){e=xc(this,e);var o=ec("http://www.topografix.com/GPX/1/1","gpx");return o.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance"),o.setAttributeNS("http://www.w3.org/2001/XMLSchema-instance","xsi:schemaLocation","http://www.topografix.com/GPX/1/1 http://www.topografix.com/GPX/1/1/gpx.xsd"),o.setAttribute("version","1.1"),o.setAttribute("creator","OpenLayers 3"),dc({node:o},Tf,Pf,t,[e]),o},e(Af,mc),(d=Af.prototype).Y=function(){return"text"},d.bc=function(t,e){return this.Hd(Ef(t),xc(this,e))},d.La=function(t,e){return this.Tf(Ef(t),xc(this,e))},d.hd=function(t,e){return this.Jd(Ef(t),xc(this,e))},d.Wa=function(){return this.defaultDataProjection},d.Od=function(t,e){return this.Re(t,xc(this,e))},d.ec=function(t,e){return this.Oi(t,xc(this,e))},d.od=function(t,e){return this.Pd(t,xc(this,e))},e(Cf,Af);var jf=/^B(\d{2})(\d{2})(\d{2})(\d{2})(\d{5})([NS])(\d{3})(\d{5})([EW])([AV])(\d{5})(\d{5})/,Lf=/^H.([A-Z]{3}).*?:(.*)/,Rf=/^HFDTE(\d{2})(\d{2})(\d{2})/,Nf=/\r\n|\r|\n/,If=(Cf.prototype.Hd=function(t,e){for(var o=this.b,i=t.split(Nf),r={},n=[],s=2e3,p=0,a=1,h=-1,l=0,u=i.length;l<u;++l){var c,y,f,g,d,v=i[l];"B"==v.charAt(0)?(c=jf.exec(v))&&(v=parseInt(c[1],10),y=parseInt(c[2],10),f=parseInt(c[3],10),g=parseInt(c[4],10)+parseInt(c[5],10)/6e4,"S"==c[6]&&(g=-g),d=parseInt(c[7],10)+parseInt(c[8],10)/6e4,"W"==c[9]&&(d=-d),n.push(d,g),o!=kf&&n.push(o==Ff?parseInt(c[11],10):o==If?parseInt(c[12],10):0),(c=Date.UTC(s,p,a,v,y,f))<h&&(c=Date.UTC(s,p,a+1,v,y,f)),n.push(c/1e3),h=c):"H"==v.charAt(0)&&((c=Rf.exec(v))?(a=parseInt(c[1],10),p=parseInt(c[2],10)-1,s=2e3+parseInt(c[3],10)):(c=Lf.exec(v))&&(r[c[1]]=c[2].trim()))}return 0===n.length?null:((i=new S(null)).da(o==kf?"XYM":"XYZM",n),(o=new x(Sc(i,!1,e))).I(r),o)},Cf.prototype.Tf=function(t,e){t=this.Hd(t,e);return t?[t]:[]},"barometric"),Ff="gps",kf="none";function Of(t,e,o,i,r,n){dt.call(this),this.l=null,this.a=t||new Image,null!==i&&(this.a.crossOrigin=i),this.c=n?document.createElement("CANVAS"):null,this.j=n,this.i=null,this.f=r,this.g=o,this.o=e,this.v=!1,this.f==ip&&Df(this)}function Df(e){var t=Ri(1,1);try{t.drawImage(e.a,0,0),t.getImageData(0,0,1,1)}catch(t){e.v=!0}}function Bf(t){this.c=void 0!==(t=t||{}).anchor?t.anchor:[.5,.5],this.j=null,this.a=void 0!==t.anchorOrigin?t.anchorOrigin:ig,this.C=void 0!==t.anchorXUnits?t.anchorXUnits:tg,this.D=void 0!==t.anchorYUnits?t.anchorYUnits:tg,this.ra=void 0!==t.crossOrigin?t.crossOrigin:null;var e=void 0!==t.img?t.img:null,o=void 0!==t.imgSize?t.imgSize:null,i=t.src,r=(V(!(void 0!==i&&e),4),V(!e||o,5),V(void 0!==(i=void 0!==i&&0!==i.length||!e?i:e.src||B(e).toString())&&0<i.length,6),void 0!==t.src?ep:ip),n=(this.f=void 0!==t.color?Pi(t.color):null,this.ra),s=this.f,p=Rs.get(i,n,s);p||(p=new Of(e,i,o,n,r,s),Rs.set(i,n,s,p)),this.b=p,this.L=void 0!==t.offset?t.offset:[0,0],this.g=void 0!==t.offsetOrigin?t.offsetOrigin:ig,this.v=null,this.A=void 0!==t.size?t.size:null,hp.call(this,{opacity:void 0!==t.opacity?t.opacity:1,rotation:void 0!==t.rotation?t.rotation:0,scale:void 0!==t.scale?t.scale:1,snapToPixel:void 0===t.snapToPixel||t.snapToPixel,rotateWithView:void 0!==t.rotateWithView&&t.rotateWithView})}e(Of,dt),Of.prototype.H=function(){this.f=rp,this.i.forEach(lt),this.i=null,this.b("change")},Of.prototype.u=function(){if(this.f=ip,this.g&&(this.a.width=this.g[0],this.a.height=this.g[1]),this.g=[this.a.width,this.a.height],this.i.forEach(lt),this.i=null,Df(this),!this.v&&null!==this.j){this.c.width=this.a.width,this.c.height=this.a.height;var t=this.c.getContext("2d");t.drawImage(this.a,0,0);for(var e=t.getImageData(0,0,this.a.width,this.a.height),o=e.data,i=this.j[0]/255,r=this.j[1]/255,n=this.j[2]/255,s=0,p=o.length;s<p;s+=4)o[s]*=i,o[s+1]*=r,o[s+2]*=n;t.putImageData(e,0,0)}this.b("change")},Of.prototype.load=function(){if(this.f==ep){this.f=op,this.i=[at(this.a,"error",this.H,this),at(this.a,"load",this.u,this)];try{this.a.src=this.o}catch(t){this.H()}}},e(Bf,hp),(d=Bf.prototype).clone=function(){var t,e,o=this.Ic(1);return this.b.f===ip&&("IMG"===o.tagName.toUpperCase()?t=o.cloneNode(!0):(e=(t=document.createElement("canvas")).getContext("2d"),t.width=o.width,t.height=o.height,e.drawImage(o,0,0))),new Bf({anchor:this.c.slice(),anchorOrigin:this.a,anchorXUnits:this.C,anchorYUnits:this.D,crossOrigin:this.ra,color:this.f&&this.f.slice?this.f.slice():this.f||void 0,img:t||void 0,imgSize:t?this.b.g.slice():void 0,src:t?void 0:this.b.o,offset:this.L.slice(),offsetOrigin:this.g,size:null!==this.A?this.A.slice():void 0,opacity:this.l,scale:this.i,snapToPixel:this.u,rotation:this.o,rotateWithView:this.H})},d.Ac=function(){if(this.j)return this.j;var t=this.c,e=this.ac();if(this.C==tg||this.D==tg){if(!e)return null;t=this.c.slice(),this.C==tg&&(t[0]*=e[0]),this.D==tg&&(t[1]*=e[1])}if(this.a!=ig){if(!e)return null;t===this.c&&(t=this.c.slice()),this.a!=rg&&this.a!=og||(t[0]=-t[0]+e[0]),this.a!=eg&&this.a!=og||(t[1]=-t[1]+e[1])}return this.j=t},d.Gn=function(){return this.f},d.Ic=function(){var t=this.b;return t.c||t.a},d.de=function(){return this.b.g},d.xe=function(){return this.b.f},d.Lf=function(){var t,e,o,i=this.b;return i.l||(i.v?((o=Ri(t=i.g[0],e=i.g[1])).fillRect(0,0,t,e),i.l=o.canvas):i.l=i.a),i.l},d.Jc=function(){if(this.v)return this.v;var t=this.L;if(this.g!=ig){var e=this.ac(),o=this.b.g;if(!e||!o)return null;t=t.slice(),this.g!=rg&&this.g!=og||(t[0]=o[0]-e[0]-t[0]),this.g!=eg&&this.g!=og||(t[1]=o[1]-e[1]-t[1])}return this.v=t},d.Hn=function(){return this.b.o},d.ac=function(){return this.A||this.b.g},d.eh=function(t,e){return c(this.b,"change",t,e)},d.load=function(){this.b.load()},d.Ii=function(t,e){ht(this.b,"change",t,e)};var Uf,Gf,Kf,Xf,Vf,Wf,zf,Hf,Yf,Zf,qf,Jf,_f,$f,Qf,tg="fraction",eg="bottom-left",og="bottom-right",ig="top-left",rg="top-right";function ng(t){this.g=(t=t||{}).font,this.j=t.rotation,this.v=t.rotateWithView,this.a=t.scale,this.H=t.text,this.l=t.textAlign,this.o=t.textBaseline,this.b=void 0!==t.fill?t.fill:new yp({color:"#333"}),this.f=void 0!==t.stroke?t.stroke:null,this.c=void 0!==t.offsetX?t.offsetX:0,this.i=void 0!==t.offsetY?t.offsetY:0}function sg(t){t=t||{},my.call(this),Uf||(Kf=new yp({color:Gf=[255,255,255,1]}),Wf=Vf="pixels",Zf=new Bf({anchor:Xf=[20,2],anchorOrigin:eg,anchorXUnits:Vf,anchorYUnits:Wf,crossOrigin:"anonymous",rotation:0,scale:Yf=.5,size:zf=[64,64],src:Hf="https://maps.google.com/mapfiles/kml/pushpin/ylw-pushpin.png"}),qf="NO_IMAGE",Jf=new fp({color:Gf,width:1}),_f=new fp({color:[51,51,51,1],width:2}),$f=new ng({font:"bold 16px Helvetica",fill:Kf,stroke:_f,scale:.8}),Qf=new gp({fill:Kf,image:Zf,text:$f,stroke:Jf,zIndex:0}),Uf=[Qf]),this.defaultDataProjection=Ye("EPSG:4326"),this.g=t.defaultStyle||Uf,this.c=void 0===t.extractStyles||t.extractStyles,this.l=void 0===t.writeStyles||t.writeStyles,this.b={},this.i=void 0===t.showPointNames||t.showPointNames}(d=ng.prototype).clone=function(){return new ng({font:this.g,rotation:this.j,rotateWithView:this.v,scale:this.a,text:this.Ka(),textAlign:this.l,textBaseline:this.o,fill:this.b?this.b.clone():void 0,stroke:this.f?this.f.clone():void 0,offsetX:this.c,offsetY:this.i})},d.dk=function(){return this.g},d.sk=function(){return this.c},d.tk=function(){return this.i},d.Vn=function(){return this.b},d.Wn=function(){return this.v},d.Xn=function(){return this.j},d.Yn=function(){return this.a},d.Zn=function(){return this.f},d.Ka=function(){return this.H},d.Dk=function(){return this.l},d.Ek=function(){return this.o},d.si=function(t){this.g=t},d.yi=function(t){this.c=t},d.zi=function(t){this.i=t},d.Sh=function(t){this.b=t},d.$n=function(t){this.j=t},d.Th=function(t){this.a=t},d.Uh=function(t){this.f=t},d.Vh=function(t){this.H=t},d.Bi=function(t){this.l=t},d.lp=function(t){this.o=t},e(sg,my);var pg=["http://www.google.com/kml/ext/2.2"],ag=[null,"http://earth.google.com/kml/2.0","http://earth.google.com/kml/2.1","http://earth.google.com/kml/2.2","http://www.opengis.net/kml/2.2"],hg={fraction:tg,pixels:"pixels"};function lg(t,e){var o,i=[0,0],r="start";return t.a&&2==(o=null===(o=t.a.de())?zf:o).length&&(r=t.a.i,i[0]=r*o[0]/2,i[1]=-r*o[1]/2,r="left"),null!==t.Ka()?((o=(t=t.Ka()).clone()).si(t.g||$f.g),o.Th(t.a||$f.a),o.Sh(t.b||$f.b),o.Uh(t.f||_f)):o=$f.clone(),o.Vh(e),o.yi(i[0]),o.zi(i[1]),o.Bi(r),new gp({text:o})}function ug(i,r,n,s,p){return function(){var t,e=p,o="";return(e=e&&this.V()?"Point"===this.V().Y():e)&&(o=this.get("name"),e=e&&o),i?e?(e=lg(i[0],o),i.concat(e)):i:r?(t=function t(e,o,i){return Array.isArray(e)?e:"string"==typeof e?(!(e in i)&&"#"+e in i&&(e="#"+e),t(i[e],o,i)):o}(r,n,s),e?(e=lg(t[0],o),t.concat(e)):t):e?(e=lg(n[0],o),n.concat(e)):n}}function cg(t){if(t=oc(t,!1),t=/^\s*#?\s*([0-9A-Fa-f]{8})\s*$/.exec(t))return t=t[1],[parseInt(t.substr(6,2),16),parseInt(t.substr(4,2),16),parseInt(t.substr(2,2),16),parseInt(t.substr(0,2),16)/255]}function yg(t){t=oc(t,!1);for(var e,o=[],i=/^\s*([+\-]?\d*\.?\d+(?:e[+\-]?\d+)?)\s*,\s*([+\-]?\d*\.?\d+(?:e[+\-]?\d+)?)(?:\s*,\s*([+\-]?\d*\.?\d+(?:e[+\-]?\d+)?))?\s*/i;e=i.exec(t);)o.push(parseFloat(e[1]),parseFloat(e[2]),e[3]?parseFloat(e[3]):0),t=t.substr(e[0].length);return""!==t?void 0:o}function fg(t){var e=oc(t,!1).trim();return t.baseURI?new URL(e,t.baseURI).href:e}function gg(t){return Ey(t)}function dg(t,e){return g(null,Ig,t,e)}function vg(t,e){if(i=g({B:[],Mi:[]},kg,t,e)){for(var o=i.B,i=i.Mi,r=0,n=Math.min(o.length,i.length);r<n;++r)o[4*r+3]=i[r];return(i=new S(null)).da("XYZM",o),i}}function bg(t,e){var o=g({},Ng,t,e),t=g(null,Og,t,e);if(t)return(e=new S(null)).da("XYZ",t),e.I(o),e}function mg(t,e){var o=g({},Ng,t,e),t=g(null,Og,t,e);if(t)return(e=new w(null)).da("XYZ",t,[t.length]),e.I(o),e}function wg(t,e){var o=g([],Xg,t,e);if(!o)return null;if(0===o.length)return new ly(o);for(var i,r=!0,n=o[0].Y(),s=1,p=o.length;s<p;++s)if(o[s].Y()!=n){r=!1;break}if(r)if("Point"==n){for(r=(i=o[0]).ka,n=i.ia(),s=1,p=o.length;s<p;++s)jt(n,o[s].ia());(i=new P(null)).da(r,n),Pg(i,o)}else"LineString"==n?(jc(i=new M(null),o),Pg(i,o)):"Polygon"==n?(Ic(i=new y(null),o),Pg(i,o)):"GeometryCollection"==n?i=new ly(o):V(!1,37);else i=new ly(o);return i}function xg(t,e){var o=g({},Ng,t,e),t=g(null,Og,t,e);if(t)return(e=new m(null)).da("XYZ",t),e.I(o),e}function Sg(t,e){var o=g({},Ng,t,e),i=g([null],Fg,t,e);if(i&&i[0]){for(var t=new w(null),r=i[0],n=[r.length],s=1,p=i.length;s<p;++s)jt(r,i[s]),n.push(r.length);return t.da("XYZ",r,n),t.I(o),t}}function Mg(t,e){var o,i,r,t=g({},_g,t,e);return t?(e="fillStyle"in t?t.fillStyle:Kf,void 0===(o=t.fill)||o||(e=null),(o="imageStyle"in t?t.imageStyle:Zf)==qf&&(o=void 0),i="textStyle"in t?t.textStyle:$f,r="strokeStyle"in t?t.strokeStyle:Jf,[new gp({fill:e,image:o,stroke:r=void 0===(t=t.outline)||t?r:null,text:i,zIndex:void 0})]):null}function Pg(t,e){for(var o,i,r=e.length,n=Array(e.length),s=Array(e.length),p=i=!1,a=0;a<r;++a)o=e[a],n[a]=o.get("extrude"),s[a]=o.get("altitudeMode"),p=p||void 0!==n[a],i=i||s[a];p&&t.set("extrude",n),i&&t.set("altitudeMode",s)}function Tg(t,e){fc(Cg,t,e)}function Ag(t,e){fc(jg,t,e)}var Eg=u(ag,{displayName:r(n),value:r(n)}),Cg=u(ag,{Data:function(t,e){var o=t.getAttribute("name"),t=(fc(Eg,t,e),e[e.length-1]);null!==o?t[o]=t.value:null!==t.displayName&&(t[t.displayName]=t.value)},SchemaData:function(t,e){fc(Jg,t,e)}}),jg=u(ag,{LatLonAltBox:function(t,e){t=g({},Lg,t,e);t&&((e=e[e.length-1]).extent=[parseFloat(t.west),parseFloat(t.south),parseFloat(t.east),parseFloat(t.north)],e.altitudeMode=t.altitudeMode,e.minAltitude=parseFloat(t.minAltitude),e.maxAltitude=parseFloat(t.maxAltitude))},Lod:function(t,e){t=g({},Rg,t,e);t&&((e=e[e.length-1]).minLodPixels=parseFloat(t.minLodPixels),e.maxLodPixels=parseFloat(t.maxLodPixels),e.minFadeExtent=parseFloat(t.minFadeExtent),e.maxFadeExtent=parseFloat(t.maxFadeExtent))}}),Lg=u(ag,{altitudeMode:r(n),minAltitude:r(Ey),maxAltitude:r(Ey),north:r(Ey),south:r(Ey),east:r(Ey),west:r(Ey)}),Rg=u(ag,{minLodPixels:r(Ey),maxLodPixels:r(Ey),minFadeExtent:r(Ey),maxFadeExtent:r(Ey)}),Ng=u(ag,{extrude:r(Py),altitudeMode:r(n)}),Ig=u(ag,{coordinates:ac(yg)}),Fg=u(ag,{innerBoundaryIs:function(t,e){t=g(void 0,Ug,t,e);t&&e[e.length-1].push(t)},outerBoundaryIs:function(t,e){t=g(void 0,Hg,t,e);t&&(e[e.length-1][0]=t)}}),kg=u(ag,{when:function(t,e){e=e[e.length-1].Mi,t=oc(t,!1),t=Date.parse(t);e.push(isNaN(t)?0:t)}},u(pg,{coord:function(t,e){e=e[e.length-1].B,t=oc(t,!1);(t=/^\s*([+\-]?\d+(?:\.\d*)?(?:e[+\-]?\d*)?)\s+([+\-]?\d+(?:\.\d*)?(?:e[+\-]?\d*)?)\s+([+\-]?\d+(?:\.\d*)?(?:e[+\-]?\d*)?)\s*$/i.exec(t))?e.push(parseFloat(t[1]),parseFloat(t[2]),parseFloat(t[3]),0):e.push(0,0,0,0)}})),Og=u(ag,{coordinates:ac(yg)}),Dg=u(ag,{href:r(fg)},u(pg,{x:r(Ey),y:r(Ey),w:r(Ey),h:r(Ey)})),Bg=u(ag,{Icon:r(function(t,e){t=g({},Dg,t,e);return t||null}),heading:r(Ey),hotSpot:r(function(t){var e=t.getAttribute("xunits"),o=t.getAttribute("yunits");return{x:parseFloat(t.getAttribute("x")),jg:hg[e],y:parseFloat(t.getAttribute("y")),kg:hg[o]}}),scale:r(gg)}),Ug=u(ag,{LinearRing:ac(dg)}),Gg=u(ag,{color:r(cg),scale:r(gg)}),Kg=u(ag,{color:r(cg),width:r(Ey)}),Xg=u(ag,{LineString:pc(bg),LinearRing:pc(mg),MultiGeometry:pc(wg),Point:pc(xg),Polygon:pc(Sg)}),Vg=u(pg,{Track:pc(vg)}),Wg=u(ag,{ExtendedData:Tg,Region:Ag,Link:function(t,e){fc(zg,t,e)},address:r(n),description:r(n),name:r(n),open:r(Py),phoneNumber:r(n),visibility:r(Py)}),zg=u(ag,{href:r(fg)}),Hg=u(ag,{LinearRing:ac(dg)}),Yg=u(ag,{Style:r(Mg),key:r(n),styleUrl:r(fg)}),Zg=u(ag,{ExtendedData:Tg,Region:Ag,MultiGeometry:r(wg,"geometry"),LineString:r(bg,"geometry"),LinearRing:r(mg,"geometry"),Point:r(xg,"geometry"),Polygon:r(Sg,"geometry"),Style:r(Mg),StyleMap:function(t,e){t=g(void 0,$g,t,e);t&&(e=e[e.length-1],Array.isArray(t)?e.Style=t:"string"==typeof t?e.styleUrl=t:V(!1,38))},address:r(n),description:r(n),name:r(n),open:r(Py),phoneNumber:r(n),styleUrl:r(fg),visibility:r(Py)},u(pg,{MultiTrack:r(function(t,e){t=g([],Vg,t,e);if(t)return jc(e=new M(null),t),e},"geometry"),Track:r(vg,"geometry")})),qg=u(ag,{color:r(cg),fill:r(Py),outline:r(Py)}),Jg=u(ag,{SimpleData:function(t,e){var o=t.getAttribute("name");null!==o&&(t=n(t),e[e.length-1][o]=t)}}),_g=u(ag,{IconStyle:function(t,e){var o,i,r,n,s,p,a,h,l,u,c,t=g({},Bg,t,e);t&&(e=e[e.length-1],c="Icon"in t?t.Icon:{},o=!("Icon"in t)||0<Object.keys(c).length,(l=c.href)?i=l:o&&(i=Hf),(l=t.hotSpot)?(r=[l.x,l.y],n=l.jg,s=l.kg):i===Hf?(r=Xf,n=Vf,s=Wf):/^http:\/\/maps\.(?:google|gstatic)\.com\//.test(i)&&(r=[.5,0],s=n=tg),l=c.x,a=c.y,void 0!==l&&void 0!==a&&(p=[l,a]),l=c.w,c=c.h,void 0!==l&&void 0!==c&&(h=[l,c]),void 0!==(c=t.heading)&&(u=Z(c)),t=t.scale,o?(i==Hf&&(h=zf,void 0===t)&&(t=Yf),o=new Bf({anchor:r,anchorOrigin:eg,anchorXUnits:n,anchorYUnits:s,crossOrigin:"anonymous",offset:p,offsetOrigin:eg,rotation:u,scale:t,size:h,src:i}),e.imageStyle=o):e.imageStyle=qf)},LabelStyle:function(t,e){t=g({},Gg,t,e);t&&(e[e.length-1].textStyle=new ng({fill:new yp({color:"color"in t?t.color:Gf}),scale:t.scale}))},LineStyle:function(t,e){t=g({},Kg,t,e);t&&(e[e.length-1].strokeStyle=new fp({color:"color"in t?t.color:Gf,width:"width"in t?t.width:1}))},PolyStyle:function(t,e){var o,t=g({},qg,t,e);t&&((e=e[e.length-1]).fillStyle=new yp({color:"color"in t?t.color:Gf}),void 0!==(o=t.fill)&&(e.fill=o),void 0!==(t=t.outline))&&(e.outline=t)}}),$g=u(ag,{Pair:function(t,e){var o,t=g({},Yg,t,e);t&&(o=t.key)&&"normal"==o&&((o=t.styleUrl)&&(e[e.length-1]=o),t=t.Style)&&(e[e.length-1]=t)}});function Qg(t,e){for(var o=e.firstChild;o;o=o.nextSibling)if(o.nodeType==Node.ELEMENT_NODE){var i=td(t,o);if(i)return i}}function td(t,e){for(var o=e.firstElementChild;o;o=o.nextElementSibling)if(Et(ag,o.namespaceURI)&&"name"==o.localName)return n(o);for(o=e.firstElementChild;o;o=o.nextElementSibling){var i=o.localName;if(Et(ag,o.namespaceURI)&&("Document"==i||"Folder"==i||"Placemark"==i||"kml"==i)&&(i=td(t,o)))return i}}function ed(t,e){for(var o=[],i=e.firstChild;i;i=i.nextSibling)i.nodeType==Node.ELEMENT_NODE&&jt(o,od(t,i));return o}function od(t,e){for(var o,i=[],r=e.firstElementChild;r;r=r.nextElementSibling)Et(ag,r.namespaceURI)&&"NetworkLink"==r.localName&&(o=g({},Wg,r,[]),i.push(o));for(r=e.firstElementChild;r;r=r.nextElementSibling)o=r.localName,!Et(ag,r.namespaceURI)||"Document"!=o&&"Folder"!=o&&"kml"!=o||jt(i,od(t,r));return i}function id(t,e){for(var o=[],i=e.firstChild;i;i=i.nextSibling)i.nodeType==Node.ELEMENT_NODE&&jt(o,t.Ie(i));return o}function rd(t,e){for(var o=[255*(4==(o=Pi(e)).length?o[3]:1),o[2],o[1],o[0]],i=0;i<4;++i){var r=parseInt(o[i],10).toString(16);o[i]=1==r.length?"0"+r:r}Fy(t,o.join(""))}function nd(t,e,o){t={node:t};var i,r,n=e.Y();"GeometryCollection"==n?(i=e.pf(),r=kd):"MultiPoint"==n?(i=e.re(),r=zd):"MultiLineString"==n?(i=e.Yc(),r=Hd):"MultiPolygon"==n?(i=e.Ad(),r=Zd):V(!1,39),dc(t,Td,r,i,o)}function sd(t,e,o){dc({node:t},Pd,Yd,[e],o)}function pd(t,e,o){var i,r={node:t},n=(e.f&&t.setAttribute("id",e.f),t=e.R(),{address:1,description:1,name:1,open:1,phoneNumber:1,styleUrl:1,visibility:1}),s=(n[e.a]=1,Object.keys(t||{}).sort().filter(function(t){return!n[t]}));0<s.length&&(i=yc(t,s),dc(r,Ed,Vd,[{names:s,values:i}],o)),(s=(s=e.Gc())&&s.call(e,0))&&(s=Array.isArray(s)?s[0]:s,this.l&&(t.Style=s),s=s.Ka())&&(t.name=s.Ka()),t=yc(t,s=Ad[o[o.length-1].node.namespaceURI]),dc(r,Ed,cc,t,o,s),t=o[0],e=(e=e.V())&&Sc(e,!0,t),dc(r,Ed,kd,[e],o)}function ad(t,e,o){var i=e.ia();(t={node:t}).layout=e.ka,t.stride=e.pa(),dc(t,Cd,Kd,[i],o)}function hd(t,e,o){var i=(e=e.Zc()).shift();dc(t={node:t},jd,Wd,e,o),dc(t,jd,qd,[i],o)}function ld(t,e){Ny(t,Math.round(1e6*e)/1e6)}(d=sg.prototype).Qf=function(t,e){if(t=g([],u(ag,{Document:sc(this.Qf,this),Folder:sc(this.Qf,this),Placemark:pc(this.Wf,this),Style:this.Qo.bind(this),StyleMap:this.Po.bind(this)}),t,e,this))return t},d.Wf=function(t,e){var o,i=g({geometry:null},Zg,t,e);if(i)return o=new x,null!==(t=t.getAttribute("id"))&&o.cc(t),t=e[0],(e=i.geometry)&&Sc(e,!1,t),o.Pa(e),delete i.geometry,this.c&&o.Df(ug(i.Style,i.styleUrl,this.g,this.b,this.i)),delete i.Style,o.I(i),o},d.Qo=function(t,e){var o=t.getAttribute("id");null!==o&&(e=Mg(t,e))&&(o=t.baseURI?new URL("#"+o,t.baseURI).href:"#"+o,this.b[o]=e)},d.Po=function(t,e){var o=t.getAttribute("id");null!==o&&(e=g(void 0,$g,t,e))&&(o=t.baseURI?new URL("#"+o,t.baseURI).href:"#"+o,this.b[o]=e)},d.ai=function(t,e){return Et(ag,t.namespaceURI)&&this.Wf(t,[wc(this,t,e)])||null},d.oc=function(t,e){if(!Et(ag,t.namespaceURI))return[];if("Document"==(o=t.localName)||"Folder"==o)return(o=this.Qf(t,[wc(this,t,e)]))||[];if("Placemark"==o)return(o=this.Wf(t,[wc(this,t,e)]))?[o]:[];if("kml"!=o)return[];for(var o=[],i=t.firstElementChild;i;i=i.nextElementSibling){var r=this.oc(i,e);r&&jt(o,r)}return o},d.Jo=function(t){return ic(t)?Qg(this,t):rc(t)?td(this,t):"string"==typeof t?Qg(this,t=nc(t)):void 0},d.Ko=function(t){var e=[];return ic(t)?jt(e,ed(this,t)):rc(t)?jt(e,od(this,t)):"string"==typeof t&&jt(e,ed(this,t=nc(t))),e},d.No=function(t){var e=[];return ic(t)?jt(e,id(this,t)):rc(t)?jt(e,this.Ie(t)):"string"==typeof t&&jt(e,id(this,t=nc(t))),e},d.Ie=function(t){for(var e,o=[],i=t.firstElementChild;i;i=i.nextElementSibling)Et(ag,i.namespaceURI)&&"Region"==i.localName&&(e=g({},jg,i,[]),o.push(e));for(i=t.firstElementChild;i;i=i.nextElementSibling)t=i.localName,!Et(ag,i.namespaceURI)||"Document"!=t&&"Folder"!=t&&"kml"!=t||jt(o,this.Ie(i));return o};var ud=u(ag,["Document","Placemark"]),cd=u(ag,{Document:l(function(t,e,o){dc({node:t},yd,Fd,e,o,void 0,this)}),Placemark:l(pd)}),yd=u(ag,{Placemark:l(pd)}),fd=u(ag,{Data:l(function(t,e,o){t.setAttribute("name",e.name),t={node:t},"object"==typeof(e=e.value)?(null!==e&&e.displayName&&dc(t,fd,cc,[e.displayName],o,["displayName"]),null!==e&&e.value&&dc(t,fd,cc,[e.value],o,["value"])):dc(t,fd,cc,[e],o,["value"])}),value:l(function(t,e){Fy(t,e)}),displayName:l(function(t,e){t.appendChild(tc.createCDATASection(e))})}),gd={Point:"Point",LineString:"LineString",LinearRing:"LinearRing",Polygon:"Polygon",MultiPoint:"MultiGeometry",MultiLineString:"MultiGeometry",MultiPolygon:"MultiGeometry",GeometryCollection:"MultiGeometry"},dd=u(ag,["href"],u(pg,["x","y","w","h"])),vd=u(ag,{href:l(Fy)},u(pg,{x:l(Ny),y:l(Ny),w:l(Ny),h:l(Ny)})),bd=u(ag,["scale","heading","Icon","hotSpot"]),md=u(ag,{Icon:l(function(t,e,o){t={node:t};var i=dd[o[o.length-1].node.namespaceURI],r=yc(e,i);dc(t,vd,cc,r,o,i),r=yc(e,i=dd[pg[0]]),dc(t,vd,Id,r,o,i)}),heading:l(Ny),hotSpot:l(function(t,e){t.setAttribute("x",e.x),t.setAttribute("y",e.y),t.setAttribute("xunits",e.jg),t.setAttribute("yunits",e.kg)}),scale:l(ld)}),wd=u(ag,["color","scale"]),xd=u(ag,{color:l(rd),scale:l(ld)}),Sd=u(ag,["color","width"]),Md=u(ag,{color:l(rd),width:l(Ny)}),Pd=u(ag,{LinearRing:l(ad)}),Td=u(ag,{LineString:l(ad),Point:l(ad),Polygon:l(hd),GeometryCollection:l(nd)}),Ad=u(ag,"name open visibility address phoneNumber description styleUrl Style".split(" ")),Ed=u(ag,{ExtendedData:l(function(t,e,o){t={node:t};var i=e.names;e=e.values;for(var r=i.length,n=0;n<r;n++)dc(t,fd,Xd,[{name:i[n],value:e[n]}],o)}),MultiGeometry:l(nd),LineString:l(ad),LinearRing:l(ad),Point:l(ad),Polygon:l(hd),Style:l(function(t,e,o){t={node:t};var i={},r=e.f,n=e.g,s=e.a;e=e.Ka(),s instanceof Bf&&(i.IconStyle=s),e&&(i.LabelStyle=e),n&&(i.LineStyle=n),r&&(i.PolyStyle=r),i=yc(i,e=Rd[o[o.length-1].node.namespaceURI]),dc(t,Nd,cc,i,o,e)}),address:l(Fy),description:l(Fy),name:l(Fy),open:l(Ry),phoneNumber:l(Fy),styleUrl:l(Fy),visibility:l(Ry)}),Cd=u(ag,{coordinates:l(function(t,e,o){var i,r=(o=o[o.length-1]).layout;o=o.stride,"XY"==r||"XYM"==r?i=2:"XYZ"==r||"XYZM"==r?i=3:V(!1,34);var n,s=e.length,p="";if(0<s){for(p+=e[0],r=1;r<i;++r)p+=","+e[r];for(n=o;n<s;n+=o)for(p+=" "+e[n],r=1;r<i;++r)p+=","+e[n+r]}Fy(t,p)})}),jd=u(ag,{outerBoundaryIs:l(sd),innerBoundaryIs:l(sd)}),Ld=u(ag,{color:l(rd)}),Rd=u(ag,["IconStyle","LabelStyle","LineStyle","PolyStyle"]),Nd=u(ag,{IconStyle:l(function(t,e,o){t={node:t};var i,r,n={},s=e.ac(),p=e.de(),a={href:e.b.o};s&&(a.w=s[0],a.h=s[1],i=e.Ac(),(r=e.Jc())&&p&&0!==r[0]&&r[1]!==s[1]&&(a.x=r[0],a.y=p[1]-(r[1]+s[1])),i)&&0!==i[0]&&i[1]!==s[1]&&(n.hotSpot={x:i[0],jg:"pixels",y:s[1]-i[1],kg:"pixels"}),n.Icon=a,1!==(s=e.i)&&(n.scale=s),0!==(e=e.o)&&(n.heading=e),n=yc(n,e=bd[o[o.length-1].node.namespaceURI]),dc(t,md,cc,n,o,e)}),LabelStyle:l(function(t,e,o){t={node:t};var i={},r=e.b;r&&(i.color=r.b),(e=e.a)&&1!==e&&(i.scale=e),i=yc(i,e=wd[o[o.length-1].node.namespaceURI]),dc(t,xd,cc,i,o,e)}),LineStyle:l(function(t,e,o){t={node:t};var i=Sd[o[o.length-1].node.namespaceURI];e=yc({color:e.b,width:e.f},i),dc(t,Md,cc,e,o,i)}),PolyStyle:l(function(t,e,o){dc({node:t},Ld,Gd,[e.b],o)})});function Id(t,e,o){return ec(pg[0],"gx:"+o)}function Fd(t,e){return ec(e[e.length-1].node.namespaceURI,"Placemark")}function kd(t,e){if(t)return ec(e[e.length-1].node.namespaceURI,gd[t.Y()])}var Od,Dd,Bd,Ud,Gd=uc("color"),Kd=uc("coordinates"),Xd=uc("Data"),Vd=uc("ExtendedData"),Wd=uc("innerBoundaryIs"),zd=uc("Point"),Hd=uc("LineString"),Yd=uc("LinearRing"),Zd=uc("Polygon"),qd=uc("outerBoundaryIs");function Jd(t,e,o,i){this.g=t,this.b=e,this.c=o,this.f=i}function _d(t){mc.call(this),t=t||{},this.defaultDataProjection=new Oe({code:"",units:"tile-pixels"}),this.b=t.featureClass||Jd,this.g=t.geometryName||"geometry",this.a=t.layerName||"layer",this.f=t.layers||null}function $d(t,e,o){for(var i=0,r=0,n=t.length;r<n;++r){for(var s=t[r],p=0,a=s.length;p<a;++p){var h=s[p];e.push(h.x,h.y)}i+=2*p,o.push(i)}}function Qd(){my.call(this),this.defaultDataProjection=Ye("EPSG:4326")}function t0(t,e){e[e.length-1].Nd[t.getAttribute("k")]=t.getAttribute("v")}sg.prototype.a=function(t,e){e=xc(this,e);var o=ec(ag[4],"kml"),i=(o.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:gx",pg[0]),o.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance"),o.setAttributeNS("http://www.w3.org/2001/XMLSchema-instance","xsi:schemaLocation","http://www.opengis.net/kml/2.2 https://developers.google.com/kml/schema/kml22gx.xsd"),{node:o}),r={},t=(1<t.length?r.Document=t:1==t.length&&(r.Placemark=t[0]),ud[o.namespaceURI]),r=yc(r,t);return dc(i,cd,cc,r,[e],t,this),o},Dd={ma:Od={}},function(t){"object"==typeof Od&&void 0!==Dd?Dd.ma=t():("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Rp=t()}(function(){return function i(r,n,s){function p(o,t){if(!n[o]){if(!r[o]){var e="function"==typeof require&&require;if(!t&&e)return e(o,!0);if(a)return a(o,!0);throw(e=Error("Cannot find module '"+o+"'")).code="MODULE_NOT_FOUND",e}e=n[o]={ma:{}},r[o][0].call(e.ma,function(t){var e=r[o][1][t];return p(e||t)},e,e.ma,i,r,n,s)}return n[o].ma}for(var a="function"==typeof require&&require,t=0;t<s.length;t++)p(s[t]);return p}({1:[function(t,e,o){o.read=function(t,e,o,i,r){var n=8*r-i-1,s=(1<<n)-1,p=s>>1,a=-7,h=o?-1:1,l=t[e+(r=o?r-1:0)];for(r+=h,o=l&(1<<-a)-1,l>>=-a,a+=n;0<a;o=256*o+t[e+r],r+=h,a-=8);for(n=o&(1<<-a)-1,o>>=-a,a+=i;0<a;n=256*n+t[e+r],r+=h,a-=8);if(0===o)o=1-p;else{if(o===s)return n?NaN:1/0*(l?-1:1);n+=Math.pow(2,i),o-=p}return(l?-1:1)*n*Math.pow(2,o-i)},o.write=function(t,e,o,i,r,n){var s,p=8*n-r-1,a=(1<<p)-1,h=a>>1,l=23===r?Math.pow(2,-24)-Math.pow(2,-77):0,u=(n=i?0:n-1,i?1:-1),c=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||1/0===e?(e=isNaN(e)?1:0,i=a):(i=Math.floor(Math.log(e)/Math.LN2),e*(s=Math.pow(2,-i))<1&&(i--,s*=2),2<=(e=1<=i+h?e+l/s:e+l*Math.pow(2,1-h))*s&&(i++,s/=2),a<=i+h?(e=0,i=a):1<=i+h?(e=(e*s-1)*Math.pow(2,r),i+=h):(e=e*Math.pow(2,h-1)*Math.pow(2,r),i=0));8<=r;t[o+n]=255&e,n+=u,e/=256,r-=8);for(i=i<<r|e,p+=r;0<p;t[o+n]=255&i,n+=u,i/=256,p-=8);t[o+n-u]|=128*c}},{}],2:[function(t,e){function o(t){this.gc=ArrayBuffer.isView&&ArrayBuffer.isView(t)?t:new Uint8Array(t||0),this.type=this.ha=0,this.length=this.gc.length}function s(t,e,o){return o?4294967296*e+(t>>>0):4294967296*(e>>>0)+(t>>>0)}e.ma=o;var i=t("ieee754");o.f=0,o.g=1,o.b=2,o.a=5,o.prototype={Uf:function(t,e,o){for(o=o||this.length;this.ha<o;){var i=this.Ia(),r=i>>3,n=this.ha;this.type=7&i,t(r,e,this),this.ha===n&&this.qp(i)}return e},Fo:function(){var t=i.read(this.gc,this.ha,!0,23,4);return this.ha+=4,t},Bo:function(){var t=i.read(this.gc,this.ha,!0,52,8);return this.ha+=8,t},Ia:function(t){var e=this.gc,o=e[this.ha++],i=127&o;if(o<128)return i;if(i|=(127&(o=e[this.ha++]))<<7,o<128)return i;if(i|=(127&(o=e[this.ha++]))<<14,o<128)return i;if(i|=(127&(o=e[this.ha++]))<<21,o<128)return i;var o=i|(15&e[this.ha])<<28,i=t,e=this,t=e.gc,r=t[e.ha++],n=(112&r)>>4;if(r<128)return s(o,n,i);if(n|=(127&(r=t[e.ha++]))<<3,r<128)return s(o,n,i);if(n|=(127&(r=t[e.ha++]))<<10,r<128)return s(o,n,i);if(n|=(127&(r=t[e.ha++]))<<17,r<128)return s(o,n,i);if(n|=(127&(r=t[e.ha++]))<<24,r<128)return s(o,n,i);if((r=t[e.ha++])<128)return s(o,n|(1&r)<<31,i);throw Error("Expected varint not more than 10 bytes")},Ro:function(){return this.Ia(!0)},Kd:function(){var t=this.Ia();return 1==t%2?(t+1)/-2:t/2},zo:function(){return!!this.Ia()},Yf:function(){for(var t=this.Ia()+this.ha,e=this.gc,o="",i=this.ha;i<t;){var r,n,s,p=e[i],a=null,h=239<p?4:223<p?3:191<p?2:1;if(t<i+h)break;1===h?p<128&&(a=p):2===h?128==(192&(r=e[i+1]))&&(a=(31&p)<<6|63&r)<=127&&(a=null):3===h?(r=e[i+1],n=e[i+2],128==(192&r)&&128==(192&n)&&((a=(15&p)<<12|(63&r)<<6|63&n)<=2047||55296<=a&&a<=57343)&&(a=null)):4===h&&(r=e[i+1],n=e[i+2],s=e[i+3],128==(192&r))&&128==(192&n)&&128==(192&s)&&((a=(15&p)<<18|(63&r)<<12|(63&n)<<6|63&s)<=65535||1114112<=a)&&(a=null),null===a?(a=65533,h=1):65535<a&&(a-=65536,o+=String.fromCharCode(a>>>10&1023|55296),a=56320|1023&a),o+=String.fromCharCode(a),i+=h}return this.ha=t,o},qp:function(t){if((t&=7)===o.f)for(;127<this.gc[this.ha++];);else if(t===o.b)this.ha=this.Ia()+this.ha;else if(t===o.a)this.ha+=4;else{if(t!==o.g)throw Error("Unimplemented type: "+t);this.ha+=8}}}},{ieee754:1}]},{},[2])(2)}),Lh=Dd.ma,Ud={ma:Bd={}},function(t){"object"==typeof Bd&&void 0!==Ud?Ud.ma=t():("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Up=t()}(function(){return function i(r,n,s){function p(o,t){if(!n[o]){if(!r[o]){var e="function"==typeof require&&require;if(!t&&e)return e(o,!0);if(a)return a(o,!0);throw(e=Error("Cannot find module '"+o+"'")).code="MODULE_NOT_FOUND",e}e=n[o]={ma:{}},r[o][0].call(e.ma,function(t){var e=r[o][1][t];return p(e||t)},e,e.ma,i,r,n,s)}return n[o].ma}for(var a="function"==typeof require&&require,t=0;t<s.length;t++)p(s[t]);return p}({1:[function(t,e){function o(t,e){this.x=t,this.y=e}(e.ma=o).prototype={clone:function(){return new o(this.x,this.y)},add:function(t){return this.clone().oj(t)},rotate:function(t){return this.clone().yj(t)},round:function(){return this.clone().zj()},angle:function(){return Math.atan2(this.y,this.x)},oj:function(t){return this.x+=t.x,this.y+=t.y,this},yj:function(t){var e=Math.cos(t),o=(t=Math.sin(t))*this.x+e*this.y;return this.x=e*this.x-t*this.y,this.y=o,this},zj:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}},o.b=function(t){return!(t instanceof o)&&Array.isArray(t)?new o(t[0],t[1]):t}},{}],2:[function(t,e){e.ma.nj=t("./lib/vectortile.js"),e.ma.Op=t("./lib/vectortilefeature.js"),e.ma.Pp=t("./lib/vectortilelayer.js")},{"./lib/vectortile.js":3,"./lib/vectortilefeature.js":4,"./lib/vectortilelayer.js":5}],3:[function(t,e){function o(t,e,o){3===t&&(t=new i(o,o.Ia()+o.ha)).length&&(e[t.name]=t)}var i=t("./vectortilelayer");e.ma=function(t,e){this.layers=t.Uf(o,{},e)}},{"./vectortilelayer":5}],4:[function(t,e){function o(t,e,o,i,r){this.properties={},this.extent=o,this.type=0,this.rc=t,this.Ye=-1,this.Td=i,this.Vd=r,t.Uf(n,this,e)}function n(t,e,o){if(1==t)e.id=o.Ia();else if(2==t)for(t=o.Ia()+o.ha;o.ha<t;){var i=e.Td[o.Ia()],r=e.Vd[o.Ia()];e.properties[i]=r}else 3==t?e.type=o.Ia():4==t&&(e.Ye=o.ha)}var a=t("point-geometry");(e.ma=o).b=["Unknown","Point","LineString","Polygon"],o.prototype.fh=function(){var t=this.rc;t.ha=this.Ye;for(var e,o=t.Ia()+t.ha,i=1,r=0,n=0,s=0,p=[];t.ha<o;)if(r||(i=7&(r=t.Ia()),r>>=3),r--,1===i||2===i)n+=t.Kd(),s+=t.Kd(),1===i&&(e&&p.push(e),e=[]),e.push(new a(n,s));else{if(7!==i)throw Error("unknown command "+i);e&&e.push(e[0].clone())}return e&&p.push(e),p},o.prototype.bbox=function(){var t=this.rc;t.ha=this.Ye;for(var e=t.Ia()+t.ha,o=1,i=0,r=0,n=0,s=1/0,p=-1/0,a=1/0,h=-1/0;t.ha<e;)if(i||(o=7&(i=t.Ia()),i>>=3),i--,1===o||2===o)(r+=t.Kd())<s&&(s=r),p<r&&(p=r),(n+=t.Kd())<a&&(a=n),h<n&&(h=n);else if(7!==o)throw Error("unknown command "+o);return[s,a,p,h]}},{"point-geometry":1}],5:[function(t,e){function o(t,e){this.version=1,this.name=null,this.extent=4096,this.length=0,this.rc=t,this.Td=[],this.Vd=[],this.Sd=[],t.Uf(i,this,e),this.length=this.Sd.length}function i(t,e,o){15===t?e.version=o.Ia():1===t?e.name=o.Yf():5===t?e.extent=o.Ia():2===t?e.Sd.push(o.ha):3===t?e.Td.push(o.Yf()):4===t&&e.Vd.push(function(t){for(var e=null,o=t.Ia()+t.ha;t.ha<o;)e=1==(e=t.Ia()>>3)?t.Yf():2===e?t.Fo():3===e?t.Bo():4===e?t.Ro():5===e?t.Ia():6===e?t.Kd():7===e?t.zo():null;return e}(o))}var r=t("./vectortilefeature.js");(e.ma=o).prototype.feature=function(t){if(t<0||t>=this.Sd.length)throw Error("feature index out of bounds");return this.rc.ha=this.Sd[t],t=this.rc.Ia()+this.rc.ha,new r(this.rc,t,this.extent,this.Td,this.Vd)}},{"./vectortilefeature.js":4}]},{},[2])(2)}),Rh=Ud.ma,(d=Jd.prototype).get=function(t){return this.f[t]},d.Kb=function(){return this.c},d.G=function(){return this.a||(this.a="Point"===this.g?he(this.b):le(this.b,0,this.b.length,2)),this.a},d.Vb=function(){return this.b},d.ia=Jd.prototype.Vb,d.V=function(){return this},d.Tm=function(){return this.f},d.Bd=Jd.prototype.V,d.pa=function(){return 2},d.Gc=G,d.Y=function(){return this.g},e(_d,mc),_d.prototype.Y=function(){return"arraybuffer"},_d.prototype.La=function(t,e){var o,i=this.f,r=new Lh(t),r=new Rh.nj(r),n=[],s=this.b;for(o in r.layers)if(!i||-1!=i.indexOf(o))for(var p,a,h,l,u,c,y,f,g,d,v=0,b=(p=r.layers[o]).length;v<b;++v)s===Jd?(h=o,l=[],u=[],$d(y=(a=p.feature(v)).fh(),u,l),c=void 0,1===(f=a.type)?c=1===y.length?"Point":"MultiPoint":2===f?c=1===y.length?"LineString":"MultiLineString":3===f&&(c="Polygon"),(a=a.properties)[this.a]=h,h=new this.b(c,u,l,a)):(y=p.feature(v),a=o,c=e,h=new this.b,l=y.id,(u=y.properties)[this.a]=a,(c=Sc(a=0===(a=y.type)?null:(g=[],d=void $d(y=y.fh(),g,f=[]),1===a?d=new(1===y.length?m:P)(null):2===a?d=new(1===y.length?S:M)(null):3===a&&(d=new w(null)),d.da("XY",g,f),d),!1,xc(this,c)))&&(u[this.g]=c),h.cc(l),h.I(u),h.Nc(this.g)),n.push(h);return n},_d.prototype.Wa=function(){return this.defaultDataProjection},_d.prototype.c=function(t){this.f=t},e(Qd,my);var mr=[null],e0=u(mr,{nd:function(t,e){e[e.length-1].bd.push(t.getAttribute("ref"))},tag:t0}),o0=u(mr,{node:function(t,e){var o=e[0],i=e[e.length-1],r=t.getAttribute("id"),n=[parseFloat(t.getAttribute("lon")),parseFloat(t.getAttribute("lat"))],t=(i.jh[r]=n,g({Nd:{}},i0,t,e));ot(t.Nd)||(Sc(n=new m(n),!1,o),(o=new x(n)).cc(r),o.I(t.Nd),i.features.push(o))},way:function(t,e){for(var o=e[0],i=t.getAttribute("id"),r=g({bd:[],Nd:{}},e0,t,e),n=e[e.length-1],s=[],p=0,a=r.bd.length;p<a;p++)jt(s,n.jh[r.bd[p]]);r.bd[0]==r.bd[r.bd.length-1]?(p=new w(null)).da("XY",s,[s.length]):(p=new S(null)).da("XY",s),Sc(p,!1,o),(o=new x(p)).cc(i),o.I(r.Nd),n.features.push(o)}}),i0=u(mr,{tag:t0});function r0(t){return t.getAttributeNS("http://www.w3.org/1999/xlink","href")}function n0(){}function s0(){}Qd.prototype.oc=function(t,e){e=wc(this,t,e);return"osm"==t.localName&&(e=g({jh:{},features:[]},o0,t,[e])).features?e.features:[]},n0.prototype.read=function(t){return ic(t)?this.a(t):rc(t)?this.b(t):"string"==typeof t?(t=nc(t),this.a(t)):null},e(s0,n0),s0.prototype.a=function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType==Node.ELEMENT_NODE)return this.b(t);return null},s0.prototype.b=function(t){return(t=g({},a0,t,[]))||null};var p0=[null,"http://www.opengis.net/ows/1.1"],a0=u(p0,{ServiceIdentification:r(function(t,e){return g({},w0,t,e)}),ServiceProvider:r(function(t,e){return g({},x0,t,e)}),OperationsMetadata:r(function(t,e){return g({},d0,t,e)})}),h0=u(p0,{DeliveryPoint:r(n),City:r(n),AdministrativeArea:r(n),PostalCode:r(n),Country:r(n),ElectronicMailAddress:r(n)}),l0=u(p0,{Value:hc(n)}),u0=u(p0,{AllowedValues:r(function(t,e){return g({},l0,t,e)})}),c0=u(p0,{Phone:r(function(t,e){return g({},v0,t,e)}),Address:r(function(t,e){return g({},h0,t,e)})}),y0=u(p0,{HTTP:r(function(t,e){return g({},f0,t,e)})}),f0=u(p0,{Get:hc(function(t,e){var o=r0(t);return o?g({href:o},b0,t,e):void 0}),Post:void 0}),g0=u(p0,{DCP:r(function(t,e){return g({},y0,t,e)})}),d0=u(p0,{Operation:function(t,e){var o=t.getAttribute("name"),t=g({},g0,t,e);t&&(e[e.length-1][o]=t)}}),v0=u(p0,{Voice:r(n),Facsimile:r(n)}),b0=u(p0,{Constraint:hc(function(t,e){var o=t.getAttribute("name");return o?g({name:o},u0,t,e):void 0})}),m0=u(p0,{IndividualName:r(n),PositionName:r(n),ContactInfo:r(function(t,e){return g({},c0,t,e)})}),w0=u(p0,{Title:r(n),ServiceTypeVersion:r(n),ServiceType:r(n)}),x0=u(p0,{ProviderName:r(n),ProviderSite:r(r0),ServiceContact:r(function(t,e){return g({},m0,t,e)})});function S0(t,e,o,i){for(var r=void 0!==i?i:[],n=i=0;n<e;){var s=t[n++];for(r[i++]=t[n++],r[i++]=s,s=2;s<o;++s)r[i++]=t[n++]}r.length=i}function M0(t){t=t||{},mc.call(this),this.defaultDataProjection=Ye("EPSG:4326"),this.b=t.factor||1e5,this.a=t.geometryLayout||"XY"}function P0(t,e,o){for(var i,r,n=Array(e),s=0;s<e;++s)n[s]=0;for(i=0,r=t.length;i<r;)for(s=0;s<e;++s,++i){var p=t[i],a=p-n[s];n[s]=p,t[i]=a}return A0(t,o||1e5)}function T0(t,e,o){for(var i,r=Array(e),n=0;n<e;++n)r[n]=0;for(t=E0(t,o||1e5),o=0,i=t.length;o<i;)for(n=0;n<e;++n,++o)r[n]+=t[o],t[o]=r[n];return t}function A0(t,e){for(var o=e||1e5,i=0,r=t.length;i<r;++i)t[i]=Math.round(t[i]*o);for(o=0,i=t.length;o<i;++o)r=t[o],t[o]=r<0?~(r<<1):r<<1;for(o="",i=0,r=t.length;i<r;++i){for(var n=t[i],s="";32<=n;)s+=String.fromCharCode(63+(32|31&n)),n>>=5;o+=s+=String.fromCharCode(n+63)}return o}function E0(t,e){for(var o=e||1e5,i=[],r=0,n=0,s=0,p=t.length;s<p;++s){var a=t.charCodeAt(s)-63,r=r|(31&a)<<n;a<32?(i.push(r),n=r=0):n+=5}for(r=0,n=i.length;r<n;++r)i[r]=1&(s=i[r])?~(s>>1):s>>1;for(r=0,n=i.length;r<n;++r)i[r]/=o;return i}function C0(t){t=t||{},mc.call(this),this.defaultDataProjection=Ye(t.defaultDataProjection||"EPSG:4326")}function j0(t,e){for(var o,i,r=[],n=0,s=t.length;n<s;++n)o=t[n],0<n&&r.pop(),i=0<=o?e[o]:e[~o].slice().reverse(),r.push.apply(r,i);for(o=0,i=r.length;o<i;++o)r[o]=r[o].slice();return r}function L0(t,e,o,i,r){var n=t.type,s=N0[n];return e="Point"===n||"MultiPoint"===n?s(t,o,i):s(t,e),(o=new x).Pa(Sc(e,!1,r)),void 0!==t.id&&o.cc(t.id),t.properties&&o.I(t.properties),o}function R0(t,e,o){t[0]=t[0]*e[0]+o[0],t[1]=t[1]*e[1]+o[1]}e(M0,Af),(d=M0.prototype).Hd=function(t,e){return new x(this.Jd(t,e))},d.Tf=function(t,e){return[this.Hd(t,e)]},d.Jd=function(t,e){var o=ro(this.a),t=T0(t,o,this.b);return S0(t,t.length,o,t),Sc(new S(bo(t,0,t.length,o),this.a),!1,xc(this,e))},d.Re=function(t,e){t=t.V();return t?this.Pd(t,e):(V(!1,40),"")},d.Oi=function(t,e){return this.Re(t[0],e)},d.Pd=function(t,e){e=(t=Sc(t,!0,xc(this,e))).ia(),t=t.pa();return S0(e,e.length,t,e),P0(e,t,this.b)},e(C0,Mc),C0.prototype.Sf=function(t,e){if("Topology"!=t.type)return[];var o=null,i=null,r=(t.transform&&(o=(n=t.transform).scale,i=n.translate),t.arcs);if(n)for(var n=o,s=i,p=0,a=r.length;p<a;++p)for(var h,l=r[p],u=n,c=s,y=0,f=0,g=0,d=l.length;g<d;++g)y+=(h=l[g])[0],f+=h[1],h[0]=y,h[1]=f,R0(h,u,c);for(n=[],p=0,a=(s=et(t.objects)).length;p<a;++p)"GeometryCollection"===s[p].type?(l=s[p],n.push.apply(n,function(t,e,o,i,r){for(var n=[],s=0,p=(t=t.geometries).length;s<p;++s)n[s]=L0(t[s],e,o,i,r);return n}(l,r,o,i,e))):(l=s[p],n.push(L0(l,r,o,i,e)));return n},C0.prototype.Wa=function(){return this.defaultDataProjection};var N0={Point:function(t,e,o){return t=t.coordinates,e&&o&&R0(t,e,o),new m(t)},LineString:function(t,e){return new S(j0(t.arcs,e))},Polygon:function(t,e){for(var o=[],i=0,r=t.arcs.length;i<r;++i)o[i]=j0(t.arcs[i],e);return new w(o)},MultiPoint:function(t,e,o){var i,r;if(t=t.coordinates,e&&o)for(i=0,r=t.length;i<r;++i)R0(t[i],e,o);return new P(t)},MultiLineString:function(t,e){for(var o=[],i=0,r=t.arcs.length;i<r;++i)o[i]=j0(t.arcs[i],e);return new M(o)},MultiPolygon:function(t,e){for(var o,i,r,n,s=[],p=0,a=t.arcs.length;p<a;++p){for(i=[],r=0,n=(o=t.arcs[p]).length;r<n;++r)i[r]=j0(o[r],e);s[p]=i}return new y(s)}};function I0(t){this.i=(t=t||{}).featureType,this.g=t.featureNS,this.b=t.gmlFormat||new f,this.c=t.schemaLocation||"http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd",my.call(this)}function F0(t,e){for(var o=e.firstChild;o;o=o.nextSibling)if(o.nodeType==Node.ELEMENT_NODE)return O0(t,o)}e(I0,my),I0.prototype.oc=function(t,e){var o={featureType:this.i,featureNS:this.g};return Q(o,wc(this,t,e||{})),o=[o],this.b.b["http://www.opengis.net/gml"].featureMember=pc(Sy.prototype.Id),o=(o=g([],this.b.b,t,o,this.b))||[]},I0.prototype.o=function(t){return ic(t)?K0(t):rc(t)?g({},G0,t,[]):"string"==typeof t?K0(t=nc(t)):void 0},I0.prototype.l=function(t){return ic(t)?F0(this,t):rc(t)?O0(this,t):"string"==typeof t?F0(this,t=nc(t)):void 0};var k0={"http://www.opengis.net/gml":{boundedBy:r(Sy.prototype.Ee,"bounds")}};function O0(t,e){var o={},i=Ly(e.getAttribute("numberOfFeatures"));return o.numberOfFeatures=i,g(o,k0,e,[],t.b)}var D0={"http://www.opengis.net/wfs":{totalInserted:r(jy),totalUpdated:r(jy),totalDeleted:r(jy)}},B0={"http://www.opengis.net/ogc":{FeatureId:pc(function(t){return t.getAttribute("fid")})}},U0={"http://www.opengis.net/wfs":{Feature:function(t,e){fc(B0,t,e)}}},G0={"http://www.opengis.net/wfs":{TransactionSummary:r(function(t,e){return g({},D0,t,e)},"transactionSummary"),InsertResults:r(function(t,e){return g([],U0,t,e)},"insertIds")}};function K0(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType==Node.ELEMENT_NODE)return g({},G0,t,[])}var X0={"http://www.opengis.net/wfs":{PropertyName:l(Fy)}};function V0(t,e){var o=ec("http://www.opengis.net/ogc","Filter"),i=ec("http://www.opengis.net/ogc","FeatureId");o.appendChild(i),i.setAttribute("fid",e),t.appendChild(o)}var W0={"http://www.opengis.net/wfs":{Insert:l(function(t,e,o){var i=ec((i=o[o.length-1]).featureNS,i.featureType);t.appendChild(i),f.prototype.Ni(i,e,o)}),Update:l(function(t,e,o){var i=o[o.length-1],r=(V(void 0!==e.f,27),i.featureType),n=i.featurePrefix,s=i.featureNS;if(t.setAttribute("typeName",(n=n||"feature")+":"+r),t.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:"+n,s),void 0!==(r=e.f)){for(var s=[],p=0,a=(n=e.S()).length;p<a;p++){var h=e.get(n[p]);void 0!==h&&s.push({name:n[p],value:h})}dc({node:t,srsName:i.srsName},W0,uc("Property"),s,o),V0(t,r)}}),Delete:l(function(t,e,o){var i=o[o.length-1],r=(V(void 0!==e.f,26),o=i.featureType,i.featurePrefix),i=i.featureNS;t.setAttribute("typeName",(r=r||"feature")+":"+o),t.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:"+r,i),void 0!==(e=e.f)&&V0(t,e)}),Property:l(function(t,e,o){var i=ec("http://www.opengis.net/wfs","Name");t.appendChild(i),Fy(i,e.name),void 0!==e.value&&null!==e.value&&(i=ec("http://www.opengis.net/wfs","Value"),t.appendChild(i),e.value instanceof eo?f.prototype.pd(i,e.value,o):Fy(i,e.value))}),Native:l(function(t,e){e.yp&&t.setAttribute("vendorId",e.yp),void 0!==e.cp&&t.setAttribute("safeToIgnore",e.cp),void 0!==e.value&&Fy(t,e.value)})}};function z0(t,e,o){var i=e.b;dc(t={node:t},J0,uc(i.Nb),[i],o),e=e.a,dc(t,J0,uc(e.Nb),[e],o)}function H0(t,e){void 0!==e.a&&t.setAttribute("matchCase",e.a.toString()),Z0(t,e.b),q0(t,""+e.g)}function Y0(t,e,o){Fy(t=ec("http://www.opengis.net/ogc",t),o),e.appendChild(t)}function Z0(t,e){Y0("PropertyName",t,e)}function q0(t,e){Y0("Literal",t,e)}var J0={"http://www.opengis.net/wfs":{Query:l(function(t,e,o){var i=o[o.length-1],r=i.featurePrefix,n=i.featureNS,s=i.propertyNames,p=i.srsName;t.setAttribute("typeName",(r?r+":":"")+e),p&&t.setAttribute("srsName",p),n&&t.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:"+r,n),(e=Q({},i)).node=t,dc(e,X0,uc("PropertyName"),s,o),(i=i.filter)&&(s=ec("http://www.opengis.net/ogc","Filter"),t.appendChild(s),dc({node:s},J0,uc(i.Nb),[i],o))})},"http://www.opengis.net/ogc":{And:l(z0),Or:l(z0),Not:l(function(t,e,o){e=e.condition,dc({node:t},J0,uc(e.Nb),[e],o)}),BBOX:l(function(t,e,o){o[o.length-1].srsName=e.srsName,Z0(t,e.geometryName),f.prototype.pd(t,e.extent,o)}),Intersects:l(function(t,e,o){o[o.length-1].srsName=e.srsName,Z0(t,e.geometryName),f.prototype.pd(t,e.geometry,o)}),Within:l(function(t,e,o){o[o.length-1].srsName=e.srsName,Z0(t,e.geometryName),f.prototype.pd(t,e.geometry,o)}),PropertyIsEqualTo:l(H0),PropertyIsNotEqualTo:l(H0),PropertyIsLessThan:l(H0),PropertyIsLessThanOrEqualTo:l(H0),PropertyIsGreaterThan:l(H0),PropertyIsGreaterThanOrEqualTo:l(H0),PropertyIsNull:l(function(t,e){Z0(t,e.b)}),PropertyIsBetween:l(function(t,e){Z0(t,e.b);var o=ec("http://www.opengis.net/ogc","LowerBoundary");t.appendChild(o),q0(o,""+e.a),o=ec("http://www.opengis.net/ogc","UpperBoundary"),t.appendChild(o),q0(o,""+e.g)}),PropertyIsLike:l(function(t,e){t.setAttribute("wildCard",e.i),t.setAttribute("singleChar",e.c),t.setAttribute("escapeChar",e.g),void 0!==e.a&&t.setAttribute("matchCase",e.a.toString()),Z0(t,e.b),q0(t,""+e.f)})}};function _0(t){t=t||{},mc.call(this),this.b=void 0!==t.splitCollection&&t.splitCollection}function $0(t){return 0===(t=t.$()).length?"":t.join(" ")}function Q0(t){for(var e=[],o=0,i=(t=t.$()).length;o<i;++o)e.push(t[o].join(" "));return e.join(",")}function tv(t){for(var e=[],o=0,i=(t=t.Zc()).length;o<i;++o)e.push("("+Q0(t[o])+")");return e.join(",")}function ev(t){var e,o=t.Y(),i=(0,ov[o])(t),o=o.toUpperCase();return t instanceof b&&(e="","XYZ"!==(t=t.ka)&&"XYZM"!==t||(e+="Z"),"XYM"!==t&&"XYZM"!==t||(e+="M"),0<(t=e).length)&&(o+=" "+t),0===i.length?o+" EMPTY":o+"("+i+")"}I0.prototype.v=function(t){var e,o,i=ec("http://www.opengis.net/wfs","GetFeature");return i.setAttribute("service","WFS"),i.setAttribute("version","1.1.0"),t&&(t.handle&&i.setAttribute("handle",t.handle),t.outputFormat&&i.setAttribute("outputFormat",t.outputFormat),void 0!==t.maxFeatures&&i.setAttribute("maxFeatures",t.maxFeatures),t.resultType&&i.setAttribute("resultType",t.resultType),void 0!==t.startIndex&&i.setAttribute("startIndex",t.startIndex),void 0!==t.count&&i.setAttribute("count",t.count),o=t.filter,t.bbox)&&(V(t.geometryName,12),e=hy(t.geometryName,t.bbox,t.srsName),o=o?ay(o,e):e),i.setAttributeNS("http://www.w3.org/2001/XMLSchema-instance","xsi:schemaLocation",this.c),o={node:i,srsName:t.srsName,featureNS:t.featureNS||this.g,featurePrefix:t.featurePrefix,geometryName:t.geometryName,filter:o,propertyNames:t.propertyNames||[]},V(Array.isArray(t.featureTypes),11),t=t.featureTypes,(e=Q({},(o=[o])[o.length-1])).node=i,dc(e,J0,uc("Query"),t,o),i},I0.prototype.A=function(t,e,o,i){var r,n,s=[],p=ec("http://www.opengis.net/wfs","Transaction");return p.setAttribute("service","WFS"),p.setAttribute("version","1.1.0"),i&&(r=i.gmlOptions||{},i.handle)&&p.setAttribute("handle",i.handle),p.setAttributeNS("http://www.w3.org/2001/XMLSchema-instance","xsi:schemaLocation",this.c),t&&(n={node:p,featureNS:i.featureNS,featureType:i.featureType,featurePrefix:i.featurePrefix,srsName:i.srsName},Q(n,r),dc(n,W0,uc("Insert"),t,s)),e&&(n={node:p,featureNS:i.featureNS,featureType:i.featureType,featurePrefix:i.featurePrefix,srsName:i.srsName},Q(n,r),dc(n,W0,uc("Update"),e,s)),o&&dc({node:p,featureNS:i.featureNS,featureType:i.featureType,featurePrefix:i.featurePrefix,srsName:i.srsName},W0,uc("Delete"),o,s),i.nativeElements&&dc({node:p,featureNS:i.featureNS,featureType:i.featureType,featurePrefix:i.featurePrefix,srsName:i.srsName},W0,uc("Native"),i.nativeElements,s),p},I0.prototype.Xf=function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType==Node.ELEMENT_NODE)return this.He(t);return null},I0.prototype.He=function(t){if(t.firstElementChild&&t.firstElementChild.firstElementChild)for(t=(t=t.firstElementChild.firstElementChild).firstElementChild;t;t=t.nextElementSibling){var e;if(0!==t.childNodes.length&&(1!==t.childNodes.length||3!==t.firstChild.nodeType))return this.b.Ee(t,e=[{}]),Ye(e.pop().srsName)}return null},e(_0,Af);var ov={Point:$0,LineString:Q0,Polygon:tv,MultiPoint:function(t){for(var e=[],o=0,i=(t=t.re()).length;o<i;++o)e.push("("+$0(t[o])+")");return e.join(",")},MultiLineString:function(t){for(var e=[],o=0,i=(t=t.Yc()).length;o<i;++o)e.push("("+Q0(t[o])+")");return e.join(",")},MultiPolygon:function(t){for(var e=[],o=0,i=(t=t.Ad()).length;o<i;++o)e.push("("+tv(t[o])+")");return e.join(",")},GeometryCollection:function(t){for(var e=[],o=0,i=(t=t.pf()).length;o<i;++o)e.push(ev(t[o]));return e.join(",")}};function iv(t){this.a=t,this.b=-1}function rv(t){this.g=t,this.a="XY"}function nv(t){t.b=function t(e){var o=e.a.charAt(++e.b),i={position:e.b,value:o};if("("==o)i.type=2;else if(","==o)i.type=5;else if(")"==o)i.type=3;else if("0"<=o&&o<="9"||"."==o||"-"==o){i.type=4;for(var r,o=e.b,n=!1,s=!1;"."==r?n=!0:"e"!=r&&"E"!=r||(s=!0),"0"<=(r=e.a.charAt(++e.b))&&r<="9"||"."==r&&(void 0===n||!n)||!s&&("e"==r||"E"==r)||s&&("-"==r||"+"==r););e=parseFloat(e.a.substring(o,e.b--)),i.value=e}else if("a"<=o&&o<="z"||"A"<=o&&o<="Z"){for(i.type=1,o=e.b;"a"<=(r=e.a.charAt(++e.b))&&r<="z"||"A"<=r&&r<="Z";);e=e.a.substring(o,e.b--).toUpperCase(),i.value=e}else{if(" "==o||"\t"==o||"\r"==o||"\n"==o)return t(e);if(""!==o)throw Error("Unexpected character: "+o);i.type=6}return i}(t.g)}function sv(t,e){e=t.b.type==e;return e&&nv(t),e}function pv(t){for(var e=[],o=t.a.length,i=0;i<o;++i){var r=t.b;if(!sv(t,4))break;e.push(r.value)}if(e.length==o)return e;throw Error(uv(t))}function av(t){for(var e=[pv(t)];sv(t,5);)e.push(pv(t));return e}function hv(t){for(var e=[t.Nf()];sv(t,5);)e.push(t.Nf());return e}function lv(t){var e=1==t.b.type&&"EMPTY"==t.b.value;return e&&nv(t),e}function uv(t){return"Unexpected `"+t.b.value+"` at position "+t.b.position+" in `"+t.g.a+"`"}(d=_0.prototype).Hd=function(t,e){t=this.Jd(t,e);return t?((e=new x).Pa(t),e):null},d.Tf=function(t,e){for(var o=[],i=this.Jd(t,e),r=[],n=0,s=(o=this.b&&"GeometryCollection"==i.Y()?i.f:[i]).length;n<s;++n)(i=new x).Pa(o[n]),r.push(i);return r},d.Jd=function(t,e){t=new rv(new iv(t));return nv(t),(t=function t(e){var o=e.b;if(sv(e,1)){var o=o.value,i="XY",r=e.b;if(1==e.b.type&&("Z"===(r=r.value)?i="XYZ":"M"===r?i="XYM":"ZM"===r&&(i="XYZM"),"XY"!==i)&&nv(e),e.a=i,"GEOMETRYCOLLECTION"==o){t:{if(sv(e,2)){for(o=[];o.push(t(e)),sv(e,5););if(sv(e,3)){e=o;break t}}else if(lv(e)){e=[];break t}throw Error(uv(e))}return new ly(e)}if(r=yv[o],i=cv[o],r&&i)return o=r.call(e),new i(o,e.a);throw Error("Invalid geometry type: "+o)}throw Error(uv(e))}(t))?Sc(t,!1,e):null},d.Re=function(t,e){t=t.V();return t?this.Pd(t,e):""},d.Oi=function(t,e){if(1==t.length)return this.Re(t[0],e);for(var o=[],i=0,r=t.length;i<r;++i)o.push(t[i].V());return o=new ly(o),this.Pd(o,e)},d.Pd=function(t,e){return ev(Sc(t,!0,e))},(d=rv.prototype).Of=function(){if(sv(this,2)){var t=pv(this);if(sv(this,3))return t}else if(lv(this))return null;throw Error(uv(this))},d.Nf=function(){if(sv(this,2)){var t=av(this);if(sv(this,3))return t}else if(lv(this))return[];throw Error(uv(this))},d.Pf=function(){if(sv(this,2)){var t=hv(this);if(sv(this,3))return t}else if(lv(this))return[];throw Error(uv(this))},d.mo=function(){if(sv(this,2)){var t;if(2==this.b.type)for(t=[this.Of()];sv(this,5);)t.push(this.Of());else t=av(this);if(sv(this,3))return t}else if(lv(this))return[];throw Error(uv(this))},d.lo=function(){if(sv(this,2)){var t=hv(this);if(sv(this,3))return t}else if(lv(this))return[];throw Error(uv(this))},d.no=function(){if(sv(this,2)){for(var t=[this.Pf()];sv(this,5);)t.push(this.Pf());if(sv(this,3))return t}else if(lv(this))return[];throw Error(uv(this))};var cv={POINT:m,LINESTRING:S,POLYGON:w,MULTIPOINT:P,MULTILINESTRING:M,MULTIPOLYGON:y},yv={POINT:rv.prototype.Of,LINESTRING:rv.prototype.Nf,POLYGON:rv.prototype.Pf,MULTIPOINT:rv.prototype.mo,MULTILINESTRING:rv.prototype.lo,MULTIPOLYGON:rv.prototype.no};function fv(){this.version=void 0}function gv(t,e){return g({},Fv,t,e)}function dv(t,e){return g({},Lv,t,e)}function vv(t,e){e=gv(t,e);if(e)return t=[Ly(t.getAttribute("width")),Ly(t.getAttribute("height"))],e.size=t,e}function bv(t,e){return g([],kv,t,e)}e(fv,n0),fv.prototype.a=function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType==Node.ELEMENT_NODE)return this.b(t);return null},fv.prototype.b=function(t){return this.version=t.getAttribute("version").trim(),(t=g({version:this.version},mv,t,[]))||null};var wr=[null,"http://www.opengis.net/wms"],mv=u(wr,{Service:r(function(t,e){return g({},xv,t,e)}),Capability:r(function(t,e){return g({},wv,t,e)})}),wv=u(wr,{Request:r(function(t,e){return g({},jv,t,e)}),Exception:r(function(t,e){return g([],Tv,t,e)}),Layer:r(function(t,e){return g({},Av,t,e)})}),xv=u(wr,{Name:r(n),Title:r(n),Abstract:r(n),KeywordList:r(bv),OnlineResource:r(r0),ContactInformation:r(function(t,e){return g({},Sv,t,e)}),Fees:r(n),AccessConstraints:r(n),LayerLimit:r(jy),MaxWidth:r(jy),MaxHeight:r(jy)}),Sv=u(wr,{ContactPersonPrimary:r(function(t,e){return g({},Mv,t,e)}),ContactPosition:r(n),ContactAddress:r(function(t,e){return g({},Pv,t,e)}),ContactVoiceTelephone:r(n),ContactFacsimileTelephone:r(n),ContactElectronicMailAddress:r(n)}),Mv=u(wr,{ContactPerson:r(n),ContactOrganization:r(n)}),Pv=u(wr,{AddressType:r(n),Address:r(n),City:r(n),StateOrProvince:r(n),PostCode:r(n),Country:r(n)}),Tv=u(wr,{Format:pc(n)}),Av=u(wr,{Name:r(n),Title:r(n),Abstract:r(n),KeywordList:r(bv),CRS:hc(n),EX_GeographicBoundingBox:r(function(t,e){var o,i,t=g({},Cv,t,e);if(t)return e=t.westBoundLongitude,o=t.southBoundLatitude,i=t.eastBoundLongitude,t=t.northBoundLatitude,void 0===e||void 0===o||void 0===i||void 0===t?void 0:[e,o,i,t]}),BoundingBox:hc(function(t){var e=[Cy(t.getAttribute("minx")),Cy(t.getAttribute("miny")),Cy(t.getAttribute("maxx")),Cy(t.getAttribute("maxy"))],o=[Cy(t.getAttribute("resx")),Cy(t.getAttribute("resy"))];return{crs:t.getAttribute("CRS"),extent:e,res:o}}),Dimension:hc(function(t){return{name:t.getAttribute("name"),units:t.getAttribute("units"),unitSymbol:t.getAttribute("unitSymbol"),default:t.getAttribute("default"),multipleValues:Ty(t.getAttribute("multipleValues")),nearestValue:Ty(t.getAttribute("nearestValue")),current:Ty(t.getAttribute("current")),values:n(t)}}),Attribution:r(function(t,e){return g({},Ev,t,e)}),AuthorityURL:hc(function(t,e){e=gv(t,e);if(e)return e.name=t.getAttribute("name"),e}),Identifier:hc(n),MetadataURL:hc(function(t,e){e=gv(t,e);if(e)return e.type=t.getAttribute("type"),e}),DataURL:hc(gv),FeatureListURL:hc(gv),Style:hc(function(t,e){return g({},Iv,t,e)}),MinScaleDenominator:r(Ey),MaxScaleDenominator:r(Ey),Layer:hc(function(t,e){var o=e[e.length-1],i=g({},Av,t,e);if(i)return void 0===(e=Ty(t.getAttribute("queryable")))&&(e=o.queryable),i.queryable=void 0!==e&&e,void 0===(e=Ly(t.getAttribute("cascaded")))&&(e=o.cascaded),i.cascaded=e,void 0===(e=Ty(t.getAttribute("opaque")))&&(e=o.opaque),i.opaque=void 0!==e&&e,void 0===(e=Ty(t.getAttribute("noSubsets")))&&(e=o.noSubsets),i.noSubsets=void 0!==e&&e,e=(e=Cy(t.getAttribute("fixedWidth")))||o.fixedWidth,i.fixedWidth=e,e=(e=Cy(t.getAttribute("fixedHeight")))||o.fixedHeight,i.fixedHeight=e,["Style","CRS","AuthorityURL"].forEach(function(t){t in o&&(i[t]=(i[t]||[]).concat(o[t]))}),"EX_GeographicBoundingBox BoundingBox Dimension Attribution MinScaleDenominator MaxScaleDenominator".split(" ").forEach(function(t){t in i||(i[t]=o[t])}),i})}),Ev=u(wr,{Title:r(n),OnlineResource:r(r0),LogoURL:r(vv)}),Cv=u(wr,{westBoundLongitude:r(Ey),eastBoundLongitude:r(Ey),southBoundLatitude:r(Ey),northBoundLatitude:r(Ey)}),jv=u(wr,{GetCapabilities:r(dv),GetMap:r(dv),GetFeatureInfo:r(dv)}),Lv=u(wr,{Format:hc(n),DCPType:hc(function(t,e){return g({},Rv,t,e)})}),Rv=u(wr,{HTTP:r(function(t,e){return g({},Nv,t,e)})}),Nv=u(wr,{Get:r(gv),Post:r(gv)}),Iv=u(wr,{Name:r(n),Title:r(n),Abstract:r(n),LegendURL:hc(vv),StyleSheetURL:r(gv),StyleURL:r(gv)}),Fv=u(wr,{Format:r(n),OnlineResource:r(r0)}),kv=u(wr,{Keyword:pc(n)});function Ov(t){t=t||{},this.g="http://mapserver.gis.umn.edu/mapserver",this.b=new Vy,this.c=t.layers||null,my.call(this)}function Dv(){this.g=new s0}function Bv(t){var e=n(t).split(" ");if(e&&2==e.length)return t=+e[0],e=+e[1],isNaN(t)||isNaN(e)?void 0:[t,e]}e(Ov,my),Ov.prototype.oc=function(t,e){var o={},i=(e&&Q(o,wc(this,t,e)),[o]),e=(t.setAttribute("namespaceURI",this.g),t.localName),o=[];if(0!==t.childNodes.length){if("msGMLOutput"==e)for(var r=0,n=t.childNodes.length;r<n;r++){var s,p,a,h=t.childNodes[r];h.nodeType===Node.ELEMENT_NODE&&(s=i[0],p=h.localName.replace("_layer",""),!this.c||Et(this.c,p))&&(p+="_feature",s.featureType=p,s.featureNS=this.g,(a={})[p]=pc(this.b.Rf,this.b),s=u([s.featureNS,null],a),h.setAttribute("namespaceURI",this.g),h=g([],s,h,i,this.b))&&jt(o,h)}"FeatureCollection"==e&&(i=g([],this.b.b,t,[{}],this.b))&&(o=i)}return o},e(Dv,n0),Dv.prototype.a=function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType==Node.ELEMENT_NODE)return this.b(t);return null},Dv.prototype.b=function(t){var e=t.getAttribute("version").trim(),o=this.g.b(t);return o&&(o.version=e,o=g(o,Uv,t,[]))||null};var sr=[null,"http://www.opengis.net/wmts/1.0"],mr=[null,"http://www.opengis.net/ows/1.1"],Uv=u(sr,{Contents:r(function(t,e){return g({},Gv,t,e)})}),Gv=u(sr,{Layer:hc(function(t,e){return g({},Kv,t,e)}),TileMatrixSet:hc(function(t,e){return g({},Zv,t,e)})}),Kv=u(sr,{Style:hc(function(t,e){e=g({},Xv,t,e);if(e)return t="true"===t.getAttribute("isDefault"),e.isDefault=t,e}),Format:hc(n),TileMatrixSetLink:hc(function(t,e){return g({},Vv,t,e)}),Dimension:hc(function(t,e){return g({},Hv,t,e)}),ResourceURL:hc(function(t){var e=t.getAttribute("format"),o=t.getAttribute("template"),i=(t=t.getAttribute("resourceType"),{});return e&&(i.format=e),o&&(i.template=o),t&&(i.resourceType=t),i})},u(mr,{Title:r(n),Abstract:r(n),WGS84BoundingBox:r(function(t,e){t=g([],Yv,t,e);return 2!=t.length?void 0:Qt(t)}),Identifier:r(n)})),Xv=u(sr,{LegendURL:hc(function(t){var e={};return e.format=t.getAttribute("format"),e.href=r0(t),e})},u(mr,{Title:r(n),Identifier:r(n)})),Vv=u(sr,{TileMatrixSet:r(n),TileMatrixSetLimits:r(function(t,e){return g([],Wv,t,e)})}),Wv=u(sr,{TileMatrixLimits:pc(function(t,e){return g({},zv,t,e)})}),zv=u(sr,{TileMatrix:r(n),MinTileRow:r(jy),MaxTileRow:r(jy),MinTileCol:r(jy),MaxTileCol:r(jy)}),Hv=u(sr,{Default:r(n),Value:hc(n)},u(mr,{Identifier:r(n)})),Yv=u(mr,{LowerCorner:pc(Bv),UpperCorner:pc(Bv)}),Zv=u(sr,{WellKnownScaleSet:r(n),TileMatrix:hc(function(t,e){return g({},qv,t,e)})},u(mr,{SupportedCRS:r(n),Identifier:r(n)})),qv=u(sr,{TopLeftCorner:r(Bv),ScaleDenominator:r(Ey),TileWidth:r(jy),TileHeight:r(jy),MatrixWidth:r(jy),MatrixHeight:r(jy)},u(mr,{Identifier:r(n)}));function Jv(t){wt.call(this),t=t||{},this.a=null,this.c=_e,this.f=void 0,c(this,St(ib),this.Ql,this),c(this,St(nb),this.Rl,this),void 0!==t.projection&&this.nh(Ye(t.projection)),void 0!==t.trackingOptions&&this.Ci(t.trackingOptions),this.ne(void 0!==t.tracking&&t.tracking)}e(Jv,wt),(d=Jv.prototype).oa=function(){this.ne(!1),wt.prototype.oa.call(this)},d.Ql=function(){var t=this.lh();t&&(this.c=Je(Ye("EPSG:4326"),t),this.a)&&this.set(ob,this.c(this.a))},d.Rl=function(){var t;gr&&((t=this.mh())&&void 0===this.f?this.f=navigator.geolocation.watchPosition(this.uo.bind(this),this.vo.bind(this),this.Wg()):t||void 0===this.f||(navigator.geolocation.clearWatch(this.f),this.f=void 0))},d.uo=function(t){t=t.coords,this.set(_v,t.accuracy),this.set(Qv,null===t.altitude?void 0:t.altitude),this.set(tb,null===t.altitudeAccuracy?void 0:t.altitudeAccuracy),this.set(eb,null===t.heading?void 0:Z(t.heading)),this.a?(this.a[0]=t.longitude,this.a[1]=t.latitude):this.a=[t.longitude,t.latitude];var e=this.c(this.a);this.set(ob,e),this.set(rb,null===t.speed?void 0:t.speed),(t=ko(Ss,this.a,t.accuracy)).sc(this.c),this.set($v,t),this.s()},d.vo=function(t){t.type="error",this.ne(!1),this.b(t)},d.Tj=function(){return this.get(_v)},d.Uj=function(){return this.get($v)||null},d.Wj=function(){return this.get(Qv)},d.Xj=function(){return this.get(tb)},d.Ol=function(){return this.get(eb)},d.Pl=function(){return this.get(ob)},d.lh=function(){return this.get(ib)},d.Bk=function(){return this.get(rb)},d.mh=function(){return this.get(nb)},d.Wg=function(){return this.get(sb)},d.nh=function(t){this.set(ib,t)},d.ne=function(t){this.set(nb,t)},d.Ci=function(t){this.set(sb,t)};var _v="accuracy",$v="accuracyGeometry",Qv="altitude",tb="altitudeAccuracy",eb="heading",ob="position",ib="projection",rb="speed",nb="tracking",sb="trackingOptions";function pb(t,e,o){b.call(this),this.dg(t,e||0,o)}function ab(t){var e=t.B[t.a]-t.B[0];return e*e+(t=t.B[t.a+1]-t.B[1])*t}function hb(t,e,o){for(var i,r,n,s,p,a,h=[],l=t(0),u=t(1),c=e(l),y=[u,l],f=[e(u),c],g=[1,0],d={},v=1e5;0<--v&&0<g.length;)s=g.pop(),l=y.pop(),c=f.pop(),(u=s.toString())in d||(h.push(c[0],c[1]),d[u]=!0),p=g.pop(),u=y.pop(),i=f.pop(),H((n=e(r=t(a=(s+p)/2)))[0],n[1],c[0],c[1],i[0],i[1])<o?(h.push(i[0],i[1]),d[u=p.toString()]=!0):(g.push(p,a,a,s),f.push(i,n,n,c),y.push(u,r,r,l));return h}function lb(t){t=t||{},this.c=this.l=null,this.g=this.i=1/0,this.f=this.j=-1/0,this.A=this.u=1/0,this.D=this.C=-1/0,this.va=void 0!==t.targetSize?t.targetSize:100,this.L=void 0!==t.maxLines?t.maxLines:100,this.b=[],this.a=[],this.ra=void 0!==t.strokeStyle?t.strokeStyle:ub,this.H=this.o=void 0,this.v=null,this.setMap(void 0!==t.map?t.map:null)}e(pb,b),(d=pb.prototype).clone=function(){var t=new pb(null);return no(t,this.ka,this.B.slice()),t.s(),t},d.Ab=function(t,e,o,i){var r=this.B,n=(t-=r[0],e-r[1]);if((e=t*t+n*n)<i){if(0===e)for(i=0;i<this.a;++i)o[i]=r[i];else for(i=this.qe()/Math.sqrt(e),o[0]=r[0]+i*t,o[1]=r[1]+i*n,i=2;i<this.a;++i)o[i]=r[i];return o.length=this.a,e}return i},d.Hc=function(t,e){var o=this.B,t=t-o[0];return t*t+(o=e-o[1])*o<=ab(this)},d.Fd=function(){return this.B.slice(0,this.a)},d.Yd=function(t){var e=this.B,o=e[this.a]-e[0];return ae(e[0]-o,e[1]-o,e[0]+o,e[1]+o,t)},d.qe=function(){return Math.sqrt(ab(this))},d.Y=function(){return"Circle"},d.Ta=function(t){var e;return!!Ae(t,this.G())&&(e=this.Fd(),t[0]<=e[0]&&t[2]>=e[0]||t[1]<=e[1]&&t[3]>=e[1]||ge(t,this.mb,this))},d.nm=function(t){var e,o=this.a,i=t.slice();for(i[o]=i[0]+(this.B[o]-this.B[0]),e=1;e<o;++e)i[o+e]=t[e];no(this,this.ka,i),this.s()},d.dg=function(t,e,o){if(t){var i;for(so(this,o,t,0),this.B||(this.B=[]),t=fo(o=this.B,t),o[t++]=o[0]+e,e=1,i=this.a;e<i;++e)o[t++]=o[e];o.length=t}else no(this,"XY",null);this.s()},d.om=function(t){this.B[this.a]=this.B[0]+t,this.s()};var ub=new fp({color:"rgba(0,0,0,0.2)"}),cb=[90,45,30,20,10,5,2,1,.5,.2,.1,.05,.01,.005,.002,.001];function yb(t,e,o,i,r,n,s){var p,a,h,l=s;return p=e,a=o,h=i,o=t.c,i=r,e=hb(function(t){return[p,a+(h-a)*t]},qe(Ye("EPSG:4326"),o),i),(l=void 0!==t.b[l]?t.b[l]:new S(null)).da("XY",e),Ae(l.G(),n)&&(t.b[s++]=l),s}function fb(t,e,o,i,r){var n,s,p,a,h=r;return n=e,s=t.f,p=t.g,a=t.c,o=o,e=hb(function(t){return[s+(p-s)*t,n]},qe(Ye("EPSG:4326"),a),o),(h=void 0!==t.a[h]?t.a[h]:new S(null)).da("XY",e),Ae(h.G(),i)&&(t.a[r++]=h),r}function gb(t,e,o,i,r){Wr.call(this,t,e),this.l=o,this.g=new Image,null!==i&&(this.g.crossOrigin=i),this.i=null,this.o=r}function db(t){t.i.forEach(lt),t.i=null}function vb(t){t=t||{},en.call(this,{handleEvent:Le}),this.i=t.formatConstructors||[],this.l=t.projection?Ye(t.projection):null,this.a=null,this.target=t.target||null}function bb(t){for(var e=0,o=(t=t.dataTransfer.files).length;e<o;++e){var i=t.item(e),r=new FileReader;r.addEventListener("load",this.j.bind(this,i)),r.readAsText(i)}}function mb(t){t.stopPropagation(),t.preventDefault(),t.dataTransfer.dropEffect="copy"}(d=lb.prototype).Sl=function(){return this.l},d.qk=function(){return this.b},d.xk=function(){return this.a},d.ah=function(t){var e,o,i,r,n,s,p,a,h,l,u=t.vectorContext,c=t.frameState,y=c.extent,f=(t=c.viewState).center,g=t.projection,d=t.resolution;for(t=d*d/(4*(t=c.pixelRatio)*t),this.c&&Ze(this.c,g)||(e=Ye("EPSG:4326"),o=g.G(),l=to(i=g.i,e,g),r=i[2],n=i[1],s=i[0],p=l[3],a=l[2],h=l[1],l=l[0],this.i=i[3],this.g=r,this.j=n,this.f=s,this.u=p,this.A=a,this.C=h,this.D=l,this.o=qe(e,g),this.H=qe(g,e),this.v=this.H(me(o)),this.c=g),g.a&&(e=Te(g=g.G()),(c=c.focus[0])<g[0]||c>g[2])&&(c=e*Math.ceil((g[0]-c)/e),y=[y[0]+c,y[1],y[2]+c,y[3]]),c=this.v[0],g=this.v[1],e=-1,i=Math.pow(this.va*d,2),r=[],n=[],d=0,o=cb.length;d<o&&(r[0]=c-(s=cb[d]/2),r[1]=g-s,n[0]=c+s,n[1]=g+s,this.o(r,r),this.o(n,n),!((s=Math.pow(n[0]-r[0],2)+Math.pow(n[1]-r[1],2))<=i));++d)e=cb[d];if(-1==(d=e))this.b.length=this.a.length=0;else{for(f=(c=this.H(f))[0],c=c[1],g=this.L,i=(e=to(e=[Math.max(y[0],this.D),Math.max(y[1],this.C),Math.min(y[2],this.A),Math.min(y[3],this.u)],this.c,"EPSG:4326"))[3],n=e[1],o=yb(this,r=W(f=Math.floor(f/d)*d,this.f,this.g),n,i,t,y,0),e=0;r!=this.f&&e++<g;)o=yb(this,r=Math.max(r-d,this.f),n,i,t,y,o);for(r=W(f,this.f,this.g),e=0;r!=this.g&&e++<g;)o=yb(this,r=Math.min(r+d,this.g),n,i,t,y,o);for(this.b.length=o,o=fb(this,f=W(c=Math.floor(c/d)*d,this.j,this.i),t,y,0),e=0;f!=this.j&&e++<g;)o=fb(this,f=Math.max(f-d,this.j),t,y,o);for(f=W(c,this.j,this.i),e=0;f!=this.i&&e++<g;)o=fb(this,f=Math.min(f+d,this.i),t,y,o);this.a.length=o}for(u.Ma(null,this.ra),t=0,f=this.b.length;t<f;++t)d=this.b[t],u.Pb(d,null);for(t=0,f=this.a.length;t<f;++t)d=this.a[t],u.Pb(d,null)},d.setMap=function(t){this.l&&(this.l.K("postcompose",this.ah,this),this.l.render()),t&&(t.J("postcompose",this.ah,this),t.render()),this.l=t},e(gb,Wr),(d=gb.prototype).oa=function(){1==this.state&&db(this),this.a&&yt(this.a),this.state=5,this.s(),Wr.prototype.oa.call(this)},d.ub=function(){return this.g},d.bb=function(){return this.l},d.Tl=function(){this.state=3,db(this),this.s()},d.Ul=function(){this.state=this.g.naturalWidth&&this.g.naturalHeight?Hr:4,db(this),this.s()},d.load=function(){0!=this.state&&3!=this.state||(this.state=1,this.s(),this.i=[at(this.g,"error",this.Tl,this),at(this.g,"load",this.Ul,this)],this.o(this,this.l))},e(vb,en),vb.prototype.j=function(t,e){for(var o=e.target.result,i=this.v,r=(r=this.l)||i.aa().o,n=[],s=0,p=(i=this.i).length;s<p;++s){var a=new i[s],h={featureProjection:r};try{n=a.La(o,h)}catch(t){n=null}if(n&&0<n.length)break}this.b(new xb(wb,t,n,r))},vb.prototype.setMap=function(t){this.a&&(this.a.forEach(lt),this.a=null),en.prototype.setMap.call(this,t),t&&(t=this.target||t.f,this.a=[c(t,"drop",bb,this),c(t,"dragenter",mb,this),c(t,"dragover",mb,this),c(t,"drop",mb,this)])};var wb="addfeatures";function xb(t,e,o,i){ft.call(this,t),this.features=o,this.file=e,this.projection=i}function Sb(t){t=t||{},bn.call(this,{handleDownEvent:Tb,handleDragEvent:Mb,handleUpEvent:Pb}),this.o=t.condition||fn,this.a=this.i=void 0,this.j=0,this.u=void 0!==t.duration?t.duration:400}function Mb(t){var e,o,i;dn(t)&&(o=(e=t.map).nb(),t=(i=t.pixel)[0]-o[0]/2,i=o[1]/2-i[1],o=Math.atan2(i,t),t=Math.sqrt(t*t+i*i),e=e.aa(),void 0!==this.i&&(i=o-this.i,on(e,e.Ra()-i)),this.i=o,void 0!==this.a&&(o=this.a*(e.Oa()/t),nn(e,o)),void 0!==this.a&&(this.j=this.a/t),this.a=t)}function Pb(t){if(!dn(t))return!0;zo(t=t.map.aa(),1,-1);var e=this.j-1,o=t.Ra(),o=t.constrainRotation(o,0),o=(on(t,o,void 0,void 0),t.Oa()),i=this.u,o=t.constrainResolution(o,0,e);return nn(t,o,void 0,i),this.j=0,!1}function Tb(t){return!(!dn(t)||!this.o(t)||(zo(t.map.aa(),1,1),this.a=this.i=void 0))}function Ab(){return[[-1/0,-1/0,1/0,1/0]]}function v(t){Il.call(this,{attributions:(t=t||{}).attributions,logo:t.logo,projection:void 0,state:"ready",wrapX:void 0===t.wrapX||t.wrapX}),this.U=G,this.P=t.format,this.xa=null==t.overlaps||t.overlaps,this.Z=t.url,void 0!==t.loader?this.U=t.loader:void 0!==this.Z&&(V(this.P,7),this.U=bc(this.Z,this.P)),this.fc=void 0!==t.strategy?t.strategy:Ab;var e,o,i,r,n=void 0===t.useSpatialIndex||t.useSpatialIndex;this.a=n?new zh:null,this.sa=new zh,this.i={},this.l={},this.o={},this.v={},this.c=null,t.features instanceof di?o=(e=t.features).a:Array.isArray(t.features)&&(o=t.features),n||void 0!==e||(e=new di(o)),void 0!==o&&jb(this,o),void 0!==e&&(i=e,r=!1,c(t=this,Nb,function(t){r||(r=!0,i.push(t.feature),r=!1)}),c(t,kb,function(t){r||(r=!0,i.remove(t.feature),r=!1)}),c(i,mi,function(t){r||(r=!0,this.gb(t.element),r=!1)},t),c(i,wi,function(t){r||(r=!0,this.rb(t.element),r=!1)},t),t.c=i)}function Eb(t,e,o){t.v[e]=[c(o,"change",t.Mh,t),c(o,Pt,t.Mh,t)]}function Cb(t,e,o){var i=!0,r=o.f;return void 0!==r?r.toString()in t.l?i=!1:t.l[r.toString()]=o:(V(!(e in t.o),30),t.o[e]=o),i}function jb(t,e){for(var o,i,r=[],n=[],s=[],p=0,a=e.length;p<a;p++)Cb(t,o=B(i=e[p]).toString(),i)&&n.push(i);for(p=0,a=n.length;p<a;p++){Eb(t,o=B(i=n[p]).toString(),i);var h=i.V();h?(o=h.G(),r.push(o),s.push(i)):t.i[o]=i}for(t.a&&t.a.load(r,s),p=0,a=n.length;p<a;p++)t.b(new Rb(Nb,n[p]))}function Lb(t,e){for(var o in t.l)if(t.l[o]===e){delete t.l[o];break}}function Rb(t,e){ft.call(this,t),this.feature=e}e(xb,ft),e(Sb,bn),e(v,Il),(d=v.prototype).gb=function(t){var e,o=B(t).toString();Cb(this,o,t)&&(Eb(this,o,t),(e=t.V())?(o=e.G(),this.a&&this.a.Da(o,t)):this.i[o]=t,this.b(new Rb(Nb,t))),this.s()},d.Tc=function(t){jb(this,t),this.s()},d.clear=function(t){if(t){for(var e in this.v)this.v[e].forEach(lt);this.c||(this.v={},this.l={},this.o={})}else if(this.a)for(var o in this.a.forEach(this.$f,this),this.i)this.$f(this.i[o]);this.c&&this.c.clear(),this.a&&this.a.clear(),this.sa.clear(),this.i={},this.b(new Rb(Fb)),this.s()},d.Fg=function(t,e){return this.a?this.a.forEach(t,e):this.c?this.c.forEach(t,e):void 0},d.Qb=function(t,e,o){return this.a?qh(this.a,t,e,o):this.c?this.c.forEach(e,o):void 0},d.Gg=function(e,o,i){return this.Qb(e,function(t){if(t.V().Ta(e)&&(t=o.call(i,t)))return t})},d.Og=function(){return this.c},d.we=function(){var t;return this.c?t=this.c.a:this.a&&(t=Yh(this.a),ot(this.i)||jt(t,et(this.i))),t},d.Ng=function(t){var e,o,i=[];return e=t,o=function(t){i.push(t)},this.Qb([e[0],e[1],e[0],e[1]],function(t){if(t.V().mb(e))return o.call(void 0,t)}),i},d.nf=function(t){return Zh(this.a,t)},d.Jg=function(t,e){var i=t[0],r=t[1],n=null,s=[NaN,NaN],p=1/0,a=[-1/0,-1/0,1/0,1/0],h=e||Le;return qh(this.a,a,function(t){var e,o;h(t)&&(e=t.V(),o=p,(p=e.Ab(i,r,s,p))<o)&&(n=t,t=Math.sqrt(p),a[0]=i-t,a[1]=r-t,a[2]=i+t,a[3]=r+t)}),n},d.G=function(){return this.a.G()},d.Mg=function(t){return void 0!==(t=this.l[t.toString()])?t:null},d.Kh=function(){return this.P},d.Lh=function(){return this.Z},d.Mh=function(t){var e=B(t=t.target).toString(),o=t.V();o?(o=o.G(),e in this.i?(delete this.i[e],this.a&&this.a.Da(o,t)):this.a&&Hh(this.a,o,t)):e in this.i||(this.a&&this.a.remove(t),this.i[e]=t),void 0!==(o=t.f)?(o=o.toString(),e in this.o?(delete this.o[e],this.l[o]=t):this.l[o]!==t&&(Lb(this,t),this.l[o]=t)):e in this.o||(Lb(this,t),this.o[e]=t),this.s(),this.b(new Rb(Ib,t))},d.Ed=function(t,e,o){for(var i=this.sa,r=0,n=(t=this.fc(t,e)).length;r<n;++r){var s=t[r];qh(i,s,function(t){return re(t.extent,s)})||(this.U.call(this,s,e,o),i.Da(s,{extent:s.slice()}))}},d.rb=function(t){var e=B(t).toString();e in this.i?delete this.i[e]:this.a&&this.a.remove(t),this.$f(t),this.s()},d.$f=function(t){var e=B(t).toString(),o=(this.v[e].forEach(lt),delete this.v[e],t.f);void 0!==o?delete this.l[o.toString()]:delete this.o[e],this.b(new Rb(kb,t))},e(Rb,ft);var Nb="addfeature",Ib="changefeature",Fb="clear",kb="removefeature";function Ob(t){var e;bn.call(this,{handleDownEvent:Bb,handleEvent:Db,handleUpEvent:Ub}),this.fa=null,this.u=!1,this.Ua=t.source||null,this.xa=t.features||null,this.Mj=t.snapTolerance||12,this.U=t.type,this.i=("Point"===(n=this.U)||"MultiPoint"===n?e=Hb:"LineString"===n||"MultiLineString"===n?e=Yb:"Polygon"===n||"MultiPolygon"===n?e=Zb:"Circle"===n&&(e=qb),e),this.Fa=t.minPoints||(this.i===Zb?3:2),this.na=t.maxPoints||1/0,this.fc=t.finishCondition||Le;var o,i,r,n=(n=t.geometryFunction)||("Circle"===this.U?function(t,e){e=e||new pb([NaN,NaN]);return e.dg(t[0],Math.sqrt(zt(t[0],t[1]))),e}:((i=this.i)===Hb?o=m:i===Yb?o=S:i===Zb&&(o=w),function(t,e){return e?i===Zb?e.qa([t[0].concat([t[0][0]])]):e.qa(t):e=new o(t),e}));this.D=n,this.P=this.C=this.a=this.L=this.j=this.o=null,this.Ob=t.clickTolerance?t.clickTolerance*t.clickTolerance:36,this.sa=new s({source:new v({useSpatialIndex:!1,wrapX:t.wrapX||!1}),style:t.style||(r=mp(),function(t){return r[t.V().Y()]})}),this.Ja=t.geometryName,this.Lj=t.condition||yn,this.Xe=t.freehand?Le:t.freehandCondition||fn,c(this,St(sn),this.Li,this)}function Db(t){this.u=this.i!==Hb&&this.Xe(t);var e=!this.u;return this.u&&"pointerdrag"===t.type&&null!==this.j?(Vb(this,t),e=!1):"pointermove"===t.type?e=Gb(this,t):"dblclick"===t.type&&(e=!1),wn.call(this,t)&&e}function Bb(t){return this.u?(this.fa=t.pixel,this.o||Xb(this,t),!0):!!this.Lj(t)&&(this.fa=t.pixel,!0)}function Ub(t){var e=this.fa,o=t.pixel,i=(i=e[0]-o[0])*i+(e=e[1]-o[1])*e,e=!0,o=this.i===qb;return(this.u?i>this.Ob:i<=this.Ob)?(Gb(this,t),this.o?this.u||o?this.zd():Kb(this,t)?this.fc(t)&&this.zd():Vb(this,t):(Xb(this,t),this.i===Hb&&this.zd()),e=!1):o&&(this.o=null),e}function Gb(t,e){var o,i,r;return t.o?(o=e.coordinate,i=t.j.V(),t.i===Hb?r=t.a:t.i===Zb?(r=(r=t.a[0])[r.length-1],Kb(t,e)&&(o=t.o.slice())):r=(r=t.a)[r.length-1],r[0]=o[0],r[1]=o[1],t.D(t.a,i),t.L&&t.L.V().qa(o),i instanceof w&&t.i!==Zb?(t.C||(t.C=new x(new S(null))),i=i.Qg(0),(o=t.C.V()).da(i.ka,i.ia())):t.P&&(o=t.C.V()).qa(t.P),zb(t)):(o=e.coordinate.slice(),t.L?t.L.V().qa(o):(t.L=new x(new m(o)),zb(t))),!0}function Kb(t,e){var o=!1;if(t.j){var i=!1,r=[t.o];if(t.i===Yb?i=t.a.length>t.Fa:t.i===Zb&&(i=t.a[0].length>t.Fa,r=[t.a[0][0],t.a[0][t.a[0].length-2]]),i)for(var i=e.map,n=0,s=r.length;n<s;n++){var p=r[n],a=i.Ga(p),h=e.pixel,o=h[0]-a[0],a=h[1]-a[1];if(o=Math.sqrt(o*o+a*a)<=(t.u?1:t.Mj)){t.o=p;break}}}return o}function Xb(t,e){e=e.coordinate;t.o=e,t.i===Hb?t.a=e.slice():t.i===Zb?(t.a=[[e.slice(),e.slice()]],t.P=t.a[0]):(t.a=[e.slice(),e.slice()],t.i===qb&&(t.P=t.a)),t.P&&(t.C=new x(new S(t.P))),e=t.D(t.a),t.j=new x,t.Ja&&t.j.Nc(t.Ja),t.j.Pa(e),zb(t),t.b(new Jb(_b,t.j))}function Vb(t,e){var o,i,e=e.coordinate,r=t.j.V();t.i===Yb?(t.o=e.slice(),(i=t.a).length>=t.na&&(t.u?i.pop():o=!0),i.push(e.slice()),t.D(i,r)):t.i===Zb&&((i=t.a[0]).length>=t.na&&(t.u?i.pop():o=!0),i.push(e.slice()),o&&(t.o=i[0]),t.D(t.a,r)),zb(t),o&&t.zd()}function Wb(t){t.o=null;var e=t.j;return e&&(t.j=null,t.L=null,t.C=null,t.sa.la().clear(!0)),e}function zb(t){var e=[];t.j&&e.push(t.j),t.C&&e.push(t.C),t.L&&e.push(t.L),(t=t.sa.la()).clear(!0),t.Tc(e)}e(Ob,bn),(d=Ob.prototype).setMap=function(t){bn.prototype.setMap.call(this,t),this.Li()},d.Vo=function(){var t,e=this.j.V();this.i===Yb?((t=this.a).splice(-2,1),this.D(t,e)):this.i===Zb&&((t=this.a[0]).splice(-2,1),this.C.V().qa(t),this.D(this.a,e)),0===t.length&&(this.o=null),zb(this)},d.zd=function(){var t=Wb(this),e=this.a,o=t.V();this.i===Yb?(e.pop(),this.D(e,o)):this.i===Zb&&(e[0].pop(),this.D(e,o),e=o.$()),"MultiPoint"===this.U?t.Pa(new P([e])):"MultiLineString"===this.U?t.Pa(new M([e])):"MultiPolygon"===this.U&&t.Pa(new y([e])),this.b(new Jb($b,t)),this.xa&&this.xa.push(t),this.Ua&&this.Ua.gb(t)},d.vm=function(t){var e=t.V();this.j=t,this.a=e.$(),t=this.a[this.a.length-1],this.o=t.slice(),this.a.push(t.slice()),zb(this),this.b(new Jb(_b,this.j))},d.Qc=Re,d.Li=function(){var t=this.v,e=this.f();t&&e||Wb(this),this.sa.setMap(e?t:null)};var Hb="Point",Yb="LineString",Zb="Polygon",qb="Circle";function Jb(t,e){ft.call(this,t),this.feature=e}e(Jb,ft);var _b="drawstart",$b="drawend";function Qb(t){var e,o;this.a=this.j=null,this.C=!1,this.D=this.o=null,(t=t||{}).extent&&this.i(t.extent),bn.call(this,{handleDownEvent:em,handleDragEvent:om,handleEvent:tm,handleUpEvent:im}),this.u=new s({source:new v({useSpatialIndex:!1,wrapX:!!t.wrapX}),style:t.boxStyle||(e=mp(),function(){return e.Polygon}),updateWhileAnimating:!0,updateWhileInteracting:!0}),this.L=new s({source:new v({useSpatialIndex:!1,wrapX:!!t.wrapX}),style:t.pointerStyle||(o=mp(),function(){return o.Point}),updateWhileAnimating:!0,updateWhileInteracting:!0})}function tm(t){var e,o;return!(t instanceof or&&("pointermove"!=t.type||this.A||pm(this,sm(this,e=t.pixel,o=t.map)||o.Sa(e)),wn.call(this,t),1))}function em(t){function e(t){var e=null,o=null;return t[0]==r[0]?e=r[2]:t[0]==r[2]&&(e=r[0]),t[1]==r[1]?o=r[3]:t[1]==r[3]&&(o=r[1]),null!==e&&null!==o?[e,o]:null}var o=t.pixel,i=t.map,r=this.G();return(t=sm(this,o,i))&&r?(o=t[0]==r[0]||t[0]==r[2]?t[0]:null,i=t[1]==r[1]||t[1]==r[3]?t[1]:null,null!==o&&null!==i?this.a=rm(e(t)):null!==o?this.a=nm(e([o,r[1]]),e([o,r[3]])):null!==i&&(this.a=nm(e([r[0],i]),e([r[2],i])))):(t=i.Sa(o),this.i([t[0],t[1],t[0],t[1]]),this.a=rm(t)),!0}function om(t){return this.a&&(t=t.coordinate,this.i(this.a(t)),pm(this,t)),!0}function im(){this.a=null;var t=this.G();return t&&0!==de(t)||this.i(null),!1}function rm(e){return function(t){return Qt([e,t])}}function nm(e,o){return e[0]==o[0]?function(t){return Qt([e,[t[0],o[1]]])}:e[1]==o[1]?function(t){return Qt([e,[o[0],t[1]]])}:null}function sm(t,e,o){var i=o.Sa(e);if(r=t.G()){(r=[[[r[0],r[1]],[r[0],r[3]]],[[r[0],r[3]],[r[2],r[3]]],[[r[2],r[3]],[r[2],r[1]]],[[r[2],r[1]],[r[0],r[1]]]]).sort(function(t,e){return Ht(i,t)-Ht(i,e)});var r=r[0],n=Ut(i,r),s=o.Ga(n);if(Math.sqrt(zt(e,s))<=10)return e=o.Ga(r[0]),o=o.Ga(r[1]),e=zt(s,e),o=zt(s,o),t.C=Math.sqrt(Math.min(e,o))<=10,n=t.C?o<e?r[1]:r[0]:n}return null}function pm(t,e){var o=t.D;o?o.V().qa(e):(o=new x(new m(e)),t.D=o,t.L.la().gb(o))}function am(t){ft.call(this,hm),this.b=t}e(Qb,bn),Qb.prototype.setMap=function(t){this.u.setMap(t),this.L.setMap(t),bn.prototype.setMap.call(this,t)},Qb.prototype.G=function(){return this.j},Qb.prototype.i=function(t){this.j=t||null;var e=this.o;e?t?e.Pa(Oo(t)):e.Pa(void 0):(this.o=e=new x(t?Oo(t):{}),this.u.la().gb(e)),this.b(new am(this.j))},e(am,ft);var hm="extentchanged";function lm(t){var e;bn.call(this,{handleDownEvent:gm,handleDragEvent:dm,handleEvent:bm,handleUpEvent:vm}),this.Ua=t.condition||vn,this.xa=function(t){return yn(t)&&cn(t)},this.Ja=t.deleteCondition||this.xa,this.Fa=this.a=null,this.sa=[0,0],this.C=this.L=!1,this.i=new zh,this.fa=void 0!==t.pixelTolerance?t.pixelTolerance:10,this.o=this.na=!1,this.j=[],this.D=new s({source:new v({useSpatialIndex:!1,wrapX:!!t.wrapX}),style:t.style||(e=mp(),function(){return e.Point}),updateWhileAnimating:!0,updateWhileInteracting:!0}),this.U={Point:this.Cm,LineString:this.uh,LinearRing:this.uh,Polygon:this.Dm,MultiPoint:this.Am,MultiLineString:this.zm,MultiPolygon:this.Bm,GeometryCollection:this.ym},this.u=t.features,this.u.forEach(this.Gf,this),c(this.u,mi,this.wm,this),c(this.u,wi,this.xm,this),this.P=null}function um(t,e){t.C||(t.C=!0,t.b(new xm(Sm,t.u,e)))}function cm(t,e){var o=e,i=t.i,r=[];i.forEach(function(t){o===t.feature&&r.push(t)});for(var n=r.length-1;0<=n;--n)i.remove(r[n]);t.a&&0===t.u.Ub()&&(t.D.la().rb(t.a),t.a=null),ht(e,"change",t.th,t)}function ym(t,e){var o=t.a;o?o.V().qa(e):(o=new x(new m(e)),t.a=o,t.D.la().gb(o))}function fm(t,e){return t.index-e.index}function gm(t){if(!this.Ua(t))return!1;if(mm(this,t.pixel,t.map),this.j.length=0,this.C=!1,e=this.a){var e,o=[],i=Qt([e=e.V().$()]),r={};(i=Zh(this.i,i)).sort(fm);for(var n=0,s=i.length;n<s;++n){var p=i[n],a=p.ta,h=B(p.feature),l=p.depth;l&&(h+="-"+l.join("-")),r[h]||(r[h]=Array(2)),Xt(a[0],e)&&!r[h][0]?(this.j.push([p,0]),r[h][0]=p):Xt(a[1],e)&&!r[h][1]?("LineString"===p.geometry.Y()||"MultiLineString"===p.geometry.Y())&&r[h][0]&&0===r[h][0].index||(this.j.push([p,1]),r[h][1]=p):B(a)in this.Fa&&!r[h][0]&&!r[h][1]&&o.push([p,e])}for(o.length&&um(this,t),t=o.length-1;0<=t;--t)this.pl.apply(this,o[t])}return!!this.a}function dm(t){this.L=!1,um(this,t),t=t.coordinate;for(var e=0,o=this.j.length;e<o;++e){for(var i=(a=this.j[e])[0],r=i.depth,n=i.geometry,s=n.$(),p=i.ta,a=a[1];t.length<n.pa();)t.push(p[a][t.length]);switch(n.Y()){case"Point":s=t,p[0]=p[1]=t;break;case"MultiPoint":s[i.index]=t,p[0]=p[1]=t;break;case"LineString":s[i.index+a]=t,p[a]=t;break;case"MultiLineString":case"Polygon":s[r[0]][i.index+a]=t,p[a]=t;break;case"MultiPolygon":s[r[1]][r[0]][i.index+a]=t,p[a]=t}i=n,this.o=!0,i.qa(s),this.o=!1}ym(this,t)}function vm(t){for(var e,o=this.j.length-1;0<=o;--o)e=this.j[o][0],Hh(this.i,Qt(e.ta),e);return this.C&&(this.b(new xm(Mm,this.u,t)),this.C=!1),!1}function bm(t){var e;return!(t instanceof or)||(Xo((this.P=t).map.aa())[1]||"pointermove"!=t.type||this.A||(this.sa=t.pixel,mm(this,t.pixel,t.map)),this.a&&this.Ja(t)&&(e=!("singleclick"!=t.type||!this.L)||this.ki()),"singleclick"==t.type&&(this.L=!1),wn.call(this,t)&&!e)}function mm(t,e,o){var i=o.Sa(e),r=te(he(i),o.aa().Oa()*t.fa);if(0<(r=Zh(t.i,r)).length){r.sort(function(t,e){return Ht(i,t.ta)-Ht(i,e.ta)});var n=r[0].ta,s=Ut(i,n),p=o.Ga(s);if(Math.sqrt(zt(e,p))<=t.fa){for(e=o.Ga(n[0]),o=o.Ga(n[1]),e=zt(p,e),p=zt(p,o),t.na=Math.sqrt(Math.min(e,p))<=t.fa,ym(t,s=t.na?p<e?n[1]:n[0]:s),(s={})[B(n)]=!0,o=1,e=r.length;o<e&&(p=r[o].ta,Xt(n[0],p[0])&&Xt(n[1],p[1])||Xt(n[0],p[1])&&Xt(n[1],p[0]));++o)s[B(p)]=!0;return void(t.Fa=s)}}t.a&&(t.D.la().rb(t.a),t.a=null)}function wm(t,e,o,i,r){qh(t.i,e.G(),function(t){t.geometry===e&&(void 0===i||void 0===t.depth||Rt(t.depth,i))&&t.index>o&&(t.index+=r)})}function xm(t,e,o){ft.call(this,t),this.features=e,this.mapBrowserEvent=o}e(lm,bn),(d=lm.prototype).Gf=function(t){var e=t.V();e&&e.Y()in this.U&&this.U[e.Y()].call(this,t,e),(e=this.v)&&e.a&&mm(this,this.sa,e),c(t,"change",this.th,this)},d.Ea=function(t){this.a&&!t&&(this.D.la().rb(this.a),this.a=null),bn.prototype.Ea.call(this,t)},d.setMap=function(t){this.D.setMap(t),bn.prototype.setMap.call(this,t)},d.wm=function(t){this.Gf(t.element)},d.th=function(t){this.o||(cm(this,t=t.target),this.Gf(t))},d.xm=function(t){cm(this,t.element)},d.Cm=function(t,e){var o=e.$(),o={feature:t,geometry:e,ta:[o,o]};this.i.Da(e.G(),o)},d.Am=function(t,e){for(var o,i=e.$(),r=0,n=i.length;r<n;++r)o={feature:t,geometry:e,depth:[r],index:r,ta:[o=i[r],o]},this.i.Da(e.G(),o)},d.uh=function(t,e){for(var o,i,r=e.$(),n=0,s=r.length-1;n<s;++n)i={feature:t,geometry:e,index:n,ta:o=r.slice(n,n+2)},this.i.Da(Qt(o),i)},d.zm=function(t,e){for(var o,i,r,n,s,p=e.$(),a=0,h=p.length;a<h;++a)for(i=0,r=(o=p[a]).length-1;i<r;++i)s={feature:t,geometry:e,depth:[a],index:i,ta:n=o.slice(i,i+2)},this.i.Da(Qt(n),s)},d.Dm=function(t,e){for(var o,i,r,n,s,p=e.$(),a=0,h=p.length;a<h;++a)for(i=0,r=(o=p[a]).length-1;i<r;++i)s={feature:t,geometry:e,depth:[a],index:i,ta:n=o.slice(i,i+2)},this.i.Da(Qt(n),s)},d.Bm=function(t,e){for(var o,i,r,n,s,p,a,h,l=e.$(),u=0,c=l.length;u<c;++u)for(n=0,s=(p=l[u]).length;n<s;++n)for(i=0,r=(o=p[n]).length-1;i<r;++i)h={feature:t,geometry:e,depth:[n,u],index:i,ta:a=o.slice(i,i+2)},this.i.Da(Qt(a),h)},d.ym=function(t,e){for(var o=e.f,i=0;i<o.length;++i)this.U[o[i].Y()].call(this,t,o[i])},d.pl=function(t,e){for(var o,i=t.ta,r=t.feature,n=t.geometry,s=t.depth,p=t.index;e.length<n.pa();)e.push(0);switch(n.Y()){case"MultiLineString":case"Polygon":(o=n.$())[s[0]].splice(p+1,0,e);break;case"MultiPolygon":(o=n.$())[s[1]][s[0]].splice(p+1,0,e);break;case"LineString":(o=n.$()).splice(p+1,0,e);break;default:return}this.o=!0,n.qa(o),this.o=!1,(o=this.i).remove(t),wm(this,n,p,s,1);t={ta:[i[0],e],feature:r,geometry:n,depth:s,index:p};o.Da(Qt(t.ta),t),this.j.push([t,1]),i={ta:[e,i[1]],feature:r,geometry:n,depth:s,index:p+1},o.Da(Qt(i.ta),i),this.j.push([i,0]),this.L=!0},d.ki=function(){if(this.P&&"pointerdrag"!=this.P.type){for(var t,e,o,i,r,n,s,p,a=this.P,h=(um(this,a),this.j),l={},u=h.length-1;0<=u;--u)p=B((s=(i=h[u])[0]).feature),s.depth&&(p+="-"+s.depth.join("-")),p in l||(l[p]={}),0===i[1]?(l[p].right=s,l[p].index=s.index):1==i[1]&&(l[p].left=s,l[p].index=s.index+1);for(p in l){switch(n=l[p].right,u=l[p].left,(r=(i=l[p].index)-1)<0&&(r=0),e=o=(h=(s=void 0!==u?u:n).geometry).$(),t=!1,h.Y()){case"MultiLineString":2<o[s.depth[0]].length&&(o[s.depth[0]].splice(i,1),t=!0);break;case"LineString":2<o.length&&(o.splice(i,1),t=!0);break;case"MultiPolygon":e=e[s.depth[1]];case"Polygon":4<(e=e[s.depth[0]]).length&&(i==e.length-1&&(i=0),e.splice(i,1),t=!0,0===i)&&(e.pop(),e.push(e[0]),r=e.length-1)}t&&(t=h,this.o=!0,t.qa(o),this.o=!1,o=[],void 0!==u&&(this.i.remove(u),o.push(u.ta[0])),void 0!==n&&(this.i.remove(n),o.push(n.ta[1])),void 0!==u&&void 0!==n&&(u={depth:s.depth,feature:s.feature,geometry:s.geometry,index:r,ta:o},this.i.Da(Qt(u.ta),u)),wm(this,h,i,s.depth,-1),this.a)&&(this.D.la().rb(this.a),this.a=null)}return this.b(new xm(Mm,this.u,a)),!(this.C=!1)}return!1},e(xm,ft);var Sm="modifystart",Mm="modifyend";function Pm(t){var e,o;en.call(this,{handleEvent:Tm}),this.C=(t=t||{}).condition||cn,this.A=t.addCondition||Re,this.D=t.removeCondition||Re,this.L=t.toggleCondition||fn,this.o=t.multi||!1,this.l=t.filter||Le,this.j=t.hitTolerance||0,this.i=new s({source:new v({useSpatialIndex:!1,features:t.features,wrapX:t.wrapX}),style:t.style||(jt((e=mp()).Polygon,e.LineString),jt(e.GeometryCollection,e.LineString),function(t){return t.V()?e[t.V().Y()]:null}),updateWhileAnimating:!0,updateWhileInteracting:!0}),t=t.layers?"function"==typeof t.layers?t.layers:(o=t.layers,function(t){return Et(o,t)}):Le,this.u=t,this.a={},c(t=this.i.la().c,mi,this.Em,this),c(t,wi,this.Im,this)}function Tm(t){if(!this.C(t))return!0;var i=this.A(t),r=this.D(t),n=this.L(t),e=!i&&!r&&!n,o=t.map,s=this.i.la().c,p=[],a=[];if(e){for(tt(this.a),o.ae(t.pixel,function(t,e){if(this.l(t,e))return a.push(t),t=B(t),this.a[t]=e,!this.o}.bind(this),{layerFilter:this.u,hitTolerance:this.j}),e=s.Ub()-1;0<=e;--e){var o=s.item(e),h=a.indexOf(o);-1<h?a.splice(h,1):(s.remove(o),p.push(o))}0!==a.length&&s.Bf(a)}else{for(o.ae(t.pixel,function(t,e){var o;if(this.l(t,e))return!i&&!n||Et(s.a,t)?(r||n)&&Et(s.a,t)&&(p.push(t),o=B(t),delete this.a[o]):(a.push(t),o=B(t),this.a[o]=e),!this.o}.bind(this),{layerFilter:this.u,hitTolerance:this.j}),e=p.length-1;0<=e;--e)s.remove(p[e]);s.Bf(a)}return(0<a.length||0<p.length)&&this.b(new Am(Em,a,p,t)),un(t)}function Am(t,e,o,i){ft.call(this,t),this.selected=e,this.deselected=o,this.mapBrowserEvent=i}e(Pm,en),(d=Pm.prototype).Fm=function(){return this.i.la().c},d.Gm=function(){return this.j},d.Hm=function(t){return t=B(t),this.a[t]},d.Jm=function(t){this.j=t},d.setMap=function(t){var e=this.v,o=this.i.la().c;e&&o.forEach(e.Ji,e),en.prototype.setMap.call(this,t),this.i.setMap(t),t&&o.forEach(t.Di,t)},d.Em=function(t){var e=this.v;e&&e.Di(t.element)},d.Im=function(t){var e=this.v;e&&e.Ji(t.element)},e(Am,ft);var Em="select";function Cm(t){bn.call(this,{handleEvent:jm,handleDownEvent:Le,handleUpEvent:Lm}),this.o=(t=t||{}).source||null,this.sa=void 0===t.vertex||t.vertex,this.P=void 0===t.edge||t.edge,this.j=t.features||null,this.na=[],this.C={},this.D={},this.U={},this.u={},this.L=null,this.i=void 0!==t.pixelTolerance?t.pixelTolerance:10,this.Fa=function(t,e){return Ht(this.L,t.ta)-Ht(this.L,e.ta)}.bind(this),this.a=new zh,this.fa={Point:this.Pm,LineString:this.xh,LinearRing:this.xh,Polygon:this.Qm,MultiPoint:this.Nm,MultiLineString:this.Mm,MultiPolygon:this.Om,GeometryCollection:this.Lm}}function jm(t){var e,o=t.pixel,i=t.coordinate,r=t.map,n=r.Sa([o[0]-this.i,o[1]+this.i]),s=r.Sa([o[0]+this.i,o[1]-this.i]),n=Qt([n,s]),p=Zh(this.a,n),n=!1,a=null;return s=null,0<p.length&&(this.L=i,p.sort(this.Fa),p=p[0].ta,this.sa&&!this.P?(i=r.Ga(p[0]),e=r.Ga(p[1]),i=zt(o,i),o=zt(o,e),(e=(e=Math.sqrt(Math.min(i,o)))<=this.i)&&(n=!0,a=o<i?p[1]:p[0],s=r.Ga(a))):this.P&&(a=Ut(i,p),s=r.Ga(a),Math.sqrt(zt(o,s))<=this.i)&&(n=!0,this.sa)&&(i=r.Ga(p[0]),e=r.Ga(p[1]),i=zt(s,i),o=zt(s,e),e=(e=Math.sqrt(Math.min(i,o)))<=this.i)&&(a=o<i?p[1]:p[0],s=r.Ga(a)),n)&&(s=[Math.round(s[0]),Math.round(s[1])]),r=a,n&&(t.coordinate=r.slice(0,2),t.pixel=s),wn.call(this,t)}function Lm(){var t=et(this.u);return t.length&&(t.forEach(this.Ki,this),this.u={}),!1}function Rm(t){var e,o;bn.call(this,{handleDownEvent:Nm,handleDragEvent:Fm,handleMoveEvent:km,handleUpEvent:Im}),t=t||{},this.o=void 0,this.a=null,this.j=void 0!==t.features?t.features:null,o=t.layers?"function"==typeof t.layers?t.layers:(e=t.layers,function(t){return Et(e,t)}):Le,this.C=o,this.u=t.hitTolerance||0,this.i=null}function Nm(t){var e;return this.i=Om(this,t.pixel,t.map),!(this.a||!this.i||(this.a=t.coordinate,km.call(this,t),e=this.j||new di([this.i]),this.b(new Dm(Bm,e,t.coordinate)),0))}function Im(t){var e;return!!this.a&&(this.a=null,km.call(this,t),e=this.j||new di([this.i]),this.b(new Dm(Gm,e,t.coordinate)),!0)}function Fm(t){var o,i,e;this.a&&(o=(t=t.coordinate)[0]-this.a[0],i=t[1]-this.a[1],(e=this.j||new di([this.i])).forEach(function(t){var e=t.V();e.translate(o,i),t.Pa(e)}),this.a=t,this.b(new Dm(Um,e,t)))}function km(t){var e=t.map.Cc();Om(this,t.pixel,t.map)?(this.o=e.style.cursor,e.style.cursor=this.a?"-webkit-grabbing":"-webkit-grab",e.style.cursor=this.a?"grabbing":"grab"):(e.style.cursor=void 0!==this.o?this.o:"",this.o=void 0)}function Om(t,e,o){return o.ae(e,function(t){if(!this.j||Et(this.j.a,t))return t}.bind(t),{layerFilter:t.C,hitTolerance:t.u})}function Dm(t,e,o){ft.call(this,t),this.features=e,this.coordinate=o}e(Cm,bn),(d=Cm.prototype).gb=function(t,e){var o,e=void 0===e||e,i=B(t),r=t.V();r&&(o=this.fa[r.Y()])&&(this.U[i]=r.G(pe()),o.call(this,t,r),e)&&(this.D[i]=c(r,"change",this.Pk.bind(this,t),this)),e&&(this.C[i]=c(t,St(t.a),this.Km,this))},d.Qj=function(t){this.gb(t)},d.Rj=function(t){this.rb(t)},d.vh=function(t){var e;t instanceof Rb?e=t.feature:t instanceof xi&&(e=t.element),this.gb(e)},d.wh=function(t){var e;t instanceof Rb?e=t.feature:t instanceof xi&&(e=t.element),this.rb(e)},d.Km=function(t){t=t.target,this.rb(t,!0),this.gb(t,!0)},d.Pk=function(t){var e;this.A?(e=B(t))in this.u||(this.u[e]=t):this.Ki(t)},d.rb=function(e,t){var t=void 0===t||t,o=B(e),i=this.U[o];if(i){var r=this.a,n=[];for(qh(r,i,function(t){e===t.feature&&n.push(t)}),i=n.length-1;0<=i;--i)r.remove(n[i]);t&&(mt(this.D[o]),delete this.D[o])}t&&(mt(this.C[o]),delete this.C[o])},d.setMap=function(t){var e,o=this.v,i=this.na;this.j?e=this.j:this.o&&(e=this.o.we()),o&&(i.forEach(mt),i.length=0,e.forEach(this.Rj,this)),bn.prototype.setMap.call(this,t),t&&(this.j?i.push(c(this.j,mi,this.vh,this),c(this.j,wi,this.wh,this)):this.o&&i.push(c(this.o,Nb,this.vh,this),c(this.o,kb,this.wh,this)),e.forEach(this.Qj,this))},d.Qc=Re,d.Ki=function(t){this.rb(t,!1),this.gb(t,!1)},d.Lm=function(t,e){for(var o=e.f,i=0;i<o.length;++i)this.fa[o[i].Y()].call(this,t,o[i])},d.xh=function(t,e){for(var o,i,r=e.$(),n=0,s=r.length-1;n<s;++n)i={feature:t,ta:o=r.slice(n,n+2)},this.a.Da(Qt(o),i)},d.Mm=function(t,e){for(var o,i,r,n,s,p=e.$(),a=0,h=p.length;a<h;++a)for(i=0,r=(o=p[a]).length-1;i<r;++i)s={feature:t,ta:n=o.slice(i,i+2)},this.a.Da(Qt(n),s)},d.Nm=function(t,e){for(var o,i=e.$(),r=0,n=i.length;r<n;++r)o={feature:t,ta:[o=i[r],o]},this.a.Da(e.G(),o)},d.Om=function(t,e){for(var o,i,r,n,s,p,a,h,l=e.$(),u=0,c=l.length;u<c;++u)for(n=0,s=(p=l[u]).length;n<s;++n)for(i=0,r=(o=p[n]).length-1;i<r;++i)h={feature:t,ta:a=o.slice(i,i+2)},this.a.Da(Qt(a),h)},d.Pm=function(t,e){t={feature:t,ta:[t=e.$(),t]};this.a.Da(e.G(),t)},d.Qm=function(t,e){for(var o,i,r,n,s,p=e.$(),a=0,h=p.length;a<h;++a)for(i=0,r=(o=p[a]).length-1;i<r;++i)s={feature:t,ta:n=o.slice(i,i+2)},this.a.Da(Qt(n),s)},e(Rm,bn),Rm.prototype.D=function(){return this.u},Rm.prototype.L=function(t){this.u=t},e(Dm,ft);var Bm="translatestart",Um="translating",Gm="translateend";function T(t){var e=Q({},t=t||{}),o=(delete e.gradient,delete e.radius,delete e.blur,delete e.shadow,delete e.weight,s.call(this,e),this.f=null,this.U=void 0!==t.shadow?t.shadow:250,this.P=void 0,this.c=null,c(this,St(Vm),this.Qk,this),this.ui(t.gradient||Km),this.ni(void 0!==t.blur?t.blur:15),this.Ah(void 0!==t.radius?t.radius:8),c(this,St(Xm),this.xf,this),c(this,St(Wm),this.xf,this),this.xf(),t.weight||"weight"),i="string"==typeof o?function(t){return t.get(o)}:o;this.l(function(t){var e=255*(t=void 0!==(t=i(t))?W(t,0,1):1)|0,o=this.c[e];return o||(o=[new gp({image:new Bf({opacity:t,src:this.P})})],this.c[e]=o),o}.bind(this)),this.set("renderOrder",null),c(this,"render",this.gl,this)}e(T,s);var Km=["#00f","#0ff","#0f0","#ff0","#f00"],Xm=((d=T.prototype).Ig=function(){return this.get(Xm)},d.Pg=function(){return this.get(Vm)},d.zh=function(){return this.get(Wm)},d.Qk=function(){for(var t=this.Pg(),e=Ri(1,256),o=e.createLinearGradient(0,0,1,256),i=1/(t.length-1),r=0,n=t.length;r<n;++r)o.addColorStop(r*i,t[r]);e.fillStyle=o,e.fillRect(0,0,1,256),this.f=e.getImageData(0,0,1,256).data},d.xf=function(){var t=this.zh(),e=this.Ig(),o=t+e+1,i=2*o;(i=Ri(i,i)).shadowOffsetX=i.shadowOffsetY=this.U,i.shadowBlur=e,i.shadowColor="#000",i.beginPath(),e=o-this.U,i.arc(e,e,t,0,2*Math.PI,!0),i.fill(),this.P=i.canvas.toDataURL(),this.c=Array(256),this.s()},d.gl=function(t){for(var e,o=(t=t.context).canvas,i=(o=t.getImageData(0,0,o.width,o.height)).data,r=0,n=i.length;r<n;r+=4)(e=4*i[r+3])&&(i[r]=this.f[e],i[r+1]=this.f[1+e],i[r+2]=this.f[2+e]);t.putImageData(o,0,0)},d.ni=function(t){this.set(Xm,t)},d.ui=function(t){this.set(Vm,t)},d.Ah=function(t){this.set(Wm,t)},"blur"),Vm="gradient",Wm="radius";function zm(t,e,o,i){function r(){delete window[s],n.parentNode.removeChild(n)}var n=document.createElement("script"),s="olc_"+B(e),p=(n.async=!0,n.src=t+(-1==t.indexOf("?")?"?":"&")+(i||"callback")+"="+s,setTimeout(function(){r(),o&&o()},1e4));window[s]=function(t){clearTimeout(p),r(),e(t)},document.getElementsByTagName("head")[0].appendChild(n)}function Hm(t,e,o,i,r,n,s,p,a,h,l){if(Wr.call(this,r,0),this.C=void 0!==l&&l,this.A=s,this.u=p,this.H=null,this.f=e,this.l=i,this.o=n||r,this.g=[],this.kd=null,this.i=0,n=i.Na(this.o),p=this.l.G(),r=this.f.G(),0===de(n=p?Se(n,p):n))this.state=4;else if((p=t.G())&&(r=r?Se(r,p):p),i=i.Ha(this.o[0]),i=El(t,o,me(n),i),!isFinite(i)||i<=0)this.state=4;else if(this.v=new Ll(t,o,n,r,i*(void 0!==h?h:.5)),0===this.v.f.length)this.state=4;else if(this.i=e.Ec(i),o=Rl(this.v),r&&(t.a?(o[1]=W(o[1],r[1],r[3]),o[3]=W(o[3],r[1],r[3])):o=Se(o,r)),de(o)){for(e=(t=ai(e,o,this.i)).ea;e<=t.ca;e++)for(o=t.ga;o<=t.ja;o++)(h=a(this.i,e,o,s))&&this.g.push(h);0===this.g.length&&(this.state=4)}else this.state=4}function Ym(t,e){for(var o=t.length,i=Array(o),r=0;r<o;++r)i[r]=function(t,o){var i=/\{z\}/g,r=/\{x\}/g,n=/\{y\}/g,s=/\{-y\}/g;return function(e){if(e)return t.replace(i,e[0].toString()).replace(r,e[1].toString()).replace(n,function(){return(-e[2]-1).toString()}).replace(s,function(){var t=o.a?o.a[e[0]]:null;return V(t,55),(t.ja-t.ga+1+e[2]).toString()})}}(t[r],e);return Zm(i)}function Zm(i){return 1===i.length?i[0]:function(t,e,o){if(t)return i[q((t[1]<<t[0])+t[2],i.length)](t,e,o)}}function qm(){}function Jm(t){var e=[],o=/\{([a-z])-([a-z])\}/.exec(t);if(o)for(var i=o[2].charCodeAt(0),r=o[1].charCodeAt(0);r<=i;++r)e.push(t.replace(o[0],String.fromCharCode(r)));else if(o=/\{(\d+)-(\d+)\}/.exec(t))for(i=parseInt(o[2],10),r=parseInt(o[1],10);r<=i;r++)e.push(t.replace(o[0],r.toString()));else e.push(t);return e}function _m(t){Ql.call(this),this.c=void 0!==t?t:2048}function $m(t){return t.f>t.c}function Qm(t){Il.call(this,{attributions:t.attributions,extent:t.extent,logo:t.logo,projection:t.projection,state:t.state,wrapX:t.wrapX}),this.sa=void 0!==t.opaque&&t.opaque,this.xa=void 0!==t.tilePixelRatio?t.tilePixelRatio:1,this.tileGrid=void 0!==t.tileGrid?t.tileGrid:null,this.a=new _m(t.cacheSize),this.l=[0,0],this.jc=""}function t1(t,e,o,i,r){if(!(e=t.Cd(e)))return!1;for(var n,s,p=!0,a=i.ea;a<=i.ca;++a)for(var h=i.ga;h<=i.ja;++h)n=t.Lb(o,a,h),s=!1,(s=e.b.hasOwnProperty(n)?(s=(n=e.get(n)).W()===Hr)&&!1!==r(n):s)||(p=!1);return p}function e1(t,e){t.jc!==e&&(t.jc=e,t.s())}function o1(t,e,o){var i,r,n=void 0!==o?o:t.f;return o=t.Db(n),t.D&&n.g&&(e=(i=e)[0],t=hi(o,i),e=ie(n=fi(n),t)?i:(i=Te(n),t[0]+=i*Math.ceil((n[0]-t[0])/i),o.wf(t,e))),i=e[0],n=e[1],t=e[2],(o=!(o.minZoom>i||i>o.maxZoom)&&(!(o=(r=o.G())?ai(o,r,i):o.a?o.a[i]:null)||_o(o,n,t)))?e:null}function i1(t,e){ft.call(this,t),this.tile=e}function r1(t){Qm.call(this,{attributions:t.attributions,cacheSize:t.cacheSize,extent:t.extent,logo:t.logo,opaque:t.opaque,projection:t.projection,state:t.state,tileGrid:t.tileGrid,tilePixelRatio:t.tilePixelRatio,wrapX:t.wrapX}),this.tileLoadFunction=t.tileLoadFunction,this.tileUrlFunction=this.zc?this.zc.bind(this):qm,this.urls=null,t.urls?this.Ya(t.urls):t.url&&this.cb(t.url),t.tileUrlFunction&&this.Xa(t.tileUrlFunction)}function A(t){r1.call(this,{attributions:t.attributions,cacheSize:t.cacheSize,extent:t.extent,logo:t.logo,opaque:t.opaque,projection:t.projection,state:t.state,tileGrid:t.tileGrid,tileLoadFunction:t.tileLoadFunction||p1,tilePixelRatio:t.tilePixelRatio,tileUrlFunction:t.tileUrlFunction,url:t.url,urls:t.urls,wrapX:t.wrapX}),this.crossOrigin=void 0!==t.crossOrigin?t.crossOrigin:null,this.tileClass=void 0!==t.tileClass?t.tileClass:gb,this.i={},this.v={},this.na=t.reprojectionErrorThreshold,this.C=!1}function n1(t,e,o,i,r,n,s){return r=(o=o1(t,e=[e,o,i],n))?t.tileUrlFunction(o,r,n):void 0,(r=new t.tileClass(e,void 0!==r?0:4,void 0!==r?r:"",t.crossOrigin,t.tileLoadFunction)).key=s,c(r,"change",t.Jh,t),r}function s1(t,e,o,i,r,n){var s=t.Lb(e,o,i),p=t.jc;if(t.a.b.hasOwnProperty(s)){if((h=t.a.get(s)).key!=p){var a=h,h=n1(t,e,o,i,r,n,p);if(0==a.W()?h.a=a.a:h.a=a,h.a){e=h.a,o=h;do{if(e.W()==Hr){e.a=null;break}}while(1!=e.W()&&0==e.W()?o.a=e.a:o=e,e=o.a)}t.a.replace(s,h)}}else h=n1(t,e,o,i,r,n,p),t.a.set(s,h);return h}function p1(t,e){t.ub().src=e}function E(t){this.A=void 0!==t.hidpi&&t.hidpi,A.call(this,{cacheSize:t.cacheSize,crossOrigin:"anonymous",opaque:!0,projection:Ye("EPSG:3857"),reprojectionErrorThreshold:t.reprojectionErrorThreshold,state:"loading",tileLoadFunction:t.tileLoadFunction,tilePixelRatio:this.A?2:1,wrapX:void 0===t.wrapX||t.wrapX}),this.P=void 0!==t.culture?t.culture:"en-us",this.u=void 0!==t.maxZoom?t.maxZoom:-1,this.c=t.key,this.o=t.imagerySet,zm("https://dev.virtualearth.net/REST/v1/Imagery/Metadata/"+this.o+"?uriScheme=https&include=ImageryProviders&key="+this.c,this.fa.bind(this),void 0,"jsonp")}e(Hm,Wr),Hm.prototype.oa=function(){1==this.state&&(this.kd.forEach(lt),this.kd=null),Wr.prototype.oa.call(this)},Hm.prototype.ub=function(){return this.H},Hm.prototype.Ld=function(){var t,e,o,i,r,n=[];this.g.forEach(function(t){t&&t.W()==Hr&&n.push({extent:this.f.Na(t.Ca),image:t.ub()})},this),(this.g.length=0)===n.length?this.state=3:(o=this.o[0],t="number"==typeof(e=this.l.Za(o))?e:e[0],e="number"==typeof e?e:e[1],o=this.l.Ha(o),i=this.f.Ha(this.i),r=this.l.Na(this.o),this.H=jl(t,e,this.A,i,this.f.G(),o,r,this.v,n,this.u,this.C),this.state=Hr),this.s()},Hm.prototype.load=function(){var i;0==this.state&&(this.state=1,this.s(),i=0,this.kd=[],this.g.forEach(function(e){var o,t=e.W();0!=t&&1!=t||(i++,o=c(e,"change",function(){var t=e.W();t!=Hr&&3!=t&&4!=t||(lt(o),0===--i&&(this.kd.forEach(lt),this.kd=null,this.Ld()))},this),this.kd.push(o))},this),this.g.forEach(function(t){0==t.W()&&t.load()}),0===i)&&setTimeout(this.Ld.bind(this),0)},e(_m,Ql),_m.prototype.Wc=function(t){for(var e,o,i;$m(this);){if((e=(i=(o=this.a.Rc).Ca[0].toString())in t)&&(o=o.Ca,e=_o(t[i],o[1],o[2])),e)break;yt(this.pop())}},e(Qm,Il),(d=Qm.prototype).Ih=function(){return $m(this.a)},d.Wc=function(t,e){t=this.Cd(t);t&&t.Wc(e)},d.qf=function(){return 0},d.Lb=function(t,e,o){return t+"/"+e+"/"+o},d.tf=function(){return this.sa},d.Va=function(){return this.tileGrid},d.Db=function(t){return this.tileGrid||ui(t)},d.Cd=function(t){var e=this.f;return e&&!Ze(e,t)?null:this.a},d.jb=function(){return this.xa},d.Dd=function(t,e,o){return o=this.Db(o),e=this.jb(e),t=ei(o.Za(t),this.l),1==e?t:ti(t,e,this.l)},d.wa=function(){this.a.clear(),this.s()},d.ig=G,e(i1,ft),e(r1,Qm),(d=r1.prototype).ib=function(){return this.tileLoadFunction},d.kb=function(){return this.tileUrlFunction},d.lb=function(){return this.urls},d.Jh=function(t){switch((t=t.target).W()){case 1:this.b(new i1("tileloadstart",t));break;case Hr:this.b(new i1("tileloadend",t));break;case 3:this.b(new i1("tileloaderror",t))}},d.sb=function(t){this.a.clear(),this.tileLoadFunction=t,this.s()},d.Xa=function(t,e){this.tileUrlFunction=t,void 0!==e?e1(this,e):this.s()},d.cb=function(t){var e=this.urls=Jm(t);this.Xa(this.zc?this.zc.bind(this):Ym(e,this.tileGrid),t)},d.Ya=function(t){var e=(this.urls=t).join("\n");this.Xa(this.zc?this.zc.bind(this):Ym(t,this.tileGrid),e)},d.ig=function(t,e,o){t=this.Lb(t,e,o),this.a.b.hasOwnProperty(t)&&this.a.get(t)},e(A,r1),(d=A.prototype).Ih=function(){if($m(this.a))return!0;for(var t in this.i)if($m(this.i[t]))return!0;return!1},d.Wc=function(t,e){var o,i=this.Cd(t);for(o in this.a.Wc(this.a==i?e:{}),this.i){var r=this.i[o];r.Wc(r==i?e:{})}},d.qf=function(t){return this.f&&t&&!Ze(this.f,t)?0:this.rf()},d.rf=function(){return 0},d.tf=function(t){return!(this.f&&t&&!Ze(this.f,t))&&r1.prototype.tf.call(this,t)},d.Db=function(t){var e=this.f;return!this.tileGrid||e&&!Ze(e,t)?((e=B(t).toString())in this.v||(this.v[e]=ui(t)),this.v[e]):this.tileGrid},d.Cd=function(t){var e=this.f;return!e||Ze(e,t)?this.a:((t=B(t).toString())in this.i||(this.i[t]=new _m),this.i[t])},d.Dc=function(t,e,o,i,r){var n,s,p,a,h,l;return this.f&&r&&!Ze(this.f,r)?(n=this.Cd(r),t=this.Lb.apply(this,o=[t,e,o]),n.b.hasOwnProperty(t)&&(s=n.get(t)),e=this.jc,s&&s.key==e?s:(p=this.f,a=this.Db(p),h=this.Db(r),l=o1(this,o,r),(i=new Hm(p,a,r,h,o,l,this.jb(i),this.rf(),function(t,e,o,i){return s1(this,t,e,o,i,p)}.bind(this),this.na,this.C)).key=e,s?(i.a=s,n.replace(t,i)):n.set(t,i),i)):s1(this,t,e,o,i,r)},d.Hb=function(t){if(this.C!=t){for(var e in this.C=t,this.i)this.i[e].clear();this.s()}},d.Ib=function(t,e){t=Ye(t);!t||(t=B(t).toString())in this.v||(this.v[t]=e)},e(E,A);var a1=new gi({html:'<a class="ol-attribution-bing-tos" href="http://www.microsoft.com/maps/product/terms.html">Terms of Use</a>'});function C(t){var e=void 0!==(t=t||{}).projection?t.projection:"EPSG:3857",o=void 0!==t.tileGrid?t.tileGrid:ci({extent:fi(e),maxZoom:t.maxZoom,minZoom:t.minZoom,tileSize:t.tileSize});A.call(this,{attributions:t.attributions,cacheSize:t.cacheSize,crossOrigin:t.crossOrigin,logo:t.logo,opaque:t.opaque,projection:e,reprojectionErrorThreshold:t.reprojectionErrorThreshold,tileGrid:o,tileLoadFunction:t.tileLoadFunction,tilePixelRatio:t.tilePixelRatio,tileUrlFunction:t.tileUrlFunction,url:t.url,urls:t.urls,wrapX:void 0===t.wrapX||t.wrapX})}function j(t){this.u=t.account,this.A=t.map||"",this.c=t.config||{},this.o={},C.call(this,{attributions:t.attributions,cacheSize:t.cacheSize,crossOrigin:t.crossOrigin,logo:t.logo,maxZoom:void 0!==t.maxZoom?t.maxZoom:18,minZoom:t.minZoom,projection:t.projection,state:"loading",wrapX:t.wrapX}),h1(this)}function h1(t){var e,o,i=JSON.stringify(t.c);t.o[i]?l1(t,t.o[i]):(e="https://"+t.u+".cartodb.com/api/v1/map",t.A&&(e+="/named/"+t.A),(o=new XMLHttpRequest).addEventListener("load",t.Sk.bind(t,i)),o.addEventListener("error",t.Rk.bind(t)),o.open("POST",e),o.setRequestHeader("Content-type","application/json"),o.send(JSON.stringify(t.c)))}function l1(t,e){t.cb("https://"+e.cdn_url.https+"/"+t.u+"/api/v1/map/"+e.layergroupid+"/{z}/{x}/{y}.png")}function L(t){v.call(this,{attributions:t.attributions,extent:t.extent,logo:t.logo,projection:t.projection,wrapX:t.wrapX}),this.C=void 0,this.fa=void 0!==t.distance?t.distance:20,this.A=[],this.na=t.geometryFunction||function(t){return V((t=t.V())instanceof m,10),t},this.u=t.source,this.u.J("change",L.prototype.Ja,this)}function u1(t){if(void 0!==t.C){t.A.length=0;for(var e=pe(),o=t.fa*t.C,i=t.u.we(),r={},n=0,s=i.length;n<s;n++){var p=i[n];B(p).toString()in r||!(p=t.na(p))||(he(p=p.$(),e),te(e,o,e),p=(p=t.u.nf(e)).filter(function(t){return!((t=B(t).toString())in r)&&(r[t]=!0)}),t.A.push(function(t,e){for(var o=[0,0],i=e.length-1;0<=i;--i){var r=t.na(e[i]);r?Bt(o,r.$()):e.splice(i,1)}return Wt(o,1/e.length),(o=new x(new m(o))).set("features",e),o}(t,p)))}}}function c1(t,e){var o=[],i=(Object.keys(e).forEach(function(t){null!==e[t]&&void 0!==e[t]&&o.push(t+"="+encodeURIComponent(e[t]))}),o.join("&"));return(t=-1===(t=t.replace(/[?&]$/,"")).indexOf("?")?t+"?":t+"&")+i}function y1(t){Ol.call(this,{attributions:(t=t||{}).attributions,logo:t.logo,projection:t.projection,resolutions:t.resolutions}),this.Z=void 0!==t.crossOrigin?t.crossOrigin:null,this.i=t.url,this.l=void 0!==t.imageLoadFunction?t.imageLoadFunction:Bl,this.u=t.params||{},this.c=null,this.v=[0,0],this.P=0,this.C=void 0!==t.ratio?t.ratio:1.5}function f1(t){Ol.call(this,{projection:t.projection,resolutions:t.resolutions}),this.Z=void 0!==t.crossOrigin?t.crossOrigin:null,this.v=void 0!==t.displayDpi?t.displayDpi:96,this.l=t.params||{},this.P=t.url,this.c=void 0!==t.imageLoadFunction?t.imageLoadFunction:Bl,this.fa=void 0===t.hidpi||t.hidpi,this.na=void 0!==t.metersPerUnit?t.metersPerUnit:1,this.u=void 0!==t.ratio?t.ratio:1,this.xa=void 0!==t.useOverlay&&t.useOverlay,this.i=null,this.C=0}function g1(t){var e=t.imageExtent,o=void 0!==t.crossOrigin?t.crossOrigin:null,i=void 0!==t.imageLoadFunction?t.imageLoadFunction:Bl;Ol.call(this,{attributions:t.attributions,logo:t.logo,projection:Ye(t.projection)}),this.c=new tp(e,void 0,1,this.j,t.url,o,i),this.i=t.imageSize||null,c(this.c,"change",this.o,this)}function d1(t){Ol.call(this,{attributions:(t=t||{}).attributions,logo:t.logo,projection:t.projection,resolutions:t.resolutions}),this.na=void 0!==t.crossOrigin?t.crossOrigin:null,this.l=t.url,this.C=void 0!==t.imageLoadFunction?t.imageLoadFunction:Bl,this.i=t.params||{},this.u=!0,m1(this),this.fa=t.serverType,this.xa=void 0===t.hidpi||t.hidpi,this.c=null,this.P=[0,0],this.Z=0,this.v=void 0!==t.ratio?t.ratio:1.5}E.prototype.U=function(){return this.c},E.prototype.Z=function(){return this.o},E.prototype.fa=function(t){var e,i,s,p,r,n,a;200!=t.statusCode||"OK"!=t.statusDescription||"ValidCredentials"!=t.authenticationResultCode||1!=t.resourceSets.length||1!=t.resourceSets[0].resources.length?kl(this,"error"):(-1==(e=t.brandLogoUri).indexOf("https")&&(e=e.replace("http","https")),i=t.resourceSets[0].resources[0],s=-1==this.u?i.zoomMax:this.u,p=ci({extent:t=fi(this.f),minZoom:i.zoomMin,maxZoom:s,tileSize:(i.imageWidth==i.imageHeight?i.imageWidth:[i.imageWidth,i.imageHeight])/this.jb()}),this.tileGrid=p,r=this.P,n=this.A,this.tileUrlFunction=Zm(i.imageUrlSubdomains.map(function(t){var e=[0,0,0],o=i.imageUrl.replace("{subdomain}",t).replace("{culture}",r);return function(t){if(t)return oi(t[0],t[1],-t[2]-1,e),t=o,n&&(t+="&dpi=d1&device=mobile"),t.replace("{quadkey}",function(t){for(var e,o=t[0],i=Array(o),r=1<<o-1,n=0;n<o;++n)e=48,t[1]&r&&(e+=1),t[2]&r&&(e+=2),i[n]=String.fromCharCode(e),r>>=1;return i.join("")}(e))}})),i.imageryProviders&&(a=Je(Ye("EPSG:4326"),this.f),(t=i.imageryProviders.map(function(t){var e=t.attribution,n={};return t.coverageAreas.forEach(function(t){var e,o,i=t.zoomMin,r=Math.min(t.zoomMax,s);for(t=je([(t=t.bbox)[1],t[0],t[3],t[2]],a),e=i;e<=r;++e)o=e.toString(),i=ai(p,t,e),o in n?n[o].push(i):n[o]=[i]}),new gi({html:e,tileRanges:n})})).push(a1),this.ua(t)),this.L=e,kl(this,"ready"))},e(C,A),e(j,C),(d=j.prototype).$j=function(){return this.c},d.wp=function(t){Q(this.c,t),h1(this)},d.fp=function(t){this.c=t||{},h1(this)},d.Sk=function(t,e){var o,e=e.target;if(!e.status||200<=e.status&&e.status<300){try{o=JSON.parse(e.responseText)}catch(t){return void kl(this,"error")}l1(this,o),this.o[t]=o,kl(this,"ready")}else kl(this,"error")},d.Rk=function(){kl(this,"error")},e(L,v),L.prototype.Ua=function(){return this.u},L.prototype.Ed=function(t,e,o){this.u.Ed(t,e,o),e!==this.C&&(this.clear(),this.C=e,u1(this),this.Tc(this.A))},L.prototype.Ob=function(t){this.fa=t,this.Ja()},L.prototype.Ja=function(){this.clear(),u1(this),this.Tc(this.A),this.s()},e(y1,Ol),(d=y1.prototype).Xm=function(){return this.u},d.Xc=function(t,e,o,i){if(void 0===this.i)return null;e=Dl(this,e);var r=this.c;if(r&&this.P==this.g&&r.resolution==e&&r.f==o&&re(r.G(),t))return r;Q(r={F:"image",FORMAT:"PNG32",TRANSPARENT:!0},this.u);var n=((t=t.slice())[0]+t[2])/2,s=(t[1]+t[3])/2,p=(1!=this.C&&(p=this.C*Te(t)/2,a=this.C*xe(t)/2,t[0]=n-p,t[1]=s-a,t[2]=n+p,t[3]=s+a),e/o),a=Math.ceil(Te(t)/p),h=Math.ceil(xe(t)/p);return t[0]=n-p*a/2,t[2]=n+p*a/2,t[1]=s-p*h/2,t[3]=s+p*h/2,this.v[0]=a,this.v[1]=h,n=t,s=this.v,i=i.hb.split(":").pop(),r.SIZE=s[0]+","+s[1],r.BBOX=n.join(","),r.BBOXSR=i,r.IMAGESR=i,r.DPI=90*o,(n=(i=this.i).replace(/MapServer\/?$/,"MapServer/export").replace(/ImageServer\/?$/,"ImageServer/exportImage"))==i&&V(!1,50),r=c1(n,r),this.c=new tp(t,e,o,this.j,r,this.Z,this.l),this.P=this.g,c(this.c,"change",this.o,this),this.c},d.Wm=function(){return this.l},d.Ym=function(){return this.i},d.Zm=function(t){this.c=null,this.l=t,this.s()},d.$m=function(t){t!=this.i&&(this.i=t,this.c=null,this.s())},d.an=function(t){Q(this.u,t),this.c=null,this.s()},e(f1,Ol),(d=f1.prototype).cn=function(){return this.l},d.Xc=function(t,e,o){e=Dl(this,e),o=this.fa?o:1;var i,r,n,s,p,a,h,l,u=this.i;return u&&this.C==this.g&&u.resolution==e&&u.f==o&&re(u.G(),t)||(1!=this.u&&Ce(t=t.slice(),this.u),l=[Te(t)/e*o,xe(t)/e*o],void 0!==this.P?(u=this.P,i=me(t),r=this.na,n=Te(t),s=xe(t),p=l[0],a=l[1],h=.0254/this.v,l={OPERATION:this.xa?"GETDYNAMICMAPOVERLAYIMAGE":"GETMAPIMAGE",VERSION:"2.0.0",LOCALE:"en",CLIENTAGENT:"ol.source.ImageMapGuide source",CLIP:"1",SETDISPLAYDPI:this.v,SETDISPLAYWIDTH:Math.round(l[0]),SETDISPLAYHEIGHT:Math.round(l[1]),SETVIEWSCALE:p*s<a*n?n*r/(p*h):s*r/(a*h),SETVIEWCENTERX:i[0],SETVIEWCENTERY:i[1]},Q(l,this.l),u=c1(u,l),c(u=new tp(t,e,o,this.j,u,this.Z,this.c),"change",this.o,this)):u=null,this.i=u,this.C=this.g),u},d.bn=function(){return this.c},d.en=function(t){Q(this.l,t),this.s()},d.dn=function(t){this.i=null,this.c=t,this.s()},e(g1,Ol),g1.prototype.Xc=function(t){return Ae(t,this.c.G())?this.c:null},g1.prototype.o=function(t){var e,o,i,r,n;this.c.W()==ip&&(i=this.c.G(),e=this.c.a(),n=this.i?(o=this.i[0],this.i[1]):(o=e.width,e.height),(i=Math.ceil(Te(i)/(xe(i)/n)))!=o)&&(r=(i=Ri(i,n)).canvas,i.drawImage(e,0,0,o,n,0,0,r.width,r.height),this.c.g=r),Ol.prototype.o.call(this,t)},e(d1,Ol);var v1=[101,101];function b1(t,e,o,i,r,n){if(V(void 0!==t.l,9),n[t.u?"CRS":"SRS"]=r.hb,"STYLES"in t.i||(n.STYLES=""),1!=i)switch(t.fa){case"geoserver":i=90*i+.5|0,n.FORMAT_OPTIONS="FORMAT_OPTIONS"in n?n.FORMAT_OPTIONS+";dpi:"+i:"dpi:"+i;break;case"mapserver":n.MAP_RESOLUTION=90*i;break;case"carmentaserver":case"qgis":n.DPI=90*i;break;default:V(!1,8)}return n.WIDTH=o[0],n.HEIGHT=o[1],o=r.b,r=t.u&&"ne"==o.substr(0,2)?[e[1],e[0],e[3],e[2]]:e,n.BBOX=r.join(","),c1(t.l,n)}function m1(t){t.u=0<=Dt(t.i.VERSION||"1.3.0")}function w1(t){var e=void 0!==(t=t||{}).attributions?t.attributions:[M1];C.call(this,{attributions:e,cacheSize:t.cacheSize,crossOrigin:void 0!==t.crossOrigin?t.crossOrigin:"anonymous",opaque:void 0===t.opaque||t.opaque,maxZoom:void 0!==t.maxZoom?t.maxZoom:19,reprojectionErrorThreshold:t.reprojectionErrorThreshold,tileLoadFunction:t.tileLoadFunction,url:void 0!==t.url?t.url:"https://{a-c}.tile.openstreetmap.org/{z}/{x}/{y}.png",wrapX:t.wrapX})}(d=d1.prototype).ln=function(t,e,o,i){var r,n;if(void 0!==this.l)return r=we(t,e,0,v1),n={SERVICE:"WMS",VERSION:"1.3.0",REQUEST:"GetFeatureInfo",FORMAT:"image/png",TRANSPARENT:!0,QUERY_LAYERS:this.i.LAYERS},Q(n,this.i,i),i=Math.floor((r[3]-t[1])/e),n[this.u?"I":"X"]=Math.floor((t[0]-r[0])/e),n[this.u?"J":"Y"]=i,b1(this,r,v1,1,Ye(o),n)},d.nn=function(){return this.i},d.Xc=function(t,e,o,i){if(void 0===this.l)return null;e=Dl(this,e),1==o||this.xa&&void 0!==this.fa||(o=1);var r,n=((t=t.slice())[0]+t[2])/2,s=(t[1]+t[3])/2,p=Te(t)/(a=e/o),a=xe(t)/a,h=this.c;return h&&this.Z==this.g&&h.resolution==e&&h.f==o&&re(h.G(),t)?h:(1!=this.v&&(h=this.v*Te(t)/2,r=this.v*xe(t)/2,t[0]=n-h,t[1]=s-r,t[2]=n+h,t[3]=s+r),Q(n={SERVICE:"WMS",VERSION:"1.3.0",REQUEST:"GetMap",FORMAT:"image/png",TRANSPARENT:!0},this.i),this.P[0]=Math.ceil(p*this.v),this.P[1]=Math.ceil(a*this.v),i=b1(this,t,this.P,o,i,n),this.c=new tp(t,e,o,this.j,i,this.na,this.C),this.Z=this.g,c(this.c,"change",this.o,this),this.c)},d.mn=function(){return this.C},d.pn=function(){return this.l},d.qn=function(t){this.c=null,this.C=t,this.s()},d.rn=function(t){t!=this.l&&(this.l=t,this.c=null,this.s())},d.sn=function(t){Q(this.i,t),m1(this),this.c=null,this.s()},e(w1,C);var x1,S1,M1=new gi({html:'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors.'});function P1(t){this.C=null,this.xa=void 0!==t.operationType?t.operationType:L1,this.Ja=void 0!==t.threads?t.threads:1,this.c=function(t){for(var e=t.length,o=Array(e),i=0;i<e;++i){var r=i,n=t[i],s=null;n instanceof Qm?(n=new p({source:n}),s=new Zp(n)):n instanceof Ol&&(n=new Js({source:n}),s=new Yp(n)),o[r]=s}return o}(t.sources);for(var e=0,o=this.c.length;e<o;++e)c(this.c[e],"change",this.s,this);this.i=Ri(),this.fa=new $r(function(){return 1},this.s.bind(this));for(var o={},i=0,r=(e=this.c.map(function(t){return ss(t.a)})).length;i<r;++i)o[B(e[i].layer)]=e[i];this.l=this.v=null,this.Z={animate:!1,attributions:{},coordinateToPixelTransform:Is(),extent:null,focus:null,index:0,layerStates:o,layerStatesArray:e,logos:{},pixelRatio:1,pixelToCoordinateTransform:Is(),postRenderFunctions:[],size:[0,0],skippedFeatureUids:{},tileQueue:this.fa,time:Date.now(),usedTiles:{},viewState:{rotation:0},viewHints:[],wantedTiles:{}},Ol.call(this,{}),void 0!==t.operation&&this.u(t.operation,t.lib)}function T1(t,e,o){var i=t.v;return!i||t.g!==i.bp||o!==i.resolution||!ue(e,i.extent)}S1={ma:x1={}},function(t){"object"==typeof x1&&void 0!==S1?S1.ma=t():("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Sp=t()}(function(){return function i(r,n,s){function p(o,t){if(!n[o]){if(!r[o]){var e="function"==typeof require&&require;if(!t&&e)return e(o,!0);if(a)return a(o,!0);throw(e=Error("Cannot find module '"+o+"'")).code="MODULE_NOT_FOUND",e}e=n[o]={ma:{}},r[o][0].call(e.ma,function(t){var e=r[o][1][t];return p(e||t)},e,e.ma,i,r,n,s)}return n[o].ma}for(var a="function"==typeof require&&require,t=0;t<s.length;t++)p(s[t]);return p}({1:[function(t,e,o){t=t("./processor"),o.ij=t},{"./processor":2}],2:[function(t,e){function p(c){var y=!0;try{new ImageData(10,10)}catch(t){y=!1}return function(t){var e=t.buffers,o=t.meta,i=t.width,r=t.height,n=e.length,s=e[0].byteLength;if(t.imageOps){for(s=Array(n),t=0;t<n;++t){var p=s,a=t,h=new Uint8ClampedArray(e[t]),l=i,u=r;h=y?new ImageData(h,l,u):{data:h,width:l,height:u},p[a]=h}i=c(s,o).data}else{for(i=new Uint8ClampedArray(s),r=Array(n),p=Array(n),t=0;t<n;++t)r[t]=new Uint8ClampedArray(e[t]),p[t]=[0,0,0,0];for(e=0;e<s;e+=4){for(t=0;t<n;++t)a=r[t],p[t][0]=a[e],p[t][1]=a[e+1],p[t][2]=a[e+2],p[t][3]=a[e+3];t=c(p,o),i[e]=t[0],i[e+1]=t[1],i[e+2]=t[2],i[e+3]=t[3]}}return i.buffer}}function o(t){this.Ze=!!t.nl;var e,o,i,r,n=[];if(e=0===t.threads?0:!this.Ze&&t.threads||1)for(var s=0;s<e;++s)n[s]=function(e,t){var o=Object.keys(e.lib||{}).map(function(t){return"var "+t+" = "+e.lib[t].toString()+";"}).concat(["var __minion__ = ("+p.toString()+")(",e.operation.toString(),");",'self.addEventListener("message", function(event) {',"  var buffer = __minion__(event.data);","  self.postMessage({buffer: buffer, meta: event.data.meta}, [buffer]);","});"]),o=URL.createObjectURL(new Blob(o,{type:"text/javascript"}));return(o=new Worker(o)).addEventListener("message",t),o}(t,this.sg.bind(this,s));else n[0]=(o=t,i=this.sg.bind(this,0),r=p(o.operation),{postMessage:function(t){setTimeout(function(){i({data:{buffer:r(t),meta:t.meta}})},0)}});this.Wd=n,this.qd=[],this.wj=t.yo||1/0,this.Ud=0,this.Sc={},this.$e=null}var a=t("./util").Hl;o.prototype.wo=function(t,e,o){this.uj({Fc:t,hh:e,Vc:o}),this.pg()},o.prototype.uj=function(t){for(this.qd.push(t);this.qd.length>this.wj;)this.qd.shift().Vc(null,null)},o.prototype.pg=function(){if(0===this.Ud&&0<this.qd.length){var t=this.$e=this.qd.shift(),e=t.Fc[0].width,o=t.Fc[0].height,i=t.Fc.map(function(t){return t.data.buffer}),r=this.Wd.length;if(1===(this.Ud=r))this.Wd[0].postMessage({buffers:i,meta:t.hh,imageOps:this.Ze,width:e,height:o},i);else for(var n=4*Math.ceil(t.Fc[0].data.length/4/r),s=0;s<r;++s){for(var p=s*n,a=[],h=0,l=i.length;h<l;++h)a.push(i[s].slice(p,p+n));this.Wd[s].postMessage({buffers:a,meta:t.hh,imageOps:this.Ze,width:e,height:o},a)}}},o.prototype.sg=function(t,e){this.Qp||(this.Sc[t]=e.data,--this.Ud,0===this.Ud&&this.xj())},o.prototype.xj=function(){var t=this.$e,e=this.Wd.length;if(1===e)i=new Uint8ClampedArray(this.Sc[0].buffer),r=this.Sc[0].meta;else for(var o=t.Fc[0].data.length,i=new Uint8ClampedArray(o),r=Array(o),o=4*Math.ceil(o/4/e),n=0;n<e;++n){var s=n*o;i.set(new Uint8ClampedArray(this.Sc[n].buffer),s),r[n]=this.Sc[n].meta}this.$e=null,this.Sc={},t.Vc(null,a(i,t.Fc[0].width,t.Fc[0].height),r),this.pg()},e.ma=o},{"./util":3}],3:[function(t,e,o){var i=!0;try{new ImageData(10,10)}catch(t){i=!1}var r=document.createElement("canvas").getContext("2d");o.Hl=function(t,e,o){return i?new ImageData(t,e,o):((e=r.createImageData(e,o)).data.set(t),e)}},{}]},{},[1])(1)}),Nh=S1.ma,e(P1,Ol),P1.prototype.u=function(t,e){this.C=new Nh.ij({operation:t,nl:this.xa===R1,yo:1,lib:e,threads:this.Ja}),this.s()},P1.prototype.U=function(t,e,o,i){o=!0;for(var r,n=0,s=this.c.length;n<s;++n)if("ready"!==(r=this.c[n].a.la()).W()){o=!1;break}if(!o)return null;if(!T1(this,t=t.slice(),e))return this.l;o=this.i.canvas,r=Math.round(Te(t)/e),n=Math.round(xe(t)/e),r===o.width&&n===o.height||(o.width=r,o.height=n),(r=Q({},this.Z)).viewState=Q({},r.viewState);var n=me(t),s=Math.round(Te(t)/e),p=Math.round(xe(t)/e);return r.extent=t,r.focus=me(t),r.size[0]=s,r.size[1]=p,(s=r.viewState).center=n,s.projection=i,s.resolution=e,this.l=i=new Tl(t,e,1,this.j,o,this.P.bind(this,r)),this.v={extent:t,resolution:e,bp:this.g},i},P1.prototype.P=function(t,e){for(var o=this.c.length,i=Array(o),r=0;r<o;++r){var n,s,p,a=this.c[r],h=t,l=t.layerStatesArray[r];if(!(a=a.o(h,l)?(n=h.size[0],s=h.size[1],!A1||(p=A1.canvas).width!==n||p.height!==s?A1=Ri(n,s):A1.clearRect(0,0,n,s),a.D(h,l,A1),A1.getImageData(0,0,n,s)):null)){i=null;break}i[r]=a}i&&(this.b(new E1(C1,t,o={})),this.C.wo(i,o,this.na.bind(this,t,e))),Qr(t.tileQueue,16,16)},P1.prototype.na=function(t,e,o,i,r){o?e(o):i&&(this.b(new E1(j1,t,r)),T1(this,t.extent,t.viewState.resolution/t.pixelRatio)||this.i.putImageData(i,0,0),e(null))};var A1=null;function E1(t,e,o){ft.call(this,t),this.extent=e.extent,this.resolution=e.viewState.resolution/e.pixelRatio,this.data=o}e(E1,ft);var C1="beforeoperations",j1="afteroperations",L1="pixel",R1="image";function N1(t){var e=t.layer.indexOf("-"),e=k1[-1==e?t.layer:t.layer.slice(0,e)],o=F1[t.layer];C.call(this,{attributions:I1,cacheSize:t.cacheSize,crossOrigin:"anonymous",maxZoom:(null!=t.maxZoom?t:e).maxZoom,minZoom:(null!=t.minZoom?t:e).minZoom,opaque:o.opaque,reprojectionErrorThreshold:t.reprojectionErrorThreshold,tileLoadFunction:t.tileLoadFunction,url:void 0!==t.url?t.url:"https://stamen-tiles-{a-d}.a.ssl.fastly.net/"+t.layer+"/{z}/{x}/{y}."+o.Bb})}e(N1,C);var I1=[new gi({html:'Map tiles by <a href="http://stamen.com/">Stamen Design</a>, under <a href="http://creativecommons.org/licenses/by/3.0/">CC BY 3.0</a>.'}),M1],F1={terrain:{Bb:"jpg",opaque:!0},"terrain-background":{Bb:"jpg",opaque:!0},"terrain-labels":{Bb:"png",opaque:!1},"terrain-lines":{Bb:"png",opaque:!1},"toner-background":{Bb:"png",opaque:!0},toner:{Bb:"png",opaque:!0},"toner-hybrid":{Bb:"png",opaque:!1},"toner-labels":{Bb:"png",opaque:!1},"toner-lines":{Bb:"png",opaque:!1},"toner-lite":{Bb:"png",opaque:!0},watercolor:{Bb:"jpg",opaque:!0}},k1={terrain:{minZoom:4,maxZoom:18},toner:{minZoom:0,maxZoom:20},watercolor:{minZoom:1,maxZoom:16}};function R(t){A.call(this,{attributions:(t=t||{}).attributions,cacheSize:t.cacheSize,crossOrigin:t.crossOrigin,logo:t.logo,projection:t.projection,reprojectionErrorThreshold:t.reprojectionErrorThreshold,tileGrid:t.tileGrid,tileLoadFunction:t.tileLoadFunction,url:t.url,urls:t.urls,wrapX:void 0===t.wrapX||t.wrapX}),this.c=t.params||{},this.o=pe(),e1(this,O1(this))}function O1(t){var e,o=0,i=[];for(e in t.c)i[o++]=e+"-"+t.c[e];return i.join("/")}function D1(t){Qm.call(this,{opaque:!1,projection:t.projection,tileGrid:t.tileGrid,wrapX:void 0===t.wrapX||t.wrapX})}function B1(t,e,o){Wr.call(this,t,Hr),this.i=e,this.f=o,this.g=null}function N(t){var e;this.c=null,A.call(this,{attributions:t.attributions,cacheSize:t.cacheSize,crossOrigin:t.crossOrigin,projection:Ye("EPSG:3857"),reprojectionErrorThreshold:t.reprojectionErrorThreshold,state:"loading",tileLoadFunction:t.tileLoadFunction,wrapX:void 0===t.wrapX||t.wrapX}),t.jsonp?zm(t.url,this.Gh.bind(this),this.ue.bind(this)):((e=new XMLHttpRequest).addEventListener("load",this.vn.bind(this)),e.addEventListener("error",this.tn.bind(this)),e.open("GET",t.url),e.send())}function U1(t){var e;Qm.call(this,{projection:Ye("EPSG:3857"),state:"loading"}),this.v=void 0===t.preemptive||t.preemptive,this.o=qm,this.i=void 0,this.c=t.jsonp||!1,t.url?this.c?zm(t.url,this.Kf.bind(this),this.ve.bind(this)):((e=new XMLHttpRequest).addEventListener("load",this.zn.bind(this)),e.addEventListener("error",this.yn.bind(this)),e.open("GET",t.url),e.send()):t.tileJSON?this.Kf(t.tileJSON):V(!1,51)}function G1(t,e,o,i,r,n){Wr.call(this,t,e),this.o=o,this.g=i,this.H=r,this.f=this.l=this.i=null,this.v=n}function K1(t){var e;0==t.state&&(t.state=1,t.v?zm(t.o,t.Hh.bind(t),t.ge.bind(t)):((e=new XMLHttpRequest).addEventListener("load",t.xn.bind(t)),e.addEventListener("error",t.wn.bind(t)),e.open("GET",t.o),e.send()))}function I(t){var e=(t=t||{}).params||{};A.call(this,{attributions:t.attributions,cacheSize:t.cacheSize,crossOrigin:t.crossOrigin,logo:t.logo,opaque:!(!("TRANSPARENT"in e)||e.TRANSPARENT),projection:t.projection,reprojectionErrorThreshold:t.reprojectionErrorThreshold,tileGrid:t.tileGrid,tileLoadFunction:t.tileLoadFunction,url:t.url,urls:t.urls,wrapX:void 0===t.wrapX||t.wrapX}),this.u=void 0!==t.gutter?t.gutter:0,this.c=e,this.o=!0,this.A=t.serverType,this.U=void 0===t.hidpi||t.hidpi,this.P="",V1(this),this.Z=pe(),z1(this),e1(this,W1(this))}function X1(t,e,o,i,r,n,s){var p=t.urls;if(p){if(s.WIDTH=o[0],s.HEIGHT=o[1],s[t.o?"CRS":"SRS"]=n.hb,"STYLES"in t.c||(s.STYLES=""),1!=r)switch(t.A){case"geoserver":o=90*r+.5|0,s.FORMAT_OPTIONS="FORMAT_OPTIONS"in s?s.FORMAT_OPTIONS+";dpi:"+o:"dpi:"+o;break;case"mapserver":s.MAP_RESOLUTION=90*r;break;case"carmentaserver":case"qgis":s.DPI=90*r;break;default:V(!1,52)}return n=n.b,t.o&&"ne"==n.substr(0,2)&&(t=i[0],i[0]=i[1],i[1]=t,t=i[2],i[2]=i[3],i[3]=t),s.BBOX=i.join(","),c1(1==p.length?p[0]:p[q((e[1]<<e[0])+e[2],p.length)],s)}}function V1(t){var e=0,o=[];if(t.urls)for(var i=0,r=t.urls.length;i<r;++i)o[e++]=t.urls[i];t.P=o.join("#")}function W1(t){var e,o=0,i=[];for(e in t.c)i[o++]=e+"-"+t.c[e];return i.join("/")}function z1(t){t.o=0<=Dt(t.c.VERSION||"1.3.0")}function H1(t,e,o,i,r){Wr.call(this,t,e),this.f=Ri(),this.l=i,this.i=null,this.g={xd:!1,bg:null,li:-1,cg:-1,jd:null},this.H=r,this.o=o}function Y1(t,e){t.wi(vc(e,t.l,t.ho.bind(t),t.fo.bind(t)))}function Z1(t){r1.call(this,{attributions:t.attributions,cacheSize:void 0!==t.cacheSize?t.cacheSize:128,extent:t.extent,logo:t.logo,opaque:!1,projection:t.projection,state:t.state,tileGrid:t.tileGrid,tileLoadFunction:t.tileLoadFunction||Y1,tileUrlFunction:t.tileUrlFunction,tilePixelRatio:t.tilePixelRatio,url:t.url,urls:t.urls,wrapX:void 0===t.wrapX||t.wrapX}),this.c=t.format||null,this.i=null==t.overlaps||t.overlaps,this.tileClass=t.tileClass||H1}function q1(t){this.l=t.matrixIds,ii.call(this,{extent:t.extent,origin:t.origin,origins:t.origins,resolutions:t.resolutions,tileSize:t.tileSize,tileSizes:t.tileSizes,sizes:t.sizes})}function J1(t,e,o){var r=[],n=[],s=[],p=[],a=[],h=void 0!==o?o:[],l=(o=Ye(t.SupportedCRS.replace(/urn:ogc:def:crs:(\w+):(.*:)?(\w+)$/,"$1:$3"))).ic(),u="ne"==o.b.substr(0,2);return t.TileMatrix.sort(function(t,e){return e.ScaleDenominator-t.ScaleDenominator}),t.TileMatrix.forEach(function(e){var t,o,i;(t=!(0<h.length)||Lt(h,function(t){return e.Identifier==t.TileMatrix}))&&(n.push(e.Identifier),t=28e-5*e.ScaleDenominator/l,o=e.TileWidth,i=e.TileHeight,s.push(u?[e.TopLeftCorner[1],e.TopLeftCorner[0]]:e.TopLeftCorner),r.push(t),p.push(o==i?o:[o,i]),a.push([e.MatrixWidth,-e.MatrixHeight]))}),new q1({extent:e,origins:s,resolutions:r,matrixIds:n,tileSizes:p,sizes:a})}function F(t){this.Z=void 0!==t.version?t.version:"1.0.0",this.u=void 0!==t.format?t.format:"image/jpeg",this.c=void 0!==t.dimensions?t.dimensions:{},this.A=t.layer,this.o=t.matrixSet,this.P=t.style;var e=t.urls,i=(void 0===e&&void 0!==t.url&&(e=Jm(t.url)),this.U=void 0!==t.requestEncoding?t.requestEncoding:$1),r=t.tileGrid,o={layer:this.A,style:this.P,tilematrixset:this.o},n=(i==$1&&Q(o,{Service:"WMTS",Request:"GetTile",Version:this.Z,Format:this.u}),this.c),s=e&&0<e.length?Zm(e.map(function(e){return e=i==$1?c1(e,o):e.replace(/\{(\w+?)\}/g,function(t,e){return e.toLowerCase()in o?o[e.toLowerCase()]:t}),function(t){var o;if(t)return o={TileMatrix:r.l[t[0]],TileCol:t[1],TileRow:-t[2]-1},Q(o,n),t=e,i==$1?c1(t,o):t.replace(/\{(\w+?)\}/g,function(t,e){return o[e]})}})):qm;A.call(this,{attributions:t.attributions,cacheSize:t.cacheSize,crossOrigin:t.crossOrigin,logo:t.logo,projection:t.projection,reprojectionErrorThreshold:t.reprojectionErrorThreshold,tileClass:t.tileClass,tileGrid:r,tileLoadFunction:t.tileLoadFunction,tilePixelRatio:t.tilePixelRatio,tileUrlFunction:s,urls:e,wrapX:void 0!==t.wrapX&&t.wrapX}),e1(this,_1(this))}function _1(t){var e,o=0,i=[];for(e in t.c)i[o++]=e+"-"+t.c[e];return i.join("/")}e(R,A),R.prototype.u=function(){return this.c},R.prototype.jb=function(t){return t},R.prototype.zc=function(t,e,o){var i,r,n,s=this.tileGrid;if(!((s=s||this.Db(o)).b.length<=t[0]))return i=s.Na(t,this.o),r=ei(s.Za(t[0]),this.l),1!=e&&(r=ti(r,e,this.l)),Q(s={F:"image",FORMAT:"PNG32",TRANSPARENT:!0},this.c),(n=this.urls)?(o=o.hb.split(":").pop(),s.SIZE=r[0]+","+r[1],s.BBOX=i.join(","),s.BBOXSR=o,s.IMAGESR=o,s.DPI=Math.round(s.DPI?s.DPI*e:90*e),c1(t=(1==n.length?n[0]:n[q((t[1]<<t[0])+t[2],n.length)]).replace(/MapServer\/?$/,"MapServer/export").replace(/ImageServer\/?$/,"ImageServer/exportImage"),s)):void 0},R.prototype.A=function(t){Q(this.c,t),e1(this,O1(this))},e(D1,Qm),D1.prototype.Dc=function(t,e,o){var i,r=this.Lb(t,e,o);return this.a.b.hasOwnProperty(r)?this.a.get(r):(i=ei(this.tileGrid.Za(t)),i=new B1(t=[t,e,o],i,e=(e=o1(this,t))?o1(this,e).toString():""),this.a.set(r,i),i)},e(B1,Wr),B1.prototype.ub=function(){var t,e;return this.g||((e=Ri((t=this.i)[0],t[1])).strokeStyle="black",e.strokeRect(.5,.5,t[0]+.5,t[1]+.5),e.fillStyle="black",e.textAlign="center",e.textBaseline="middle",e.font="24px sans-serif",e.fillText(this.f,t[0]/2,t[1]/2),this.g=e.canvas)},e(N,A),(d=N.prototype).vn=function(t){if(!(t=t.target).status||200<=t.status&&t.status<300){var e;try{e=JSON.parse(t.responseText)}catch(t){return void this.ue()}this.Gh(e)}else this.ue()},d.tn=function(){this.ue()},d.Fk=function(){return this.c},d.Gh=function(t){var e,o=Ye("EPSG:4326"),i=this.f,r=(void 0!==t.bounds&&(n=Je(o,i),e=je(t.bounds,n)),t.minzoom||0),n=t.maxzoom||22;if(this.tileGrid=i=ci({extent:fi(i),maxZoom:n,minZoom:r}),this.tileUrlFunction=Ym(t.tiles,i),void 0!==t.attribution&&!this.j){o=void 0!==e?e:o.G(),e={};for(;r<=n;++r)e[r.toString()]=[ai(i,o,r)];this.ua([new gi({html:t.attribution,tileRanges:e})])}this.c=t,kl(this,"ready")},d.ue=function(){kl(this,"error")},e(U1,Qm),(d=U1.prototype).zn=function(t){if(!(t=t.target).status||200<=t.status&&t.status<300){var e;try{e=JSON.parse(t.responseText)}catch(t){return void this.ve()}this.Kf(e)}else this.ve()},d.yn=function(){this.ve()},d.Ck=function(){return this.i},d.Pj=function(t,e,o,i,r){var n,s,p;this.tileGrid?(e=this.tileGrid.fe(t,e),e=this.Dc(e[0],e[1],e[2],1,this.f),n=t,s=o,p=i,t=r,0==e.state&&!0===t?(at(e,"change",function(){s.call(p,this.getData(n))},e),K1(e)):!0===t?setTimeout(function(){s.call(p,this.getData(n))}.bind(e),0):s.call(p,e.getData(n))):!0===r?setTimeout(function(){o.call(i,null)},0):o.call(i,null)},d.ve=function(){kl(this,"error")},d.Kf=function(t){var e,o=Ye("EPSG:4326"),i=this.f,r=(void 0!==t.bounds&&(n=Je(o,i),e=je(t.bounds,n)),t.minzoom||0),n=t.maxzoom||22,s=(this.tileGrid=i=ci({extent:fi(i),maxZoom:n,minZoom:r}),this.i=t.template,t.grids);if(s){if(this.o=Ym(s,i),void 0!==t.attribution){for(o=void 0!==e?e:o.G(),e={};r<=n;++r)e[s=r.toString()]=[ai(i,o,r)];this.ua([new gi({html:t.attribution,tileRanges:e})])}kl(this,"ready")}else kl(this,"error")},d.Dc=function(t,e,o,i,r){var n=this.Lb(t,e,o);return this.a.b.hasOwnProperty(n)?this.a.get(n):(e=o1(this,t=[t,e,o],r),i=new G1(t,void 0!==(i=this.o(e,i,r))?0:4,void 0!==i?i:"",this.tileGrid.Na(t),this.v,this.c),this.a.set(n,i),i)},d.ig=function(t,e,o){t=this.Lb(t,e,o),this.a.b.hasOwnProperty(t)&&this.a.get(t)},e(G1,Wr),(d=G1.prototype).ub=function(){return null},d.getData=function(t){var e;return!this.i||!this.l||"string"!=typeof(e=this.i[Math.floor((1-(t[1]-this.g[1])/(this.g[3]-this.g[1]))*this.i.length)])?null:(93<=(e=e.charCodeAt(Math.floor((t[0]-this.g[0])/(this.g[2]-this.g[0])*e.length)))&&e--,35<=e&&e--,t=null,(e-=32)in this.l&&(e=this.l[e],t=this.f&&e in this.f?this.f[e]:e),t)},d.bb=function(){return this.o},d.ge=function(){this.state=3,this.s()},d.Hh=function(t){this.i=t.grid,this.l=t.keys,this.f=t.data,this.state=4,this.s()},d.xn=function(t){if(!(t=t.target).status||200<=t.status&&t.status<300){var e;try{e=JSON.parse(t.responseText)}catch(t){return void this.ge()}this.Hh(e)}else this.ge()},d.wn=function(){this.ge()},d.load=function(){this.H&&K1(this)},e(I,A),(d=I.prototype).An=function(t,e,o,i){o=Ye(o);var r,n,s,p=this.tileGrid;if(e=(p=p||this.Db(o)).fe(t,e),!(p.b.length<=e[0]))return r=p.Ha(e[0]),n=p.Na(e,this.Z),p=ei(p.Za(e[0]),this.l),0!==(s=this.u)&&(p=Qo(p,s,this.l),n=te(n,r*s,n)),s={SERVICE:"WMS",VERSION:"1.3.0",REQUEST:"GetFeatureInfo",FORMAT:"image/png",TRANSPARENT:!0,QUERY_LAYERS:this.c.LAYERS},Q(s,this.c,i),i=Math.floor((n[3]-t[1])/r),s[this.o?"I":"X"]=Math.floor((t[0]-n[0])/r),s[this.o?"J":"Y"]=i,X1(this,e,p,n,1,o,s)},d.rf=function(){return this.u},d.Lb=function(t,e,o){return this.P+A.prototype.Lb.call(this,t,e,o)},d.Bn=function(){return this.c},d.jb=function(t){return this.U&&void 0!==this.A?t:1},d.zc=function(t,e,o){var i,r,n,s=this.tileGrid;if(!((s=s||this.Db(o)).b.length<=t[0]))return 1==e||this.U&&void 0!==this.A||(e=1),i=s.Ha(t[0]),r=s.Na(t,this.Z),s=ei(s.Za(t[0]),this.l),0!==(n=this.u)&&(s=Qo(s,n,this.l),r=te(r,i*n,r)),1!=e&&(s=ti(s,e,this.l)),Q(i={SERVICE:"WMS",VERSION:"1.3.0",REQUEST:"GetMap",FORMAT:"image/png",TRANSPARENT:!0},this.c),X1(this,t,s,r,e,o,i)},d.Ya=function(t){A.prototype.Ya.call(this,t),V1(this)},d.Cn=function(t){Q(this.c,t),V1(this),z1(this),e1(this,W1(this))},e(H1,Wr),(d=H1.prototype).ub=function(){return-1==this.g.cg?null:this.f.canvas},d.Yl=function(){return this.l},d.bb=function(){return this.o},d.load=function(){0==this.state&&(this.state=1,this.s(),this.H(this,this.o),this.v(null,NaN,null))},d.ho=function(t,e){this.Ff(e),this.ri(t)},d.fo=function(){this.state=3,this.s()},d.ri=function(t){this.i=t,this.state=Hr,this.s()},d.Ff=function(t){this.j=t},d.wi=function(t){this.v=t},e(Z1,r1),Z1.prototype.Dc=function(t,e,o,i,r){var n=this.Lb(t,e,o);return this.a.b.hasOwnProperty(n)?this.a.get(n):(i=(e=o1(this,t=[t,e,o],r))?this.tileUrlFunction(e,i,r):void 0,c(i=new this.tileClass(t,void 0!==i?0:4,void 0!==i?i:"",this.c,this.tileLoadFunction),"change",this.Jh,this),this.a.set(n,i),i)},Z1.prototype.jb=function(t){return null==t?r1.prototype.jb.call(this,t):t},Z1.prototype.Dd=function(t,e){t=ei(this.tileGrid.Za(t));return[Math.round(t[0]*e),Math.round(t[1]*e)]},e(q1,ii),q1.prototype.o=function(){return this.l},e(F,A),(d=F.prototype).bk=function(){return this.c},d.Dn=function(){return this.u},d.En=function(){return this.A},d.pk=function(){return this.o},d.Ak=function(){return this.U},d.Fn=function(){return this.P},d.Hk=function(){return this.Z},d.xp=function(t){Q(this.c,t),e1(this,_1(this))};var $1="KVP";function Q1(t){var e=(s=(t=t||{}).size)[0],o=s[1],i=[],r=256;switch(void 0!==t.tierSizeCalculation?t.tierSizeCalculation:ew){case ew:for(;r<e||r<o;)i.push([Math.ceil(e/r),Math.ceil(o/r)]),r+=r;break;case ow:for(;r<e||r<o;)i.push([Math.ceil(e/r),Math.ceil(o/r)]),e>>=1,o>>=1;break;default:V(!1,53)}i.push([1,1]),i.reverse();for(var r=[1],n=[0],o=1,e=i.length;o<e;o++)r.push(1<<o),n.push(i[o-1][0]*i[o-1][1]+n[o-1]);r.reverse();var s=new ii({extent:s=[0,-s[1],s[0],0],origin:Me(s),resolutions:r}),p=t.url;A.call(this,{attributions:t.attributions,cacheSize:t.cacheSize,crossOrigin:t.crossOrigin,logo:t.logo,reprojectionErrorThreshold:t.reprojectionErrorThreshold,tileClass:tw,tileGrid:s,tileUrlFunction:function(t){var e,o;if(t)return e=t[0],o=t[1],t=-t[2]-1,p+"TileGroup"+((o+t*i[e][0]+n[e])/256|0)+"/"+e+"-"+o+"-"+t+".jpg"}})}function tw(t,e,o,i,r){gb.call(this,t,e,o,i,r),this.f=null}e(Q1,A),e(tw,gb),tw.prototype.ub=function(){var t,e;return this.f||(t=gb.prototype.ub.call(this),this.state==Hr?256==t.width&&256==t.height?this.f=t:((e=Ri(256,256)).drawImage(t,0,0),this.f=e.canvas):t)};var ew="default",ow="truncated";function iw(t,e){this.b=e,this.a=[{x:0,y:0,width:t,height:t}],this.f={},this.g=Ri(t,t),this.c=this.g.canvas}function rw(t,e,o,i){e=[e,1],0<o.width&&0<o.height&&e.push(o),0<i.width&&0<i.height&&e.push(i),t.a.splice.apply(t.a,e)}function nw(t){this.a=void 0!==(t=t||{}).initialSize?t.initialSize:256,this.g=void 0!==t.maxSize?t.maxSize:void 0!==k?k:2048,this.b=void 0!==t.space?t.space:1,this.c=[new iw(this.a,this.b)],this.f=this.a,this.i=[new iw(this.f,this.b)]}function sw(t,e,o,i,r,n,s){for(var p,a=e?t.i:t.c,h=0,l=a.length;h<l;++h){if(p=(p=a[h]).add(o,i,r,n,s))return p;p||h!==l-1||(e?(p=Math.min(2*t.f,t.g),t.f=p):(p=Math.min(2*t.a,t.g),t.a=p),p=new iw(p,t.b),a.push(p),++l)}return null}return iw.prototype.get=function(t){return this.f[t]||null},iw.prototype.add=function(t,e,o,i,r){for(var n,s=0,p=this.a.length;s<p;++s)if((n=this.a[s]).width>=e+this.b&&n.height>=o+this.b)return p={offsetX:n.x+this.b,offsetY:n.y+this.b,image:this.c},this.f[t]=p,i.call(r,this.g,n.x+this.b,n.y+this.b),t=s,e+=this.b,i=o+this.b,e=n.width-e>n.height-i?(o={x:n.x+e,y:n.y,width:n.width-e,height:n.height},{x:n.x,y:n.y+i,width:e,height:n.height-i}):(o={x:n.x+e,y:n.y,width:n.width-e,height:i},{x:n.x,y:n.y+i,width:n.width,height:n.height-i}),rw(this,t,o,e),p;return null},nw.prototype.add=function(t,e,o,i,r,n){return!(e+this.b>this.g||o+this.b>this.g)&&(i=sw(this,!1,t,e,o,i,n))?(t=sw(this,!0,t,e,o,void 0!==r?r:G,n),{offsetX:i.offsetX,offsetY:i.offsetY,image:i.image,yf:t.image}):null},t("ol.animation.bounce",function(t){var r=t.resolution,n=t.start||Date.now(),s=void 0!==t.duration?t.duration:1e3,p=t.easing||$t;return function(t,e){var o,i;return e.time<n?(e.animate=!0,e.viewHints[qo]+=1,!0):e.time<n+s&&(o=p((e.time-n)/s),i=r-e.viewState.resolution,e.animate=!0,e.viewState.resolution+=o*i,e.viewHints[qo]+=1,!0)}}),t("ol.animation.pan",function(t){var e=t.source,n=t.start||Date.now(),s=e[0],p=e[1],a=void 0!==t.duration?t.duration:1e3,h=t.easing||Jt;return function(t,e){var o,i,r;return e.time<n?(e.animate=!0,e.viewHints[qo]+=1,!0):e.time<n+a&&(o=1-h((e.time-n)/a),i=s-e.viewState.center[0],r=p-e.viewState.center[1],e.animate=!0,e.viewState.center[0]+=o*i,e.viewState.center[1]+=o*r,e.viewHints[qo]+=1,!0)}}),t("ol.animation.rotate",function(t){var r=t.rotation||0,n=t.start||Date.now(),s=void 0!==t.duration?t.duration:1e3,p=t.easing||Jt,a=t.anchor||null;return function(t,e){var o,i;return e.time<n?(e.animate=!0,e.viewHints[qo]+=1,!0):e.time<n+s&&(o=1-p((e.time-n)/s),o=(r-e.viewState.rotation)*o,e.animate=!0,e.viewState.rotation+=o,a&&((i=e.viewState.center)[0]-=a[0],i[1]-=a[1],Vt(i,o),Bt(i,a)),e.viewHints[qo]+=1,!0)}}),t("ol.animation.zoom",function(t){var r=t.resolution,n=t.start||Date.now(),s=void 0!==t.duration?t.duration:1e3,p=t.easing||Jt;return function(t,e){var o,i;return e.time<n?(e.animate=!0,e.viewHints[qo]+=1,!0):e.time<n+s&&(o=1-p((e.time-n)/s),i=r-e.viewState.resolution,e.animate=!0,e.viewState.resolution+=o*i,e.viewHints[qo]+=1,!0)}}),t("ol.Attribution",gi),gi.prototype.getHTML=gi.prototype.g,t("ol.Collection",di),di.prototype.extend=di.prototype.Bf,di.prototype.getArray=di.prototype.Il,di.prototype.getLength=di.prototype.Ub,di.prototype.insertAt=di.prototype.ke,di.prototype.removeAt=di.prototype.Zf,di.prototype.setAt=di.prototype.ep,t("ol.color.asArray",Pi),t("ol.color.asString",Ti),t("ol.colorlike.asColorLike",Li),t("ol.coordinate.add",Bt),t("ol.coordinate.createStringXY",function(e){return function(t){return Yt(t,e)}}),t("ol.coordinate.format",Kt),t("ol.coordinate.rotate",Vt),t("ol.coordinate.toStringHDMS",function(t,e){return t?Gt(t[1],"NS",e)+" "+Gt(t[0],"EW",e):""}),t("ol.coordinate.toStringXY",Yt),t("ol.DeviceOrientation",Zu),Zu.prototype.getAlpha=Zu.prototype.Vj,Zu.prototype.getBeta=Zu.prototype.Yj,Zu.prototype.getGamma=Zu.prototype.ek,Zu.prototype.getHeading=Zu.prototype.Jl,Zu.prototype.getTracking=Zu.prototype.kh,Zu.prototype.setTracking=Zu.prototype.Cf,t("ol.easing.easeIn",Zt),t("ol.easing.easeOut",qt),t("ol.easing.inAndOut",Jt),t("ol.easing.linear",_t),t("ol.easing.upAndDown",$t),t("ol.Feature",x),x.prototype.getGeometry=x.prototype.V,x.prototype.getId=x.prototype.Ll,x.prototype.getGeometryName=x.prototype.gk,x.prototype.getStyle=x.prototype.Ml,x.prototype.getStyleFunction=x.prototype.Gc,x.prototype.setGeometry=x.prototype.Pa,x.prototype.setStyle=x.prototype.Df,x.prototype.setId=x.prototype.cc,x.prototype.setGeometryName=x.prototype.Nc,t("ol.featureloader.xhr",bc),t("ol.Geolocation",Jv),Jv.prototype.getAccuracy=Jv.prototype.Tj,Jv.prototype.getAccuracyGeometry=Jv.prototype.Uj,Jv.prototype.getAltitude=Jv.prototype.Wj,Jv.prototype.getAltitudeAccuracy=Jv.prototype.Xj,Jv.prototype.getHeading=Jv.prototype.Ol,Jv.prototype.getPosition=Jv.prototype.Pl,Jv.prototype.getProjection=Jv.prototype.lh,Jv.prototype.getSpeed=Jv.prototype.Bk,Jv.prototype.getTracking=Jv.prototype.mh,Jv.prototype.getTrackingOptions=Jv.prototype.Wg,Jv.prototype.setProjection=Jv.prototype.nh,Jv.prototype.setTracking=Jv.prototype.ne,Jv.prototype.setTrackingOptions=Jv.prototype.Ci,t("ol.Graticule",lb),lb.prototype.getMap=lb.prototype.Sl,lb.prototype.getMeridians=lb.prototype.qk,lb.prototype.getParallels=lb.prototype.xk,t("ol.has.DEVICE_PIXEL_RATIO",ur),t("ol.has.CANVAS",yr),t("ol.has.DEVICE_ORIENTATION",fr),t("ol.has.GEOLOCATION",gr),t("ol.has.TOUCH",dr),t("ol.has.WEBGL",nr),tp.prototype.getImage=tp.prototype.a,gb.prototype.getImage=gb.prototype.ub,t("ol.inherits",e),t("ol.Kinetic",tn),t("ol.loadingstrategy.all",Ab),t("ol.loadingstrategy.bbox",function(t){return[t]}),t("ol.loadingstrategy.tile",function(n){return function(t,e){var o=n.Ec(e),i=ai(n,t,o),r=[];for((o=[o,0,0])[1]=i.ea;o[1]<=i.ca;++o[1])for(o[2]=i.ga;o[2]<=i.ja;++o[2])r.push(n.Na(o));return r}}),t("ol.Map",a),a.prototype.addControl=a.prototype.Bj,a.prototype.addInteraction=a.prototype.Cj,a.prototype.addLayer=a.prototype.ug,a.prototype.addOverlay=a.prototype.vg,a.prototype.beforeRender=a.prototype.Ij,a.prototype.forEachFeatureAtPixel=a.prototype.ae,a.prototype.forEachLayerAtPixel=a.prototype.Wl,a.prototype.hasFeatureAtPixel=a.prototype.ml,a.prototype.getEventCoordinate=a.prototype.ck,a.prototype.getEventPixel=a.prototype.ce,a.prototype.getTarget=a.prototype.vf,a.prototype.getTargetElement=a.prototype.Cc,a.prototype.getCoordinateFromPixel=a.prototype.Sa,a.prototype.getControls=a.prototype.ak,a.prototype.getOverlays=a.prototype.vk,a.prototype.getOverlayById=a.prototype.uk,a.prototype.getInteractions=a.prototype.hk,a.prototype.getLayerGroup=a.prototype.Bc,a.prototype.getLayers=a.prototype.oh,a.prototype.getPixelFromCoordinate=a.prototype.Ga,a.prototype.getSize=a.prototype.nb,a.prototype.getView=a.prototype.aa,a.prototype.getViewport=a.prototype.Ik,a.prototype.renderSync=a.prototype.$o,a.prototype.removeControl=a.prototype.To,a.prototype.removeInteraction=a.prototype.Uo,a.prototype.removeLayer=a.prototype.Wo,a.prototype.removeOverlay=a.prototype.Xo,a.prototype.setLayerGroup=a.prototype.vi,a.prototype.setSize=a.prototype.eg,a.prototype.setTarget=a.prototype.ph,a.prototype.setView=a.prototype.mp,a.prototype.updateSize=a.prototype.ld,t("ol.Object",wt),wt.prototype.getKeys=wt.prototype.S,wt.prototype.getProperties=wt.prototype.R,wt.prototype.setProperties=wt.prototype.I,wt.prototype.unset=wt.prototype.T,t("ol.Observable",bt),t("ol.Observable.unByKey",mt),bt.prototype.changed=bt.prototype.s,bt.prototype.dispatchEvent=bt.prototype.b,bt.prototype.getRevision=bt.prototype.M,bt.prototype.on=bt.prototype.J,bt.prototype.once=bt.prototype.N,bt.prototype.un=bt.prototype.K,bt.prototype.unByKey=bt.prototype.O,t("ol.Overlay",hu),hu.prototype.getElement=hu.prototype.be,hu.prototype.getId=hu.prototype.Xl,hu.prototype.getMap=hu.prototype.oe,hu.prototype.getOffset=hu.prototype.Tg,hu.prototype.getPosition=hu.prototype.qh,hu.prototype.getPositioning=hu.prototype.Ug,hu.prototype.setElement=hu.prototype.pi,hu.prototype.setOffset=hu.prototype.xi,hu.prototype.setPosition=hu.prototype.Ef,hu.prototype.setPositioning=hu.prototype.Ai,t("ol.render.toContext",function(t,e){var o=t.canvas,e=e||{},i=e.pixelRatio||ur;return(e=e.size)&&(o.width=e[0]*i,o.height=e[1]*i,o.style.width=e[0]+"px",o.style.height=e[1]+"px"),new Ep(t,i,o=[0,0,o.width,o.height],Gs(Is(),i,i),0)}),t("ol.size.toSize",ei),Wr.prototype.getTileCoord=Wr.prototype.c,H1.prototype.getFormat=H1.prototype.Yl,H1.prototype.setFeatures=H1.prototype.ri,H1.prototype.setProjection=H1.prototype.Ff,H1.prototype.setLoader=H1.prototype.wi,t("ol.View",h),h.prototype.constrainCenter=h.prototype.Zd,h.prototype.getCenter=h.prototype.fb,h.prototype.calculateExtent=h.prototype.Uc,h.prototype.getMaxResolution=h.prototype.Zl,h.prototype.getMinResolution=h.prototype.$l,h.prototype.getProjection=h.prototype.am,h.prototype.getResolution=h.prototype.Oa,h.prototype.getResolutions=h.prototype.bm,h.prototype.getRotation=h.prototype.Ra,h.prototype.getZoom=h.prototype.Kk,h.prototype.fit=h.prototype.lf,h.prototype.centerOn=h.prototype.Kj,h.prototype.setCenter=h.prototype.Mb,h.prototype.setResolution=h.prototype.Oc,h.prototype.setRotation=h.prototype.pe,h.prototype.setZoom=h.prototype.pp,t("ol.xml.getAllTextContent",oc),t("ol.xml.parse",nc),eh.prototype.getGL=eh.prototype.bo,eh.prototype.useProgram=eh.prototype.Lc,t("ol.tilegrid.createXYZ",ci),t("ol.tilegrid.TileGrid",ii),ii.prototype.forEachTileCoord=ii.prototype.Hg,ii.prototype.getMaxZoom=ii.prototype.Rg,ii.prototype.getMinZoom=ii.prototype.Sg,ii.prototype.getOrigin=ii.prototype.Kc,ii.prototype.getResolution=ii.prototype.Ha,ii.prototype.getResolutions=ii.prototype.Wh,ii.prototype.getTileCoordExtent=ii.prototype.Na,ii.prototype.getTileCoordForCoordAndResolution=ii.prototype.fe,ii.prototype.getTileCoordForCoordAndZ=ii.prototype.wf,ii.prototype.getTileSize=ii.prototype.Za,ii.prototype.getZForResolution=ii.prototype.Ec,t("ol.tilegrid.WMTS",q1),q1.prototype.getMatrixIds=q1.prototype.o,t("ol.tilegrid.WMTS.createFromCapabilitiesMatrixSet",J1),t("ol.style.AtlasManager",nw),t("ol.style.Circle",cp),cp.prototype.setRadius=cp.prototype.Qa,t("ol.style.Fill",yp),yp.prototype.getColor=yp.prototype.g,yp.prototype.setColor=yp.prototype.f,t("ol.style.Icon",Bf),Bf.prototype.getAnchor=Bf.prototype.Ac,Bf.prototype.getColor=Bf.prototype.Gn,Bf.prototype.getImage=Bf.prototype.Ic,Bf.prototype.getOrigin=Bf.prototype.Jc,Bf.prototype.getSrc=Bf.prototype.Hn,Bf.prototype.getSize=Bf.prototype.ac,t("ol.style.Image",hp),hp.prototype.getOpacity=hp.prototype.ye,hp.prototype.getRotateWithView=hp.prototype.ze,hp.prototype.getRotation=hp.prototype.Ae,hp.prototype.getScale=hp.prototype.Be,hp.prototype.getSnapToPixel=hp.prototype.ee,hp.prototype.setOpacity=hp.prototype.dd,hp.prototype.setRotation=hp.prototype.Ce,hp.prototype.setScale=hp.prototype.ed,t("ol.style.RegularShape",lp),lp.prototype.getAnchor=lp.prototype.Ac,lp.prototype.getAngle=lp.prototype.Nh,lp.prototype.getFill=lp.prototype.Oh,lp.prototype.getImage=lp.prototype.Ic,lp.prototype.getOrigin=lp.prototype.Jc,lp.prototype.getPoints=lp.prototype.Ph,lp.prototype.getRadius=lp.prototype.Qh,lp.prototype.getRadius2=lp.prototype.Vg,lp.prototype.getSize=lp.prototype.ac,lp.prototype.getStroke=lp.prototype.Rh,t("ol.style.Stroke",fp),fp.prototype.getColor=fp.prototype.In,fp.prototype.getLineCap=fp.prototype.kk,fp.prototype.getLineDash=fp.prototype.Jn,fp.prototype.getLineJoin=fp.prototype.lk,fp.prototype.getMiterLimit=fp.prototype.rk,fp.prototype.getWidth=fp.prototype.Kn,fp.prototype.setColor=fp.prototype.Ln,fp.prototype.setLineCap=fp.prototype.ip,fp.prototype.setLineJoin=fp.prototype.jp,fp.prototype.setMiterLimit=fp.prototype.kp,fp.prototype.setWidth=fp.prototype.np,t("ol.style.Style",gp),gp.prototype.getGeometry=gp.prototype.V,gp.prototype.getGeometryFunction=gp.prototype.fk,gp.prototype.getFill=gp.prototype.Mn,gp.prototype.setFill=gp.prototype.Qn,gp.prototype.getImage=gp.prototype.Nn,gp.prototype.setImage=gp.prototype.Rn,gp.prototype.getStroke=gp.prototype.On,gp.prototype.setStroke=gp.prototype.Sn,gp.prototype.getText=gp.prototype.Ka,gp.prototype.setText=gp.prototype.Tn,gp.prototype.getZIndex=gp.prototype.Pn,gp.prototype.setGeometry=gp.prototype.Pa,gp.prototype.setZIndex=gp.prototype.Un,t("ol.style.Text",ng),ng.prototype.getFont=ng.prototype.dk,ng.prototype.getOffsetX=ng.prototype.sk,ng.prototype.getOffsetY=ng.prototype.tk,ng.prototype.getFill=ng.prototype.Vn,ng.prototype.getRotateWithView=ng.prototype.Wn,ng.prototype.getRotation=ng.prototype.Xn,ng.prototype.getScale=ng.prototype.Yn,ng.prototype.getStroke=ng.prototype.Zn,ng.prototype.getText=ng.prototype.Ka,ng.prototype.getTextAlign=ng.prototype.Dk,ng.prototype.getTextBaseline=ng.prototype.Ek,ng.prototype.setFont=ng.prototype.si,ng.prototype.setOffsetX=ng.prototype.yi,ng.prototype.setOffsetY=ng.prototype.zi,ng.prototype.setFill=ng.prototype.Sh,ng.prototype.setRotation=ng.prototype.$n,ng.prototype.setScale=ng.prototype.Th,ng.prototype.setStroke=ng.prototype.Uh,ng.prototype.setText=ng.prototype.Vh,ng.prototype.setTextAlign=ng.prototype.Bi,ng.prototype.setTextBaseline=ng.prototype.lp,t("ol.Sphere",Ne),Ne.prototype.geodesicArea=Ne.prototype.a,Ne.prototype.haversineDistance=Ne.prototype.b,t("ol.source.BingMaps",E),t("ol.source.BingMaps.TOS_ATTRIBUTION",a1),E.prototype.getApiKey=E.prototype.U,E.prototype.getImagerySet=E.prototype.Z,t("ol.source.CartoDB",j),j.prototype.getConfig=j.prototype.$j,j.prototype.updateConfig=j.prototype.wp,j.prototype.setConfig=j.prototype.fp,t("ol.source.Cluster",L),L.prototype.getSource=L.prototype.Ua,L.prototype.setDistance=L.prototype.Ob,t("ol.source.Image",Ol),t("ol.source.ImageArcGISRest",y1),y1.prototype.getParams=y1.prototype.Xm,y1.prototype.getImageLoadFunction=y1.prototype.Wm,y1.prototype.getUrl=y1.prototype.Ym,y1.prototype.setImageLoadFunction=y1.prototype.Zm,y1.prototype.setUrl=y1.prototype.$m,y1.prototype.updateParams=y1.prototype.an,t("ol.source.ImageCanvas",Vl),t("ol.source.ImageMapGuide",f1),f1.prototype.getParams=f1.prototype.cn,f1.prototype.getImageLoadFunction=f1.prototype.bn,f1.prototype.updateParams=f1.prototype.en,f1.prototype.setImageLoadFunction=f1.prototype.dn,t("ol.source.ImageStatic",g1),t("ol.source.ImageVector",Wl),Wl.prototype.getSource=Wl.prototype.fn,Wl.prototype.getStyle=Wl.prototype.gn,Wl.prototype.getStyleFunction=Wl.prototype.hn,Wl.prototype.setStyle=Wl.prototype.Fh,t("ol.source.ImageWMS",d1),d1.prototype.getGetFeatureInfoUrl=d1.prototype.ln,d1.prototype.getParams=d1.prototype.nn,d1.prototype.getImageLoadFunction=d1.prototype.mn,d1.prototype.getUrl=d1.prototype.pn,d1.prototype.setImageLoadFunction=d1.prototype.qn,d1.prototype.setUrl=d1.prototype.rn,d1.prototype.updateParams=d1.prototype.sn,t("ol.source.OSM",w1),t("ol.source.OSM.ATTRIBUTION",M1),t("ol.source.Raster",P1),P1.prototype.setOperation=P1.prototype.u,t("ol.source.Source",Il),Il.prototype.getAttributions=Il.prototype.za,Il.prototype.getLogo=Il.prototype.ya,Il.prototype.getProjection=Il.prototype.Aa,Il.prototype.getState=Il.prototype.W,Il.prototype.refresh=Il.prototype.wa,Il.prototype.setAttributions=Il.prototype.ua,t("ol.source.Stamen",N1),t("ol.source.Tile",Qm),Qm.prototype.getTileGrid=Qm.prototype.Va,t("ol.source.TileArcGISRest",R),R.prototype.getParams=R.prototype.u,R.prototype.updateParams=R.prototype.A,t("ol.source.TileDebug",D1),t("ol.source.TileImage",A),A.prototype.setRenderReprojectionEdges=A.prototype.Hb,A.prototype.setTileGridForProjection=A.prototype.Ib,t("ol.source.TileJSON",N),N.prototype.getTileJSON=N.prototype.Fk,t("ol.source.TileUTFGrid",U1),U1.prototype.getTemplate=U1.prototype.Ck,U1.prototype.forDataAtCoordinateAndResolution=U1.prototype.Pj,t("ol.source.TileWMS",I),I.prototype.getGetFeatureInfoUrl=I.prototype.An,I.prototype.getParams=I.prototype.Bn,I.prototype.updateParams=I.prototype.Cn,r1.prototype.getTileLoadFunction=r1.prototype.ib,r1.prototype.getTileUrlFunction=r1.prototype.kb,r1.prototype.getUrls=r1.prototype.lb,r1.prototype.setTileLoadFunction=r1.prototype.sb,r1.prototype.setTileUrlFunction=r1.prototype.Xa,r1.prototype.setUrl=r1.prototype.cb,r1.prototype.setUrls=r1.prototype.Ya,t("ol.source.Vector",v),v.prototype.addFeature=v.prototype.gb,v.prototype.addFeatures=v.prototype.Tc,v.prototype.forEachFeature=v.prototype.Fg,v.prototype.forEachFeatureInExtent=v.prototype.Qb,v.prototype.forEachFeatureIntersectingExtent=v.prototype.Gg,v.prototype.getFeaturesCollection=v.prototype.Og,v.prototype.getFeatures=v.prototype.we,v.prototype.getFeaturesAtCoordinate=v.prototype.Ng,v.prototype.getFeaturesInExtent=v.prototype.nf,v.prototype.getClosestFeatureToCoordinate=v.prototype.Jg,v.prototype.getExtent=v.prototype.G,v.prototype.getFeatureById=v.prototype.Mg,v.prototype.getFormat=v.prototype.Kh,v.prototype.getUrl=v.prototype.Lh,v.prototype.removeFeature=v.prototype.rb,t("ol.source.VectorTile",Z1),t("ol.source.WMTS",F),F.prototype.getDimensions=F.prototype.bk,F.prototype.getFormat=F.prototype.Dn,F.prototype.getLayer=F.prototype.En,F.prototype.getMatrixSet=F.prototype.pk,F.prototype.getRequestEncoding=F.prototype.Ak,F.prototype.getStyle=F.prototype.Fn,F.prototype.getVersion=F.prototype.Hk,F.prototype.updateDimensions=F.prototype.xp,t("ol.source.WMTS.optionsFromCapabilities",function(t,r){var e,o=Lt(t.Contents.Layer,function(t){return t.Identifier==r.layer}),n=t.Contents.TileMatrixSet,i=1<o.TileMatrixSetLink.length?"projection"in r?It(o.TileMatrixSetLink,function(e){var t=Lt(n,function(t){return t.Identifier==e.TileMatrixSet}).SupportedCRS.replace(/urn:ogc:def:crs:(\w+):(.*:)?(\w+)$/,"$1:$3"),o=Ye(t),i=Ye(r.projection);return o&&i?Ze(o,i):t==r.projection}):It(o.TileMatrixSetLink,function(t){return t.TileMatrixSet==r.matrixSet}):0,s=o.TileMatrixSetLink[i=i<0?0:i].TileMatrixSet,p=o.TileMatrixSetLink[i].TileMatrixSetLimits,a=o.Format[0],h=("format"in r&&(a=r.format),i=It(o.Style,function(t){return"style"in r?t.Title==r.style:t.isDefault}),i=o.Style[i=i<0?0:i].Identifier,{}),l=("Dimension"in o&&o.Dimension.forEach(function(t){var e=t.Identifier,o=t.Default;void 0===o&&(o=t.Value[0]),h[e]=o}),Lt(t.Contents.TileMatrixSet,function(t){return t.Identifier==s})),u="projection"in r?Ye(r.projection):Ye(l.SupportedCRS.replace(/urn:ogc:def:crs:(\w+):(.*:)?(\w+)$/,"$1:$3")),c=(void 0!==(f=o.WGS84BoundingBox)&&(e=Ye("EPSG:4326").G(),e=f[0]==e[0]&&f[2]==e[2],y=to(f,"EPSG:4326",u),f=u.G())&&(re(f,y)||(y=void 0)),p=J1(l,y,p),[]),l=void 0!==(l=r.requestEncoding)?l:"";if("OperationsMetadata"in t&&"GetTile"in t.OperationsMetadata)for(var y,f=0,g=(y=t.OperationsMetadata.GetTile.DCP.HTTP.Get).length;f<g;++f){var d=Lt(y[f].Constraint,function(t){return"GetEncoding"==t.name}).AllowedValues.Value;if((l=""===l?d[0]:l)!==$1)break;Et(d,$1)&&c.push(y[f].href)}return 0===c.length&&(l="REST",o.ResourceURL.forEach(function(t){"tile"===t.resourceType&&(a=t.format,c.push(t.template))})),{urls:c,layer:r.layer,matrixSet:s,format:a,projection:u,requestEncoding:l,tileGrid:p,style:i,dimensions:h,wrapX:e}}),t("ol.source.XYZ",C),t("ol.source.Zoomify",Q1),Jd.prototype.getExtent=Jd.prototype.G,Jd.prototype.getGeometry=Jd.prototype.V,Jd.prototype.getProperties=Jd.prototype.Tm,Jd.prototype.getType=Jd.prototype.Y,t("ol.render.VectorContext",Ap),vl.prototype.setStyle=vl.prototype.Gd,vl.prototype.drawGeometry=vl.prototype.tc,vl.prototype.drawFeature=vl.prototype.jf,Ep.prototype.drawCircle=Ep.prototype.hc,Ep.prototype.setStyle=Ep.prototype.Gd,Ep.prototype.drawGeometry=Ep.prototype.tc,Ep.prototype.drawFeature=Ep.prototype.jf,t("ol.proj.common.add",Es),t("ol.proj.METERS_PER_UNIT",Fe),t("ol.proj.setProj4",function(t){ke=t}),t("ol.proj.getPointResolution",Ke),t("ol.proj.addEquivalentProjections",Xe),t("ol.proj.addProjection",Ve),t("ol.proj.addCoordinateTransforms",ze),t("ol.proj.fromLonLat",function(t,e){return Qe(t,"EPSG:4326",void 0!==e?e:"EPSG:3857")}),t("ol.proj.toLonLat",function(t,e){return Qe(t,void 0!==e?e:"EPSG:3857","EPSG:4326")}),t("ol.proj.get",Ye),t("ol.proj.equivalent",Ze),t("ol.proj.getTransform",qe),t("ol.proj.transform",Qe),t("ol.proj.transformExtent",to),t("ol.proj.Projection",Oe),Oe.prototype.getCode=Oe.prototype.Zj,Oe.prototype.getExtent=Oe.prototype.G,Oe.prototype.getUnits=Oe.prototype.Eb,Oe.prototype.getMetersPerUnit=Oe.prototype.ic,Oe.prototype.getWorldExtent=Oe.prototype.Jk,Oe.prototype.isGlobal=Oe.prototype.rl,Oe.prototype.setGlobal=Oe.prototype.hp,Oe.prototype.setExtent=Oe.prototype.Sm,Oe.prototype.setWorldExtent=Oe.prototype.op,Oe.prototype.setGetPointResolution=Oe.prototype.gp,t("ol.proj.Units.METERS_PER_UNIT",Fe),t("ol.layer.Base",ns),ns.prototype.getExtent=ns.prototype.G,ns.prototype.getMaxResolution=ns.prototype.Wb,ns.prototype.getMinResolution=ns.prototype.Xb,ns.prototype.getOpacity=ns.prototype.Yb,ns.prototype.getVisible=ns.prototype.Fb,ns.prototype.getZIndex=ns.prototype.Zb,ns.prototype.setExtent=ns.prototype.kc,ns.prototype.setMaxResolution=ns.prototype.pc,ns.prototype.setMinResolution=ns.prototype.qc,ns.prototype.setOpacity=ns.prototype.lc,ns.prototype.setVisible=ns.prototype.mc,ns.prototype.setZIndex=ns.prototype.nc,t("ol.layer.Group",ys),ys.prototype.getLayers=ys.prototype.cd,ys.prototype.setLayers=ys.prototype.yh,t("ol.layer.Heatmap",T),T.prototype.getBlur=T.prototype.Ig,T.prototype.getGradient=T.prototype.Pg,T.prototype.getRadius=T.prototype.zh,T.prototype.setBlur=T.prototype.ni,T.prototype.setGradient=T.prototype.ui,T.prototype.setRadius=T.prototype.Ah,t("ol.layer.Image",Js),Js.prototype.getSource=Js.prototype.la,t("ol.layer.Layer",o),o.prototype.getSource=o.prototype.la,o.prototype.setSource=o.prototype.Pc,t("ol.layer.Tile",p),p.prototype.getPreload=p.prototype.f,p.prototype.getSource=p.prototype.la,p.prototype.setPreload=p.prototype.l,p.prototype.getUseInterimTilesOnError=p.prototype.c,p.prototype.setUseInterimTilesOnError=p.prototype.C,t("ol.layer.Vector",s),s.prototype.getSource=s.prototype.la,s.prototype.getStyle=s.prototype.D,s.prototype.getStyleFunction=s.prototype.L,s.prototype.setStyle=s.prototype.l,t("ol.layer.VectorTile",i),i.prototype.getPreload=i.prototype.f,i.prototype.getUseInterimTilesOnError=i.prototype.c,i.prototype.setPreload=i.prototype.P,i.prototype.setUseInterimTilesOnError=i.prototype.U,t("ol.interaction.DoubleClickZoom",pn),t("ol.interaction.DoubleClickZoom.handleEvent",an),t("ol.interaction.DragAndDrop",vb),t("ol.interaction.DragAndDrop.handleEvent",Le),t("ol.interaction.DragBox",Nn),Nn.prototype.getGeometry=Nn.prototype.V,t("ol.interaction.DragPan",xn),t("ol.interaction.DragRotate",Tn),t("ol.interaction.DragRotateAndZoom",Sb),t("ol.interaction.DragZoom",Kn),t("ol.interaction.Draw",Ob),t("ol.interaction.Draw.handleEvent",Db),Ob.prototype.removeLastPoint=Ob.prototype.Vo,Ob.prototype.finishDrawing=Ob.prototype.zd,Ob.prototype.extend=Ob.prototype.vm,t("ol.interaction.Draw.createRegularPolygon",function(r,n){return function(t,e){var o=t[0],t=t[1],i=Math.sqrt(zt(o,t)),e=e||Do(new pb(o),r);return Bo(e,o,i,n||Math.atan((t[1]-o[1])/(t[0]-o[0]))),e}}),t("ol.interaction.Draw.createBox",function(){return function(t,e){t=Qt(t),e=e||new w(null);return e.qa([[ve(t),be(t),Pe(t),Me(t),ve(t)]]),e}}),t("ol.interaction.Extent",Qb),Qb.prototype.getExtent=Qb.prototype.G,Qb.prototype.setExtent=Qb.prototype.i,am.prototype.extent_=am.prototype.b,t("ol.interaction.defaults",rs),t("ol.interaction.Interaction",en),en.prototype.getActive=en.prototype.f,en.prototype.getMap=en.prototype.c,en.prototype.setActive=en.prototype.Ea,t("ol.interaction.KeyboardPan",Xn),t("ol.interaction.KeyboardPan.handleEvent",Vn),t("ol.interaction.KeyboardZoom",Wn),t("ol.interaction.KeyboardZoom.handleEvent",zn),t("ol.interaction.Modify",lm),t("ol.interaction.Modify.handleEvent",bm),lm.prototype.removePoint=lm.prototype.ki,t("ol.interaction.MouseWheelZoom",Hn),t("ol.interaction.MouseWheelZoom.handleEvent",Yn),Hn.prototype.setMouseAnchor=Hn.prototype.P,t("ol.interaction.PinchRotate",Jn),t("ol.interaction.PinchZoom",ts),t("ol.interaction.Pointer",bn),t("ol.interaction.Pointer.handleEvent",wn),t("ol.interaction.Select",Pm),Pm.prototype.getFeatures=Pm.prototype.Fm,Pm.prototype.getHitTolerance=Pm.prototype.Gm,Pm.prototype.getLayer=Pm.prototype.Hm,t("ol.interaction.Select.handleEvent",Tm),Pm.prototype.setHitTolerance=Pm.prototype.Jm,t("ol.interaction.Snap",Cm),Cm.prototype.addFeature=Cm.prototype.gb,Cm.prototype.removeFeature=Cm.prototype.rb,t("ol.interaction.Translate",Rm),Rm.prototype.getHitTolerance=Rm.prototype.D,Rm.prototype.setHitTolerance=Rm.prototype.L,t("ol.geom.Circle",pb),pb.prototype.getCenter=pb.prototype.Fd,pb.prototype.getRadius=pb.prototype.qe,pb.prototype.getType=pb.prototype.Y,pb.prototype.intersectsExtent=pb.prototype.Ta,pb.prototype.setCenter=pb.prototype.nm,pb.prototype.setCenterAndRadius=pb.prototype.dg,pb.prototype.setRadius=pb.prototype.om,pb.prototype.transform=pb.prototype.ob,t("ol.geom.Geometry",eo),eo.prototype.getClosestPoint=eo.prototype.Cb,eo.prototype.intersectsCoordinate=eo.prototype.mb,eo.prototype.getExtent=eo.prototype.G,eo.prototype.simplify=eo.prototype.Jb,eo.prototype.transform=eo.prototype.ob,t("ol.geom.GeometryCollection",ly),ly.prototype.getGeometries=ly.prototype.pf,ly.prototype.getType=ly.prototype.Y,ly.prototype.intersectsExtent=ly.prototype.Ta,ly.prototype.setGeometries=ly.prototype.ti,ly.prototype.applyTransform=ly.prototype.sc,t("ol.geom.LinearRing",So),So.prototype.getArea=So.prototype.rm,So.prototype.getCoordinates=So.prototype.$,So.prototype.getType=So.prototype.Y,So.prototype.setCoordinates=So.prototype.qa,t("ol.geom.LineString",S),S.prototype.appendCoordinate=S.prototype.Dj,S.prototype.forEachSegment=S.prototype.Sj,S.prototype.getCoordinateAtM=S.prototype.pm,S.prototype.getCoordinates=S.prototype.$,S.prototype.getCoordinateAt=S.prototype.Kg,S.prototype.getLength=S.prototype.qm,S.prototype.getType=S.prototype.Y,S.prototype.intersectsExtent=S.prototype.Ta,S.prototype.setCoordinates=S.prototype.qa,t("ol.geom.MultiLineString",M),M.prototype.appendLineString=M.prototype.Ej,M.prototype.getCoordinateAtM=M.prototype.sm,M.prototype.getCoordinates=M.prototype.$,M.prototype.getLineString=M.prototype.mk,M.prototype.getLineStrings=M.prototype.Yc,M.prototype.getType=M.prototype.Y,M.prototype.intersectsExtent=M.prototype.Ta,M.prototype.setCoordinates=M.prototype.qa,t("ol.geom.MultiPoint",P),P.prototype.appendPoint=P.prototype.Gj,P.prototype.getCoordinates=P.prototype.$,P.prototype.getPoint=P.prototype.yk,P.prototype.getPoints=P.prototype.re,P.prototype.getType=P.prototype.Y,P.prototype.intersectsExtent=P.prototype.Ta,P.prototype.setCoordinates=P.prototype.qa,t("ol.geom.MultiPolygon",y),y.prototype.appendPolygon=y.prototype.Hj,y.prototype.getArea=y.prototype.tm,y.prototype.getCoordinates=y.prototype.$,y.prototype.getInteriorPoints=y.prototype.jk,y.prototype.getPolygon=y.prototype.zk,y.prototype.getPolygons=y.prototype.Ad,y.prototype.getType=y.prototype.Y,y.prototype.intersectsExtent=y.prototype.Ta,y.prototype.setCoordinates=y.prototype.qa,t("ol.geom.Point",m),m.prototype.getCoordinates=m.prototype.$,m.prototype.getType=m.prototype.Y,m.prototype.intersectsExtent=m.prototype.Ta,m.prototype.setCoordinates=m.prototype.qa,t("ol.geom.Polygon",w),w.prototype.appendLinearRing=w.prototype.Fj,w.prototype.getArea=w.prototype.um,w.prototype.getCoordinates=w.prototype.$,w.prototype.getInteriorPoint=w.prototype.ik,w.prototype.getLinearRingCount=w.prototype.nk,w.prototype.getLinearRing=w.prototype.Qg,w.prototype.getLinearRings=w.prototype.Zc,w.prototype.getType=w.prototype.Y,w.prototype.intersectsExtent=w.prototype.Ta,w.prototype.setCoordinates=w.prototype.qa,t("ol.geom.Polygon.circular",ko),t("ol.geom.Polygon.fromExtent",Oo),t("ol.geom.Polygon.fromCircle",Do),t("ol.geom.SimpleGeometry",b),b.prototype.getFirstCoordinate=b.prototype.Rb,b.prototype.getLastCoordinate=b.prototype.Sb,b.prototype.getLayout=b.prototype.Tb,b.prototype.applyTransform=b.prototype.sc,t("ol.format.EsriJSON",Fc),Fc.prototype.readFeature=Fc.prototype.bc,Fc.prototype.readFeatures=Fc.prototype.La,Fc.prototype.readGeometry=Fc.prototype.hd,Fc.prototype.readProjection=Fc.prototype.Wa,Fc.prototype.writeGeometry=Fc.prototype.od,Fc.prototype.writeGeometryObject=Fc.prototype.Te,Fc.prototype.writeFeature=Fc.prototype.Od,Fc.prototype.writeFeatureObject=Fc.prototype.md,Fc.prototype.writeFeatures=Fc.prototype.ec,Fc.prototype.writeFeaturesObject=Fc.prototype.Se,t("ol.format.Feature",mc),t("ol.format.GeoJSON",fy),fy.prototype.readFeature=fy.prototype.bc,fy.prototype.readFeatures=fy.prototype.La,fy.prototype.readGeometry=fy.prototype.hd,fy.prototype.readProjection=fy.prototype.Wa,fy.prototype.writeFeature=fy.prototype.Od,fy.prototype.writeFeatureObject=fy.prototype.md,fy.prototype.writeFeatures=fy.prototype.ec,fy.prototype.writeFeaturesObject=fy.prototype.Se,fy.prototype.writeGeometry=fy.prototype.od,fy.prototype.writeGeometryObject=fy.prototype.Te,t("ol.format.GML",f),f.prototype.writeFeatures=f.prototype.ec,f.prototype.writeFeaturesNode=f.prototype.a,t("ol.format.GML2",Vy),t("ol.format.GML3",f),f.prototype.writeGeometryNode=f.prototype.H,f.prototype.writeFeatures=f.prototype.ec,f.prototype.writeFeaturesNode=f.prototype.a,Sy.prototype.readFeatures=Sy.prototype.La,t("ol.format.GPX",Wy),Wy.prototype.readFeature=Wy.prototype.bc,Wy.prototype.readFeatures=Wy.prototype.La,Wy.prototype.readProjection=Wy.prototype.Wa,Wy.prototype.writeFeatures=Wy.prototype.ec,Wy.prototype.writeFeaturesNode=Wy.prototype.a,t("ol.format.IGC",Cf),Cf.prototype.readFeature=Cf.prototype.bc,Cf.prototype.readFeatures=Cf.prototype.La,Cf.prototype.readProjection=Cf.prototype.Wa,t("ol.format.KML",sg),sg.prototype.readFeature=sg.prototype.bc,sg.prototype.readFeatures=sg.prototype.La,sg.prototype.readName=sg.prototype.Jo,sg.prototype.readNetworkLinks=sg.prototype.Ko,sg.prototype.readRegion=sg.prototype.No,sg.prototype.readRegionFromNode=sg.prototype.Ie,sg.prototype.readProjection=sg.prototype.Wa,sg.prototype.writeFeatures=sg.prototype.ec,sg.prototype.writeFeaturesNode=sg.prototype.a,t("ol.format.MVT",_d),_d.prototype.readFeatures=_d.prototype.La,_d.prototype.readProjection=_d.prototype.Wa,_d.prototype.setLayers=_d.prototype.c,t("ol.format.OSMXML",Qd),Qd.prototype.readFeatures=Qd.prototype.La,Qd.prototype.readProjection=Qd.prototype.Wa,t("ol.format.Polyline",M0),t("ol.format.Polyline.encodeDeltas",P0),t("ol.format.Polyline.decodeDeltas",T0),t("ol.format.Polyline.encodeFloats",A0),t("ol.format.Polyline.decodeFloats",E0),M0.prototype.readFeature=M0.prototype.bc,M0.prototype.readFeatures=M0.prototype.La,M0.prototype.readGeometry=M0.prototype.hd,M0.prototype.readProjection=M0.prototype.Wa,M0.prototype.writeGeometry=M0.prototype.od,t("ol.format.TopoJSON",C0),C0.prototype.readFeatures=C0.prototype.La,C0.prototype.readProjection=C0.prototype.Wa,t("ol.format.WFS",I0),I0.prototype.readFeatures=I0.prototype.La,I0.prototype.readTransactionResponse=I0.prototype.o,I0.prototype.readFeatureCollectionMetadata=I0.prototype.l,I0.prototype.writeGetFeature=I0.prototype.v,I0.prototype.writeTransaction=I0.prototype.A,I0.prototype.readProjection=I0.prototype.Wa,t("ol.format.WKT",_0),_0.prototype.readFeature=_0.prototype.bc,_0.prototype.readFeatures=_0.prototype.La,_0.prototype.readGeometry=_0.prototype.hd,_0.prototype.writeFeature=_0.prototype.Od,_0.prototype.writeFeatures=_0.prototype.ec,_0.prototype.writeGeometry=_0.prototype.od,t("ol.format.WMSCapabilities",fv),t("ol.format.WMSGetFeatureInfo",Ov),Ov.prototype.readFeatures=Ov.prototype.La,t("ol.format.WMTSCapabilities",Dv),t("ol.format.filter.And",Wc),t("ol.format.filter.Bbox",zc),t("ol.format.filter.Comparison",Hc),t("ol.format.filter.ComparisonBinary",Yc),t("ol.format.filter.EqualTo",Zc),t("ol.format.filter.Filter",Kc),t("ol.format.filter.GreaterThan",qc),t("ol.format.filter.GreaterThanOrEqualTo",Jc),t("ol.format.filter.and",ay),t("ol.format.filter.or",function(t,e){return new sy(t,e)}),t("ol.format.filter.not",function(t){return new ry(t)}),t("ol.format.filter.bbox",hy),t("ol.format.filter.intersects",function(t,e,o){return new $c(t,e,o)}),t("ol.format.filter.within",function(t,e,o){return new py(t,e,o)}),t("ol.format.filter.equalTo",function(t,e,o){return new Zc(t,e,o)}),t("ol.format.filter.notEqualTo",function(t,e,o){return new ny(t,e,o)}),t("ol.format.filter.lessThan",function(t,e){return new oy(t,e)}),t("ol.format.filter.lessThanOrEqualTo",function(t,e){return new iy(t,e)}),t("ol.format.filter.greaterThan",function(t,e){return new qc(t,e)}),t("ol.format.filter.greaterThanOrEqualTo",function(t,e){return new Jc(t,e)}),t("ol.format.filter.isNull",function(t){return new ey(t)}),t("ol.format.filter.between",function(t,e,o){return new Qc(t,e,o)}),t("ol.format.filter.like",function(t,e,o,i,r,n){return new ty(t,e,o,i,r,n)}),t("ol.format.filter.Intersects",$c),t("ol.format.filter.IsBetween",Qc),t("ol.format.filter.IsLike",ty),t("ol.format.filter.IsNull",ey),t("ol.format.filter.LessThan",oy),t("ol.format.filter.LessThanOrEqualTo",iy),t("ol.format.filter.Not",ry),t("ol.format.filter.NotEqualTo",ny),t("ol.format.filter.Or",sy),t("ol.format.filter.Spatial",_c),t("ol.format.filter.Within",py),t("ol.extent.boundingExtent",Qt),t("ol.extent.buffer",te),t("ol.extent.containsCoordinate",ie),t("ol.extent.containsExtent",re),t("ol.extent.containsXY",ne),t("ol.extent.createEmpty",pe),t("ol.extent.equals",ue),t("ol.extent.extend",ce),t("ol.extent.getBottomLeft",ve),t("ol.extent.getBottomRight",be),t("ol.extent.getCenter",me),t("ol.extent.getHeight",xe),t("ol.extent.getIntersection",Se),t("ol.extent.getSize",function(t){return[t[2]-t[0],t[3]-t[1]]}),t("ol.extent.getTopLeft",Me),t("ol.extent.getTopRight",Pe),t("ol.extent.getWidth",Te),t("ol.extent.intersects",Ae),t("ol.extent.isEmpty",Ee),t("ol.extent.applyTransform",je),t("ol.events.condition.altKeyOnly",function(t){return(t=t.originalEvent).altKey&&!(t.metaKey||t.ctrlKey)&&!t.shiftKey}),t("ol.events.condition.altShiftKeysOnly",hn),t("ol.events.condition.always",Le),t("ol.events.condition.click",function(t){return"click"==t.type}),t("ol.events.condition.never",Re),t("ol.events.condition.pointerMove",un),t("ol.events.condition.singleClick",cn),t("ol.events.condition.doubleClick",function(t){return"dblclick"==t.type}),t("ol.events.condition.noModifierKeys",yn),t("ol.events.condition.platformModifierKeyOnly",function(t){return!(t=t.originalEvent).altKey&&(lr?t.metaKey:t.ctrlKey)&&!t.shiftKey}),t("ol.events.condition.shiftKeyOnly",fn),t("ol.events.condition.targetNotEditable",gn),t("ol.events.condition.mouseOnly",dn),t("ol.events.condition.primaryAction",vn),t("ol.control.Attribution",Oi),t("ol.control.Attribution.render",Di),Oi.prototype.getCollapsible=Oi.prototype.dm,Oi.prototype.setCollapsible=Oi.prototype.gm,Oi.prototype.setCollapsed=Oi.prototype.fm,Oi.prototype.getCollapsed=Oi.prototype.cm,t("ol.control.Control",ki),ki.prototype.getMap=ki.prototype.i,ki.prototype.setTarget=ki.prototype.c,t("ol.control.FullScreen",Ui),t("ol.control.defaults",Zi),t("ol.control.MousePosition",qi),t("ol.control.MousePosition.render",Ji),qi.prototype.getCoordinateFormat=qi.prototype.Lg,qi.prototype.getProjection=qi.prototype.rh,qi.prototype.setCoordinateFormat=qi.prototype.oi,qi.prototype.setProjection=qi.prototype.sh,t("ol.control.OverviewMap",Eu),t("ol.control.OverviewMap.render",Cu),Eu.prototype.getCollapsible=Eu.prototype.jm,Eu.prototype.setCollapsible=Eu.prototype.mm,Eu.prototype.setCollapsed=Eu.prototype.lm,Eu.prototype.getCollapsed=Eu.prototype.im,Eu.prototype.getOverviewMap=Eu.prototype.wk,t("ol.control.Rotate",zi),t("ol.control.Rotate.render",Hi),t("ol.control.ScaleLine",Nu),Nu.prototype.getUnits=Nu.prototype.Eb,t("ol.control.ScaleLine.render",Fu),Nu.prototype.setUnits=Nu.prototype.D,t("ol.control.Zoom",Yi),t("ol.control.ZoomSlider",Xu),t("ol.control.ZoomSlider.render",Wu),t("ol.control.ZoomToExtent",Yu),wt.prototype.changed=wt.prototype.s,wt.prototype.dispatchEvent=wt.prototype.b,wt.prototype.getRevision=wt.prototype.M,wt.prototype.on=wt.prototype.J,wt.prototype.once=wt.prototype.N,wt.prototype.un=wt.prototype.K,wt.prototype.unByKey=wt.prototype.O,di.prototype.getKeys=di.prototype.S,di.prototype.getProperties=di.prototype.R,di.prototype.setProperties=di.prototype.I,di.prototype.unset=di.prototype.T,di.prototype.changed=di.prototype.s,di.prototype.dispatchEvent=di.prototype.b,di.prototype.getRevision=di.prototype.M,di.prototype.on=di.prototype.J,di.prototype.once=di.prototype.N,di.prototype.un=di.prototype.K,di.prototype.unByKey=di.prototype.O,Zu.prototype.getKeys=Zu.prototype.S,Zu.prototype.getProperties=Zu.prototype.R,Zu.prototype.setProperties=Zu.prototype.I,Zu.prototype.unset=Zu.prototype.T,Zu.prototype.changed=Zu.prototype.s,Zu.prototype.dispatchEvent=Zu.prototype.b,Zu.prototype.getRevision=Zu.prototype.M,Zu.prototype.on=Zu.prototype.J,Zu.prototype.once=Zu.prototype.N,Zu.prototype.un=Zu.prototype.K,Zu.prototype.unByKey=Zu.prototype.O,x.prototype.getKeys=x.prototype.S,x.prototype.getProperties=x.prototype.R,x.prototype.setProperties=x.prototype.I,x.prototype.unset=x.prototype.T,x.prototype.changed=x.prototype.s,x.prototype.dispatchEvent=x.prototype.b,x.prototype.getRevision=x.prototype.M,x.prototype.on=x.prototype.J,x.prototype.once=x.prototype.N,x.prototype.un=x.prototype.K,x.prototype.unByKey=x.prototype.O,Jv.prototype.getKeys=Jv.prototype.S,Jv.prototype.getProperties=Jv.prototype.R,Jv.prototype.setProperties=Jv.prototype.I,Jv.prototype.unset=Jv.prototype.T,Jv.prototype.changed=Jv.prototype.s,Jv.prototype.dispatchEvent=Jv.prototype.b,Jv.prototype.getRevision=Jv.prototype.M,Jv.prototype.on=Jv.prototype.J,Jv.prototype.once=Jv.prototype.N,Jv.prototype.un=Jv.prototype.K,Jv.prototype.unByKey=Jv.prototype.O,gb.prototype.getTileCoord=gb.prototype.c,a.prototype.getKeys=a.prototype.S,a.prototype.getProperties=a.prototype.R,a.prototype.setProperties=a.prototype.I,a.prototype.unset=a.prototype.T,a.prototype.changed=a.prototype.s,a.prototype.dispatchEvent=a.prototype.b,a.prototype.getRevision=a.prototype.M,a.prototype.on=a.prototype.J,a.prototype.once=a.prototype.N,a.prototype.un=a.prototype.K,a.prototype.unByKey=a.prototype.O,hu.prototype.getKeys=hu.prototype.S,hu.prototype.getProperties=hu.prototype.R,hu.prototype.setProperties=hu.prototype.I,hu.prototype.unset=hu.prototype.T,hu.prototype.changed=hu.prototype.s,hu.prototype.dispatchEvent=hu.prototype.b,hu.prototype.getRevision=hu.prototype.M,hu.prototype.on=hu.prototype.J,hu.prototype.once=hu.prototype.N,hu.prototype.un=hu.prototype.K,hu.prototype.unByKey=hu.prototype.O,H1.prototype.getTileCoord=H1.prototype.c,h.prototype.getKeys=h.prototype.S,h.prototype.getProperties=h.prototype.R,h.prototype.setProperties=h.prototype.I,h.prototype.unset=h.prototype.T,h.prototype.changed=h.prototype.s,h.prototype.dispatchEvent=h.prototype.b,h.prototype.getRevision=h.prototype.M,h.prototype.on=h.prototype.J,h.prototype.once=h.prototype.N,h.prototype.un=h.prototype.K,h.prototype.unByKey=h.prototype.O,q1.prototype.forEachTileCoord=q1.prototype.Hg,q1.prototype.getMaxZoom=q1.prototype.Rg,q1.prototype.getMinZoom=q1.prototype.Sg,q1.prototype.getOrigin=q1.prototype.Kc,q1.prototype.getResolution=q1.prototype.Ha,q1.prototype.getResolutions=q1.prototype.Wh,q1.prototype.getTileCoordExtent=q1.prototype.Na,q1.prototype.getTileCoordForCoordAndResolution=q1.prototype.fe,q1.prototype.getTileCoordForCoordAndZ=q1.prototype.wf,q1.prototype.getTileSize=q1.prototype.Za,q1.prototype.getZForResolution=q1.prototype.Ec,lp.prototype.getOpacity=lp.prototype.ye,lp.prototype.getRotateWithView=lp.prototype.ze,lp.prototype.getRotation=lp.prototype.Ae,lp.prototype.getScale=lp.prototype.Be,lp.prototype.getSnapToPixel=lp.prototype.ee,lp.prototype.setOpacity=lp.prototype.dd,lp.prototype.setRotation=lp.prototype.Ce,lp.prototype.setScale=lp.prototype.ed,cp.prototype.getAngle=cp.prototype.Nh,cp.prototype.getFill=cp.prototype.Oh,cp.prototype.getPoints=cp.prototype.Ph,cp.prototype.getRadius=cp.prototype.Qh,cp.prototype.getRadius2=cp.prototype.Vg,cp.prototype.getStroke=cp.prototype.Rh,cp.prototype.getOpacity=cp.prototype.ye,cp.prototype.getRotateWithView=cp.prototype.ze,cp.prototype.getRotation=cp.prototype.Ae,cp.prototype.getScale=cp.prototype.Be,cp.prototype.getSnapToPixel=cp.prototype.ee,cp.prototype.setOpacity=cp.prototype.dd,cp.prototype.setRotation=cp.prototype.Ce,cp.prototype.setScale=cp.prototype.ed,Bf.prototype.getOpacity=Bf.prototype.ye,Bf.prototype.getRotateWithView=Bf.prototype.ze,Bf.prototype.getRotation=Bf.prototype.Ae,Bf.prototype.getScale=Bf.prototype.Be,Bf.prototype.getSnapToPixel=Bf.prototype.ee,Bf.prototype.setOpacity=Bf.prototype.dd,Bf.prototype.setRotation=Bf.prototype.Ce,Bf.prototype.setScale=Bf.prototype.ed,Il.prototype.getKeys=Il.prototype.S,Il.prototype.getProperties=Il.prototype.R,Il.prototype.setProperties=Il.prototype.I,Il.prototype.unset=Il.prototype.T,Il.prototype.changed=Il.prototype.s,Il.prototype.dispatchEvent=Il.prototype.b,Il.prototype.getRevision=Il.prototype.M,Il.prototype.on=Il.prototype.J,Il.prototype.once=Il.prototype.N,Il.prototype.un=Il.prototype.K,Il.prototype.unByKey=Il.prototype.O,Qm.prototype.getAttributions=Qm.prototype.za,Qm.prototype.getLogo=Qm.prototype.ya,Qm.prototype.getProjection=Qm.prototype.Aa,Qm.prototype.getState=Qm.prototype.W,Qm.prototype.refresh=Qm.prototype.wa,Qm.prototype.setAttributions=Qm.prototype.ua,Qm.prototype.getKeys=Qm.prototype.S,Qm.prototype.getProperties=Qm.prototype.R,Qm.prototype.setProperties=Qm.prototype.I,Qm.prototype.unset=Qm.prototype.T,Qm.prototype.changed=Qm.prototype.s,Qm.prototype.dispatchEvent=Qm.prototype.b,Qm.prototype.getRevision=Qm.prototype.M,Qm.prototype.on=Qm.prototype.J,Qm.prototype.once=Qm.prototype.N,Qm.prototype.un=Qm.prototype.K,Qm.prototype.unByKey=Qm.prototype.O,r1.prototype.getTileGrid=r1.prototype.Va,r1.prototype.refresh=r1.prototype.wa,r1.prototype.getAttributions=r1.prototype.za,r1.prototype.getLogo=r1.prototype.ya,r1.prototype.getProjection=r1.prototype.Aa,r1.prototype.getState=r1.prototype.W,r1.prototype.setAttributions=r1.prototype.ua,r1.prototype.getKeys=r1.prototype.S,r1.prototype.getProperties=r1.prototype.R,r1.prototype.setProperties=r1.prototype.I,r1.prototype.unset=r1.prototype.T,r1.prototype.changed=r1.prototype.s,r1.prototype.dispatchEvent=r1.prototype.b,r1.prototype.getRevision=r1.prototype.M,r1.prototype.on=r1.prototype.J,r1.prototype.once=r1.prototype.N,r1.prototype.un=r1.prototype.K,r1.prototype.unByKey=r1.prototype.O,A.prototype.getTileLoadFunction=A.prototype.ib,A.prototype.getTileUrlFunction=A.prototype.kb,A.prototype.getUrls=A.prototype.lb,A.prototype.setTileLoadFunction=A.prototype.sb,A.prototype.setTileUrlFunction=A.prototype.Xa,A.prototype.setUrl=A.prototype.cb,A.prototype.setUrls=A.prototype.Ya,A.prototype.getTileGrid=A.prototype.Va,A.prototype.refresh=A.prototype.wa,A.prototype.getAttributions=A.prototype.za,A.prototype.getLogo=A.prototype.ya,A.prototype.getProjection=A.prototype.Aa,A.prototype.getState=A.prototype.W,A.prototype.setAttributions=A.prototype.ua,A.prototype.getKeys=A.prototype.S,A.prototype.getProperties=A.prototype.R,A.prototype.setProperties=A.prototype.I,A.prototype.unset=A.prototype.T,A.prototype.changed=A.prototype.s,A.prototype.dispatchEvent=A.prototype.b,A.prototype.getRevision=A.prototype.M,A.prototype.on=A.prototype.J,A.prototype.once=A.prototype.N,A.prototype.un=A.prototype.K,A.prototype.unByKey=A.prototype.O,E.prototype.setRenderReprojectionEdges=E.prototype.Hb,E.prototype.setTileGridForProjection=E.prototype.Ib,E.prototype.getTileLoadFunction=E.prototype.ib,E.prototype.getTileUrlFunction=E.prototype.kb,E.prototype.getUrls=E.prototype.lb,E.prototype.setTileLoadFunction=E.prototype.sb,E.prototype.setTileUrlFunction=E.prototype.Xa,E.prototype.setUrl=E.prototype.cb,E.prototype.setUrls=E.prototype.Ya,E.prototype.getTileGrid=E.prototype.Va,E.prototype.refresh=E.prototype.wa,E.prototype.getAttributions=E.prototype.za,E.prototype.getLogo=E.prototype.ya,E.prototype.getProjection=E.prototype.Aa,E.prototype.getState=E.prototype.W,E.prototype.setAttributions=E.prototype.ua,E.prototype.getKeys=E.prototype.S,E.prototype.getProperties=E.prototype.R,E.prototype.setProperties=E.prototype.I,E.prototype.unset=E.prototype.T,E.prototype.changed=E.prototype.s,E.prototype.dispatchEvent=E.prototype.b,E.prototype.getRevision=E.prototype.M,E.prototype.on=E.prototype.J,E.prototype.once=E.prototype.N,E.prototype.un=E.prototype.K,E.prototype.unByKey=E.prototype.O,C.prototype.setRenderReprojectionEdges=C.prototype.Hb,C.prototype.setTileGridForProjection=C.prototype.Ib,C.prototype.getTileLoadFunction=C.prototype.ib,C.prototype.getTileUrlFunction=C.prototype.kb,C.prototype.getUrls=C.prototype.lb,C.prototype.setTileLoadFunction=C.prototype.sb,C.prototype.setTileUrlFunction=C.prototype.Xa,C.prototype.setUrl=C.prototype.cb,C.prototype.setUrls=C.prototype.Ya,C.prototype.getTileGrid=C.prototype.Va,C.prototype.refresh=C.prototype.wa,C.prototype.getAttributions=C.prototype.za,C.prototype.getLogo=C.prototype.ya,C.prototype.getProjection=C.prototype.Aa,C.prototype.getState=C.prototype.W,C.prototype.setAttributions=C.prototype.ua,C.prototype.getKeys=C.prototype.S,C.prototype.getProperties=C.prototype.R,C.prototype.setProperties=C.prototype.I,C.prototype.unset=C.prototype.T,C.prototype.changed=C.prototype.s,C.prototype.dispatchEvent=C.prototype.b,C.prototype.getRevision=C.prototype.M,C.prototype.on=C.prototype.J,C.prototype.once=C.prototype.N,C.prototype.un=C.prototype.K,C.prototype.unByKey=C.prototype.O,j.prototype.setRenderReprojectionEdges=j.prototype.Hb,j.prototype.setTileGridForProjection=j.prototype.Ib,j.prototype.getTileLoadFunction=j.prototype.ib,j.prototype.getTileUrlFunction=j.prototype.kb,j.prototype.getUrls=j.prototype.lb,j.prototype.setTileLoadFunction=j.prototype.sb,j.prototype.setTileUrlFunction=j.prototype.Xa,j.prototype.setUrl=j.prototype.cb,j.prototype.setUrls=j.prototype.Ya,j.prototype.getTileGrid=j.prototype.Va,j.prototype.refresh=j.prototype.wa,j.prototype.getAttributions=j.prototype.za,j.prototype.getLogo=j.prototype.ya,j.prototype.getProjection=j.prototype.Aa,j.prototype.getState=j.prototype.W,j.prototype.setAttributions=j.prototype.ua,j.prototype.getKeys=j.prototype.S,j.prototype.getProperties=j.prototype.R,j.prototype.setProperties=j.prototype.I,j.prototype.unset=j.prototype.T,j.prototype.changed=j.prototype.s,j.prototype.dispatchEvent=j.prototype.b,j.prototype.getRevision=j.prototype.M,j.prototype.on=j.prototype.J,j.prototype.once=j.prototype.N,j.prototype.un=j.prototype.K,j.prototype.unByKey=j.prototype.O,v.prototype.getAttributions=v.prototype.za,v.prototype.getLogo=v.prototype.ya,v.prototype.getProjection=v.prototype.Aa,v.prototype.getState=v.prototype.W,v.prototype.refresh=v.prototype.wa,v.prototype.setAttributions=v.prototype.ua,v.prototype.getKeys=v.prototype.S,v.prototype.getProperties=v.prototype.R,v.prototype.setProperties=v.prototype.I,v.prototype.unset=v.prototype.T,v.prototype.changed=v.prototype.s,v.prototype.dispatchEvent=v.prototype.b,v.prototype.getRevision=v.prototype.M,v.prototype.on=v.prototype.J,v.prototype.once=v.prototype.N,v.prototype.un=v.prototype.K,v.prototype.unByKey=v.prototype.O,L.prototype.addFeature=L.prototype.gb,L.prototype.addFeatures=L.prototype.Tc,L.prototype.forEachFeature=L.prototype.Fg,L.prototype.forEachFeatureInExtent=L.prototype.Qb,L.prototype.forEachFeatureIntersectingExtent=L.prototype.Gg,L.prototype.getFeaturesCollection=L.prototype.Og,L.prototype.getFeatures=L.prototype.we,L.prototype.getFeaturesAtCoordinate=L.prototype.Ng,L.prototype.getFeaturesInExtent=L.prototype.nf,L.prototype.getClosestFeatureToCoordinate=L.prototype.Jg,L.prototype.getExtent=L.prototype.G,L.prototype.getFeatureById=L.prototype.Mg,L.prototype.getFormat=L.prototype.Kh,L.prototype.getUrl=L.prototype.Lh,L.prototype.removeFeature=L.prototype.rb,L.prototype.getAttributions=L.prototype.za,L.prototype.getLogo=L.prototype.ya,L.prototype.getProjection=L.prototype.Aa,L.prototype.getState=L.prototype.W,L.prototype.refresh=L.prototype.wa,L.prototype.setAttributions=L.prototype.ua,L.prototype.getKeys=L.prototype.S,L.prototype.getProperties=L.prototype.R,L.prototype.setProperties=L.prototype.I,L.prototype.unset=L.prototype.T,L.prototype.changed=L.prototype.s,L.prototype.dispatchEvent=L.prototype.b,L.prototype.getRevision=L.prototype.M,L.prototype.on=L.prototype.J,L.prototype.once=L.prototype.N,L.prototype.un=L.prototype.K,L.prototype.unByKey=L.prototype.O,Ol.prototype.getAttributions=Ol.prototype.za,Ol.prototype.getLogo=Ol.prototype.ya,Ol.prototype.getProjection=Ol.prototype.Aa,Ol.prototype.getState=Ol.prototype.W,Ol.prototype.refresh=Ol.prototype.wa,Ol.prototype.setAttributions=Ol.prototype.ua,Ol.prototype.getKeys=Ol.prototype.S,Ol.prototype.getProperties=Ol.prototype.R,Ol.prototype.setProperties=Ol.prototype.I,Ol.prototype.unset=Ol.prototype.T,Ol.prototype.changed=Ol.prototype.s,Ol.prototype.dispatchEvent=Ol.prototype.b,Ol.prototype.getRevision=Ol.prototype.M,Ol.prototype.on=Ol.prototype.J,Ol.prototype.once=Ol.prototype.N,Ol.prototype.un=Ol.prototype.K,Ol.prototype.unByKey=Ol.prototype.O,y1.prototype.getAttributions=y1.prototype.za,y1.prototype.getLogo=y1.prototype.ya,y1.prototype.getProjection=y1.prototype.Aa,y1.prototype.getState=y1.prototype.W,y1.prototype.refresh=y1.prototype.wa,y1.prototype.setAttributions=y1.prototype.ua,y1.prototype.getKeys=y1.prototype.S,y1.prototype.getProperties=y1.prototype.R,y1.prototype.setProperties=y1.prototype.I,y1.prototype.unset=y1.prototype.T,y1.prototype.changed=y1.prototype.s,y1.prototype.dispatchEvent=y1.prototype.b,y1.prototype.getRevision=y1.prototype.M,y1.prototype.on=y1.prototype.J,y1.prototype.once=y1.prototype.N,y1.prototype.un=y1.prototype.K,y1.prototype.unByKey=y1.prototype.O,Vl.prototype.getAttributions=Vl.prototype.za,Vl.prototype.getLogo=Vl.prototype.ya,Vl.prototype.getProjection=Vl.prototype.Aa,Vl.prototype.getState=Vl.prototype.W,Vl.prototype.refresh=Vl.prototype.wa,Vl.prototype.setAttributions=Vl.prototype.ua,Vl.prototype.getKeys=Vl.prototype.S,Vl.prototype.getProperties=Vl.prototype.R,Vl.prototype.setProperties=Vl.prototype.I,Vl.prototype.unset=Vl.prototype.T,Vl.prototype.changed=Vl.prototype.s,Vl.prototype.dispatchEvent=Vl.prototype.b,Vl.prototype.getRevision=Vl.prototype.M,Vl.prototype.on=Vl.prototype.J,Vl.prototype.once=Vl.prototype.N,Vl.prototype.un=Vl.prototype.K,Vl.prototype.unByKey=Vl.prototype.O,f1.prototype.getAttributions=f1.prototype.za,f1.prototype.getLogo=f1.prototype.ya,f1.prototype.getProjection=f1.prototype.Aa,f1.prototype.getState=f1.prototype.W,f1.prototype.refresh=f1.prototype.wa,f1.prototype.setAttributions=f1.prototype.ua,f1.prototype.getKeys=f1.prototype.S,f1.prototype.getProperties=f1.prototype.R,f1.prototype.setProperties=f1.prototype.I,f1.prototype.unset=f1.prototype.T,f1.prototype.changed=f1.prototype.s,f1.prototype.dispatchEvent=f1.prototype.b,f1.prototype.getRevision=f1.prototype.M,f1.prototype.on=f1.prototype.J,f1.prototype.once=f1.prototype.N,f1.prototype.un=f1.prototype.K,f1.prototype.unByKey=f1.prototype.O,g1.prototype.getAttributions=g1.prototype.za,g1.prototype.getLogo=g1.prototype.ya,g1.prototype.getProjection=g1.prototype.Aa,g1.prototype.getState=g1.prototype.W,g1.prototype.refresh=g1.prototype.wa,g1.prototype.setAttributions=g1.prototype.ua,g1.prototype.getKeys=g1.prototype.S,g1.prototype.getProperties=g1.prototype.R,g1.prototype.setProperties=g1.prototype.I,g1.prototype.unset=g1.prototype.T,g1.prototype.changed=g1.prototype.s,g1.prototype.dispatchEvent=g1.prototype.b,g1.prototype.getRevision=g1.prototype.M,g1.prototype.on=g1.prototype.J,g1.prototype.once=g1.prototype.N,g1.prototype.un=g1.prototype.K,g1.prototype.unByKey=g1.prototype.O,Wl.prototype.getAttributions=Wl.prototype.za,Wl.prototype.getLogo=Wl.prototype.ya,Wl.prototype.getProjection=Wl.prototype.Aa,Wl.prototype.getState=Wl.prototype.W,Wl.prototype.refresh=Wl.prototype.wa,Wl.prototype.setAttributions=Wl.prototype.ua,Wl.prototype.getKeys=Wl.prototype.S,Wl.prototype.getProperties=Wl.prototype.R,Wl.prototype.setProperties=Wl.prototype.I,Wl.prototype.unset=Wl.prototype.T,Wl.prototype.changed=Wl.prototype.s,Wl.prototype.dispatchEvent=Wl.prototype.b,Wl.prototype.getRevision=Wl.prototype.M,Wl.prototype.on=Wl.prototype.J,Wl.prototype.once=Wl.prototype.N,Wl.prototype.un=Wl.prototype.K,Wl.prototype.unByKey=Wl.prototype.O,d1.prototype.getAttributions=d1.prototype.za,d1.prototype.getLogo=d1.prototype.ya,d1.prototype.getProjection=d1.prototype.Aa,d1.prototype.getState=d1.prototype.W,d1.prototype.refresh=d1.prototype.wa,d1.prototype.setAttributions=d1.prototype.ua,d1.prototype.getKeys=d1.prototype.S,d1.prototype.getProperties=d1.prototype.R,d1.prototype.setProperties=d1.prototype.I,d1.prototype.unset=d1.prototype.T,d1.prototype.changed=d1.prototype.s,d1.prototype.dispatchEvent=d1.prototype.b,d1.prototype.getRevision=d1.prototype.M,d1.prototype.on=d1.prototype.J,d1.prototype.once=d1.prototype.N,d1.prototype.un=d1.prototype.K,d1.prototype.unByKey=d1.prototype.O,w1.prototype.setRenderReprojectionEdges=w1.prototype.Hb,w1.prototype.setTileGridForProjection=w1.prototype.Ib,w1.prototype.getTileLoadFunction=w1.prototype.ib,w1.prototype.getTileUrlFunction=w1.prototype.kb,w1.prototype.getUrls=w1.prototype.lb,w1.prototype.setTileLoadFunction=w1.prototype.sb,w1.prototype.setTileUrlFunction=w1.prototype.Xa,w1.prototype.setUrl=w1.prototype.cb,w1.prototype.setUrls=w1.prototype.Ya,w1.prototype.getTileGrid=w1.prototype.Va,w1.prototype.refresh=w1.prototype.wa,w1.prototype.getAttributions=w1.prototype.za,w1.prototype.getLogo=w1.prototype.ya,w1.prototype.getProjection=w1.prototype.Aa,w1.prototype.getState=w1.prototype.W,w1.prototype.setAttributions=w1.prototype.ua,w1.prototype.getKeys=w1.prototype.S,w1.prototype.getProperties=w1.prototype.R,w1.prototype.setProperties=w1.prototype.I,w1.prototype.unset=w1.prototype.T,w1.prototype.changed=w1.prototype.s,w1.prototype.dispatchEvent=w1.prototype.b,w1.prototype.getRevision=w1.prototype.M,w1.prototype.on=w1.prototype.J,w1.prototype.once=w1.prototype.N,w1.prototype.un=w1.prototype.K,w1.prototype.unByKey=w1.prototype.O,P1.prototype.getAttributions=P1.prototype.za,P1.prototype.getLogo=P1.prototype.ya,P1.prototype.getProjection=P1.prototype.Aa,P1.prototype.getState=P1.prototype.W,P1.prototype.refresh=P1.prototype.wa,P1.prototype.setAttributions=P1.prototype.ua,P1.prototype.getKeys=P1.prototype.S,P1.prototype.getProperties=P1.prototype.R,P1.prototype.setProperties=P1.prototype.I,P1.prototype.unset=P1.prototype.T,P1.prototype.changed=P1.prototype.s,P1.prototype.dispatchEvent=P1.prototype.b,P1.prototype.getRevision=P1.prototype.M,P1.prototype.on=P1.prototype.J,P1.prototype.once=P1.prototype.N,P1.prototype.un=P1.prototype.K,P1.prototype.unByKey=P1.prototype.O,N1.prototype.setRenderReprojectionEdges=N1.prototype.Hb,N1.prototype.setTileGridForProjection=N1.prototype.Ib,N1.prototype.getTileLoadFunction=N1.prototype.ib,N1.prototype.getTileUrlFunction=N1.prototype.kb,N1.prototype.getUrls=N1.prototype.lb,N1.prototype.setTileLoadFunction=N1.prototype.sb,N1.prototype.setTileUrlFunction=N1.prototype.Xa,N1.prototype.setUrl=N1.prototype.cb,N1.prototype.setUrls=N1.prototype.Ya,N1.prototype.getTileGrid=N1.prototype.Va,N1.prototype.refresh=N1.prototype.wa,N1.prototype.getAttributions=N1.prototype.za,N1.prototype.getLogo=N1.prototype.ya,N1.prototype.getProjection=N1.prototype.Aa,N1.prototype.getState=N1.prototype.W,N1.prototype.setAttributions=N1.prototype.ua,N1.prototype.getKeys=N1.prototype.S,N1.prototype.getProperties=N1.prototype.R,N1.prototype.setProperties=N1.prototype.I,N1.prototype.unset=N1.prototype.T,N1.prototype.changed=N1.prototype.s,N1.prototype.dispatchEvent=N1.prototype.b,N1.prototype.getRevision=N1.prototype.M,N1.prototype.on=N1.prototype.J,N1.prototype.once=N1.prototype.N,N1.prototype.un=N1.prototype.K,N1.prototype.unByKey=N1.prototype.O,R.prototype.setRenderReprojectionEdges=R.prototype.Hb,R.prototype.setTileGridForProjection=R.prototype.Ib,R.prototype.getTileLoadFunction=R.prototype.ib,R.prototype.getTileUrlFunction=R.prototype.kb,R.prototype.getUrls=R.prototype.lb,R.prototype.setTileLoadFunction=R.prototype.sb,R.prototype.setTileUrlFunction=R.prototype.Xa,R.prototype.setUrl=R.prototype.cb,R.prototype.setUrls=R.prototype.Ya,R.prototype.getTileGrid=R.prototype.Va,R.prototype.refresh=R.prototype.wa,R.prototype.getAttributions=R.prototype.za,R.prototype.getLogo=R.prototype.ya,R.prototype.getProjection=R.prototype.Aa,R.prototype.getState=R.prototype.W,R.prototype.setAttributions=R.prototype.ua,R.prototype.getKeys=R.prototype.S,R.prototype.getProperties=R.prototype.R,R.prototype.setProperties=R.prototype.I,R.prototype.unset=R.prototype.T,R.prototype.changed=R.prototype.s,R.prototype.dispatchEvent=R.prototype.b,R.prototype.getRevision=R.prototype.M,R.prototype.on=R.prototype.J,R.prototype.once=R.prototype.N,R.prototype.un=R.prototype.K,R.prototype.unByKey=R.prototype.O,D1.prototype.getTileGrid=D1.prototype.Va,D1.prototype.refresh=D1.prototype.wa,D1.prototype.getAttributions=D1.prototype.za,D1.prototype.getLogo=D1.prototype.ya,D1.prototype.getProjection=D1.prototype.Aa,D1.prototype.getState=D1.prototype.W,D1.prototype.setAttributions=D1.prototype.ua,D1.prototype.getKeys=D1.prototype.S,D1.prototype.getProperties=D1.prototype.R,D1.prototype.setProperties=D1.prototype.I,D1.prototype.unset=D1.prototype.T,D1.prototype.changed=D1.prototype.s,D1.prototype.dispatchEvent=D1.prototype.b,D1.prototype.getRevision=D1.prototype.M,D1.prototype.on=D1.prototype.J,D1.prototype.once=D1.prototype.N,D1.prototype.un=D1.prototype.K,D1.prototype.unByKey=D1.prototype.O,N.prototype.setRenderReprojectionEdges=N.prototype.Hb,N.prototype.setTileGridForProjection=N.prototype.Ib,N.prototype.getTileLoadFunction=N.prototype.ib,N.prototype.getTileUrlFunction=N.prototype.kb,N.prototype.getUrls=N.prototype.lb,N.prototype.setTileLoadFunction=N.prototype.sb,N.prototype.setTileUrlFunction=N.prototype.Xa,N.prototype.setUrl=N.prototype.cb,N.prototype.setUrls=N.prototype.Ya,N.prototype.getTileGrid=N.prototype.Va,N.prototype.refresh=N.prototype.wa,N.prototype.getAttributions=N.prototype.za,N.prototype.getLogo=N.prototype.ya,N.prototype.getProjection=N.prototype.Aa,N.prototype.getState=N.prototype.W,N.prototype.setAttributions=N.prototype.ua,N.prototype.getKeys=N.prototype.S,N.prototype.getProperties=N.prototype.R,N.prototype.setProperties=N.prototype.I,N.prototype.unset=N.prototype.T,N.prototype.changed=N.prototype.s,N.prototype.dispatchEvent=N.prototype.b,N.prototype.getRevision=N.prototype.M,N.prototype.on=N.prototype.J,N.prototype.once=N.prototype.N,N.prototype.un=N.prototype.K,N.prototype.unByKey=N.prototype.O,U1.prototype.getTileGrid=U1.prototype.Va,U1.prototype.refresh=U1.prototype.wa,U1.prototype.getAttributions=U1.prototype.za,U1.prototype.getLogo=U1.prototype.ya,U1.prototype.getProjection=U1.prototype.Aa,U1.prototype.getState=U1.prototype.W,U1.prototype.setAttributions=U1.prototype.ua,U1.prototype.getKeys=U1.prototype.S,U1.prototype.getProperties=U1.prototype.R,U1.prototype.setProperties=U1.prototype.I,U1.prototype.unset=U1.prototype.T,U1.prototype.changed=U1.prototype.s,U1.prototype.dispatchEvent=U1.prototype.b,U1.prototype.getRevision=U1.prototype.M,U1.prototype.on=U1.prototype.J,U1.prototype.once=U1.prototype.N,U1.prototype.un=U1.prototype.K,U1.prototype.unByKey=U1.prototype.O,I.prototype.setRenderReprojectionEdges=I.prototype.Hb,I.prototype.setTileGridForProjection=I.prototype.Ib,I.prototype.getTileLoadFunction=I.prototype.ib,I.prototype.getTileUrlFunction=I.prototype.kb,I.prototype.getUrls=I.prototype.lb,I.prototype.setTileLoadFunction=I.prototype.sb,I.prototype.setTileUrlFunction=I.prototype.Xa,I.prototype.setUrl=I.prototype.cb,I.prototype.setUrls=I.prototype.Ya,I.prototype.getTileGrid=I.prototype.Va,I.prototype.refresh=I.prototype.wa,I.prototype.getAttributions=I.prototype.za,I.prototype.getLogo=I.prototype.ya,I.prototype.getProjection=I.prototype.Aa,I.prototype.getState=I.prototype.W,I.prototype.setAttributions=I.prototype.ua,I.prototype.getKeys=I.prototype.S,I.prototype.getProperties=I.prototype.R,I.prototype.setProperties=I.prototype.I,I.prototype.unset=I.prototype.T,I.prototype.changed=I.prototype.s,I.prototype.dispatchEvent=I.prototype.b,I.prototype.getRevision=I.prototype.M,I.prototype.on=I.prototype.J,I.prototype.once=I.prototype.N,I.prototype.un=I.prototype.K,I.prototype.unByKey=I.prototype.O,Z1.prototype.getTileLoadFunction=Z1.prototype.ib,Z1.prototype.getTileUrlFunction=Z1.prototype.kb,Z1.prototype.getUrls=Z1.prototype.lb,Z1.prototype.setTileLoadFunction=Z1.prototype.sb,Z1.prototype.setTileUrlFunction=Z1.prototype.Xa,Z1.prototype.setUrl=Z1.prototype.cb,Z1.prototype.setUrls=Z1.prototype.Ya,Z1.prototype.getTileGrid=Z1.prototype.Va,Z1.prototype.refresh=Z1.prototype.wa,Z1.prototype.getAttributions=Z1.prototype.za,Z1.prototype.getLogo=Z1.prototype.ya,Z1.prototype.getProjection=Z1.prototype.Aa,Z1.prototype.getState=Z1.prototype.W,Z1.prototype.setAttributions=Z1.prototype.ua,Z1.prototype.getKeys=Z1.prototype.S,Z1.prototype.getProperties=Z1.prototype.R,Z1.prototype.setProperties=Z1.prototype.I,Z1.prototype.unset=Z1.prototype.T,Z1.prototype.changed=Z1.prototype.s,Z1.prototype.dispatchEvent=Z1.prototype.b,Z1.prototype.getRevision=Z1.prototype.M,Z1.prototype.on=Z1.prototype.J,Z1.prototype.once=Z1.prototype.N,Z1.prototype.un=Z1.prototype.K,Z1.prototype.unByKey=Z1.prototype.O,F.prototype.setRenderReprojectionEdges=F.prototype.Hb,F.prototype.setTileGridForProjection=F.prototype.Ib,F.prototype.getTileLoadFunction=F.prototype.ib,F.prototype.getTileUrlFunction=F.prototype.kb,F.prototype.getUrls=F.prototype.lb,F.prototype.setTileLoadFunction=F.prototype.sb,F.prototype.setTileUrlFunction=F.prototype.Xa,F.prototype.setUrl=F.prototype.cb,F.prototype.setUrls=F.prototype.Ya,F.prototype.getTileGrid=F.prototype.Va,F.prototype.refresh=F.prototype.wa,F.prototype.getAttributions=F.prototype.za,F.prototype.getLogo=F.prototype.ya,F.prototype.getProjection=F.prototype.Aa,F.prototype.getState=F.prototype.W,F.prototype.setAttributions=F.prototype.ua,F.prototype.getKeys=F.prototype.S,F.prototype.getProperties=F.prototype.R,F.prototype.setProperties=F.prototype.I,F.prototype.unset=F.prototype.T,F.prototype.changed=F.prototype.s,F.prototype.dispatchEvent=F.prototype.b,F.prototype.getRevision=F.prototype.M,F.prototype.on=F.prototype.J,F.prototype.once=F.prototype.N,F.prototype.un=F.prototype.K,F.prototype.unByKey=F.prototype.O,Q1.prototype.setRenderReprojectionEdges=Q1.prototype.Hb,Q1.prototype.setTileGridForProjection=Q1.prototype.Ib,Q1.prototype.getTileLoadFunction=Q1.prototype.ib,Q1.prototype.getTileUrlFunction=Q1.prototype.kb,Q1.prototype.getUrls=Q1.prototype.lb,Q1.prototype.setTileLoadFunction=Q1.prototype.sb,Q1.prototype.setTileUrlFunction=Q1.prototype.Xa,Q1.prototype.setUrl=Q1.prototype.cb,Q1.prototype.setUrls=Q1.prototype.Ya,Q1.prototype.getTileGrid=Q1.prototype.Va,Q1.prototype.refresh=Q1.prototype.wa,Q1.prototype.getAttributions=Q1.prototype.za,Q1.prototype.getLogo=Q1.prototype.ya,Q1.prototype.getProjection=Q1.prototype.Aa,Q1.prototype.getState=Q1.prototype.W,Q1.prototype.setAttributions=Q1.prototype.ua,Q1.prototype.getKeys=Q1.prototype.S,Q1.prototype.getProperties=Q1.prototype.R,Q1.prototype.setProperties=Q1.prototype.I,Q1.prototype.unset=Q1.prototype.T,Q1.prototype.changed=Q1.prototype.s,Q1.prototype.dispatchEvent=Q1.prototype.b,Q1.prototype.getRevision=Q1.prototype.M,Q1.prototype.on=Q1.prototype.J,Q1.prototype.once=Q1.prototype.N,Q1.prototype.un=Q1.prototype.K,Q1.prototype.unByKey=Q1.prototype.O,Hm.prototype.getTileCoord=Hm.prototype.c,Fp.prototype.changed=Fp.prototype.s,Fp.prototype.dispatchEvent=Fp.prototype.b,Fp.prototype.getRevision=Fp.prototype.M,Fp.prototype.on=Fp.prototype.J,Fp.prototype.once=Fp.prototype.N,Fp.prototype.un=Fp.prototype.K,Fp.prototype.unByKey=Fp.prototype.O,Ml.prototype.changed=Ml.prototype.s,Ml.prototype.dispatchEvent=Ml.prototype.b,Ml.prototype.getRevision=Ml.prototype.M,Ml.prototype.on=Ml.prototype.J,Ml.prototype.once=Ml.prototype.N,Ml.prototype.un=Ml.prototype.K,Ml.prototype.unByKey=Ml.prototype.O,zl.prototype.changed=zl.prototype.s,zl.prototype.dispatchEvent=zl.prototype.b,zl.prototype.getRevision=zl.prototype.M,zl.prototype.on=zl.prototype.J,zl.prototype.once=zl.prototype.N,zl.prototype.un=zl.prototype.K,zl.prototype.unByKey=zl.prototype.O,_l.prototype.changed=_l.prototype.s,_l.prototype.dispatchEvent=_l.prototype.b,_l.prototype.getRevision=_l.prototype.M,_l.prototype.on=_l.prototype.J,_l.prototype.once=_l.prototype.N,_l.prototype.un=_l.prototype.K,_l.prototype.unByKey=_l.prototype.O,$l.prototype.changed=$l.prototype.s,$l.prototype.dispatchEvent=$l.prototype.b,$l.prototype.getRevision=$l.prototype.M,$l.prototype.on=$l.prototype.J,$l.prototype.once=$l.prototype.N,$l.prototype.un=$l.prototype.K,$l.prototype.unByKey=$l.prototype.O,Xp.prototype.changed=Xp.prototype.s,Xp.prototype.dispatchEvent=Xp.prototype.b,Xp.prototype.getRevision=Xp.prototype.M,Xp.prototype.on=Xp.prototype.J,Xp.prototype.once=Xp.prototype.N,Xp.prototype.un=Xp.prototype.K,Xp.prototype.unByKey=Xp.prototype.O,Hp.prototype.changed=Hp.prototype.s,Hp.prototype.dispatchEvent=Hp.prototype.b,Hp.prototype.getRevision=Hp.prototype.M,Hp.prototype.on=Hp.prototype.J,Hp.prototype.once=Hp.prototype.N,Hp.prototype.un=Hp.prototype.K,Hp.prototype.unByKey=Hp.prototype.O,Yp.prototype.changed=Yp.prototype.s,Yp.prototype.dispatchEvent=Yp.prototype.b,Yp.prototype.getRevision=Yp.prototype.M,Yp.prototype.on=Yp.prototype.J,Yp.prototype.once=Yp.prototype.N,Yp.prototype.un=Yp.prototype.K,Yp.prototype.unByKey=Yp.prototype.O,Zp.prototype.changed=Zp.prototype.s,Zp.prototype.dispatchEvent=Zp.prototype.b,Zp.prototype.getRevision=Zp.prototype.M,Zp.prototype.on=Zp.prototype.J,Zp.prototype.once=Zp.prototype.N,Zp.prototype.un=Zp.prototype.K,Zp.prototype.unByKey=Zp.prototype.O,Ma.prototype.changed=Ma.prototype.s,Ma.prototype.dispatchEvent=Ma.prototype.b,Ma.prototype.getRevision=Ma.prototype.M,Ma.prototype.on=Ma.prototype.J,Ma.prototype.once=Ma.prototype.N,Ma.prototype.un=Ma.prototype.K,Ma.prototype.unByKey=Ma.prototype.O,Pa.prototype.changed=Pa.prototype.s,Pa.prototype.dispatchEvent=Pa.prototype.b,Pa.prototype.getRevision=Pa.prototype.M,Pa.prototype.on=Pa.prototype.J,Pa.prototype.once=Pa.prototype.N,Pa.prototype.un=Pa.prototype.K,Pa.prototype.unByKey=Pa.prototype.O,ns.prototype.getKeys=ns.prototype.S,ns.prototype.getProperties=ns.prototype.R,ns.prototype.setProperties=ns.prototype.I,ns.prototype.unset=ns.prototype.T,ns.prototype.changed=ns.prototype.s,ns.prototype.dispatchEvent=ns.prototype.b,ns.prototype.getRevision=ns.prototype.M,ns.prototype.on=ns.prototype.J,ns.prototype.once=ns.prototype.N,ns.prototype.un=ns.prototype.K,ns.prototype.unByKey=ns.prototype.O,ys.prototype.getExtent=ys.prototype.G,ys.prototype.getMaxResolution=ys.prototype.Wb,ys.prototype.getMinResolution=ys.prototype.Xb,ys.prototype.getOpacity=ys.prototype.Yb,ys.prototype.getVisible=ys.prototype.Fb,ys.prototype.getZIndex=ys.prototype.Zb,ys.prototype.setExtent=ys.prototype.kc,ys.prototype.setMaxResolution=ys.prototype.pc,ys.prototype.setMinResolution=ys.prototype.qc,ys.prototype.setOpacity=ys.prototype.lc,ys.prototype.setVisible=ys.prototype.mc,ys.prototype.setZIndex=ys.prototype.nc,ys.prototype.getKeys=ys.prototype.S,ys.prototype.getProperties=ys.prototype.R,ys.prototype.setProperties=ys.prototype.I,ys.prototype.unset=ys.prototype.T,ys.prototype.changed=ys.prototype.s,ys.prototype.dispatchEvent=ys.prototype.b,ys.prototype.getRevision=ys.prototype.M,ys.prototype.on=ys.prototype.J,ys.prototype.once=ys.prototype.N,ys.prototype.un=ys.prototype.K,ys.prototype.unByKey=ys.prototype.O,o.prototype.getExtent=o.prototype.G,o.prototype.getMaxResolution=o.prototype.Wb,o.prototype.getMinResolution=o.prototype.Xb,o.prototype.getOpacity=o.prototype.Yb,o.prototype.getVisible=o.prototype.Fb,o.prototype.getZIndex=o.prototype.Zb,o.prototype.setExtent=o.prototype.kc,o.prototype.setMaxResolution=o.prototype.pc,o.prototype.setMinResolution=o.prototype.qc,o.prototype.setOpacity=o.prototype.lc,o.prototype.setVisible=o.prototype.mc,o.prototype.setZIndex=o.prototype.nc,o.prototype.getKeys=o.prototype.S,o.prototype.getProperties=o.prototype.R,o.prototype.setProperties=o.prototype.I,o.prototype.unset=o.prototype.T,o.prototype.changed=o.prototype.s,o.prototype.dispatchEvent=o.prototype.b,o.prototype.getRevision=o.prototype.M,o.prototype.on=o.prototype.J,o.prototype.once=o.prototype.N,o.prototype.un=o.prototype.K,o.prototype.unByKey=o.prototype.O,s.prototype.setSource=s.prototype.Pc,s.prototype.getExtent=s.prototype.G,s.prototype.getMaxResolution=s.prototype.Wb,s.prototype.getMinResolution=s.prototype.Xb,s.prototype.getOpacity=s.prototype.Yb,s.prototype.getVisible=s.prototype.Fb,s.prototype.getZIndex=s.prototype.Zb,s.prototype.setExtent=s.prototype.kc,s.prototype.setMaxResolution=s.prototype.pc,s.prototype.setMinResolution=s.prototype.qc,s.prototype.setOpacity=s.prototype.lc,s.prototype.setVisible=s.prototype.mc,s.prototype.setZIndex=s.prototype.nc,s.prototype.getKeys=s.prototype.S,s.prototype.getProperties=s.prototype.R,s.prototype.setProperties=s.prototype.I,s.prototype.unset=s.prototype.T,s.prototype.changed=s.prototype.s,s.prototype.dispatchEvent=s.prototype.b,s.prototype.getRevision=s.prototype.M,s.prototype.on=s.prototype.J,s.prototype.once=s.prototype.N,s.prototype.un=s.prototype.K,s.prototype.unByKey=s.prototype.O,T.prototype.getSource=T.prototype.la,T.prototype.getStyle=T.prototype.D,T.prototype.getStyleFunction=T.prototype.L,T.prototype.setStyle=T.prototype.l,T.prototype.setSource=T.prototype.Pc,T.prototype.getExtent=T.prototype.G,T.prototype.getMaxResolution=T.prototype.Wb,T.prototype.getMinResolution=T.prototype.Xb,T.prototype.getOpacity=T.prototype.Yb,T.prototype.getVisible=T.prototype.Fb,T.prototype.getZIndex=T.prototype.Zb,T.prototype.setExtent=T.prototype.kc,T.prototype.setMaxResolution=T.prototype.pc,T.prototype.setMinResolution=T.prototype.qc,T.prototype.setOpacity=T.prototype.lc,T.prototype.setVisible=T.prototype.mc,T.prototype.setZIndex=T.prototype.nc,T.prototype.getKeys=T.prototype.S,T.prototype.getProperties=T.prototype.R,T.prototype.setProperties=T.prototype.I,T.prototype.unset=T.prototype.T,T.prototype.changed=T.prototype.s,T.prototype.dispatchEvent=T.prototype.b,T.prototype.getRevision=T.prototype.M,T.prototype.on=T.prototype.J,T.prototype.once=T.prototype.N,T.prototype.un=T.prototype.K,T.prototype.unByKey=T.prototype.O,Js.prototype.setSource=Js.prototype.Pc,Js.prototype.getExtent=Js.prototype.G,Js.prototype.getMaxResolution=Js.prototype.Wb,Js.prototype.getMinResolution=Js.prototype.Xb,Js.prototype.getOpacity=Js.prototype.Yb,Js.prototype.getVisible=Js.prototype.Fb,Js.prototype.getZIndex=Js.prototype.Zb,Js.prototype.setExtent=Js.prototype.kc,Js.prototype.setMaxResolution=Js.prototype.pc,Js.prototype.setMinResolution=Js.prototype.qc,Js.prototype.setOpacity=Js.prototype.lc,Js.prototype.setVisible=Js.prototype.mc,Js.prototype.setZIndex=Js.prototype.nc,Js.prototype.getKeys=Js.prototype.S,Js.prototype.getProperties=Js.prototype.R,Js.prototype.setProperties=Js.prototype.I,Js.prototype.unset=Js.prototype.T,Js.prototype.changed=Js.prototype.s,Js.prototype.dispatchEvent=Js.prototype.b,Js.prototype.getRevision=Js.prototype.M,Js.prototype.on=Js.prototype.J,Js.prototype.once=Js.prototype.N,Js.prototype.un=Js.prototype.K,Js.prototype.unByKey=Js.prototype.O,p.prototype.setSource=p.prototype.Pc,p.prototype.getExtent=p.prototype.G,p.prototype.getMaxResolution=p.prototype.Wb,p.prototype.getMinResolution=p.prototype.Xb,p.prototype.getOpacity=p.prototype.Yb,p.prototype.getVisible=p.prototype.Fb,p.prototype.getZIndex=p.prototype.Zb,p.prototype.setExtent=p.prototype.kc,p.prototype.setMaxResolution=p.prototype.pc,p.prototype.setMinResolution=p.prototype.qc,p.prototype.setOpacity=p.prototype.lc,p.prototype.setVisible=p.prototype.mc,p.prototype.setZIndex=p.prototype.nc,p.prototype.getKeys=p.prototype.S,p.prototype.getProperties=p.prototype.R,p.prototype.setProperties=p.prototype.I,p.prototype.unset=p.prototype.T,p.prototype.changed=p.prototype.s,p.prototype.dispatchEvent=p.prototype.b,p.prototype.getRevision=p.prototype.M,p.prototype.on=p.prototype.J,p.prototype.once=p.prototype.N,p.prototype.un=p.prototype.K,p.prototype.unByKey=p.prototype.O,i.prototype.getSource=i.prototype.la,i.prototype.getStyle=i.prototype.D,i.prototype.getStyleFunction=i.prototype.L,i.prototype.setStyle=i.prototype.l,i.prototype.setSource=i.prototype.Pc,i.prototype.getExtent=i.prototype.G,i.prototype.getMaxResolution=i.prototype.Wb,i.prototype.getMinResolution=i.prototype.Xb,i.prototype.getOpacity=i.prototype.Yb,i.prototype.getVisible=i.prototype.Fb,i.prototype.getZIndex=i.prototype.Zb,i.prototype.setExtent=i.prototype.kc,i.prototype.setMaxResolution=i.prototype.pc,i.prototype.setMinResolution=i.prototype.qc,i.prototype.setOpacity=i.prototype.lc,i.prototype.setVisible=i.prototype.mc,i.prototype.setZIndex=i.prototype.nc,i.prototype.getKeys=i.prototype.S,i.prototype.getProperties=i.prototype.R,i.prototype.setProperties=i.prototype.I,i.prototype.unset=i.prototype.T,i.prototype.changed=i.prototype.s,i.prototype.dispatchEvent=i.prototype.b,i.prototype.getRevision=i.prototype.M,i.prototype.on=i.prototype.J,i.prototype.once=i.prototype.N,i.prototype.un=i.prototype.K,i.prototype.unByKey=i.prototype.O,en.prototype.getKeys=en.prototype.S,en.prototype.getProperties=en.prototype.R,en.prototype.setProperties=en.prototype.I,en.prototype.unset=en.prototype.T,en.prototype.changed=en.prototype.s,en.prototype.dispatchEvent=en.prototype.b,en.prototype.getRevision=en.prototype.M,en.prototype.on=en.prototype.J,en.prototype.once=en.prototype.N,en.prototype.un=en.prototype.K,en.prototype.unByKey=en.prototype.O,pn.prototype.getActive=pn.prototype.f,pn.prototype.getMap=pn.prototype.c,pn.prototype.setActive=pn.prototype.Ea,pn.prototype.getKeys=pn.prototype.S,pn.prototype.getProperties=pn.prototype.R,pn.prototype.setProperties=pn.prototype.I,pn.prototype.unset=pn.prototype.T,pn.prototype.changed=pn.prototype.s,pn.prototype.dispatchEvent=pn.prototype.b,pn.prototype.getRevision=pn.prototype.M,pn.prototype.on=pn.prototype.J,pn.prototype.once=pn.prototype.N,pn.prototype.un=pn.prototype.K,pn.prototype.unByKey=pn.prototype.O,vb.prototype.getActive=vb.prototype.f,vb.prototype.getMap=vb.prototype.c,vb.prototype.setActive=vb.prototype.Ea,vb.prototype.getKeys=vb.prototype.S,vb.prototype.getProperties=vb.prototype.R,vb.prototype.setProperties=vb.prototype.I,vb.prototype.unset=vb.prototype.T,vb.prototype.changed=vb.prototype.s,vb.prototype.dispatchEvent=vb.prototype.b,vb.prototype.getRevision=vb.prototype.M,vb.prototype.on=vb.prototype.J,vb.prototype.once=vb.prototype.N,vb.prototype.un=vb.prototype.K,vb.prototype.unByKey=vb.prototype.O,bn.prototype.getActive=bn.prototype.f,bn.prototype.getMap=bn.prototype.c,bn.prototype.setActive=bn.prototype.Ea,bn.prototype.getKeys=bn.prototype.S,bn.prototype.getProperties=bn.prototype.R,bn.prototype.setProperties=bn.prototype.I,bn.prototype.unset=bn.prototype.T,bn.prototype.changed=bn.prototype.s,bn.prototype.dispatchEvent=bn.prototype.b,bn.prototype.getRevision=bn.prototype.M,bn.prototype.on=bn.prototype.J,bn.prototype.once=bn.prototype.N,bn.prototype.un=bn.prototype.K,bn.prototype.unByKey=bn.prototype.O,Nn.prototype.getActive=Nn.prototype.f,Nn.prototype.getMap=Nn.prototype.c,Nn.prototype.setActive=Nn.prototype.Ea,Nn.prototype.getKeys=Nn.prototype.S,Nn.prototype.getProperties=Nn.prototype.R,Nn.prototype.setProperties=Nn.prototype.I,Nn.prototype.unset=Nn.prototype.T,Nn.prototype.changed=Nn.prototype.s,Nn.prototype.dispatchEvent=Nn.prototype.b,Nn.prototype.getRevision=Nn.prototype.M,Nn.prototype.on=Nn.prototype.J,Nn.prototype.once=Nn.prototype.N,Nn.prototype.un=Nn.prototype.K,Nn.prototype.unByKey=Nn.prototype.O,xn.prototype.getActive=xn.prototype.f,xn.prototype.getMap=xn.prototype.c,xn.prototype.setActive=xn.prototype.Ea,xn.prototype.getKeys=xn.prototype.S,xn.prototype.getProperties=xn.prototype.R,xn.prototype.setProperties=xn.prototype.I,xn.prototype.unset=xn.prototype.T,xn.prototype.changed=xn.prototype.s,xn.prototype.dispatchEvent=xn.prototype.b,xn.prototype.getRevision=xn.prototype.M,xn.prototype.on=xn.prototype.J,xn.prototype.once=xn.prototype.N,xn.prototype.un=xn.prototype.K,xn.prototype.unByKey=xn.prototype.O,Tn.prototype.getActive=Tn.prototype.f,Tn.prototype.getMap=Tn.prototype.c,Tn.prototype.setActive=Tn.prototype.Ea,Tn.prototype.getKeys=Tn.prototype.S,Tn.prototype.getProperties=Tn.prototype.R,Tn.prototype.setProperties=Tn.prototype.I,Tn.prototype.unset=Tn.prototype.T,Tn.prototype.changed=Tn.prototype.s,Tn.prototype.dispatchEvent=Tn.prototype.b,Tn.prototype.getRevision=Tn.prototype.M,Tn.prototype.on=Tn.prototype.J,Tn.prototype.once=Tn.prototype.N,Tn.prototype.un=Tn.prototype.K,Tn.prototype.unByKey=Tn.prototype.O,Sb.prototype.getActive=Sb.prototype.f,Sb.prototype.getMap=Sb.prototype.c,Sb.prototype.setActive=Sb.prototype.Ea,Sb.prototype.getKeys=Sb.prototype.S,Sb.prototype.getProperties=Sb.prototype.R,Sb.prototype.setProperties=Sb.prototype.I,Sb.prototype.unset=Sb.prototype.T,Sb.prototype.changed=Sb.prototype.s,Sb.prototype.dispatchEvent=Sb.prototype.b,Sb.prototype.getRevision=Sb.prototype.M,Sb.prototype.on=Sb.prototype.J,Sb.prototype.once=Sb.prototype.N,Sb.prototype.un=Sb.prototype.K,Sb.prototype.unByKey=Sb.prototype.O,Kn.prototype.getGeometry=Kn.prototype.V,Kn.prototype.getActive=Kn.prototype.f,Kn.prototype.getMap=Kn.prototype.c,Kn.prototype.setActive=Kn.prototype.Ea,Kn.prototype.getKeys=Kn.prototype.S,Kn.prototype.getProperties=Kn.prototype.R,Kn.prototype.setProperties=Kn.prototype.I,Kn.prototype.unset=Kn.prototype.T,Kn.prototype.changed=Kn.prototype.s,Kn.prototype.dispatchEvent=Kn.prototype.b,Kn.prototype.getRevision=Kn.prototype.M,Kn.prototype.on=Kn.prototype.J,Kn.prototype.once=Kn.prototype.N,Kn.prototype.un=Kn.prototype.K,Kn.prototype.unByKey=Kn.prototype.O,Ob.prototype.getActive=Ob.prototype.f,Ob.prototype.getMap=Ob.prototype.c,Ob.prototype.setActive=Ob.prototype.Ea,Ob.prototype.getKeys=Ob.prototype.S,Ob.prototype.getProperties=Ob.prototype.R,Ob.prototype.setProperties=Ob.prototype.I,Ob.prototype.unset=Ob.prototype.T,Ob.prototype.changed=Ob.prototype.s,Ob.prototype.dispatchEvent=Ob.prototype.b,Ob.prototype.getRevision=Ob.prototype.M,Ob.prototype.on=Ob.prototype.J,Ob.prototype.once=Ob.prototype.N,Ob.prototype.un=Ob.prototype.K,Ob.prototype.unByKey=Ob.prototype.O,Qb.prototype.getActive=Qb.prototype.f,Qb.prototype.getMap=Qb.prototype.c,Qb.prototype.setActive=Qb.prototype.Ea,Qb.prototype.getKeys=Qb.prototype.S,Qb.prototype.getProperties=Qb.prototype.R,Qb.prototype.setProperties=Qb.prototype.I,Qb.prototype.unset=Qb.prototype.T,Qb.prototype.changed=Qb.prototype.s,Qb.prototype.dispatchEvent=Qb.prototype.b,Qb.prototype.getRevision=Qb.prototype.M,Qb.prototype.on=Qb.prototype.J,Qb.prototype.once=Qb.prototype.N,Qb.prototype.un=Qb.prototype.K,Qb.prototype.unByKey=Qb.prototype.O,Xn.prototype.getActive=Xn.prototype.f,Xn.prototype.getMap=Xn.prototype.c,Xn.prototype.setActive=Xn.prototype.Ea,Xn.prototype.getKeys=Xn.prototype.S,Xn.prototype.getProperties=Xn.prototype.R,Xn.prototype.setProperties=Xn.prototype.I,Xn.prototype.unset=Xn.prototype.T,Xn.prototype.changed=Xn.prototype.s,Xn.prototype.dispatchEvent=Xn.prototype.b,Xn.prototype.getRevision=Xn.prototype.M,Xn.prototype.on=Xn.prototype.J,Xn.prototype.once=Xn.prototype.N,Xn.prototype.un=Xn.prototype.K,Xn.prototype.unByKey=Xn.prototype.O,Wn.prototype.getActive=Wn.prototype.f,Wn.prototype.getMap=Wn.prototype.c,Wn.prototype.setActive=Wn.prototype.Ea,Wn.prototype.getKeys=Wn.prototype.S,Wn.prototype.getProperties=Wn.prototype.R,Wn.prototype.setProperties=Wn.prototype.I,Wn.prototype.unset=Wn.prototype.T,Wn.prototype.changed=Wn.prototype.s,Wn.prototype.dispatchEvent=Wn.prototype.b,Wn.prototype.getRevision=Wn.prototype.M,Wn.prototype.on=Wn.prototype.J,Wn.prototype.once=Wn.prototype.N,Wn.prototype.un=Wn.prototype.K,Wn.prototype.unByKey=Wn.prototype.O,lm.prototype.getActive=lm.prototype.f,lm.prototype.getMap=lm.prototype.c,lm.prototype.setActive=lm.prototype.Ea,lm.prototype.getKeys=lm.prototype.S,lm.prototype.getProperties=lm.prototype.R,lm.prototype.setProperties=lm.prototype.I,lm.prototype.unset=lm.prototype.T,lm.prototype.changed=lm.prototype.s,lm.prototype.dispatchEvent=lm.prototype.b,lm.prototype.getRevision=lm.prototype.M,lm.prototype.on=lm.prototype.J,lm.prototype.once=lm.prototype.N,lm.prototype.un=lm.prototype.K,lm.prototype.unByKey=lm.prototype.O,Hn.prototype.getActive=Hn.prototype.f,Hn.prototype.getMap=Hn.prototype.c,Hn.prototype.setActive=Hn.prototype.Ea,Hn.prototype.getKeys=Hn.prototype.S,Hn.prototype.getProperties=Hn.prototype.R,Hn.prototype.setProperties=Hn.prototype.I,Hn.prototype.unset=Hn.prototype.T,Hn.prototype.changed=Hn.prototype.s,Hn.prototype.dispatchEvent=Hn.prototype.b,Hn.prototype.getRevision=Hn.prototype.M,Hn.prototype.on=Hn.prototype.J,Hn.prototype.once=Hn.prototype.N,Hn.prototype.un=Hn.prototype.K,Hn.prototype.unByKey=Hn.prototype.O,Jn.prototype.getActive=Jn.prototype.f,Jn.prototype.getMap=Jn.prototype.c,Jn.prototype.setActive=Jn.prototype.Ea,Jn.prototype.getKeys=Jn.prototype.S,Jn.prototype.getProperties=Jn.prototype.R,Jn.prototype.setProperties=Jn.prototype.I,Jn.prototype.unset=Jn.prototype.T,Jn.prototype.changed=Jn.prototype.s,Jn.prototype.dispatchEvent=Jn.prototype.b,Jn.prototype.getRevision=Jn.prototype.M,Jn.prototype.on=Jn.prototype.J,Jn.prototype.once=Jn.prototype.N,Jn.prototype.un=Jn.prototype.K,Jn.prototype.unByKey=Jn.prototype.O,ts.prototype.getActive=ts.prototype.f,ts.prototype.getMap=ts.prototype.c,ts.prototype.setActive=ts.prototype.Ea,ts.prototype.getKeys=ts.prototype.S,ts.prototype.getProperties=ts.prototype.R,ts.prototype.setProperties=ts.prototype.I,ts.prototype.unset=ts.prototype.T,ts.prototype.changed=ts.prototype.s,ts.prototype.dispatchEvent=ts.prototype.b,ts.prototype.getRevision=ts.prototype.M,ts.prototype.on=ts.prototype.J,ts.prototype.once=ts.prototype.N,ts.prototype.un=ts.prototype.K,ts.prototype.unByKey=ts.prototype.O,Pm.prototype.getActive=Pm.prototype.f,Pm.prototype.getMap=Pm.prototype.c,Pm.prototype.setActive=Pm.prototype.Ea,Pm.prototype.getKeys=Pm.prototype.S,Pm.prototype.getProperties=Pm.prototype.R,Pm.prototype.setProperties=Pm.prototype.I,Pm.prototype.unset=Pm.prototype.T,Pm.prototype.changed=Pm.prototype.s,Pm.prototype.dispatchEvent=Pm.prototype.b,Pm.prototype.getRevision=Pm.prototype.M,Pm.prototype.on=Pm.prototype.J,Pm.prototype.once=Pm.prototype.N,Pm.prototype.un=Pm.prototype.K,Pm.prototype.unByKey=Pm.prototype.O,Cm.prototype.getActive=Cm.prototype.f,Cm.prototype.getMap=Cm.prototype.c,Cm.prototype.setActive=Cm.prototype.Ea,Cm.prototype.getKeys=Cm.prototype.S,Cm.prototype.getProperties=Cm.prototype.R,Cm.prototype.setProperties=Cm.prototype.I,Cm.prototype.unset=Cm.prototype.T,Cm.prototype.changed=Cm.prototype.s,Cm.prototype.dispatchEvent=Cm.prototype.b,Cm.prototype.getRevision=Cm.prototype.M,Cm.prototype.on=Cm.prototype.J,Cm.prototype.once=Cm.prototype.N,Cm.prototype.un=Cm.prototype.K,Cm.prototype.unByKey=Cm.prototype.O,Rm.prototype.getActive=Rm.prototype.f,Rm.prototype.getMap=Rm.prototype.c,Rm.prototype.setActive=Rm.prototype.Ea,Rm.prototype.getKeys=Rm.prototype.S,Rm.prototype.getProperties=Rm.prototype.R,Rm.prototype.setProperties=Rm.prototype.I,Rm.prototype.unset=Rm.prototype.T,Rm.prototype.changed=Rm.prototype.s,Rm.prototype.dispatchEvent=Rm.prototype.b,Rm.prototype.getRevision=Rm.prototype.M,Rm.prototype.on=Rm.prototype.J,Rm.prototype.once=Rm.prototype.N,Rm.prototype.un=Rm.prototype.K,Rm.prototype.unByKey=Rm.prototype.O,eo.prototype.getKeys=eo.prototype.S,eo.prototype.getProperties=eo.prototype.R,eo.prototype.setProperties=eo.prototype.I,eo.prototype.unset=eo.prototype.T,eo.prototype.changed=eo.prototype.s,eo.prototype.dispatchEvent=eo.prototype.b,eo.prototype.getRevision=eo.prototype.M,eo.prototype.on=eo.prototype.J,eo.prototype.once=eo.prototype.N,eo.prototype.un=eo.prototype.K,eo.prototype.unByKey=eo.prototype.O,b.prototype.getClosestPoint=b.prototype.Cb,b.prototype.intersectsCoordinate=b.prototype.mb,b.prototype.getExtent=b.prototype.G,b.prototype.simplify=b.prototype.Jb,b.prototype.transform=b.prototype.ob,b.prototype.getKeys=b.prototype.S,b.prototype.getProperties=b.prototype.R,b.prototype.setProperties=b.prototype.I,b.prototype.unset=b.prototype.T,b.prototype.changed=b.prototype.s,b.prototype.dispatchEvent=b.prototype.b,b.prototype.getRevision=b.prototype.M,b.prototype.on=b.prototype.J,b.prototype.once=b.prototype.N,b.prototype.un=b.prototype.K,b.prototype.unByKey=b.prototype.O,pb.prototype.getFirstCoordinate=pb.prototype.Rb,pb.prototype.getLastCoordinate=pb.prototype.Sb,pb.prototype.getLayout=pb.prototype.Tb,pb.prototype.getClosestPoint=pb.prototype.Cb,pb.prototype.intersectsCoordinate=pb.prototype.mb,pb.prototype.getExtent=pb.prototype.G,pb.prototype.simplify=pb.prototype.Jb,pb.prototype.getKeys=pb.prototype.S,pb.prototype.getProperties=pb.prototype.R,pb.prototype.setProperties=pb.prototype.I,pb.prototype.unset=pb.prototype.T,pb.prototype.changed=pb.prototype.s,pb.prototype.dispatchEvent=pb.prototype.b,pb.prototype.getRevision=pb.prototype.M,pb.prototype.on=pb.prototype.J,pb.prototype.once=pb.prototype.N,pb.prototype.un=pb.prototype.K,pb.prototype.unByKey=pb.prototype.O,ly.prototype.getClosestPoint=ly.prototype.Cb,ly.prototype.intersectsCoordinate=ly.prototype.mb,ly.prototype.getExtent=ly.prototype.G,ly.prototype.simplify=ly.prototype.Jb,ly.prototype.transform=ly.prototype.ob,ly.prototype.getKeys=ly.prototype.S,ly.prototype.getProperties=ly.prototype.R,ly.prototype.setProperties=ly.prototype.I,ly.prototype.unset=ly.prototype.T,ly.prototype.changed=ly.prototype.s,ly.prototype.dispatchEvent=ly.prototype.b,ly.prototype.getRevision=ly.prototype.M,ly.prototype.on=ly.prototype.J,ly.prototype.once=ly.prototype.N,ly.prototype.un=ly.prototype.K,ly.prototype.unByKey=ly.prototype.O,So.prototype.getFirstCoordinate=So.prototype.Rb,So.prototype.getLastCoordinate=So.prototype.Sb,So.prototype.getLayout=So.prototype.Tb,So.prototype.getClosestPoint=So.prototype.Cb,So.prototype.intersectsCoordinate=So.prototype.mb,So.prototype.getExtent=So.prototype.G,So.prototype.simplify=So.prototype.Jb,So.prototype.transform=So.prototype.ob,So.prototype.getKeys=So.prototype.S,So.prototype.getProperties=So.prototype.R,So.prototype.setProperties=So.prototype.I,So.prototype.unset=So.prototype.T,So.prototype.changed=So.prototype.s,So.prototype.dispatchEvent=So.prototype.b,So.prototype.getRevision=So.prototype.M,So.prototype.on=So.prototype.J,So.prototype.once=So.prototype.N,So.prototype.un=So.prototype.K,So.prototype.unByKey=So.prototype.O,S.prototype.getFirstCoordinate=S.prototype.Rb,S.prototype.getLastCoordinate=S.prototype.Sb,S.prototype.getLayout=S.prototype.Tb,S.prototype.getClosestPoint=S.prototype.Cb,S.prototype.intersectsCoordinate=S.prototype.mb,S.prototype.getExtent=S.prototype.G,S.prototype.simplify=S.prototype.Jb,S.prototype.transform=S.prototype.ob,S.prototype.getKeys=S.prototype.S,S.prototype.getProperties=S.prototype.R,S.prototype.setProperties=S.prototype.I,S.prototype.unset=S.prototype.T,S.prototype.changed=S.prototype.s,S.prototype.dispatchEvent=S.prototype.b,S.prototype.getRevision=S.prototype.M,S.prototype.on=S.prototype.J,S.prototype.once=S.prototype.N,S.prototype.un=S.prototype.K,S.prototype.unByKey=S.prototype.O,M.prototype.getFirstCoordinate=M.prototype.Rb,M.prototype.getLastCoordinate=M.prototype.Sb,M.prototype.getLayout=M.prototype.Tb,M.prototype.getClosestPoint=M.prototype.Cb,M.prototype.intersectsCoordinate=M.prototype.mb,M.prototype.getExtent=M.prototype.G,M.prototype.simplify=M.prototype.Jb,M.prototype.transform=M.prototype.ob,M.prototype.getKeys=M.prototype.S,M.prototype.getProperties=M.prototype.R,M.prototype.setProperties=M.prototype.I,M.prototype.unset=M.prototype.T,M.prototype.changed=M.prototype.s,M.prototype.dispatchEvent=M.prototype.b,M.prototype.getRevision=M.prototype.M,M.prototype.on=M.prototype.J,M.prototype.once=M.prototype.N,M.prototype.un=M.prototype.K,M.prototype.unByKey=M.prototype.O,P.prototype.getFirstCoordinate=P.prototype.Rb,P.prototype.getLastCoordinate=P.prototype.Sb,P.prototype.getLayout=P.prototype.Tb,P.prototype.getClosestPoint=P.prototype.Cb,P.prototype.intersectsCoordinate=P.prototype.mb,P.prototype.getExtent=P.prototype.G,P.prototype.simplify=P.prototype.Jb,P.prototype.transform=P.prototype.ob,P.prototype.getKeys=P.prototype.S,P.prototype.getProperties=P.prototype.R,P.prototype.setProperties=P.prototype.I,P.prototype.unset=P.prototype.T,P.prototype.changed=P.prototype.s,P.prototype.dispatchEvent=P.prototype.b,P.prototype.getRevision=P.prototype.M,P.prototype.on=P.prototype.J,P.prototype.once=P.prototype.N,P.prototype.un=P.prototype.K,P.prototype.unByKey=P.prototype.O,y.prototype.getFirstCoordinate=y.prototype.Rb,y.prototype.getLastCoordinate=y.prototype.Sb,y.prototype.getLayout=y.prototype.Tb,y.prototype.getClosestPoint=y.prototype.Cb,y.prototype.intersectsCoordinate=y.prototype.mb,y.prototype.getExtent=y.prototype.G,y.prototype.simplify=y.prototype.Jb,y.prototype.transform=y.prototype.ob,y.prototype.getKeys=y.prototype.S,y.prototype.getProperties=y.prototype.R,y.prototype.setProperties=y.prototype.I,y.prototype.unset=y.prototype.T,y.prototype.changed=y.prototype.s,y.prototype.dispatchEvent=y.prototype.b,y.prototype.getRevision=y.prototype.M,y.prototype.on=y.prototype.J,y.prototype.once=y.prototype.N,y.prototype.un=y.prototype.K,y.prototype.unByKey=y.prototype.O,m.prototype.getFirstCoordinate=m.prototype.Rb,m.prototype.getLastCoordinate=m.prototype.Sb,m.prototype.getLayout=m.prototype.Tb,m.prototype.getClosestPoint=m.prototype.Cb,m.prototype.intersectsCoordinate=m.prototype.mb,m.prototype.getExtent=m.prototype.G,m.prototype.simplify=m.prototype.Jb,m.prototype.transform=m.prototype.ob,m.prototype.getKeys=m.prototype.S,m.prototype.getProperties=m.prototype.R,m.prototype.setProperties=m.prototype.I,m.prototype.unset=m.prototype.T,m.prototype.changed=m.prototype.s,m.prototype.dispatchEvent=m.prototype.b,m.prototype.getRevision=m.prototype.M,m.prototype.on=m.prototype.J,m.prototype.once=m.prototype.N,m.prototype.un=m.prototype.K,m.prototype.unByKey=m.prototype.O,w.prototype.getFirstCoordinate=w.prototype.Rb,w.prototype.getLastCoordinate=w.prototype.Sb,w.prototype.getLayout=w.prototype.Tb,w.prototype.getClosestPoint=w.prototype.Cb,w.prototype.intersectsCoordinate=w.prototype.mb,w.prototype.getExtent=w.prototype.G,w.prototype.simplify=w.prototype.Jb,w.prototype.transform=w.prototype.ob,w.prototype.getKeys=w.prototype.S,w.prototype.getProperties=w.prototype.R,w.prototype.setProperties=w.prototype.I,w.prototype.unset=w.prototype.T,w.prototype.changed=w.prototype.s,w.prototype.dispatchEvent=w.prototype.b,w.prototype.getRevision=w.prototype.M,w.prototype.on=w.prototype.J,w.prototype.once=w.prototype.N,w.prototype.un=w.prototype.K,w.prototype.unByKey=w.prototype.O,f.prototype.readFeatures=f.prototype.La,Vy.prototype.readFeatures=Vy.prototype.La,f.prototype.readFeatures=f.prototype.La,ki.prototype.getKeys=ki.prototype.S,ki.prototype.getProperties=ki.prototype.R,ki.prototype.setProperties=ki.prototype.I,ki.prototype.unset=ki.prototype.T,ki.prototype.changed=ki.prototype.s,ki.prototype.dispatchEvent=ki.prototype.b,ki.prototype.getRevision=ki.prototype.M,ki.prototype.on=ki.prototype.J,ki.prototype.once=ki.prototype.N,ki.prototype.un=ki.prototype.K,ki.prototype.unByKey=ki.prototype.O,Oi.prototype.getMap=Oi.prototype.i,Oi.prototype.setTarget=Oi.prototype.c,Oi.prototype.getKeys=Oi.prototype.S,Oi.prototype.getProperties=Oi.prototype.R,Oi.prototype.setProperties=Oi.prototype.I,Oi.prototype.unset=Oi.prototype.T,Oi.prototype.changed=Oi.prototype.s,Oi.prototype.dispatchEvent=Oi.prototype.b,Oi.prototype.getRevision=Oi.prototype.M,Oi.prototype.on=Oi.prototype.J,Oi.prototype.once=Oi.prototype.N,Oi.prototype.un=Oi.prototype.K,Oi.prototype.unByKey=Oi.prototype.O,Ui.prototype.getMap=Ui.prototype.i,Ui.prototype.setTarget=Ui.prototype.c,Ui.prototype.getKeys=Ui.prototype.S,Ui.prototype.getProperties=Ui.prototype.R,Ui.prototype.setProperties=Ui.prototype.I,Ui.prototype.unset=Ui.prototype.T,Ui.prototype.changed=Ui.prototype.s,Ui.prototype.dispatchEvent=Ui.prototype.b,Ui.prototype.getRevision=Ui.prototype.M,Ui.prototype.on=Ui.prototype.J,Ui.prototype.once=Ui.prototype.N,Ui.prototype.un=Ui.prototype.K,Ui.prototype.unByKey=Ui.prototype.O,qi.prototype.getMap=qi.prototype.i,qi.prototype.setTarget=qi.prototype.c,qi.prototype.getKeys=qi.prototype.S,qi.prototype.getProperties=qi.prototype.R,qi.prototype.setProperties=qi.prototype.I,qi.prototype.unset=qi.prototype.T,qi.prototype.changed=qi.prototype.s,qi.prototype.dispatchEvent=qi.prototype.b,qi.prototype.getRevision=qi.prototype.M,qi.prototype.on=qi.prototype.J,qi.prototype.once=qi.prototype.N,qi.prototype.un=qi.prototype.K,qi.prototype.unByKey=qi.prototype.O,Eu.prototype.getMap=Eu.prototype.i,Eu.prototype.setTarget=Eu.prototype.c,Eu.prototype.getKeys=Eu.prototype.S,Eu.prototype.getProperties=Eu.prototype.R,Eu.prototype.setProperties=Eu.prototype.I,Eu.prototype.unset=Eu.prototype.T,Eu.prototype.changed=Eu.prototype.s,Eu.prototype.dispatchEvent=Eu.prototype.b,Eu.prototype.getRevision=Eu.prototype.M,Eu.prototype.on=Eu.prototype.J,Eu.prototype.once=Eu.prototype.N,Eu.prototype.un=Eu.prototype.K,Eu.prototype.unByKey=Eu.prototype.O,zi.prototype.getMap=zi.prototype.i,zi.prototype.setTarget=zi.prototype.c,zi.prototype.getKeys=zi.prototype.S,zi.prototype.getProperties=zi.prototype.R,zi.prototype.setProperties=zi.prototype.I,zi.prototype.unset=zi.prototype.T,zi.prototype.changed=zi.prototype.s,zi.prototype.dispatchEvent=zi.prototype.b,zi.prototype.getRevision=zi.prototype.M,zi.prototype.on=zi.prototype.J,zi.prototype.once=zi.prototype.N,zi.prototype.un=zi.prototype.K,zi.prototype.unByKey=zi.prototype.O,Nu.prototype.getMap=Nu.prototype.i,Nu.prototype.setTarget=Nu.prototype.c,Nu.prototype.getKeys=Nu.prototype.S,Nu.prototype.getProperties=Nu.prototype.R,Nu.prototype.setProperties=Nu.prototype.I,Nu.prototype.unset=Nu.prototype.T,Nu.prototype.changed=Nu.prototype.s,Nu.prototype.dispatchEvent=Nu.prototype.b,Nu.prototype.getRevision=Nu.prototype.M,Nu.prototype.on=Nu.prototype.J,Nu.prototype.once=Nu.prototype.N,Nu.prototype.un=Nu.prototype.K,Nu.prototype.unByKey=Nu.prototype.O,Yi.prototype.getMap=Yi.prototype.i,Yi.prototype.setMap=Yi.prototype.setMap,Yi.prototype.setTarget=Yi.prototype.c,Yi.prototype.get=Yi.prototype.get,Yi.prototype.getKeys=Yi.prototype.S,Yi.prototype.getProperties=Yi.prototype.R,Yi.prototype.set=Yi.prototype.set,Yi.prototype.setProperties=Yi.prototype.I,Yi.prototype.unset=Yi.prototype.T,Yi.prototype.changed=Yi.prototype.s,Yi.prototype.dispatchEvent=Yi.prototype.b,Yi.prototype.getRevision=Yi.prototype.M,Yi.prototype.on=Yi.prototype.J,Yi.prototype.once=Yi.prototype.N,Yi.prototype.un=Yi.prototype.K,Yi.prototype.unByKey=Yi.prototype.O,Xu.prototype.getMap=Xu.prototype.i,Xu.prototype.setTarget=Xu.prototype.c,Xu.prototype.getKeys=Xu.prototype.S,Xu.prototype.getProperties=Xu.prototype.R,Xu.prototype.setProperties=Xu.prototype.I,Xu.prototype.unset=Xu.prototype.T,Xu.prototype.changed=Xu.prototype.s,Xu.prototype.dispatchEvent=Xu.prototype.b,Xu.prototype.getRevision=Xu.prototype.M,Xu.prototype.on=Xu.prototype.J,Xu.prototype.once=Xu.prototype.N,Xu.prototype.un=Xu.prototype.K,Xu.prototype.unByKey=Xu.prototype.O,Yu.prototype.getMap=Yu.prototype.i,Yu.prototype.setTarget=Yu.prototype.c,Yu.prototype.getKeys=Yu.prototype.S,Yu.prototype.getProperties=Yu.prototype.R,Yu.prototype.setProperties=Yu.prototype.I,Yu.prototype.unset=Yu.prototype.T,Yu.prototype.changed=Yu.prototype.s,Yu.prototype.dispatchEvent=Yu.prototype.b,Yu.prototype.getRevision=Yu.prototype.M,Yu.prototype.on=Yu.prototype.J,Yu.prototype.once=Yu.prototype.N,Yu.prototype.un=Yu.prototype.K,Yu.prototype.unByKey=Yu.prototype.O,D.ol});