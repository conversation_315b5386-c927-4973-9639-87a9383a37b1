/*
 * My Account Page JavaScript
 * Handles user profile display with progressive enhancement
 */
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { getRoleDisplayName, formatNotAvailable } from '../../core/helpers/format-helpers.js';
import { formatLabRoleDisplay } from '../lab/shared/lab-helpers.js';
import { UserApi } from '../../core/services/user-api.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS
};

// Configure page authentication requirements
// This will be processed by authentication.js middleware
window.PAGE_AUTH_CONFIG = {
    onAuthSuccess: initializeMyAccountPage
};

// Initialize my account page
async function initializeMyAccountPage(userInfo) {
    try {
        await initializeAccountProfile(userInfo);
    } catch (error) {
        console.error('My account page initialization failed:', error);
        showApiErrorMessage();
    }
}

// Initialize account profile with progressive enhancement
async function initializeAccountProfile(userInfo) {
    try {
        // First render: Display JWT data immediately for better UX
        renderUserProfile(userInfo, 'jwt');

        // Second render: Update with authoritative API data
        const apiUserData = await UserApi.getMyProfile();
        renderUserProfile(apiUserData, 'api');

        return true;
    } catch (error) {
        console.error('Account page initialization failed:', error);
        showApiErrorMessage();
        return false;
    }
}

// Display API connection error message for account pages
// When server is down, this is a critical error that prevents full functionality
function showApiErrorMessage() {
    showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
}


// Render user profile information with progressive enhancement
function renderUserProfile(userData, dataSource) {
    const $profileContainer = $('#wb-cont');

    if ($profileContainer.length === 0) {
        console.error('Profile container not found');
        return;
    }

    // Clear any existing error messages
    $profileContainer.find('.alert-danger').remove();

    // Render basic profile information
    renderBasicProfile(userData);

    // Render roles information
    renderUserRoles(userData);

    console.log(`Profile rendered from ${dataSource} data`);
}

// Render basic profile information (name, email, etc.)
function renderBasicProfile(userData) {
    const $emailElement = $('#user-email');

    if ($emailElement.length) {
        $emailElement.text(userData.email || formatNotAvailable());
    }
}

// Render user roles information
function renderUserRoles(userData) {
    const $rolesContainer = $('#user-roles');

    if ($rolesContainer.length === 0) {
        return;
    }

    // Clear existing roles
    $rolesContainer.empty();

    // Use lab_roles instead of roles (API returns lab_roles)
    if (!userData.lab_roles || userData.lab_roles.length === 0) {
        const noRolesText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.common.noRolesAssigned }, 'message');
        $rolesContainer.html(noRolesText);
        return;
    }

    // Create roles list
    const $rolesList = $('<ul class="list-unstyled"></ul>');

    userData.lab_roles.forEach(roleData => {
        // Use lab helper to format role display text
        const roleText = formatLabRoleDisplay(roleData, getRoleDisplayName);
        const $roleItem = $(`<li class="role-item">${roleText}</li>`);
        $rolesList.append($roleItem);
    });

    $rolesContainer.append($rolesList);
}

