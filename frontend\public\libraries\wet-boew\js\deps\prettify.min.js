var DecorationsT,JobT,SourceSpansT,HACK_TO_FIX_JS_INCLUDE_PL,PR,prettyPrintOne,prettyPrint,IN_GLOBAL_SCOPE=!1;window.PR_SHOULD_USE_CONTINUATION=!0,function(){var A=window,e=["break,continue,do,else,for,if,return,while"],n=[[e,"auto,case,char,const,default,double,enum,extern,float,goto,inline,int,long,register,restrict,short,signed,sizeof,static,struct,switch,typedef,union,unsigned,void,volatile"],"catch,class,delete,false,import,new,operator,private,protected,public,this,throw,true,try,typeof"],t=[n,"alignas,alignof,align_union,asm,axiom,bool,concept,concept_map,const_cast,constexpr,decltype,delegate,dynamic_cast,explicit,export,friend,generic,late_check,mutable,namespace,noexcept,noreturn,nullptr,property,reinterpret_cast,static_assert,static_cast,template,typeid,typename,using,virtual,where"],r=[n,"abstract,assert,boolean,byte,extends,finally,final,implements,import,instanceof,interface,null,native,package,strictfp,super,synchronized,throws,transient"],s=[n,"abstract,add,alias,as,ascending,async,await,base,bool,by,byte,checked,decimal,delegate,descending,dynamic,event,finally,fixed,foreach,from,get,global,group,implicit,in,interface,internal,into,is,join,let,lock,null,object,out,override,orderby,params,partial,readonly,ref,remove,sbyte,sealed,select,set,stackalloc,string,select,uint,ulong,unchecked,unsafe,ushort,value,var,virtual,where,yield"],n=[n,"abstract,async,await,constructor,debugger,enum,eval,export,function,get,implements,instanceof,interface,let,null,set,undefined,var,with,yield,Infinity,NaN"],a="caller,delete,die,do,dump,elsif,eval,exit,foreach,for,goto,if,import,last,local,my,next,no,our,print,package,redo,require,sub,undef,unless,until,use,wantarray,while,BEGIN,END",l=[e,"and,as,assert,class,def,del,elif,except,exec,finally,from,global,import,in,is,lambda,nonlocal,not,or,pass,print,raise,try,with,yield,False,True,None"],o=[e,"alias,and,begin,case,class,def,defined,elsif,end,ensure,false,in,module,next,nil,not,or,redo,rescue,retry,self,super,then,true,undef,unless,until,when,yield,BEGIN,END"],e=[e,"case,done,elif,esac,eval,fi,function,in,local,set,then,until"],i=/^(DIR|FILE|array|vector|(de|priority_)?queue|(forward_)?list|stack|(const_)?(reverse_)?iterator|(unordered_)?(multi)?(set|map)|bitset|u?(int|float)\d*)\b/,u="str",c="com",d="typ",p="lit",f="pun",P="pln",E="src",g="atv";function h(e){for(var u=0,c=!1,n=!1,t=0,r=e.length;t<r;++t)if((l=e[t]).ignoreCase)n=!0;else if(/[a-z]/i.test(l.source.replace(/\\u[0-9a-f]{4}|\\x[0-9a-f]{2}|\\[^ux]/gi,""))){n=!(c=!0);break}var s={b:8,t:9,n:10,v:11,f:12,r:13};function d(e){var n=e.charCodeAt(0);return 92!==n?n:(n=e.charAt(1),s[n]||("0"<=n&&n<="7"?parseInt(e.substring(1),8):"u"===n||"x"===n?parseInt(e.substring(2),16):e.charCodeAt(1)))}function p(e){return e<32?(e<16?"\\x0":"\\x")+e.toString(16):"\\"===(e=String.fromCharCode(e))||"-"===e||"]"===e||"^"===e?"\\"+e:e}function a(e){for(var n=e.source.match(new RegExp("(?:\\[(?:[^\\x5C\\x5D]|\\\\[\\s\\S])*\\]|\\\\u[A-Fa-f0-9]{4}|\\\\x[A-Fa-f0-9]{2}|\\\\[0-9]+|\\\\[^ux0-9]|\\(\\?[:!=]|[\\(\\)\\^]|[^\\x5B\\x5C\\(\\)\\^]+)","g")),t=n.length,r=[],s=0,a=0;s<t;++s)"("===(o=n[s])?++a:"\\"===o.charAt(0)&&(l=+o.substring(1))&&(l<=a?r[l]=-1:n[s]=p(l));for(s=1;s<r.length;++s)-1===r[s]&&(r[s]=++u);for(var l,s=0,a=0;s<t;++s)"("===(o=n[s])?r[++a]||(n[s]="(?:"):"\\"===o.charAt(0)&&(l=+o.substring(1))&&l<=a&&(n[s]="\\"+r[l]);for(s=0;s<t;++s)"^"===n[s]&&"^"!==n[s+1]&&(n[s]="");if(e.ignoreCase&&c)for(s=0;s<t;++s){var o,i=(o=n[s]).charAt(0);2<=o.length&&"["===i?n[s]=function(e){var n=e.substring(1,e.length-1).match(new RegExp("\\\\u[0-9A-Fa-f]{4}|\\\\x[0-9A-Fa-f]{2}|\\\\[0-3][0-7]{0,2}|\\\\[0-7]{1,2}|\\\\[\\s\\S]|-|[^-\\\\]","g")),t=[],e="^"===n[0],r=["["];e&&r.push("^");for(var s=e?1:0,a=n.length;s<a;++s){var l,o=n[s];/\\[bdsw]/i.test(o)?r.push(o):(o=d(o),s+2<a&&"-"===n[s+1]?(l=d(n[s+2]),s+=2):l=o,t.push([o,l]),l<65||122<o||(l<65||90<o||t.push([32|Math.max(65,o),32|Math.min(l,90)]),l<97)||122<o||t.push([-33&Math.max(97,o),-33&Math.min(l,122)]))}t.sort(function(e,n){return e[0]-n[0]||n[1]-e[1]});for(var i=[],u=[],s=0;s<t.length;++s)(c=t[s])[0]<=u[1]+1?u[1]=Math.max(u[1],c[1]):i.push(u=c);for(s=0;s<i.length;++s){var c=i[s];r.push(p(c[0])),c[1]>c[0]&&(c[1]+1>c[0]&&r.push("-"),r.push(p(c[1])))}return r.push("]"),r.join("")}(o):"\\"!==i&&(n[s]=o.replace(/[a-zA-Z]/g,function(e){e=e.charCodeAt(0);return"["+String.fromCharCode(-33&e,32|e)+"]"}))}return n.join("")}for(var l,o=[],t=0,r=e.length;t<r;++t){if((l=e[t]).global||l.multiline)throw new Error(""+l);o.push("(?:"+a(l)+")")}return new RegExp(o.join("|"),n?"gi":"g")}function T(e,a){var l=/(?:^|\s)nocode(?:\s|$)/,o=[],i=0,u=[],c=0;return function e(n){var t=n.nodeType;if(1==t){if(!l.test(n.className)){for(var r=n.firstChild;r;r=r.nextSibling)e(r);var s=n.nodeName.toLowerCase();"br"!==s&&"li"!==s||(o[c]="\n",u[c<<1]=i++,u[c++<<1|1]=n)}}else 3!=t&&4!=t||(s=n.nodeValue).length&&(s=a?s.replace(/\r\n?/g,"\n"):s.replace(/[ \t\r\n]+/g," "),o[c]=s,u[c<<1]=i,i+=s.length,u[c++<<1|1]=n)}(e),{sourceCode:o.join("").replace(/\n$/,""),spans:u}}function L(e,n,t,r,s){t&&(r(r={sourceNode:e,pre:1,langExtension:null,numberLines:null,sourceCode:t,spans:null,basePos:n,decorations:null}),s.push.apply(s,r.decorations))}var k=/\S/;function m(e,w){for(var S,C={},n=e.concat(w),t=[],r={},s=0,a=n.length;s<a;++s){var l=n[s],o=l[3];if(o)for(var i=o.length;0<=--i;)C[o.charAt(i)]=l;var u=l[1],c=""+u;r.hasOwnProperty(c)||(t.push(u),r[c]=null)}t.push(/[\0-\uffff]/),S=h(t);function N(e){for(var n=e.sourceCode,t=e.basePos,r=e.sourceNode,s=[t,P],a=0,l=n.match(S)||[],o={},i=0,u=l.length;i<u;++i){var c,d=l[i],p=o[d],f=void 0;if("string"==typeof p)c=!1;else{var g=C[d.charAt(0)];if(g)f=d.match(g[1]),p=g[0];else{for(var h=0;h<_;++h)if(g=w[h],f=d.match(g[1])){p=g[0];break}f||(p=P)}!(c=5<=p.length&&"lang-"===p.substring(0,5))||f&&"string"==typeof f[1]||(c=!1,p=E),c||(o[d]=p)}var m,y,v,b,x=a;a+=d.length,c?(m=f[1],v=(y=d.indexOf(m))+m.length,f[2]&&(y=(v=d.length-f[2].length)-m.length),b=p.substring(5),L(r,t+x,d.substring(0,y),N,s),L(r,t+x+y,m,$(b,m),s),L(r,t+x+v,d.substring(v),N,s)):s.push(t+x,p)}e.decorations=s}var _=w.length;return N}function y(e){var n=[],t=[],r=(e.tripleQuotedStrings?n.push([u,/^(?:\'\'\'(?:[^\'\\]|\\[\s\S]|\'{1,2}(?=[^\']))*(?:\'\'\'|$)|\"\"\"(?:[^\"\\]|\\[\s\S]|\"{1,2}(?=[^\"]))*(?:\"\"\"|$)|\'(?:[^\\\']|\\[\s\S])*(?:\'|$)|\"(?:[^\\\"]|\\[\s\S])*(?:\"|$))/,null,"'\""]):e.multiLineStrings?n.push([u,/^(?:\'(?:[^\\\']|\\[\s\S])*(?:\'|$)|\"(?:[^\\\"]|\\[\s\S])*(?:\"|$)|\`(?:[^\\\`]|\\[\s\S])*(?:\`|$))/,null,"'\"`"]):n.push([u,/^(?:\'(?:[^\\\'\r\n]|\\.)*(?:\'|$)|\"(?:[^\\\"\r\n]|\\.)*(?:\"|$))/,null,"\"'"]),e.verbatimStrings&&t.push([u,/^@\"(?:[^\"]|\"\")*(?:\"|$)/,null]),e.hashComments),r=(r&&(e.cStyleComments?(n.push(1<r?[c,/^#(?:##(?:[^#]|#(?!##))*(?:###|$)|.*)/,null,"#"]:[c,/^#(?:(?:define|e(?:l|nd)if|else|error|ifn?def|include|line|pragma|undef|warning)\b|[^\r\n]*)/,null,"#"]),t.push([u,/^<(?:(?:(?:\.\.\/)*|\/?)(?:[\w-]+(?:\/[\w-]+)+)?[\w-]+\.h(?:h|pp|\+\+)?|[a-z]\w*)>/,null])):n.push([c,/^#[^\r\n]*/,null,"#"])),e.cStyleComments&&(t.push([c,/^\/\/[^\r\n]*/,null]),t.push([c,/^\/\*[\s\S]*?(?:\*\/|$)/,null])),e.regexLiterals),r=(r&&(s=(r=1<r?"":"\n\r")?".":"[\\S\\s]",t.push(["lang-regex",RegExp("^(?:^^\\.?|[+-]|[!=]=?=?|\\#|%=?|&&?=?|\\(|\\*=?|[+\\-]=|->|\\/=?|::?|<<?=?|>>?>?=?|,|;|\\?|@|\\[|~|{|\\^\\^?=?|\\|\\|?=?|break|case|continue|delete|do|else|finally|instanceof|return|throw|try|typeof)\\s*("+("/(?=[^/*"+r+"])(?:[^/\\x5B\\x5C"+r+"]|\\x5C"+s+"|\\x5B(?:[^\\x5C\\x5D"+r+"]|\\x5C"+s+")*(?:\\x5D|$))+/")+")")])),e.types),s=(r&&t.push([d,r]),(""+e.keywords).replace(/^ | $/g,"")),r=(s.length&&t.push(["kwd",new RegExp("^(?:"+s.replace(/[\s,]+/g,"|")+")\\b"),null]),n.push([P,/^\s+/,null," \r\n\t "]),"^.[^\\s\\w.$@'\"`/\\\\]*");return e.regexLiterals&&(r+="(?!s*/)"),t.push([p,/^@[a-z_$][a-z_$@0-9]*/i,null],[d,/^(?:[@_]?[A-Z]+[a-z][A-Za-z_$@0-9]*|\w+_t\b)/,null],[P,/^[a-z_$][a-z_$@0-9]*/i,null],[p,new RegExp("^(?:0x[a-f0-9]+|(?:\\d(?:_\\d+)*\\d*(?:\\.\\d*)?|\\.\\d\\+)(?:e[+\\-]?\\d+)?)[a-z]*","i"),null,"0123456789"],[P,/^\\[\s\S]?/,null],[f,new RegExp(r),null]),m(n,t)}var v=y({keywords:[t,s,r,n,a,l,o,e],hashComments:!0,cStyleComments:!0,multiLineStrings:!0,regexLiterals:!0});function R(e,n,l){for(var o=/(?:^|\s)nocode(?:\s|$)/,i=/\r\n?|\n/,u=e.ownerDocument,t=u.createElement("li");e.firstChild;)t.appendChild(e.firstChild);var r=[t];function c(e){for(;!e.nextSibling;)if(!(e=e.parentNode))return;for(var n,t=function e(n,t){var t=t?n.cloneNode(!1):n,r=n.parentNode;if(r){var s=e(r,1),a=n.nextSibling;s.appendChild(t);for(var l=a;l;l=a)a=l.nextSibling,s.appendChild(l)}return t}(e.nextSibling,0);(n=t.parentNode)&&1===n.nodeType;)t=n;r.push(t)}for(var s=0;s<r.length;++s)!function e(n){var t,r,s=n.nodeType;if(1!=s||o.test(n.className))3!=s&&4!=s||!l||(t=(s=n.nodeValue).match(i))&&(r=s.substring(0,t.index),n.nodeValue=r,(s=s.substring(t.index+t[0].length))&&n.parentNode.insertBefore(u.createTextNode(s),n.nextSibling),c(n),r||n.parentNode.removeChild(n));else if("br"===n.nodeName)c(n),n.parentNode&&n.parentNode.removeChild(n);else for(var a=n.firstChild;a;a=a.nextSibling)e(a)}(r[s]);n===(0|n)&&r[0].setAttribute("value",n);for(var a=u.createElement("ol"),d=(a.className="linenums",Math.max(0,n-1|0)||0),s=0,p=r.length;s<p;++s)(t=r[s]).className="L"+(s+d)%10,t.firstChild||t.appendChild(u.createTextNode(" ")),a.appendChild(t);e.appendChild(a)}var b={};function x(e,n){for(var t=n.length;0<=--t;){var r=n[t];b.hasOwnProperty(r)?A.console&&console.warn("cannot override language handler %s",r):b[r]=e}}function $(e,n){return e&&b.hasOwnProperty(e)||(e=/^\s*</.test(n)?"default-markup":"default-code"),b[e]}function O(e){var n=e.langExtension;try{var t,r,s=T(e.sourceNode,e.pre),a=s.sourceCode,l=(e.sourceCode=a,e.spans=s.spans,e.basePos=0,$(n,a)(e),e),o=(o=/\bMSIE\s(\d+)/.exec(navigator.userAgent))&&+o[1]<=8,i=/\n/g,u=l.sourceCode,c=u.length,d=0,p=l.spans,f=p.length,g=0,h=l.decorations,m=0;for(h[x=h.length]=c,r=t=0;r<x;)h[r]!==h[r+2]?(h[t++]=h[r++],h[t++]=h[r++]):r+=2;for(x=t,r=t=0;r<x;){for(var y=h[r],v=h[r+1],b=r+2;b+2<=x&&h[b+1]===v;)b+=2;h[t++]=y,h[t++]=v,r=b}var x=h.length=t,l=l.sourceNode,w="";l&&(w=l.style.display,l.style.display="none");try{for(;g<f;){p[g];var S,C,N,_,P=p[g+2]||c,E=h[m+2]||c,b=Math.min(P,E),L=p[g+1];1!==L.nodeType&&(S=u.substring(d,b))&&(o&&(S=S.replace(i,"\r")),L.nodeValue=S,(N=(C=L.ownerDocument).createElement("span")).className=h[m+1],(_=L.parentNode).replaceChild(N,L),N.appendChild(L),d<P)&&(p[g+1]=L=C.createTextNode(u.substring(b,P)),_.insertBefore(L,N.nextSibling)),P<=(d=b)&&(g+=2),E<=d&&(m+=2)}}finally{l&&(l.style.display=w)}}catch(e){A.console&&console.log(e&&e.stack||e)}}function w(e,n,t){var t=t||!1,n=n||null,r=document.createElement("div");return r.innerHTML="<pre>"+e+"</pre>",r=r.firstChild,t&&R(r,t,!0),O({langExtension:n,numberLines:t,sourceNode:r,pre:1,sourceCode:null,basePos:null,spans:null,decorations:null}),r.innerHTML}function S(h,e){var n=e||document.body,m=n.ownerDocument||document;function t(e){return n.getElementsByTagName(e)}for(var r=[t("pre"),t("code"),t("xmp")],y=[],s=0;s<r.length;++s)for(var a=0,l=r[s].length;a<l;++a)y.push(r[s][a]);var r=null,v=Date,b=(v.now||(v={now:function(){return+new Date}}),0),x=/\blang(?:uage)?-([\w.]+)(?!\S)/,w=/\bprettyprint\b/,S=/\bprettyprinted\b/,C=/pre|xmp/i,N=/^code$/i,_=/^(?:pre|code|xmp)$/i,P={};!function e(){for(var n=A.PR_SHOULD_USE_CONTINUATION?v.now()+250:1/0;b<y.length&&v.now()<n;b++){for(var t=y[b],r=P,s=t;s=s.previousSibling;){var a=s.nodeType,l=(7===a||8===a)&&s.nodeValue;if(l?!/^\??prettify\b/.test(l):3!==a||/\S/.test(s.nodeValue))break;if(l){r={},l.replace(/\b(\w+)=([\w:.%+-]+)/g,function(e,n,t){r[n]=t});break}}var o=t.className;if((r!==P||w.test(o))&&!S.test(o)){for(var i,u,c,d,p=!1,f=t.parentNode;f;f=f.parentNode){var g=f.tagName;if(_.test(g)&&f.className&&w.test(f.className)){p=!0;break}}p||(t.className+=" prettyprinted",i=(i=r.lang)||(i=!(i=o.match(x))&&(u=function(e){for(var n=void 0,t=e.firstChild;t;t=t.nextSibling)var r=t.nodeType,n=1===r?n?e:t:3===r&&k.test(t.nodeValue)?e:n;return n===e?void 0:n}(t))&&N.test(u.tagName)?u.className.match(x):i)&&i[1],c=C.test(t.tagName)?1:(u=t.currentStyle,c=m.defaultView,(c=u?u.whiteSpace:c&&c.getComputedStyle?c.getComputedStyle(t,null).getPropertyValue("white-space"):0)&&"pre"===c.substring(0,3)),(d=(d="true"===(d=r.linenums)||+d)||!!(d=o.match(/\blinenums\b(?::(\d+))?/))&&(!d[1]||!d[1].length||+d[1]))&&R(t,d,c),O({langExtension:i,sourceNode:t,numberLines:d,pre:c,sourceCode:null,basePos:null,spans:null,decorations:null}))}}b<y.length?A.setTimeout(e,250):"function"==typeof h&&h()}()}x(v,["default-code"]),x(m([],[[P,/^[^<?]+/],["dec",/^<!\w[^>]*(?:>|$)/],[c,/^<\!--[\s\S]*?(?:-\->|$)/],["lang-",/^<\?([\s\S]+?)(?:\?>|$)/],["lang-",/^<%([\s\S]+?)(?:%>|$)/],[f,/^(?:<[%?]|[%?]>)/],["lang-",/^<xmp\b[^>]*>([\s\S]+?)<\/xmp\b[^>]*>/i],["lang-js",/^<script\b[^>]*>([\s\S]*?)(<\/script\b[^>]*>)/i],["lang-css",/^<style\b[^>]*>([\s\S]*?)(<\/style\b[^>]*>)/i],["lang-in.tag",/^(<\/?[a-z][^<>]*>)/i]]),["default-markup","htm","html","mxml","xhtml","xml","xsl"]),x(m([[P,/^[\s]+/,null," \t\r\n"],[g,/^(?:\"[^\"]*\"?|\'[^\']*\'?)/,null,"\"'"]],[["tag",/^^<\/?[a-z](?:[\w.:-]*\w)?|\/?>$/i],["atn",/^(?!style[\s=]|on)[a-z](?:[\w:-]*\w)?/i],["lang-uq.val",/^=\s*([^>\'\"\s]*(?:[^>\'\"\s\/]|\/(?=\s)))/],[f,/^[=<>\/]+/],["lang-js",/^on\w+\s*=\s*\"([^\"]+)\"/i],["lang-js",/^on\w+\s*=\s*\'([^\']+)\'/i],["lang-js",/^on\w+\s*=\s*([^\"\'>\s]+)/i],["lang-css",/^style\s*=\s*\"([^\"]+)\"/i],["lang-css",/^style\s*=\s*\'([^\']+)\'/i],["lang-css",/^style\s*=\s*([^\"\'>\s]+)/i]]),["in.tag"]),x(m([],[[g,/^[\s\S]+/]]),["uq.val"]),x(y({keywords:t,hashComments:!0,cStyleComments:!0,types:i}),["c","cc","cpp","cxx","cyc","m"]),x(y({keywords:"null,true,false"}),["json"]),x(y({keywords:s,hashComments:!0,cStyleComments:!0,verbatimStrings:!0,types:i}),["cs"]),x(y({keywords:r,cStyleComments:!0}),["java"]),x(y({keywords:e,hashComments:!0,multiLineStrings:!0}),["bash","bsh","csh","sh"]),x(y({keywords:l,hashComments:!0,multiLineStrings:!0,tripleQuotedStrings:!0}),["cv","py","python"]),x(y({keywords:a,hashComments:!0,multiLineStrings:!0,regexLiterals:2}),["perl","pl","pm"]),x(y({keywords:o,hashComments:!0,multiLineStrings:!0,regexLiterals:!0}),["rb","ruby"]),x(y({keywords:n,cStyleComments:!0,regexLiterals:!0}),["javascript","js","ts","typescript"]),x(y({keywords:"all,and,by,catch,class,else,extends,false,finally,for,if,in,is,isnt,loop,new,no,not,null,of,off,on,or,return,super,then,throw,true,try,unless,until,when,while,yes",hashComments:3,cStyleComments:!0,multilineStrings:!0,tripleQuotedStrings:!0,regexLiterals:!0}),["coffee"]),x(m([],[[u,/^[\s\S]+/]]),["regex"]);var C=A.PR={createSimpleLexer:m,registerLangHandler:x,sourceDecorator:y,PR_ATTRIB_NAME:"atn",PR_ATTRIB_VALUE:g,PR_COMMENT:c,PR_DECLARATION:"dec",PR_KEYWORD:"kwd",PR_LITERAL:p,PR_NOCODE:"nocode",PR_PLAIN:P,PR_PUNCTUATION:f,PR_SOURCE:E,PR_STRING:u,PR_TAG:"tag",PR_TYPE:d,prettyPrintOne:IN_GLOBAL_SCOPE?A.prettyPrintOne=w:prettyPrintOne=w,prettyPrint:prettyPrint=IN_GLOBAL_SCOPE?A.prettyPrint=S:S},v=A.define;"function"==typeof v&&v.amd&&v("google-code-prettify",[],function(){return C})}();