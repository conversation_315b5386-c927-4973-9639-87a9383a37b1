"""
Unit tests for authentication helper functions.

This file contains unit tests for the functions in app/utils/auth_helpers.py:

- _validate_azure_token:
    - test_validate_azure_token_success
    - test_validate_azure_token_auth_fails
    - test_validate_azure_token_missing_oid
    - test_validate_azure_token_missing_email
- _generate_user_token:
    - test_generate_user_token_single_role
    - test_generate_user_token_multiple_roles
    - test_generate_user_token_no_roles
- _handle_existing_user:
    - test_handle_existing_user_success
    - test_handle_existing_user_email_conflict
    - test_handle_existing_user_update_fails
- _handle_user_linking:
    - test_handle_user_linking_success
    - test_handle_user_linking_already_linked
    - test_handle_user_linking_not_found
- _create_new_user:
    - test_create_new_user_success
    - test_create_new_user_creation_fails
    - test_create_new_user_race_condition

It uses mocking to isolate the functions from database and external dependencies.
"""
import uuid
import pytest
from unittest.mock import patch, MagicMock
from fastapi import HTTPException
from sqlalchemy.exc import IntegrityError

from app.utils.auth_helpers import (
    _validate_azure_token,
    _handle_existing_user,
    _handle_user_linking,
    _create_new_user,
    _generate_user_token
)
from app.models.user import User as UserModel
from app.common.exceptions import ValidationError, NotFoundError
from app.common.constants import ERROR_MESSAGES, UserRole

@pytest.fixture
def mock_db_session():
    """Fixture for a mocked database session."""
    db_session = MagicMock()
    db_session.__enter__.return_value = db_session
    db_session.__exit__.return_value = None
    return db_session

class TestAuthHelpers:
    """Test suite for authentication helper functions."""

    #region _validate_azure_token tests
    @patch('app.utils.auth_helpers.authenticate_msal_user')
    def test_validate_azure_token_success(self, mock_auth_msal, mock_db_session):
        """Test _validate_azure_token with a valid token."""
        mock_claims = {'oid': 'test_oid', 'preferred_username': '<EMAIL>'}
        mock_auth_msal.return_value = (MagicMock(), mock_claims)

        token_claims, azure_oid, email = _validate_azure_token(mock_db_session, 'valid_token')

        assert token_claims == mock_claims
        assert azure_oid == 'test_oid'
        assert email == '<EMAIL>'

    @patch('app.utils.auth_helpers.authenticate_msal_user')
    def test_validate_azure_token_auth_fails(self, mock_auth_msal, mock_db_session):
        """Test _validate_azure_token when MSAL authentication fails."""
        mock_auth_msal.return_value = (None, None)

        with pytest.raises(HTTPException) as exc_info:
            _validate_azure_token(mock_db_session, 'invalid_token')
        
        assert exc_info.value.status_code == 401
        assert exc_info.value.detail == ERROR_MESSAGES["auth"]["credentials_invalid"]

    @patch('app.utils.auth_helpers.authenticate_msal_user')
    def test_validate_azure_token_missing_oid(self, mock_auth_msal, mock_db_session):
        """Test _validate_azure_token with a token missing the 'oid' claim."""
        mock_claims = {'preferred_username': '<EMAIL>'}
        mock_auth_msal.return_value = (MagicMock(), mock_claims)

        with pytest.raises(HTTPException) as exc_info:
            _validate_azure_token(mock_db_session, 'token_no_oid')

        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == ERROR_MESSAGES["auth"]["credentials_invalid"]

    @patch('app.utils.auth_helpers.authenticate_msal_user')
    def test_validate_azure_token_missing_email(self, mock_auth_msal, mock_db_session):
        """Test _validate_azure_token with a token missing the email claim."""
        mock_claims = {'oid': 'test_oid'}
        mock_auth_msal.return_value = (MagicMock(), mock_claims)

        with pytest.raises(HTTPException) as exc_info:
            _validate_azure_token(mock_db_session, 'token_no_email')

        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == ERROR_MESSAGES["auth"]["credentials_invalid"]
    #endregion

    #region _generate_user_token tests
    @patch('app.utils.auth_helpers.get_staff_with_lab_names_by_user_id')
    @patch('app.utils.auth_helpers.create_access_token')
    def test_generate_user_token_single_role(self, mock_create_token, mock_get_staff, mock_db_session):
        """Test token generation for a user with a single role."""
        mock_user = UserModel(user_id='user1', email='<EMAIL>', azure_ad_id='azure1')
        mock_staff = MagicMock(lab_id='lab1', role=UserRole.LAB_ADMIN)
        mock_get_staff.return_value = [(mock_staff, 'Test Lab')]
        mock_create_token.return_value = 'fake_token'

        token = _generate_user_token(mock_db_session, mock_user)

        assert token == 'fake_token'
        mock_create_token.assert_called_once()
        token_data = mock_create_token.call_args[1]['data']
        assert token_data['role'] == UserRole.LAB_ADMIN
        assert token_data['lab'] == 'lab1'

    @patch('app.utils.auth_helpers.get_staff_with_lab_names_by_user_id')
    @patch('app.utils.auth_helpers.create_access_token')
    def test_generate_user_token_multiple_roles(self, mock_create_token, mock_get_staff, mock_db_session):
        """Test token generation for a user with multiple roles."""
        mock_user = UserModel(user_id='user1', email='<EMAIL>', azure_ad_id='azure1')
        mock_staff1 = MagicMock(lab_id='lab1', role=UserRole.LAB_ADMIN)
        mock_staff2 = MagicMock(lab_id='lab2', role=UserRole.LAB_PERSONNEL)
        mock_get_staff.return_value = [(mock_staff1, 'Test Lab 1'), (mock_staff2, 'Test Lab 2')]
        mock_create_token.return_value = 'fake_token'

        token = _generate_user_token(mock_db_session, mock_user)

        assert token == 'fake_token'
        mock_create_token.assert_called_once()
        token_data = mock_create_token.call_args[1]['data']
        assert token_data['role'] is None
        assert token_data['lab'] is None
        assert len(token_data['lab_roles']) == 2

    @patch('app.utils.auth_helpers.get_staff_with_lab_names_by_user_id')
    def test_generate_user_token_no_roles(self, mock_get_staff, mock_db_session):
        """Test token generation for a user with no valid roles."""
        mock_user = UserModel(user_id='user1', email='<EMAIL>')
        mock_get_staff.return_value = []

        with pytest.raises(HTTPException) as exc_info:
            _generate_user_token(mock_db_session, mock_user)
        
        assert exc_info.value.status_code == 403
        assert exc_info.value.detail == ERROR_MESSAGES["auth"]["no_valid_roles"]
    #endregion

    #region _handle_existing_user tests
    def test_handle_existing_user_success(self, mock_db_session):
        """Test _handle_existing_user successfully updates a user."""
        mock_user = UserModel(user_id='user1', email='<EMAIL>', azure_ad_id='azure1')
        
        updated_user = _handle_existing_user(mock_db_session, mock_user, '<EMAIL>')

        assert updated_user.email == '<EMAIL>'
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once_with(mock_user)

    @patch('app.utils.auth_helpers.crud_get_user_by_email')
    def test_handle_existing_user_email_conflict(self, mock_get_user, mock_db_session):
        """Test _handle_existing_user with an email conflict."""
        mock_user = UserModel(user_id='user1', email='<EMAIL>', azure_ad_id='azure1')
        conflicting_user = UserModel(user_id='user2', email='<EMAIL>')
        mock_db_session.commit.side_effect = IntegrityError(None, None, None)
        mock_get_user.return_value = conflicting_user

        with pytest.raises(ValidationError) as exc_info:
            _handle_existing_user(mock_db_session, mock_user, '<EMAIL>')

        assert exc_info.value.args[0] == ERROR_MESSAGES["user"]["email_exists"]
        mock_db_session.rollback.assert_called_once()

    def test_handle_existing_user_update_fails(self, mock_db_session):
        """Test _handle_existing_user with a generic update failure."""
        mock_user = UserModel(user_id='user1', email='<EMAIL>', azure_ad_id='azure1')
        mock_db_session.commit.side_effect = Exception("DB error")

        with pytest.raises(ValidationError) as exc_info:
            _handle_existing_user(mock_db_session, mock_user, '<EMAIL>')

        assert exc_info.value.args[0] == ERROR_MESSAGES["user"]["error_updating"]
        mock_db_session.rollback.assert_called_once()
    #endregion

    #region _handle_user_linking tests
    @patch('app.utils.auth_helpers.crud_get_user_by_email')
    def test_handle_user_linking_success(self, mock_get_user, mock_db_session):
        """Test successfully linking a local user to an Azure AD account."""
        local_user = UserModel(user_id='user1', email='<EMAIL>', azure_ad_id=None)
        mock_get_user.return_value = local_user

        linked_user = _handle_user_linking(mock_db_session, 'new_azure_id', '<EMAIL>')

        assert linked_user.azure_ad_id == 'new_azure_id'
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once_with(local_user)

    @patch('app.utils.auth_helpers.crud_get_user_by_email')
    def test_handle_user_linking_already_linked(self, mock_get_user, mock_db_session):
        """Test linking when the email is already linked to another Azure AD account."""
        local_user = UserModel(user_id='user1', email='<EMAIL>', azure_ad_id='existing_azure_id')
        mock_get_user.return_value = local_user

        with pytest.raises(ValidationError) as exc_info:
            _handle_user_linking(mock_db_session, 'new_azure_id', '<EMAIL>')

        assert exc_info.value.args[0] == ERROR_MESSAGES["user"]["email_exists"]

    @patch('app.utils.auth_helpers.crud_get_user_by_email')
    def test_handle_user_linking_not_found(self, mock_get_user, mock_db_session):
        """Test linking when no local user is found with the email."""
        mock_get_user.side_effect = NotFoundError()

        result = _handle_user_linking(mock_db_session, 'new_azure_id', '<EMAIL>')

        assert result is None
    #endregion

    #region _create_new_user tests
    @patch('app.utils.auth_helpers.create_user_jit')
    @patch('app.utils.auth_helpers.create_staff')
    def test_create_new_user_success(self, mock_create_staff, mock_create_user, mock_db_session):
        """Test successfully creating a new user."""
        user_id = uuid.uuid4()
        new_user_obj = UserModel(user_id=user_id, email='<EMAIL>', azure_ad_id='azure1')
        mock_create_user.return_value = new_user_obj

        created_user = _create_new_user(mock_db_session, 'azure1', '<EMAIL>')

        assert created_user == new_user_obj
        mock_create_user.assert_called_once()
        mock_create_staff.assert_called_once()

    @patch('app.utils.auth_helpers.create_user_jit')
    def test_create_new_user_creation_fails(self, mock_create_user, mock_db_session):
        """Test user creation failure."""
        mock_create_user.side_effect = IntegrityError(None, None, None)
        
        # Mock the subsequent lookup to raise NotFoundError
        with patch('app.utils.auth_helpers.crud_get_user_by_email', side_effect=NotFoundError()):
            with pytest.raises(ValidationError) as exc_info:
                _create_new_user(mock_db_session, 'azure1', '<EMAIL>')

        assert exc_info.value.args[0] == ERROR_MESSAGES["user"]["error_creating"]
        mock_db_session.rollback.assert_called_once()

    @patch('app.utils.auth_helpers.create_user_jit')
    @patch('app.utils.auth_helpers.crud_get_user_by_email')
    def test_create_new_user_race_condition(self, mock_get_user, mock_create_user, mock_db_session):
        """Test user creation race condition where user is created by a concurrent request."""
        mock_create_user.side_effect = IntegrityError(None, None, None)
        concurrent_user = UserModel(user_id=uuid.uuid4(), email='<EMAIL>', azure_ad_id='azure1')
        mock_get_user.return_value = concurrent_user

        user = _create_new_user(mock_db_session, 'azure1', '<EMAIL>')

        assert user == concurrent_user
        mock_db_session.rollback.assert_called_once()
        mock_get_user.assert_called_once_with(mock_db_session, '<EMAIL>')
    #endregion
