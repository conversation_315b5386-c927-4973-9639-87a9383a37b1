#!/usr/bin/env python3
import os
import argparse

def print_tree(start_path, prefix=''):
    entries = sorted(os.listdir(start_path), key=lambda x: (not os.path.isdir(os.path.join(start_path, x)), x.lower()))
    entries_count = len(entries)
    
    for idx, entry in enumerate(entries):
        full_path = os.path.join(start_path, entry)
        connector = '└── ' if idx == entries_count - 1 else '├── '
        print(prefix + connector + entry + ('/' if os.path.isdir(full_path) else ''))
        if os.path.isdir(full_path):
            extension = '    ' if idx == entries_count - 1 else '│   '
            print_tree(full_path, prefix + extension)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Print folder structure (with files)")
    parser.add_argument("path", help="Path to target directory")
    args = parser.parse_args()

    if not os.path.exists(args.path):
        print(f"Path does not exist: {args.path}")
    else:
        print(f"Directory structure for: {args.path}\n")
        print_tree(os.path.abspath(args.path))
