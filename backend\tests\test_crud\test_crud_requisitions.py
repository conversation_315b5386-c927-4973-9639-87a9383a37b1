"""
Test suite for CRUD operations on Requisition objects.

This class contains tests for the following functions:
- create_requisition:
    - test_create_requisition_success
    - test_create_requisition_db_error
    - test_create_requisition_invalid_data
- get_requisitions:
    - test_get_requisitions_scientist
    - test_get_requisitions_admin
    - test_get_requisitions_admin_with_no_id
    - test_get_requisitions_with_status
    - test_get_requisitions_with_multiple_statuses
    - test_get_requisitions_returns_detailed_response
- get_requisition:
    - test_get_requisition_by_id
    - test_get_nonexistent_requisition
    - test_get_requisition_by_id_no_lab_id
    - test_get_requisition_by_id_wrong_lab_id
    - test_get_requisition_by_id_admin
- update_requisition:
    - test_update_requisition_success
    - test_update_nonexistent_requisition
    - test_archive_requisition
    - test_update_wrong_lab
- list_requisitions_for_lab:
    - test_list_requisitions_for_lab_success
    - test_list_requisitions_for_lab_with_status_filter
    - test_list_requisitions_for_lab_multiple_statuses
    - test_list_requisitions_for_lab_empty_result
    - test_list_requisitions_for_lab_access_control
- add_samples_to_requisition:
    - test_add_samples_to_requisition
    - test_add_samples_to_nonexistent_requisition
    - test_add_nonexistent_samples_to_requisition
    - test_add_duplicate_samples_to_requisition
- get_requisition_samples:
    - test_get_requisition_samples
    - test_get_samples_from_nonexistent_requisition
- remove_sample_from_requisition:
    - test_remove_sample_from_requisition
    - test_remove_nonexistent_sample_from_requisition
    - test_remove_sample_from_nonexistent_requisition
- add_tests_to_requisition_sample:
    - test_add_tests_lab_admin_success
    - test_add_tests_lab_personnel_success
    - test_add_tests_scientist_denied
    - test_add_tests_no_test_types
    - test_add_tests_nonexistent_requisition
    - test_add_tests_nonexistent_sample
- get_requisition_sample_tests:
    - test_get_requisition_sample_tests_success
    - test_get_requisition_sample_tests_nonexistent_requisition
    - test_get_requisition_sample_tests_nonexistent_sample
    - test_get_requisition_sample_tests_no_tests
- update_requisition_sample_test:
    - test_update_test_status_lab_admin_success
    - test_update_test_status_lab_personnel_success
    - test_update_test_status_scientist_denied
    - test_update_test_status_wrong_lab
    - test_update_test_status_nonexistent_test
    - test_update_test_status_completed
- remove_test_from_requisition_sample:
    - test_remove_test_from_sample_lab_admin_success
    - test_remove_test_from_sample_lab_personnel_success
    - test_remove_test_from_sample_scientist_denied
    - test_remove_nonexistent_test_from_sample
    - test_remove_tests_from_sample_wrong_lab
    - test_remove_test_from_nonexistent_requisition
    - test_remove_test_from_nonexistent_sample
"""

from datetime import timezone
import pytest
from unittest.mock import patch, MagicMock
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from app.crud.requisition import (
    get_requisition_sample_tests,
    get_requisitions,
    get_requisition,
    create_requisition,
    remove_test_from_requisition_sample,
    update_requisition,
    add_samples_to_requisition,
    get_requisition_samples,
    remove_sample_from_requisition,
    add_tests_to_requisition_sample,
    update_requisition_sample_test,
    list_requisitions_for_lab
)
from app.schemas.requisition import RequisitionCreate, RequisitionSampleTestUpdate, RequisitionUpdate
from app.common.constants import SampleTestStatus, UserRole, RequisitionStatus, ERROR_MESSAGES
from app.common.exceptions import NotFoundError, ValidationError, StateError, AccessDeniedError
import uuid
from fastapi import HTTPException

from tests.test_utils.constants import SAMPLE_TEST_DATA, TEST_REQUISITION_DATA
from tests.test_utils.helpers import TestHelpers
from tests.test_utils.verification import (
    EntityVerifier,
    RequisitionVerifier,
    RequisitionReadWithDetailsVerifier,
    SampleVerifier,
    TestTypeVerifier,
    RequisitionSampleVerifier,
    RequisitionSampleTestVerifier,
    verify_crud_error_response
)
from app.models.requisition_sample import RequisitionSample

class TestRequisitionCRUD:
    #####################
    # create_requisition TESTS
    #####################
    def test_create_requisition_success(self, db: Session, test_staff_user, test_lab):
        # Use test data from constants
        requisition_data = RequisitionCreate(
            **TEST_REQUISITION_DATA["scientist"]
        )

        requisition = create_requisition(db, requisition_data, test_staff_user.user_id, user_role=UserRole.SCIENTIST, lab_id = str(test_lab.lab_id))
        RequisitionVerifier.verify_response(requisition.__dict__, {
            "lsa_number": TEST_REQUISITION_DATA["scientist"]["lsa_number"],
            "status": RequisitionStatus.SUBMITTED.value,
            "submitted_by": test_staff_user.user_id
        })

    def test_create_requisition_db_error(self, db: Session, test_staff_user, test_lab):
        # Mock the database add operation to simulate an error
        error_msg = "Database error"
        db.commit = MagicMock(side_effect=SQLAlchemyError("Database error"))

        requisition_data = RequisitionCreate(
            **TEST_REQUISITION_DATA["scientist"]
        )

        with pytest.raises(StateError) as exc_info:
            create_requisition(db, requisition_data, test_staff_user.user_id, user_role=UserRole.SCIENTIST, lab_id = str(test_lab.lab_id))

        verify_crud_error_response(exc_info.value, 409, f"Error creating requisition: {error_msg}")

    def test_create_requisition_invalid_data(self, db: Session, test_staff_user, test_lab):
        # Test with invalid priority value
        with pytest.raises(ValueError):
            requisition_data = RequisitionCreate(
                **TEST_REQUISITION_DATA["invalid"]["invalid_priority"]
            )
            create_requisition(db, requisition_data, test_staff_user.user_id, user_role=UserRole.SCIENTIST,  lab_id = str(test_lab.lab_id))
    
    #####################
    # get_requisitions TESTS
    #####################

    def test_get_requisitions_scientist(self, db: Session, test_staff_user, test_requisitions):
        requisitions = get_requisitions(
            db,
            test_staff_user.user_id,
            UserRole.SCIENTIST,
            lab_id = test_staff_user.lab_id
        )
        assert len(requisitions) >= 1
        for req in requisitions:
            RequisitionReadWithDetailsVerifier.verify_common_fields(req.__dict__)
            RequisitionReadWithDetailsVerifier.verify_response(req.__dict__, {
                "submitted_by": test_staff_user.user_id
            })

        # Check that all reqs across labs are returned
        assert all(req.lab_id in [lab.lab_id for lab in test_requisitions] for req in requisitions)

    def test_get_requisitions_admin(self, db: Session, test_staff_user, test_requisitions):
        # Test that admin can see all requisitions regardless of who submitted them
        requisitions = get_requisitions(
            db,
            test_staff_user.user_id,
            UserRole.LAB_ADMIN,
            lab_id = str(test_requisitions[0].lab_id)
        )

        #Should only get 3 labs back since there are 3 labs from test_requisitions that have is lab_id
        assert all(str(req.lab_id) == str(test_requisitions[0].lab_id) for req in requisitions), "Admins should only see requisitions from their lab"

        # Verify each requisition has the common fields and detailed fields
        for req in requisitions:
            RequisitionReadWithDetailsVerifier.verify_common_fields(req.__dict__)

        # Verify we get requisitions from different users
        submitter_ids = {req.submitted_by for req in requisitions}
        assert len(submitter_ids) > 1, ERROR_MESSAGES["requisition"]["admin_access_validation"]

        # Verify we get requisitions with different statuses
        status_types = {req.status for req in requisitions}
        assert len(status_types) == 3, ERROR_MESSAGES["requisition"]["status_variety_validation"]
        assert all(status.value in status_types for status in [
            RequisitionStatus.SUBMITTED,
            RequisitionStatus.IN_PROGRESS,
            RequisitionStatus.COMPLETE
        ])

    # Check that if role lab admin AND no lab_id
    def test_get_requisitions_admin_with_no_id(self, db: Session, test_staff_user, test_requisitions):
        with pytest.raises(StateError, match=ERROR_MESSAGES["requisition"]["unauthorized_lab"]):
            requisitions = get_requisitions(
                db,
                test_staff_user.user_id,
                UserRole.LAB_ADMIN,
                lab_id=None
            )

    def test_get_requisitions_with_status(self, db: Session, test_staff_user, test_requisition):
        requisitions = get_requisitions(
            db,
            test_staff_user.user_id,
            UserRole.SCIENTIST,
            lab_id = test_staff_user.lab_id,
            statuses=[RequisitionStatus.SUBMITTED.value]
        )
        for req in requisitions:
            RequisitionVerifier.verify_response(req.__dict__, {
                "status": RequisitionStatus.SUBMITTED.value,
                "submitted_by": test_staff_user.user_id
            })
            # Verify detailed response fields
            RequisitionReadWithDetailsVerifier.verify_common_fields(req.__dict__)

    def test_get_requisitions_with_multiple_statuses(self, db: Session, test_staff_user, test_requisitions):
        """Test filtering requisitions with multiple status values"""
        multiple_statuses = [RequisitionStatus.SUBMITTED.value, RequisitionStatus.IN_PROGRESS.value]
        requisitions = get_requisitions(
            db,
            test_staff_user.user_id,
            UserRole.SCIENTIST,
            lab_id = test_staff_user.lab_id,
            statuses=multiple_statuses
        )

        # Verify all returned requisitions have one of the specified statuses
        for req in requisitions:
            assert req.status in multiple_statuses, ERROR_MESSAGES["requisition"]["status_filter_mismatch"].format(status=req.status, filter=multiple_statuses)
            RequisitionReadWithDetailsVerifier.verify_response(req.__dict__, {
                "submitted_by": test_staff_user.user_id
            })

    def test_get_requisitions_returns_detailed_response(self, db: Session, test_staff_user, test_requisition):
        """Test that get_requisitions returns RequisitionReadWithDetails objects with all required fields"""
        requisitions = get_requisitions(
            db,
            test_staff_user.user_id,
            UserRole.SCIENTIST,
            lab_id = test_staff_user.lab_id
        )

        assert len(requisitions) >= 1
        req = requisitions[0]

        # Verify all standard and detailed requisition fields
        RequisitionReadWithDetailsVerifier.verify_common_fields(req.__dict__)

        # Verify req_name format (should include LSA number)
        assert req.lsa_number in req.req_name, ERROR_MESSAGES["requisition"]["req_name_format_invalid"]
    
    #####################
    # get_requisition TESTS
    #####################
    def test_get_requisition_by_id(self, db: Session, test_staff_user, test_requisition):
        requisition = get_requisition(
            db,
            test_requisition.req_id,
            test_staff_user.user_id,
            UserRole.SCIENTIST,
            lab_id = test_staff_user.lab_id
        )
        RequisitionVerifier.verify_common_fields(requisition.__dict__)
        RequisitionVerifier.verify_response(requisition.__dict__, {
            "req_id": test_requisition.req_id
        })

    def test_get_nonexistent_requisition(self, db: Session, test_staff_user):
        """Test getting a non-existent requisition"""
        random_uuid = uuid.uuid4()
        with pytest.raises(NotFoundError) as exc_info:
            get_requisition(
                db,
                str(random_uuid),
                test_staff_user.user_id,
                UserRole.SCIENTIST,
                lab_id = test_staff_user.lab_id
            )
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["requisition"]["not_found"])
    

    def test_get_requisition_by_id_no_lab_id(self, db: Session, test_staff_user, test_requisition):
        with pytest.raises(NotFoundError, match=ERROR_MESSAGES["requisition"]["not_found"]):
            get_requisition(
                db,
                test_requisition.req_id,
                test_staff_user.user_id,
                UserRole.LAB_ADMIN,
                lab_id = test_staff_user.lab_id
            )
    
    def test_get_requisition_by_id_wrong_lab_id(self, db: Session, test_staff_user, test_requisitions):
        with pytest.raises(NotFoundError, match=ERROR_MESSAGES["requisition"]["not_found"]):
            get_requisition(
                db,
                test_requisitions[0].req_id,
                test_staff_user.user_id,
                UserRole.LAB_ADMIN,
                lab_id = test_requisitions[1].lab_id
            )
    
    def test_get_requisition_by_id_admin(self, db: Session, test_staff_user, test_requisition):
        requisition = get_requisition(
            db,
            test_requisition.req_id,
            test_staff_user.user_id,
            UserRole.LAB_ADMIN,
            lab_id = test_requisition.lab_id
        )
        RequisitionVerifier.verify_response(requisition.__dict__)
        assert requisition.req_id == test_requisition.req_id
    

    #####################
    # update_requisition TESTS
    #####################
    def test_update_requisition_success(self, db: Session, test_requisition):
        update_data = RequisitionUpdate(
            **TEST_REQUISITION_DATA["updates"]["status_change"]
        )
        updated_requisition = update_requisition(
            db,
            test_requisition.req_id,
            update_data,
            "test_user",
            UserRole.LAB_ADMIN,
            test_requisition.lab_id
        )
        assert updated_requisition is not None
        RequisitionVerifier.verify_response(updated_requisition.__dict__, {
            "status": RequisitionStatus.IN_PROGRESS.value
        })

    def test_update_nonexistent_requisition(self, db: Session):
        """Test updating a non-existent requisition"""
        random_uuid = uuid.uuid4()
        update_data = RequisitionUpdate(
            **TEST_REQUISITION_DATA["updates"]["status_change"],
        )
        with pytest.raises(NotFoundError) as exc_info:
            update_requisition(
                db,
                str(random_uuid),
                update_data,
                "test_user",
                UserRole.LAB_ADMIN,
                str(random_uuid)
            )
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["requisition"]["not_found"])

    def test_archive_requisition(self, db: Session, test_requisition):
        update_data = RequisitionUpdate(
            **TEST_REQUISITION_DATA["updates"]["archive"]
        )
        updated_requisition = update_requisition(
            db,
            test_requisition.req_id,
            update_data,
            "test_user",
            UserRole.LAB_ADMIN,
            test_requisition.lab_id
        )
        assert updated_requisition is not None
        RequisitionVerifier.verify_response(updated_requisition.__dict__, {
            "is_archived": True
        })
    
    def test_update_wrong_lab(self, db: Session, test_requisition):
        update_data = RequisitionUpdate(
            **TEST_REQUISITION_DATA["updates"]["status_change"]
        )

        with pytest.raises(AccessDeniedError, match=ERROR_MESSAGES["requisition"]["unauthorized_modify"]):
            updated_requisition = update_requisition(
                db,
                test_requisition.req_id,
                update_data,
                "test_user",
                UserRole.LAB_ADMIN,
                str(uuid.uuid4())
            )

    #####################
    # list_requisitions_for_lab TESTS
    #####################
    def test_list_requisitions_for_lab_success(self, db: Session, test_staff_user, test_requisitions):
        """Test successfully listing requisitions for a specific lab"""
        lab_id = str(test_requisitions[0].lab_id)

        requisitions = list_requisitions_for_lab(
            db,
            lab_id,
            test_staff_user.user_id,
            UserRole.LAB_ADMIN,
            statuses=None
        )

        # Verify all returned requisitions belong to the specified lab
        for req in requisitions:
            assert str(req.lab_id) == lab_id, ERROR_MESSAGES["requisition"]["lab_filter_mismatch"].format(lab_id=req.lab_id, filter_id=lab_id)
            assert req.req_name.startswith("LSA-"), ERROR_MESSAGES["requisition"]["req_name_prefix_invalid"]
            RequisitionReadWithDetailsVerifier.verify_common_fields(req.__dict__)

    def test_list_requisitions_for_lab_with_status_filter(self, db: Session, test_staff_user, test_requisitions):
        """Test listing requisitions for a lab with single status filter"""
        lab_id = str(test_requisitions[0].lab_id)
        status_filter = [RequisitionStatus.SUBMITTED.value]

        requisitions = list_requisitions_for_lab(
            db,
            lab_id,
            test_staff_user.user_id,
            UserRole.LAB_ADMIN,
            statuses=status_filter
        )

        # Verify all returned requisitions have the specified status
        for req in requisitions:
            RequisitionReadWithDetailsVerifier.verify_response(req.__dict__, {
                "status": RequisitionStatus.SUBMITTED.value
            })
            assert str(req.lab_id) == lab_id, ERROR_MESSAGES["requisition"]["lab_filter_mismatch"].format(lab_id=req.lab_id, filter_id=lab_id)

    def test_list_requisitions_for_lab_multiple_statuses(self, db: Session, test_staff_user, test_requisitions):
        """Test listing requisitions for a lab with multiple status filters"""
        lab_id = str(test_requisitions[0].lab_id)
        status_filters = [RequisitionStatus.SUBMITTED.value, RequisitionStatus.IN_PROGRESS.value]

        requisitions = list_requisitions_for_lab(
            db,
            lab_id,
            test_staff_user.user_id,
            UserRole.LAB_ADMIN,
            statuses=status_filters
        )

        # Verify all returned requisitions have one of the specified statuses
        for req in requisitions:
            assert req.status in status_filters, ERROR_MESSAGES["requisition"]["status_filter_mismatch"].format(status=req.status, filter=status_filters)
            assert str(req.lab_id) == lab_id, ERROR_MESSAGES["requisition"]["lab_filter_mismatch"].format(lab_id=req.lab_id, filter_id=lab_id)
            RequisitionReadWithDetailsVerifier.verify_common_fields(req.__dict__)

    def test_list_requisitions_for_lab_empty_result(self, db: Session, test_staff_user):
        """Test listing requisitions for a lab with no matching results"""
        non_existent_lab_id = str(uuid.uuid4())

        requisitions = list_requisitions_for_lab(
            db,
            non_existent_lab_id,
            test_staff_user.user_id,
            UserRole.LAB_ADMIN,
            statuses=None
        )

        # Should return empty list for non-existent lab
        assert len(requisitions) == 0, ERROR_MESSAGES["requisition"]["empty_result_validation"]

    def test_list_requisitions_for_lab_access_control(self, db: Session, test_staff_user, test_requisitions):
        """Test access control for list_requisitions_for_lab function"""
        lab_id = str(test_requisitions[0].lab_id)

        # Test LAB_PERSONNEL access
        requisitions = list_requisitions_for_lab(
            db,
            lab_id,
            test_staff_user.user_id,
            UserRole.LAB_PERSONNEL,
            statuses=None
        )

        # Should return requisitions for lab personnel
        for req in requisitions:
            assert str(req.lab_id) == lab_id, ERROR_MESSAGES["requisition"]["lab_access_validation"]
            RequisitionReadWithDetailsVerifier.verify_common_fields(req.__dict__)

    #####################
    # add_samples_to_requisition TESTS
    #####################
    def test_add_samples_to_requisition(self, db: Session, test_requisition, test_samples, test_staff_lab_personal):
        """Test adding samples to a requisition"""
        sample_ids = [str(sample.sample_id) for sample in test_samples]
        
        result = add_samples_to_requisition(
            db, 
            test_requisition.req_id, 
            sample_ids, 
            test_staff_lab_personal.user_id,
            test_staff_lab_personal.role,
            test_staff_lab_personal.lab_id
        )
        
        # Verify samples were added by querying them
        added_samples = get_requisition_samples(
            db,
            test_requisition.req_id,
            test_staff_lab_personal.user_id,
            test_staff_lab_personal.role,
            test_staff_lab_personal.lab_id
        )
        assert len(added_samples) == len(sample_ids)
        
        # Verify each requisition sample using the verifier
        for sample in added_samples:
            RequisitionSampleVerifier.verify_common_fields(sample.__dict__)
            sample_dict = sample.__dict__.copy()
            if isinstance(sample_dict.get('requisition_id'), uuid.UUID):
                sample_dict['requisition_id'] = str(sample_dict['requisition_id'])
            EntityVerifier.verify_response(sample_dict, {
                "requisition_id": str(test_requisition.req_id)
            })
            assert str(sample.sample_id) in sample_ids

    def test_add_samples_to_nonexistent_requisition(self, db: Session, test_samples, test_staff_lab_personal):
        """Test adding samples to a non-existent requisition"""
        non_existent_req_id = str(uuid.uuid4())
        sample_ids = [str(sample.sample_id) for sample in test_samples]
        
        with pytest.raises(NotFoundError) as exc_info:
            add_samples_to_requisition(db, non_existent_req_id, sample_ids, test_staff_lab_personal.user_id, test_staff_lab_personal.role, test_staff_lab_personal.lab_id)
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["requisition"]["not_found"])

    def test_add_nonexistent_samples_to_requisition(self, db: Session, test_requisition, test_staff_lab_personal):
        """Test adding non-existent samples to a requisition"""
        non_existent_sample_ids = [str(uuid.uuid4())]
        
        with pytest.raises(NotFoundError) as exc_info:
            add_samples_to_requisition(
                db, 
                test_requisition.req_id, 
                non_existent_sample_ids, 
                test_staff_lab_personal.user_id,
                test_staff_lab_personal.role,
                test_staff_lab_personal.lab_id
            )
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["sample"]["not_found"])

    def test_add_duplicate_samples_to_requisition(self, db: Session, test_requisition, test_samples, test_staff_lab_personal):
        """Test adding duplicate samples to a requisition"""
        sample_ids = [str(test_samples[0].sample_id), str(test_samples[0].sample_id)]  # Duplicate ID
        
        with pytest.raises(ValidationError) as exc_info:
            add_samples_to_requisition(
                db, 
                test_requisition.req_id, 
                sample_ids, 
                test_staff_lab_personal.user_id,
                test_staff_lab_personal.role,
                test_staff_lab_personal.lab_id
            )
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["requisition"]["duplicate_samples"])
    
    def test_add_samples_to_requisition_wrong_lab(self, db: Session, test_requisition, test_samples, test_staff_lab_personal):
        sample_ids = [str(sample.sample_id) for sample in test_samples]

        with pytest.raises(AccessDeniedError, match=ERROR_MESSAGES["requisition"]["unauthorized_modify"]):
            result = add_samples_to_requisition(
                db, 
                test_requisition.req_id, 
                sample_ids, 
                test_staff_lab_personal.user_id,
                test_staff_lab_personal.role,
                str(uuid.uuid4())
            )
    
    #####################
    # get_requisition_samples TESTS
    #####################
    @pytest.mark.usefixtures("test_requisition_with_samples")
    def test_get_requisition_samples(self, db: Session, test_requisition_with_samples, test_staff_user):
        """Test getting samples from a requisition"""
        samples = get_requisition_samples(
            db, 
            test_requisition_with_samples.req_id,
            test_staff_user.user_id,
            test_staff_user.role,
            test_requisition_with_samples.lab_id
        )
        
        assert len(samples) > 0
        
        sample = samples[0]
        
        RequisitionSampleVerifier.verify_common_fields(sample.__dict__)
        RequisitionSampleVerifier.verify_response(sample.__dict__, {
            "requisition_id": str(test_requisition_with_samples.req_id)
        })
        
        SampleVerifier.verify_common_fields(sample.sample.__dict__)

    def test_get_samples_from_nonexistent_requisition(self, db: Session, test_staff_user):
        """Test getting samples from a non-existent requisition"""
        non_existent_req_id = str(uuid.uuid4())
        
        with pytest.raises(NotFoundError) as exc_info:
            get_requisition_samples(db, non_existent_req_id, test_staff_user.user_id, test_staff_user.role, str(uuid.uuid4()))
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["requisition"]["not_found"])
    
    #####################
    # remove_sample_from_requisition TESTS
    #####################
    def test_remove_sample_from_requisition(self, db: Session, test_requisition_with_samples, test_staff_lab_personal):
        """Test removing a sample from a requisition"""

        # First get the samples in the requisition
        initial_samples = get_requisition_samples(
            db,
            test_requisition_with_samples.req_id,
            test_staff_lab_personal.user_id,
            test_staff_lab_personal.role,
            test_requisition_with_samples.lab_id
        )
        assert len(initial_samples) > 0
        sample_to_remove = initial_samples[0]

        result = remove_sample_from_requisition(
            db,
            test_requisition_with_samples.req_id,
            str(sample_to_remove.sample_id),
            test_staff_lab_personal.user_id,
            test_staff_lab_personal.role,
            test_requisition_with_samples.lab_id
        )

        assert result == {
            "message": ERROR_MESSAGES["requisition"]["remove_sample_success"],
            "requisition_id": str(test_requisition_with_samples.req_id),
            "sample_id": str(sample_to_remove.sample_id)
        }

    def test_remove_nonexistent_sample_from_requisition(self, db: Session, test_requisition, test_staff_lab_personal):
        """Test removing a non-existent sample from a requisition"""
        non_existent_sample_id = str(uuid.uuid4())
        
        with pytest.raises(NotFoundError) as exc_info:
            remove_sample_from_requisition(
                db, 
                test_requisition.req_id, 
                non_existent_sample_id, 
                test_staff_lab_personal.user_id,
                test_staff_lab_personal.role,
                test_requisition.lab_id
            )
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["sample"]["not_found_in_requisition"])

    def test_remove_sample_from_nonexistent_requisition(self, db: Session, test_samples, test_staff_lab_personal):
        """Test removing a sample from a non-existent requisition"""
        non_existent_req_id = str(uuid.uuid4())
        sample_id = str(test_samples[0].sample_id)
        
        with pytest.raises(NotFoundError) as exc_info:
            remove_sample_from_requisition(
                db, 
                non_existent_req_id, 
                sample_id, 
                test_staff_lab_personal.user_id,
                test_staff_lab_personal.role,
                str(uuid.uuid4())
            )
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["requisition"]["not_found"])

    #####################
    # add_tests_to_requisition_sample TESTS
    #####################
    def test_add_tests_lab_admin_success(self, db: Session, test_requisition_with_samples, test_staff_admin, test_types):
        """Test that lab admin can successfully add tests to a requisition sample."""
        sample = test_requisition_with_samples.samples[0]
        test_type_ids = [str(test_types[0].test_type_id)]
        
        result = add_tests_to_requisition_sample(
            db, 
            str(test_requisition_with_samples.req_id),
            str(sample.sample_id),
            test_type_ids,
            str(test_staff_admin.user_id),
            test_staff_admin.role,
            str(test_requisition_with_samples.lab_id)
        )
        
        assert len(result) == 1
        test_result = result[0]
        
        # Convert test_result to dict for verification
        test_dict = test_result.__dict__
        
        # Verify the test result
        RequisitionSampleTestVerifier.verify_common_fields(test_dict)
        assert str(test_result.test_type_id) == test_type_ids[0]
        assert test_result.requisition_sample.requisition_id == test_requisition_with_samples.req_id
        assert test_result.requisition_sample.sample_id == sample.sample_id
        
        # Also verify the test type directly
        TestTypeVerifier.verify_common_fields(test_result.test_type.__dict__)

    def test_add_tests_lab_personnel_success(self, db: Session, test_requisition_with_samples, test_staff_lab_personal, test_types):
        """Test that lab personnel can successfully add tests to a requisition sample."""
        sample = test_requisition_with_samples.samples[0]
        test_type_ids = [str(test_types[0].test_type_id)]
        
        result = add_tests_to_requisition_sample(
            db, 
            str(test_requisition_with_samples.req_id),
            str(sample.sample_id),
            test_type_ids,
            str(test_staff_lab_personal.user_id),
            test_staff_lab_personal.role,
            str(test_requisition_with_samples.lab_id)
        )
        
        assert len(result) == 1
        test_result = result[0]
        
        # Convert test_result to dict for verification
        test_dict = test_result.__dict__
        
        # Verify the test result
        RequisitionSampleTestVerifier.verify_common_fields(test_dict)
        assert str(test_result.test_type_id) == test_type_ids[0]
        assert test_result.requisition_sample.requisition_id == test_requisition_with_samples.req_id
        assert test_result.requisition_sample.sample_id == sample.sample_id
        
        # Also verify the test type directly
        TestTypeVerifier.verify_common_fields(test_result.test_type.__dict__)

    def test_add_tests_scientist_denied(self, db: Session, test_requisition_with_samples, test_staff_user, test_types):
        """Test that scientists cannot add tests to a requisition sample."""
        sample = test_requisition_with_samples.samples[0]
        test_type_ids = [str(test_types[0].test_type_id)]
        
        with pytest.raises(AccessDeniedError) as exc_info:
            add_tests_to_requisition_sample(
                db, 
                str(test_requisition_with_samples.req_id),
                str(sample.sample_id),
                test_type_ids,
                str(test_staff_user.user_id),
                test_staff_user.role,
                str(test_requisition_with_samples.lab_id)
            )
        
        verify_crud_error_response(exc_info.value, 403, ERROR_MESSAGES["requisition"]["unauthorized_modify"])

    def test_add_tests_no_test_types(self, db: Session, test_requisition_with_samples, test_staff_admin):
        """Test that providing no test types raises ValidationError."""
        sample = test_requisition_with_samples.samples[0]
        
        with pytest.raises(ValidationError) as exc_info:
            add_tests_to_requisition_sample(
                db, 
                str(test_requisition_with_samples.req_id),
                str(sample.sample_id),
                [],
                str(test_staff_admin.user_id),
                UserRole.LAB_ADMIN,
                str(test_requisition_with_samples.lab_id)
            )
        
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["test"]["no_test_types"])

    def test_add_tests_nonexistent_requisition(self, db: Session, test_requisition_with_samples, test_staff_admin, test_types):
        """Test that adding tests to a nonexistent requisition raises NotFoundError."""
        sample = test_requisition_with_samples.samples[0]
        test_type_ids = [str(test_types[0].test_type_id)]
        
        with pytest.raises(NotFoundError) as exc_info:
            add_tests_to_requisition_sample(
                db, 
                str(uuid.uuid4()),  
                str(sample.sample_id),
                test_type_ids,
                str(test_staff_admin.user_id),
                UserRole.LAB_ADMIN,
                str(test_requisition_with_samples.lab_id)
            )
        
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["requisition"]["not_found"])

    def test_add_tests_nonexistent_sample(self, db: Session, test_requisition_with_samples, test_staff_admin, test_types):
        """Test that adding tests to a nonexistent sample raises NotFoundError."""
        test_type_ids = [str(test_types[0].test_type_id)]
        
        with pytest.raises(NotFoundError) as exc_info:
            add_tests_to_requisition_sample(
                db, 
                str(test_requisition_with_samples.req_id),
                str(uuid.uuid4()),  
                test_type_ids,
                str(test_staff_admin.user_id),
                UserRole.LAB_ADMIN,
                str(test_requisition_with_samples.lab_id)
            )
        
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["sample"]["not_found_in_requisition"])

    #####################
    # get_requisition_sample_tests TESTS
    #####################
    def test_get_requisition_sample_tests_success(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test getting tests for a requisition sample."""
        sample = test_requisition_with_tests.samples[0]
        
        result = get_requisition_sample_tests(
            db, 
            str(test_requisition_with_tests.req_id),
            str(sample.sample_id),
            str(test_staff_admin.user_id),
            test_staff_admin.role,
            str(test_requisition_with_tests.lab_id)
        )
        
        assert len(result) == 1
        test_result = result[0]
        
        # Verify the test using the verifier directly
        RequisitionSampleTestVerifier.verify_common_fields(test_result.__dict__)
        RequisitionSampleTestVerifier.verify_response(test_result.__dict__, {
            "requisition_id": str(test_requisition_with_tests.req_id),
            "sample_id": str(sample.sample_id)
        })
        
        # Also verify the test type directly
        TestTypeVerifier.verify_common_fields(test_result.test_type.__dict__)

    def test_get_requisition_sample_tests_nonexistent_requisition(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test getting tests from a nonexistent requisition."""
        sample = test_requisition_with_tests.samples[0]
        
        with pytest.raises(NotFoundError) as exc_info:
            get_requisition_sample_tests(
                db,
                str(uuid.uuid4()),  
                str(sample.sample_id),
                str(test_staff_admin.user_id),
                test_staff_admin.role,
                str(test_requisition_with_tests.lab_id)
            )
        
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["requisition"]["not_found"])

    def test_get_requisition_sample_tests_nonexistent_sample(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test getting tests for a nonexistent sample."""
        with pytest.raises(NotFoundError) as exc_info:
            get_requisition_sample_tests(
                db,
                str(test_requisition_with_tests.req_id),
                str(uuid.uuid4()),  
                str(test_staff_admin.user_id),
                test_staff_admin.role,
                str(test_requisition_with_tests.lab_id)
            )
        
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["sample"]["not_found_in_requisition"])
    
    def test_get_requisition_sample_tests_wrong_lab(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test getting tests for a nonexistent sample."""
        with pytest.raises(AccessDeniedError) as exc_info:
            get_requisition_sample_tests(
                db,
                str(test_requisition_with_tests.req_id),
                str(uuid.uuid4()),  
                str(test_staff_admin.user_id),
                test_staff_admin.role,
                str(uuid.uuid4())
            )
        
        assert str(exc_info.value) == ERROR_MESSAGES["requisition"]["unauthorized_modify"]

    def test_get_requisition_sample_tests_no_tests(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test getting tests for a sample that has no tests."""
        # Create a new sample without tests
        new_sample = TestHelpers.Samples.create_test_sample(db, SAMPLE_TEST_DATA["sample1"])
        
        # Add the sample to the requisition
        add_samples_to_requisition(
            db,
            str(test_requisition_with_tests.req_id),
            [str(new_sample.sample_id)],
            str(test_staff_admin.user_id),
            UserRole.LAB_ADMIN,
            str(test_staff_admin.lab_id)
        )
        
        result = get_requisition_sample_tests(
            db,
            str(test_requisition_with_tests.req_id),
            str(new_sample.sample_id),
            str(test_staff_admin.user_id),
            test_staff_admin.role,
            str(test_staff_admin.lab_id)
        )
        
        assert len(result) == 0

    #####################
    # update_requisition_sample_test TESTS
    #####################
    def test_update_test_status_lab_admin_success(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test that lab admin can successfully update test status."""
        test_id = str(test_requisition_with_tests.samples[0].tests[0].req_sample_test_id)
        test_update = RequisitionSampleTestUpdate(status=SampleTestStatus.IN_PROGRESS)
        
        result = update_requisition_sample_test(
            db,
            str(test_requisition_with_tests.req_id),
            str(test_requisition_with_tests.samples[0].sample_id),
            test_id,
            test_update,
            str(test_staff_admin.user_id),
            test_staff_admin.role,
            str(test_staff_admin.lab_id)
        )
        
        assert result is not None
        
        # Verify the test result directly
        test_dict = result.__dict__
        RequisitionSampleTestVerifier.verify_common_fields(test_dict)
        EntityVerifier.verify_response(test_dict)
        assert result.status == SampleTestStatus.IN_PROGRESS.value
        assert str(result.processed_by) == str(test_staff_admin.user_id)

    def test_update_test_status_lab_personnel_success(self, db: Session, test_requisition_with_tests, test_staff_lab_personal):
        """Test that lab personnel can successfully update test status."""
        test_id = str(test_requisition_with_tests.samples[0].tests[0].req_sample_test_id)
        test_update = RequisitionSampleTestUpdate(status=SampleTestStatus.IN_PROGRESS)
        
        result = update_requisition_sample_test(
            db,
            str(test_requisition_with_tests.req_id),
            str(test_requisition_with_tests.samples[0].sample_id),
            test_id,
            test_update,
            str(test_staff_lab_personal.user_id),
            test_staff_lab_personal.role,
            str(test_staff_lab_personal.lab_id)
        )
        
        assert result is not None
        
        # Verify the test result directly
        test_dict = result.__dict__
        RequisitionSampleTestVerifier.verify_common_fields(test_dict)
        EntityVerifier.verify_response(test_dict)
        assert result.status == SampleTestStatus.IN_PROGRESS.value
        assert str(result.processed_by) == str(test_staff_lab_personal.user_id)

    def test_update_test_status_scientist_denied(self, db: Session, test_requisition_with_tests, test_staff_user):
        """Test that scientists cannot update test status."""
        test = test_requisition_with_tests.samples[0].tests[0]
        test_update = RequisitionSampleTestUpdate(status=SampleTestStatus.IN_PROGRESS)
        
        with pytest.raises(AccessDeniedError) as exc_info:
            update_requisition_sample_test(
                db,
                str(test_requisition_with_tests.req_id),
                str(test_requisition_with_tests.samples[0].sample_id),
                str(test.req_sample_test_id),
                test_update,
                str(test_staff_user.user_id),
                test_staff_user.role,
                str(test_requisition_with_tests.lab_id)
            )
        
        assert str(exc_info.value) == ERROR_MESSAGES["requisition"]["unauthorized_modify"]
    
    def test_update_test_status_wrong_lab(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test updating a req that doesn't belong to the lab"""
        test = test_requisition_with_tests.samples[0].tests[0]
        test_update = RequisitionSampleTestUpdate(status=SampleTestStatus.IN_PROGRESS)

        with pytest.raises(AccessDeniedError) as exc_info:
            update_requisition_sample_test(
                db,
                str(test_requisition_with_tests.req_id),
                str(test_requisition_with_tests.samples[0].sample_id),
                str(test.req_sample_test_id),
                test_update,
                str(test_staff_admin.user_id),
                test_staff_admin.role,
                str(uuid.uuid4())
            )
        
        verify_crud_error_response(exc_info.value, 403, ERROR_MESSAGES["requisition"]["unauthorized_modify"])

    def test_update_test_status_nonexistent_test(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test updating status of a nonexistent test."""
        test_update = RequisitionSampleTestUpdate(status=SampleTestStatus.IN_PROGRESS)
        
        with pytest.raises(NotFoundError) as exc_info:
            update_requisition_sample_test(
                db,
                str(test_requisition_with_tests.req_id),
                str(test_requisition_with_tests.samples[0].sample_id),
                str(uuid.uuid4()),
                test_update,
                str(test_staff_admin.user_id),
                test_staff_admin.role,
                str(test_staff_admin.lab_id)
            )
        
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["test"]["not_found"])

    def test_update_test_status_completed(self, db, test_requisition_with_tests, test_staff_admin):
        """Test that completing a test sets the correct completion timestamp."""
        from datetime import datetime, timezone
        
        test = test_requisition_with_tests.samples[0].tests[0]
        test_update = RequisitionSampleTestUpdate(status=SampleTestStatus.COMPLETE)
        
        result = update_requisition_sample_test(
            db,
            str(test_requisition_with_tests.req_id),
            str(test_requisition_with_tests.samples[0].sample_id),
            str(test.req_sample_test_id),
            test_update,
            str(test_staff_admin.user_id),
            test_staff_admin.role,
            str(test_staff_admin.lab_id)
        )
        
        assert result is not None
        
        # Verify the test result directly
        test_dict = result.__dict__
        
        RequisitionSampleTestVerifier.verify_common_fields(test_dict)
        EntityVerifier.verify_response(test_dict)
        assert result.status == SampleTestStatus.COMPLETE.value
        assert result.completed_at is not None
        assert isinstance(result.completed_at, datetime) 
        assert str(result.processed_by) == str(test_staff_admin.user_id)
    
    #####################
    # remove_test_from_requisition_sample TESTS
    #####################
    def test_remove_test_from_sample_lab_admin_success(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test that lab admin can successfully remove a test from a requisition sample."""
        test = test_requisition_with_tests.samples[0].tests[0]

        result = remove_test_from_requisition_sample(
            db,
            str(test_requisition_with_tests.req_id),
            str(test_requisition_with_tests.samples[0].sample_id),
            str(test.req_sample_test_id),
            str(test_staff_admin.user_id),
            test_staff_admin.role,
            str(test_staff_admin.lab_id)
        )

        assert result == {
            "message": ERROR_MESSAGES["requisition"]["remove_test_success"],
            "requisition_id": str(test_requisition_with_tests.req_id),
            "sample_id": str(test_requisition_with_tests.samples[0].sample_id),
            "test_id": str(test.req_sample_test_id)
        }

    def test_remove_test_from_sample_lab_personnel_success(self, db: Session, test_requisition_with_tests, test_staff_lab_personal):
        """Test that lab personnel can successfully remove a test from a requisition sample."""
        test = test_requisition_with_tests.samples[0].tests[0]

        result = remove_test_from_requisition_sample(
            db,
            str(test_requisition_with_tests.req_id),
            str(test_requisition_with_tests.samples[0].sample_id),
            str(test.req_sample_test_id),
            str(test_staff_lab_personal.user_id),
            test_staff_lab_personal.role,
            str(test_staff_lab_personal.lab_id)
        )

        assert result == {
            "message": ERROR_MESSAGES["requisition"]["remove_test_success"],
            "requisition_id": str(test_requisition_with_tests.req_id),
            "sample_id": str(test_requisition_with_tests.samples[0].sample_id),
            "test_id": str(test.req_sample_test_id)
        }

    def test_remove_test_from_sample_scientist_denied(self, db: Session, test_requisition_with_tests, test_staff_user):
        """Test that scientists cannot remove tests from a requisition sample."""
        test = test_requisition_with_tests.samples[0].tests[0]
        
        with pytest.raises(AccessDeniedError) as exc_info:
            remove_test_from_requisition_sample(
                db,
                str(test_requisition_with_tests.req_id),
                str(test_requisition_with_tests.samples[0].sample_id),
                str(test.req_sample_test_id),
                str(test_staff_user.user_id),
                test_staff_user.role,
                str(test_requisition_with_tests.lab_id)
            )
        

        verify_crud_error_response(exc_info.value, 403, ERROR_MESSAGES["requisition"]["unauthorized_modify"])
    
    def test_remove_tests_from_sample_wrong_lab(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test updating a req that doesn't belong to the lab"""
        test = test_requisition_with_tests.samples[0].tests[0]
        test_update = RequisitionSampleTestUpdate(status=SampleTestStatus.IN_PROGRESS)

        with pytest.raises(AccessDeniedError) as exc_info:
            update_requisition_sample_test(
                db,
                str(test_requisition_with_tests.req_id),
                str(test_requisition_with_tests.samples[0].sample_id),
                str(test.req_sample_test_id),
                test_update,
                str(test_staff_admin.user_id),
                test_staff_admin.role,
                str(uuid.uuid4())
            )
        
        verify_crud_error_response(exc_info.value, 403, ERROR_MESSAGES["requisition"]["unauthorized_modify"])

    def test_remove_nonexistent_test_from_sample(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test removing a nonexistent test."""
        with pytest.raises(NotFoundError) as exc_info:
            remove_test_from_requisition_sample(
                db,
                str(test_requisition_with_tests.req_id),
                str(test_requisition_with_tests.samples[0].sample_id),
                str(uuid.uuid4()),
                str(test_staff_admin.user_id),
                test_staff_admin.role,
                str(test_staff_admin.lab_id)
            )
        
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["test"]["not_found"])

    def test_remove_test_from_nonexistent_requisition(self, db: Session, test_staff_admin):
        """Test removing a test from a nonexistent requisition."""
        with pytest.raises(NotFoundError) as exc_info:
            remove_test_from_requisition_sample(
                db,
                str(uuid.uuid4()),  
                str(uuid.uuid4()),
                str(uuid.uuid4()),
                str(test_staff_admin.user_id),
                test_staff_admin.role,
                str(test_staff_admin.lab_id)
            )
        
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["requisition"]["not_found"])

    def test_remove_test_from_nonexistent_sample(self, db: Session, test_requisition_with_tests, test_staff_admin):
        """Test removing a test from a nonexistent sample."""
        with pytest.raises(NotFoundError) as exc_info:
            remove_test_from_requisition_sample(
                db,
                str(test_requisition_with_tests.req_id),
                str(uuid.uuid4()),  
                str(uuid.uuid4()),
                str(test_staff_admin.user_id),
                test_staff_admin.role,
                str(test_staff_admin.lab_id)
            )
        
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["sample"]["not_found_in_requisition"])