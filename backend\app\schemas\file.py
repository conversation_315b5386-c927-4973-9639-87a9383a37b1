from pydantic import BaseModel, UUID4, ConfigDict, Field, field_validator
from typing import Optional
from datetime import datetime
from uuid import UUID

class FileBase(BaseModel):
    file_name: str
    file_type: Optional[str] = None
    file_size: Optional[int] = None
    requisition_id: Optional[UUID4] = None
    req_sample_id: Optional[UUID4] = None
    req_sample_test_id: Optional[UUID4] = None

class FileCreate(FileBase):
    storage_id: str
    file_type: str
    file_size: int
    uploaded_by: UUID4
    requisition_id: Optional[UUID4] = None
    req_sample_id: Optional[UUID4] = None
    req_sample_test_id: Optional[UUID4] = None

    @field_validator('file_name')
    def validate_filename(cls, v):
        if not v.strip():
            raise ValueError('Filename cannot be empty')
        return v

    @field_validator('file_size')
    def validate_file_size(cls, v):
        if v <= 0:
            raise ValueError('File size must be positive')
        return v

class PublishFileRequest(BaseModel):
    is_published: bool

class File(FileBase):
    file_id: UUID4
    storage_id: str
    uploaded_by: UUID4
    is_published: bool
    created_at: datetime
    updated_at: Optional[datetime]

    model_config = ConfigDict(from_attributes=True)
