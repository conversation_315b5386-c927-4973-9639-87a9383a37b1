"""
Test suite for CRUD operations on Staff objects.

Test Coverage:
    get_staff_by_user_id
        1. test_staff_user_id_not_found - user_id not found
        2. test_staff_user_id_found - user_id is found. Single-type
        3. test_staff_user_id_found_multi - user_id is found - Multi-type
    
    get_staff_with_lab_names_by_user_id
        1. test_get_staff_with_lab_names_by_user_id_not_found - user_id not found
        2. test_get_staff_with_lab_names_by_user_id_found_admin - user_id is found with lab role
        3. test_get_staff_with_lab_names_by_user_id_scientist - user_id is a scientist
        4. test_get_staff_with_lab_names_by_user_id_multiple_roles - user_id has multiple roles
    
    get_combination
        1. test_get_combination_not_found - no record found
        2. test_get_combination_found - Record found
    
    get_users_in_lab
        1. test_get_users_in_lab_not_found - lab_id not found
        2. test_get_users_in_lab_found - lab_id found
    
    create_staff
        1. test_get_create_staff_nonexisting_lab - lab_id doesn't exist
        3. test_create_staff_combo_found - combo already exists
        4. test_create_staff_scientist - trying to add scientist with id
        5. test_create_staff_db_error - db error
        6. test_create_staff_success - proper add

    edit_staff:
        1. test_edit_staff_scientist - Try to change role into scientist
        2. test_edit_staff_combo_exists - Combo trying to change into already exists
        3. test_edit_staff_staff_id - staff_id doesn't exist
        4. test_edit_staff_user_id_match - staff_id does not match the users ID
        5. test_edit_staff_lab_id_match - staff_id does not match the lab ID
        6. test_edit_staff_no_lab_admins_left - Lab admin becoming lab_personnel leave lab with 0 admins
        7. test_edit_staff_db_error - db error
        8. test_edit_staff_admin_to_personnel - lab admin becomes lab_personnel success
        9. test_edit_staff_personnel_to_admin - lab personnel becomes lab_admin success
    
    delete_staff
        1. test_delete_staff_record_not_exist - Record does not exist
        2. test_delete_staff_last_admin - Last Admin to exist
        3. test_delete_staff_db_error - DB error
        4. test_delete_staff_success - record exists
"""

import pytest
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from unittest.mock import MagicMock
import uuid

from app.crud.staff import get_staff_by_user_id, get_combination, get_users_in_lab, create_staff, edit_staff, delete_staff, get_staff_with_lab_names_by_user_id
from app.models.staff import Staff
from app.schemas.staff import StaffCreate, StaffType
from app.common.constants import UserRole, ERROR_MESSAGES
from app.common.exceptions import NotFoundError
from tests.test_utils.helpers import TestHelpers
from tests.test_utils.verification import UserVerifier, verify_crud_error_response


class TestStaffCRUD:
    #####################
    # get_staff_by_user_id TESTS
    #####################

    def test_staff_user_id_not_found(self, db: Session):
        """Test getting staff by a non-existent user ID"""
        staff = get_staff_by_user_id(db, str(uuid.uuid4()))
        assert len(staff) == 0

    def test_staff_user_id_found(self, db: Session, test_staff_admin):
        """Test getting staff by an existing user ID"""
        staff = get_staff_by_user_id(db, str(test_staff_admin.user_id))
        
        assert len(staff) == 1
        assert staff[0].user_id == test_staff_admin.user_id
        assert staff[0].lab_id == test_staff_admin.lab_id
        assert staff[0].role == test_staff_admin.role

    def test_staff_user_id_found_multi(self, db: Session, test_staff_multiple_roles):
        """Test getting staff by a user ID with multiple lab roles"""
        staff = get_staff_by_user_id(db, str(test_staff_multiple_roles[0].user_id))
        
        assert len(staff) == len(test_staff_multiple_roles)
        
        # Verify all roles are returned
        roles = [s.role for s in staff]
        for test_staff in test_staff_multiple_roles:
            assert test_staff.role in roles
            
        # Verify all lab IDs are returned
        lab_ids = [s.lab_id for s in staff]
        for test_staff in test_staff_multiple_roles:
            assert test_staff.lab_id in lab_ids

    #########################################
    # get_staff_with_lab_names_by_user_id TESTS
    #########################################

    def test_get_staff_with_lab_names_by_user_id_not_found(self, db: Session):
        """Test getting staff with lab names for a non-existent user ID"""
        results = get_staff_with_lab_names_by_user_id(db, str(uuid.uuid4()))
        assert len(results) == 0

    def test_get_staff_with_lab_names_by_user_id_found_admin(self, db: Session, test_staff_admin, test_lab):
        """Test getting staff with lab names for a user with an admin role"""
        results = get_staff_with_lab_names_by_user_id(db, str(test_staff_admin.user_id))
        
        assert len(results) == 1
        staff, lab_name = results[0]
        
        assert staff.user_id == test_staff_admin.user_id
        assert staff.lab_id == test_staff_admin.lab_id
        assert staff.role == test_staff_admin.role
        assert lab_name == test_lab.lab_name

    def test_get_staff_with_lab_names_by_user_id_scientist(self, db: Session, test_staff_user):
        """Test getting staff with lab names for a scientist"""
        results = get_staff_with_lab_names_by_user_id(db, str(test_staff_user.user_id))
        
        # A scientist is associated with one staff record with no lab
        assert len(results) == 1
        staff, lab_name = results[0]
        
        assert staff.user_id == test_staff_user.user_id
        assert staff.role == UserRole.SCIENTIST
        assert staff.lab_id is None
        assert lab_name is None

    def test_get_staff_with_lab_names_by_user_id_multiple_roles(self, db: Session, test_staff_multiple_roles, test_lab):
        """Test getting staff with lab names for a user with multiple lab roles"""
        user_id = str(test_staff_multiple_roles[0].user_id)
        results = get_staff_with_lab_names_by_user_id(db, user_id)
        
        assert len(results) == len(test_staff_multiple_roles)
        
        # Create a map of roles to lab names from the results for easy verification
        role_to_lab_name_map = {staff.role: lab_name for staff, lab_name in results}
        
        # Verify the lab admin role
        assert UserRole.LAB_ADMIN in role_to_lab_name_map
        assert role_to_lab_name_map[UserRole.LAB_ADMIN] == test_lab.lab_name
        
        # Verify the scientist role
        assert UserRole.SCIENTIST in role_to_lab_name_map
        assert role_to_lab_name_map[UserRole.SCIENTIST] is None

    #####################
    # get_combination TESTS
    #####################
    def test_get_combination_not_found(self, db: Session):
        """Test getting staff by a non-existent user ID and lab ID combination"""
        staff = get_combination(db, str(uuid.uuid4()), str(uuid.uuid4()), UserRole.LAB_ADMIN)
        assert len(staff) == 0

    def test_get_combination_found(self, db: Session, test_staff_admin):
        """Test getting staff by an existing user ID and lab ID combination"""
        staff = get_combination(
            db, 
            str(test_staff_admin.user_id), 
            str(test_staff_admin.lab_id),
            test_staff_admin.role
        )
        
        assert len(staff) == 1
        assert staff[0].user_id == test_staff_admin.user_id
        assert staff[0].lab_id == test_staff_admin.lab_id
        assert staff[0].role == test_staff_admin.role

    #####################
    # get_users_in_lab TESTS
    #####################
    def test_get_users_in_lab_not_found(self, db: Session):
        """Test getting users in a non-existent lab"""
        staff = get_users_in_lab(db, str(uuid.uuid4()))
        assert len(staff) == 0

    def test_get_users_in_lab_found(self, db: Session, test_staff_admin, test_admin):
        staff = get_users_in_lab(db, test_staff_admin.lab_id)
        
        assert staff[0][0].user_id == test_staff_admin.user_id
        assert staff[0][1] == test_staff_admin.role
        
        # Use UserVerifier directly on the user part of the result
        user = staff[0][0]
        UserVerifier.verify_common_fields(user.__dict__)
        UserVerifier.verify_response(
            user.__dict__,
            {
                "user_id": test_admin.user_id,
                "email": test_admin.email
            }
        )
    
    #####################
    # create_staff TESTS
    #####################

    def test_create_staff_nonexisting_lab(self, db: Session, test_admin):
        new_staff = StaffCreate(
            user_id= test_admin.user_id,
            role= UserRole.LAB_ADMIN
        )
        with pytest.raises(IntegrityError):
            staff = create_staff(db, new_staff, str(uuid.uuid4()))
    
    def test_create_staff_combo_found(self, db: Session, test_staff_admin):
        new_staff = StaffCreate(
            user_id= test_staff_admin.user_id,
            role= test_staff_admin.role
        )
        with pytest.raises(ValueError, match=ERROR_MESSAGES["staff"]["combo_exists"]):
            staff = create_staff(db, new_staff, test_staff_admin.lab_id)
    

    def test_create_staff_scientist(self, db: Session, test_staff_admin):
        new_staff = StaffCreate(
            user_id= test_staff_admin.user_id,
            role= "scientist"
        )
        with pytest.raises(ValueError, match=ERROR_MESSAGES["staff"]["scientist_lab"]):
            staff = create_staff(db, new_staff, test_staff_admin.lab_id)
    
    def test_create_staff_db_error(self, db: Session, test_staff_admin):
        new_staff = StaffCreate(
            user_id= test_staff_admin.user_id,
            role= "lab_personnel"
        )
        #Overrides db with error:
        db.add = MagicMock(side_effect=SQLAlchemyError(ERROR_MESSAGES["staff"]["server_error"]))

        with pytest.raises(SQLAlchemyError, match=ERROR_MESSAGES["staff"]["server_error"] ):
            staff = create_staff(db, new_staff, test_staff_admin.lab_id)
    
    def test_create_staff_success(self, db: Session, test_staff_admin):
        new_staff = StaffCreate(
            user_id= test_staff_admin.user_id,
            role= "lab_personnel"
        )
        staff = create_staff(db, new_staff, test_staff_admin.lab_id)

        assert staff.user_id == new_staff.user_id
        assert staff.lab_id == test_staff_admin.lab_id
        assert staff.role == "lab_personnel"
    
    #####################
    # edit_staff TESTS
    #####################
    def test_edit_staff_scientist(self, db: Session, test_staff_admin):
        new_staff = StaffType(
            user_id= str(test_staff_admin.user_id),
            lab_id= str(test_staff_admin.lab_id),
            staff_id= str(test_staff_admin.staff_id),
            role= UserRole.SCIENTIST
        )
        with pytest.raises(ValueError, match=ERROR_MESSAGES["staff"]["scientist_change"]):
            staff = edit_staff(db, new_staff)
    
    def test_edit_staff_combo_exists(self, db: Session, test_staff_admin):
        new_staff = StaffType(
            user_id= str(test_staff_admin.user_id),
            lab_id= str(test_staff_admin.lab_id),
            staff_id= str(test_staff_admin.staff_id),
            role= UserRole.LAB_ADMIN
        )
        with pytest.raises(ValueError, match=ERROR_MESSAGES["staff"]["combo_exists"]):
            staff = edit_staff(db, new_staff)
    
    def test_edit_staff_staff_id(self, db: Session, test_staff_admin):
        new_staff = StaffType(
            user_id= str(test_staff_admin.user_id),
            lab_id= str(test_staff_admin.lab_id),
            staff_id= str(uuid.uuid4()),
            role= UserRole.LAB_PERSONNEL
        )

        with pytest.raises(ValueError, match=ERROR_MESSAGES["staff"]["not_found"]):
            staff = edit_staff(db, new_staff)
    
    def test_edit_staff_user_id_match(self, db: Session, test_staff_admin, test_staff_lab_personal):
        new_staff = StaffType(
            user_id= str(test_staff_lab_personal.user_id),
            lab_id= str(test_staff_lab_personal.lab_id),
            staff_id= str(test_staff_admin.staff_id),
            role= UserRole.LAB_ADMIN
        )

        with pytest.raises(ValueError, match=ERROR_MESSAGES["staff"]["not_found"]):
            staff = edit_staff(db, new_staff)
    
    def test_edit_staff_lab_id_match(self, db: Session, test_staff_admin, test_labs):
        new_staff = StaffType(
            user_id= str(test_staff_admin.user_id),
            lab_id= str(test_labs[1].lab_id),
            staff_id= str(test_staff_admin.staff_id),
            role= UserRole.LAB_ADMIN
        )

        with pytest.raises(ValueError, match=ERROR_MESSAGES["staff"]["not_found"]):
            staff = edit_staff(db, new_staff)
    
    def test_edit_staff_no_lab_admins_left(self, db: Session, test_staff_admin):
        new_staff = StaffType(
            user_id= str(test_staff_admin.user_id),
            lab_id= str(test_staff_admin.lab_id),
            staff_id= str(test_staff_admin.staff_id),
            role= UserRole.LAB_PERSONNEL
        )

        with pytest.raises(ValueError, match=ERROR_MESSAGES["staff"]["last_admin"]):
            staff = edit_staff(db, new_staff)
    
    def test_edit_staff_db_error(self, db: Session, test_staff_admin, test_staff_multiple_admins):
        admin_staff = test_staff_multiple_admins[0]
        new_staff = StaffType(
            user_id= str(admin_staff.user_id),
            lab_id= str(admin_staff.lab_id),
            staff_id= str(admin_staff.staff_id),
            role= UserRole.LAB_PERSONNEL
        )
        #Overrides db with error:
        db.refresh = MagicMock(side_effect=SQLAlchemyError(ERROR_MESSAGES["staff"]["server_error"]))

        with pytest.raises(SQLAlchemyError, match=ERROR_MESSAGES["staff"]["server_error"] ):
            staff = edit_staff(db, new_staff)
    
    def test_edit_staff_admin_to_personnel(self, db: Session, test_staff_admin, test_staff_multiple_admins):
        admin_staff = test_staff_multiple_admins[0]
        new_staff = StaffType(
            user_id= str(admin_staff.user_id),
            lab_id= str(admin_staff.lab_id),
            staff_id= str(admin_staff.staff_id),
            role= UserRole.LAB_PERSONNEL
        )
        
        staff = edit_staff(db, new_staff)

        assert staff.role == UserRole.LAB_PERSONNEL
        assert staff.user_id == admin_staff.user_id
        assert staff.lab_id == admin_staff.lab_id
        assert staff.staff_id == admin_staff.staff_id
    
    def test_edit_staff_personnel_to_admin(self, db: Session, test_staff_lab_personal, test_staff_admin):
        new_staff = StaffType(
            user_id= str(test_staff_lab_personal.user_id),
            lab_id= str(test_staff_lab_personal.lab_id),
            staff_id= str(test_staff_lab_personal.staff_id),
            role= UserRole.LAB_ADMIN
        )
        
        staff = edit_staff(db, new_staff)

        assert staff.role == UserRole.LAB_ADMIN
        assert staff.user_id == test_staff_lab_personal.user_id
        assert staff.lab_id == test_staff_lab_personal.lab_id
        assert staff.staff_id == test_staff_lab_personal.staff_id
    
    #####################
    # delete_staff TESTS
    #####################
    def test_delete_staff_record_not_exist(self, db: Session, test_staff_lab_personal, test_staff_admin):
        with pytest.raises(ValueError, match=ERROR_MESSAGES["staff"]["combo_not_exists"]):
            message = delete_staff(db, str(uuid.uuid4()))
    
    def test_delete_staff_last_admin(self, db: Session, test_staff_lab_personal, test_staff_admin):
        with pytest.raises(ValueError, match=ERROR_MESSAGES["staff"]["last_admin"]):
            message = delete_staff(db, test_staff_admin.staff_id)
    
    def test_delete_staff_db_error(self, db: Session, test_staff_lab_personal, test_staff_admin):
        #Overrides db with error:
        db.commit = MagicMock(side_effect=SQLAlchemyError(ERROR_MESSAGES["staff"]["server_error"]))

        with pytest.raises(SQLAlchemyError, match=ERROR_MESSAGES["staff"]["server_error"] ):
            message = delete_staff(db, test_staff_lab_personal.staff_id)
    
    def test_delete_staff_success(self, db: Session, test_staff_lab_personal, test_staff_admin):
        message = delete_staff(db, test_staff_lab_personal.staff_id)
        
        assert message["message"] == "Staff member deleted successfully"
        still_exist = get_combination(db, test_staff_lab_personal.user_id, test_staff_lab_personal.lab_id, test_staff_lab_personal.role)
        assert still_exist == []
        