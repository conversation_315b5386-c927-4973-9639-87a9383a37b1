/*
 * Test Type Table
 * Displaying test type information
 */
import { Table } from '../../core/components/table.js';
import { createTableConfig } from '../../core/helpers/table-helpers.js';
import { TestApi } from '../../core/services/test-api.js';
import { TEST_CONFIGS } from '../../core/config/test-configs.js';

class TestTypeTable extends Table {
    constructor(options = {}) {
        super(createTableConfig(TEST_CONFIGS, 'testTypeTable', 'test', options));
    }

    // Fetch test type data from API with error handling
    async fetchData() {
        try {
            const testTypeData = await TestApi.listTestTypes();
            return testTypeData || [];
        } catch (error) {
            console.error('Failed to fetch test types:', error);
            throw error;
        }
    }
}

// Factory function and default instance
export const createTestTypeTable = (options = {}) => new TestTypeTable(options);
export const testTypeTable = createTestTypeTable();
