!function(a){a.color={},a.color.make=function(t,e,i,o){var n={};return n.r=t||0,n.g=e||0,n.b=i||0,n.a=null!=o?o:1,n.add=function(t,e){for(var i=0;i<t.length;++i)n[t.charAt(i)]+=e;return n.normalize()},n.scale=function(t,e){for(var i=0;i<t.length;++i)n[t.charAt(i)]*=e;return n.normalize()},n.toString=function(){return 1<=n.a?"rgb("+[n.r,n.g,n.b].join(",")+")":"rgba("+[n.r,n.g,n.b,n.a].join(",")+")"},n.normalize=function(){function t(t,e,i){return e<t?t:i<e?i:e}return n.r=t(0,parseInt(n.r),255),n.g=t(0,parseInt(n.g),255),n.b=t(0,parseInt(n.b),255),n.a=t(0,n.a,1),n},n.clone=function(){return a.color.make(n.r,n.b,n.g,n.a)},n.normalize()},a.color.extract=function(t,e){for(var i;(""==(i=t.css(e).toLowerCase())||"transparent"==i)&&(t=t.parent()).length&&!a.nodeName(t.get(0),"body"););return a.color.parse(i="rgba(0, 0, 0, 0)"==i?"transparent":i)},a.color.parse=function(t){var e,i=a.color.make;return(e=/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(t))?i(parseInt(e[1],10),parseInt(e[2],10),parseInt(e[3],10)):(e=/rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]+(?:\.[0-9]+)?)\s*\)/.exec(t))?i(parseInt(e[1],10),parseInt(e[2],10),parseInt(e[3],10),parseFloat(e[4])):(e=/rgb\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*\)/.exec(t))?i(2.55*parseFloat(e[1]),2.55*parseFloat(e[2]),2.55*parseFloat(e[3])):(e=/rgba\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\s*\)/.exec(t))?i(2.55*parseFloat(e[1]),2.55*parseFloat(e[2]),2.55*parseFloat(e[3]),parseFloat(e[4])):(e=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(t))?i(parseInt(e[1],16),parseInt(e[2],16),parseInt(e[3],16)):(e=/#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])/.exec(t))?i(parseInt(e[1]+e[1],16),parseInt(e[2]+e[2],16),parseInt(e[3]+e[3],16)):"transparent"==(t=a.trim(t).toLowerCase())?i(255,255,255,0):i((e=o[t]||[0,0,0])[0],e[1],e[2])};var o={aqua:[0,255,255],azure:[240,255,255],beige:[245,245,220],black:[0,0,0],blue:[0,0,255],brown:[165,42,42],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgrey:[169,169,169],darkgreen:[0,100,0],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkviolet:[148,0,211],fuchsia:[255,0,255],gold:[255,215,0],green:[0,128,0],indigo:[75,0,130],khaki:[240,230,140],lightblue:[173,216,230],lightcyan:[224,255,255],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightyellow:[255,255,224],lime:[0,255,0],magenta:[255,0,255],maroon:[128,0,0],navy:[0,0,128],olive:[128,128,0],orange:[255,165,0],pink:[255,192,203],purple:[128,0,128],violet:[128,0,128],red:[255,0,0],silver:[192,192,192],white:[255,255,255],yellow:[255,255,0]}}(jQuery),function($){var u=Object.prototype.hasOwnProperty;function K(t,e){var i=e.children("."+t)[0];if(null==i&&((i=document.createElement("canvas")).className=t,$(i).css({direction:"ltr",position:"absolute",left:0,top:0}).appendTo(e),!i.getContext)){if(!window.G_vmlCanvasManager)throw new Error("Canvas is not available. If you're using IE with a fall-back such as Excanvas, then there's either a mistake in your conditional include, or the page has no DOCTYPE and is rendering in Quirks Mode.");i=window.G_vmlCanvasManager.initElement(i)}this.element=i;t=this.context=i.getContext("2d"),i=window.devicePixelRatio||1,t=t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;this.pixelRatio=i/t,this.resize(e.width(),e.height()),this.textContainer=null,this.text={},this._textCache={}}function o(y,W,t,e){var H=[],j={colors:["#edc240","#afd8f8","#cb4b4b","#4da74d","#9440ed"],legend:{show:!0,noColumns:1,labelFormatter:null,labelBoxBorderColor:"#ccc",container:null,position:"ne",margin:5,backgroundColor:null,backgroundOpacity:.85,sorted:null},xaxis:{show:null,position:"bottom",mode:null,font:null,color:null,tickColor:null,transform:null,inverseTransform:null,min:null,max:null,autoscaleMargin:null,ticks:null,tickFormatter:null,labelWidth:null,labelHeight:null,reserveSpace:null,tickLength:null,alignTicksWithAxis:null,tickDecimals:null,tickSize:null,minTickSize:null},yaxis:{autoscaleMargin:.02,position:"left"},xaxes:[],yaxes:[],series:{points:{show:!1,radius:3,lineWidth:2,fill:!0,fillColor:"#ffffff",symbol:"circle"},lines:{lineWidth:2,fill:!1,fillColor:null,steps:!1},bars:{show:!1,lineWidth:2,barWidth:1,fill:!0,fillColor:null,align:"left",horizontal:!1,zero:!0},shadowSize:3,highlightColor:null},grid:{show:!0,aboveData:!1,color:"#545454",backgroundColor:null,borderColor:null,tickColor:null,margin:0,labelMargin:5,axisMargin:8,borderWidth:2,minBorderMargin:null,markings:null,markingsColor:"#f4f4f4",markingsLineWidth:2,clickable:!1,hoverable:!1,autoHighlight:!0,mouseActiveRadius:10},interaction:{redrawOverlayInterval:1e3/60},hooks:{}},C=null,s=null,h=null,k=null,c=null,E=[],B=[],w={left:0,right:0,top:0,bottom:0},M=0,T=0,G={processOptions:[],processRawData:[],processDatapoints:[],processOffset:[],drawBackground:[],drawSeries:[],draw:[],bindEvents:[],drawOverlay:[],shutdown:[]},S=this;S.setData=f,S.setupGrid=I,S.draw=A,S.getPlaceholder=function(){return y},S.getCanvas=function(){return C.element},S.getPlotOffset=function(){return w},S.width=function(){return M},S.height=function(){return T},S.offset=function(){var t=h.offset();return t.left+=w.left,t.top+=w.top,t},S.getData=function(){return H},S.getAxes=function(){var i={};return $.each(E.concat(B),function(t,e){e&&(i[e.direction+(1!=e.n?e.n:"")+"axis"]=e)}),i},S.getXAxes=function(){return E},S.getYAxes=function(){return B},S.c2p=u,S.p2c=function(t){var e,i,o,n={};for(e=0;e<E.length;++e)if((i=E[e])&&i.used&&(o="x"+i.n,null==t[o]&&1==i.n&&(o="x"),null!=t[o])){n.left=i.p2c(t[o]);break}for(e=0;e<B.length;++e)if((i=B[e])&&i.used&&(o="y"+i.n,null==t[o]&&1==i.n&&(o="y"),null!=t[o])){n.top=i.p2c(t[o]);break}return n},S.getOptions=function(){return j},S.highlight=q,S.unhighlight=Q,S.triggerRedrawOverlay=v,S.pointOffset=function(t){return{left:parseInt(E[V(t,"x")-1].p2c(+t.x)+w.left,10),top:parseInt(B[V(t,"y")-1].p2c(+t.y)+w.top,10)}},S.shutdown=p,S.destroy=function(){p(),y.removeData("plot").empty(),H=[],E=[],B=[],x=[],S=G=c=k=h=s=C=j=null},S.resize=function(){var t=y.width(),e=y.height();C.resize(t,e),s.resize(t,e)},S.hooks=G;for(var z={Canvas:K},i=0;i<e.length;++i){var o=e[i];o.init(S,z),o.options&&$.extend(!0,j,o.options)}$.extend(!0,j,t),t&&t.colors&&(j.colors=t.colors),null==j.xaxis.color&&(j.xaxis.color=$.color.parse(j.grid.color).scale("a",.22).toString()),null==j.yaxis.color&&(j.yaxis.color=$.color.parse(j.grid.color).scale("a",.22).toString()),null==j.xaxis.tickColor&&(j.xaxis.tickColor=j.grid.tickColor||j.xaxis.color),null==j.yaxis.tickColor&&(j.yaxis.tickColor=j.grid.tickColor||j.yaxis.color),null==j.grid.borderColor&&(j.grid.borderColor=j.grid.color),null==j.grid.tickColor&&(j.grid.tickColor=$.color.parse(j.grid.color).scale("a",.22).toString());var n,a,r,l,t=(t=y.css("font-size"))?+t.replace("px",""):13,d={style:y.css("font-style"),size:Math.round(.8*t),variant:y.css("font-variant"),weight:y.css("font-weight"),family:y.css("font-family")};for(r=j.xaxes.length||1,n=0;n<r;++n)(a=j.xaxes[n])&&!a.tickColor&&(a.tickColor=a.color),a=$.extend(!0,{},j.xaxis,a),(j.xaxes[n]=a).font&&(a.font=$.extend({},d,a.font),a.font.color||(a.font.color=a.color),a.font.lineHeight||(a.font.lineHeight=Math.round(1.15*a.font.size)));for(r=j.yaxes.length||1,n=0;n<r;++n)(a=j.yaxes[n])&&!a.tickColor&&(a.tickColor=a.color),a=$.extend(!0,{},j.yaxis,a),(j.yaxes[n]=a).font&&(a.font=$.extend({},d,a.font),a.font.color||(a.font.color=a.color),a.font.lineHeight||(a.font.lineHeight=Math.round(1.15*a.font.size)));for(j.xaxis.noTicks&&null==j.xaxis.ticks&&(j.xaxis.ticks=j.xaxis.noTicks),j.yaxis.noTicks&&null==j.yaxis.ticks&&(j.yaxis.ticks=j.yaxis.noTicks),j.x2axis&&(j.xaxes[1]=$.extend(!0,{},j.xaxis,j.x2axis),j.xaxes[1].position="top",null==j.x2axis.min&&(j.xaxes[1].min=null),null==j.x2axis.max)&&(j.xaxes[1].max=null),j.y2axis&&(j.yaxes[1]=$.extend(!0,{},j.yaxis,j.y2axis),j.yaxes[1].position="right",null==j.y2axis.min&&(j.yaxes[1].min=null),null==j.y2axis.max)&&(j.yaxes[1].max=null),j.grid.coloredAreas&&(j.grid.markings=j.grid.coloredAreas),j.grid.coloredAreasColor&&(j.grid.markingsColor=j.grid.coloredAreasColor),j.lines&&$.extend(!0,j.series.lines,j.lines),j.points&&$.extend(!0,j.series.points,j.points),j.bars&&$.extend(!0,j.series.bars,j.bars),null!=j.shadowSize&&(j.series.shadowSize=j.shadowSize),null!=j.highlightColor&&(j.series.highlightColor=j.highlightColor),n=0;n<j.xaxes.length;++n)Y(E,n+1).options=j.xaxes[n];for(n=0;n<j.yaxes.length;++n)Y(B,n+1).options=j.yaxes[n];for(l in G)j.hooks[l]&&j.hooks[l].length&&(G[l]=G[l].concat(j.hooks[l]));_(G.processOptions,[j]),y.css("padding",0).children().filter(function(){return!$(this).hasClass("flot-overlay")&&!$(this).hasClass("flot-base")}).remove(),"static"==y.css("position")&&y.css("position","relative"),C=new K("flot-base",y),s=new K("flot-overlay",y),k=C.context,c=s.context,h=$(s.element).unbind();t=y.data("plot");function _(t,e){e=[S].concat(e);for(var i=0;i<t.length;++i)t[i].apply(this,e)}function f(O){var t,e=(H=function(t){for(var e=[],i=0;i<t.length;++i){var o=$.extend(!0,{},j.series);null!=t[i].data?(o.data=t[i].data,delete t[i].data,$.extend(!0,o,t[i]),t[i].data=o.data):o.data=t[i],e.push(o)}return e}(O)).length,i=-1;for(t=0;t<H.length;++t){var o=H[t].color;null!=o&&(e--,"number"==typeof o)&&i<o&&(i=o)}e<=i&&(e=i+1);var n,a=[],r=j.colors,l=r.length,s=0;for(t=0;t<e;t++)n=$.color.parse(r[t%l]||"#666"),t%l==0&&t&&(s=0<=s?s<.5?-s-.2:0:-s),a[t]=n.scale("rgb",1+s);var c,h=0;for(t=0;t<H.length;++t){if(null==(c=H[t]).color?(c.color=a[h].toString(),++h):"number"==typeof c.color&&(c.color=a[c.color].toString()),null==c.lines.show){var d,f=!0;for(d in c)if(c[d]&&c[d].show){f=!1;break}f&&(c.lines.show=!0)}null==c.lines.zero&&(c.lines.zero=!!c.lines.fill),c.xaxis=Y(E,V(c,"x")),c.yaxis=Y(B,V(c,"y"))}var u,p,m,x,g,b,v,k,y,w,M,T,C,S,W=Number.POSITIVE_INFINITY,z=Number.NEGATIVE_INFINITY,I=Number.MAX_VALUE;function A(t,e,i){e<t.datamin&&e!=-I&&(t.datamin=e),i>t.datamax&&i!=I&&(t.datamax=i)}for($.each(X(),function(t,e){e.datamin=W,e.datamax=z,e.used=!1}),u=0;u<H.length;++u)(g=H[u]).datapoints={points:[]},_(G.processRawData,[g,g.data,g.datapoints]);for(u=0;u<H.length;++u)if(g=H[u],M=g.data,(T=g.datapoints.format)||((T=[]).push({x:!0,number:!0,required:!0}),T.push({y:!0,number:!0,required:!0}),(g.bars.show||g.lines.show&&g.lines.fill)&&(S=!!(g.bars.show&&g.bars.zero||g.lines.show&&g.lines.zero),T.push({y:!0,number:!0,required:!1,defaultValue:0,autoscale:S}),g.bars.horizontal)&&(delete T[T.length-1].y,T[T.length-1].x=!0),g.datapoints.format=T),null==g.datapoints.pointsize){g.datapoints.pointsize=T.length,v=g.datapoints.pointsize,b=g.datapoints.points;var R=g.lines.show&&g.lines.steps;for(g.xaxis.used=g.yaxis.used=!0,p=m=0;p<M.length;++p,m+=v){var F=null==(w=M[p]);if(!F)for(x=0;x<v;++x)k=w[x],(y=T[x])&&(y.number&&null!=k&&(k=+k,isNaN(k)?k=null:k==1/0?k=I:k==-1/0&&(k=-I)),null==k)&&(y.required&&(F=!0),null!=y.defaultValue)&&(k=y.defaultValue),b[m+x]=k;if(F)for(x=0;x<v;++x)null!=(k=b[m+x])&&!1!==(y=T[x]).autoscale&&(y.x&&A(g.xaxis,k,k),y.y)&&A(g.yaxis,k,k),b[m+x]=null;else if(R&&0<m&&null!=b[m-v]&&b[m-v]!=b[m]&&b[m-v+1]!=b[m+1]){for(x=0;x<v;++x)b[m+v+x]=b[m+x];b[m+1]=b[m-v+1],m+=v}}}for(u=0;u<H.length;++u)g=H[u],_(G.processDatapoints,[g,g.datapoints]);for(u=0;u<H.length;++u){g=H[u],b=g.datapoints.points,v=g.datapoints.pointsize,T=g.datapoints.format;var P=W,N=W,D=z,L=z;for(p=0;p<b.length;p+=v)if(null!=b[p])for(x=0;x<v;++x)k=b[p+x],(y=T[x])&&!1!==y.autoscale&&k!=I&&k!=-I&&(y.x&&(k<P&&(P=k),D<k)&&(D=k),y.y)&&(k<N&&(N=k),L<k)&&(L=k);if(g.bars.show){switch(g.bars.align){case"left":C=0;break;case"right":C=-g.bars.barWidth;break;default:C=-g.bars.barWidth/2}g.bars.horizontal?(N+=C,L+=C+g.bars.barWidth):(P+=C,D+=C+g.bars.barWidth)}A(g.xaxis,P,D),A(g.yaxis,N,L)}$.each(X(),function(t,e){e.datamin==W&&(e.datamin=null),e.datamax==z&&(e.datamax=null)})}function V(t,e){t=t[e+"axis"];return t="number"!=typeof(t="object"==typeof t?t.n:t)?1:t}function X(){return $.grep(E.concat(B),function(t){return t})}function u(t){for(var e,i={},o=0;o<E.length;++o)(e=E[o])&&e.used&&(i["x"+e.n]=e.c2p(t.left));for(o=0;o<B.length;++o)(e=B[o])&&e.used&&(i["y"+e.n]=e.c2p(t.top));return void 0!==i.x1&&(i.x=i.x1),void 0!==i.y1&&(i.y=i.y1),i}function Y(t,e){return t[e-1]||(t[e-1]={n:e,direction:t==E?"x":"y",options:$.extend(!0,{},t==E?j.xaxis:j.yaxis)}),t[e-1]}function p(){g&&clearTimeout(g),h.unbind("mousemove",D),h.unbind("mouseleave",L),h.unbind("click",O),_(G.shutdown,[h])}function I(){var t,e,i=X(),o=j.grid.show;for(e in w){var n=j.grid.margin||0;w[e]="number"==typeof n?n:n[e]||0}for(e in _(G.processOffset,[w]),w)"object"==typeof j.grid.borderWidth?w[e]+=o?j.grid.borderWidth[e]:0:w[e]+=o?j.grid.borderWidth:0;if($.each(i,function(t,e){var i,o=e.options,o=(e.show=null==o.show?e.used:o.show,e.reserveSpace=null==o.reserveSpace?e.show:o.reserveSpace,e),e=o.options,n=+(null!=e.min?e.min:o.datamin),a=+(null!=e.max?e.max:o.datamax),r=a-n;0==r?(i=0==a?1:.01,null==e.min&&(n-=i),null!=e.max&&null==e.min||(a+=i)):null!=(i=e.autoscaleMargin)&&(null==e.min&&(n-=r*i)<0&&null!=o.datamin&&0<=o.datamin&&(n=0),null==e.max)&&0<(a+=r*i)&&null!=o.datamax&&o.datamax<=0&&(a=0),o.min=n,o.max=a}),o){var a=$.grep(i,function(t){return t.show||t.reserveSpace});for($.each(a,function(t,e){var i=e,o=i.options,n=(n="number"==typeof o.ticks&&0<o.ticks?o.ticks:.3*Math.sqrt("x"==i.direction?C.width:C.height),(i.max-i.min)/n),a=-Math.floor(Math.log(n)/Math.LN10),r=o.tickDecimals;null!=r&&r<a&&(a=r);var l,s=Math.pow(10,-a),c=n/s;if(c<1.5?f=1:c<3?(f=2,2.25<c&&(null==r||a+1<=r)&&(f=2.5,++a)):f=c<7.5?5:10,f*=s,null!=o.minTickSize&&f<o.minTickSize&&(f=o.minTickSize),i.delta=n,i.tickDecimals=Math.max(0,null!=r?r:a),i.tickSize=o.tickSize||f,"time"==o.mode&&!i.tickGenerator)throw new Error("Time mode requires the flot.time plugin.");i.tickGenerator||(i.tickGenerator=function(t){for(var e,i,o,n=[],a=(i=t.min,(o=t.tickSize)*Math.floor(i/o)),r=0,l=Number.NaN;e=l,l=a+r*t.tickSize,n.push(l),++r,l<t.max&&l!=e;);return n},i.tickFormatter=function(t,e){var i=e.tickDecimals?Math.pow(10,e.tickDecimals):1,t=""+Math.round(t*i)/i;if(null!=e.tickDecimals){var o=t.indexOf("."),o=-1==o?0:t.length-o-1;if(o<e.tickDecimals)return(o?t:t+".")+(""+i).substr(1,e.tickDecimals-o)}return t}),$.isFunction(o.tickFormatter)&&(i.tickFormatter=function(t,e){return""+o.tickFormatter(t,e)}),null!=o.alignTicksWithAxis&&(l=("x"==i.direction?E:B)[o.alignTicksWithAxis-1])&&l.used&&l!=i&&(0<(c=i.tickGenerator(i)).length&&(null==o.min&&(i.min=Math.min(i.min,c[0])),null==o.max)&&1<c.length&&(i.max=Math.max(i.max,c[c.length-1])),i.tickGenerator=function(t){for(var e,i=[],o=0;o<l.ticks.length;++o)e=(l.ticks[o].v-l.min)/(l.max-l.min),e=t.min+e*(t.max-t.min),i.push(e);return i},i.mode||null!=o.tickDecimals||(s=Math.max(0,1-Math.floor(Math.log(i.delta)/Math.LN10)),1<(n=i.tickGenerator(i)).length&&/\..*0$/.test((n[1]-n[0]).toFixed(s)))||(i.tickDecimals=s));var h,d,f,u=e,r=u.options.ticks,p=[];for(null==r||"number"==typeof r&&0<r?p=u.tickGenerator(u):r&&(p=$.isFunction(r)?r(u):r),u.ticks=[],h=0;h<p.length;++h){var m=null,x=p[h];"object"==typeof x?(d=+x[0],1<x.length&&(m=x[1])):d=+x,null==m&&(m=u.tickFormatter(d,u)),isNaN(d)||u.ticks.push({v:d,label:m})}f=(a=e).ticks,a.options.autoscaleMargin&&0<f.length&&(null==a.options.min&&(a.min=Math.min(a.min,f[0].v)),null==a.options.max)&&1<f.length&&(a.max=Math.max(a.max,f[f.length-1].v));for(var c=e,n=c.options,g=c.ticks||[],b=n.labelWidth||0,v=n.labelHeight||0,k=b||("x"==c.direction?Math.floor(C.width/(g.length||1)):null),i=c.direction+"Axis "+c.direction+c.n+"Axis",y="flot-"+c.direction+"-axis flot-"+c.direction+c.n+"-axis "+i,w=n.font||"flot-tick-label tickLabel",M=0;M<g.length;++M){var T=g[M];T.label&&(T=C.getTextInfo(y,T.label,w,null,k),b=Math.max(b,T.width),v=Math.max(v,T.height))}c.labelWidth=n.labelWidth||b,c.labelHeight=n.labelHeight||v}),t=a.length-1;0<=t;--t)!function(i){var t=i.labelWidth,e=i.labelHeight,o=i.options.position,n="x"===i.direction,a=i.options.tickLength,r=j.grid.axisMargin,l=j.grid.labelMargin,s=!0,c=!0,h=!0,d=!1;$.each(n?E:B,function(t,e){e&&(e.show||e.reserveSpace)&&(e===i?d=!0:e.options.position===o&&(d?c=!1:s=!1),d||(h=!1))}),c&&(r=0),null==a&&(a=h?"full":5),isNaN(+a)||(l+=+a),n?(e+=l,"bottom"==o?(w.bottom+=e+r,i.box={top:C.height-w.bottom,height:e}):(i.box={top:w.top+r,height:e},w.top+=e+r)):(t+=l,"left"==o?(i.box={left:w.left+r,width:t},w.left+=t+r):(w.right+=t+r,i.box={left:C.width-w.right,width:t})),i.position=o,i.tickLength=a,i.box.padding=l,i.innermost=s}(a[t]);var r,l=j.grid.minBorderMargin;if(null==l)for(r=l=0;r<H.length;++r)l=Math.max(l,2*(H[r].points.radius+H[r].points.lineWidth/2));var s={left:l,right:l,top:l,bottom:l};$.each(X(),function(t,e){e.reserveSpace&&e.ticks&&e.ticks.length&&("x"===e.direction?(s.left=Math.max(s.left,e.labelWidth/2),s.right=Math.max(s.right,e.labelWidth/2)):(s.bottom=Math.max(s.bottom,e.labelHeight/2),s.top=Math.max(s.top,e.labelHeight/2)))}),w.left=Math.ceil(Math.max(s.left,w.left)),w.right=Math.ceil(Math.max(s.right,w.right)),w.top=Math.ceil(Math.max(s.top,w.top)),w.bottom=Math.ceil(Math.max(s.bottom,w.bottom)),$.each(a,function(t,e){"x"==(e=e).direction?(e.box.left=w.left-e.labelWidth/2,e.box.width=C.width-w.left-w.right+e.labelWidth):(e.box.top=w.top-e.labelHeight/2,e.box.height=C.height-w.bottom-w.top+e.labelHeight)})}if(M=C.width-w.left-w.right,T=C.height-w.bottom-w.top,$.each(i,function(t,e){function i(t){return t}var o,n,a,r;n=(e=e).options.transform||i,a=e.options.inverseTransform,r="x"==e.direction?(o=e.scale=M/Math.abs(n(e.max)-n(e.min)),Math.min(n(e.max),n(e.min))):(o=-(o=e.scale=T/Math.abs(n(e.max)-n(e.min))),Math.max(n(e.max),n(e.min))),e.p2c=n==i?function(t){return(t-r)*o}:function(t){return(n(t)-r)*o},e.c2p=a?function(t){return a(r+t/o)}:function(t){return r+t/o}}),o&&$.each(X(),function(t,e){var i,o,n,a,r,l=e.box,s=e.direction+"Axis "+e.direction+e.n+"Axis",c="flot-"+e.direction+"-axis flot-"+e.direction+e.n+"-axis "+s,h=e.options.font||"flot-tick-label tickLabel";if(C.removeText(c),e.show&&0!=e.ticks.length)for(var d=0;d<e.ticks.length;++d)!(i=e.ticks[d]).label||i.v<e.min||i.v>e.max||("x"==e.direction?(a="center",o=w.left+e.p2c(i.v),"bottom"==e.position?n=l.top+l.padding:(n=l.top+l.height-l.padding,r="bottom")):(r="middle",n=w.top+e.p2c(i.v),"left"==e.position?(o=l.left+l.width-l.padding,a="right"):o=l.left+l.padding),C.addText(c,o,n,i.label,h,null,null,a,r))}),null!=j.legend.container?$(j.legend.container).html(""):y.find(".legend").remove(),j.legend.show){for(var c,h,d,f=[],u=[],p=!1,m=j.legend.labelFormatter,x=0;x<H.length;++x)(c=H[x]).label&&(h=m?m(c.label,c):c.label)&&u.push({label:h,color:c.color});j.legend.sorted&&($.isFunction(j.legend.sorted)?u.sort(j.legend.sorted):"reverse"==j.legend.sorted?u.reverse():(d="descending"!=j.legend.sorted,u.sort(function(t,e){return t.label==e.label?0:t.label<e.label!=d?1:-1})));for(var g,b,v,x=0;x<u.length;++x){var k=u[x];x%j.legend.noColumns==0&&(p&&f.push("</tr>"),f.push("<tr>"),p=!0),f.push('<td class="legendColorBox"><div style="border:1px solid '+j.legend.labelBoxBorderColor+';padding:1px"><div style="width:4px;height:0;border:5px solid '+k.color+';overflow:hidden"></div></div></td><td class="legendLabel">'+k.label+"</td>")}p&&f.push("</tr>"),0!=f.length&&(i='<table style="font-size:smaller;color:'+j.grid.color+'">'+f.join("")+"</table>",null!=j.legend.container?$(j.legend.container).html(i):(g="",b=j.legend.position,null==(v=j.legend.margin)[0]&&(v=[v,v]),"n"==b.charAt(0)?g+="top:"+(v[1]+w.top)+"px;":"s"==b.charAt(0)&&(g+="bottom:"+(v[1]+w.bottom)+"px;"),"e"==b.charAt(1)?g+="right:"+(v[0]+w.right)+"px;":"w"==b.charAt(1)&&(g+="left:"+(v[0]+w.left)+"px;"),b=$('<div class="legend">'+i.replace('style="','style="position:absolute;'+g+";")+"</div>").appendTo(y),0!=j.legend.backgroundOpacity&&(null==(v=j.legend.backgroundColor)&&((v=(v=j.grid.backgroundColor)&&"string"==typeof v?$.color.parse(v):$.color.extract(b,"background-color")).a=1,v=v.toString()),i=b.children(),$('<div style="position:absolute;width:'+i.width()+"px;height:"+i.height()+"px;"+g+"background-color:"+v+';"> </div>').prependTo(b).css("opacity",j.legend.backgroundOpacity))))}}function A(){C.clear(),_(G.drawBackground,[k]);var t=j.grid;t.show&&t.backgroundColor&&(k.save(),k.translate(w.left,w.top),k.fillStyle=J(j.grid.backgroundColor,T,0,"rgba(255, 255, 255, 0)"),k.fillRect(0,0,M,T),k.restore()),t.show&&!t.aboveData&&P();for(var e=0;e<H.length;++e){_(G.drawSeries,[k,H[e]]),i=void 0;var i=H[e];if(i.lines.show){o=void 0;a=void 0;r=void 0;l=void 0;var o=i;var n=function(t,e,i,o,n){var a=t.points,r=t.pointsize,l=null,s=null;k.beginPath();for(var c=r;c<a.length;c+=r){var h=a[c-r],d=a[c-r+1],f=a[c],u=a[c+1];if(null!=h&&null!=f){if(d<=u&&d<n.min){if(u<n.min)continue;h=(n.min-d)/(u-d)*(f-h)+h,d=n.min}else if(u<=d&&u<n.min){if(d<n.min)continue;f=(n.min-d)/(u-d)*(f-h)+h,u=n.min}if(u<=d&&d>n.max){if(u>n.max)continue;h=(n.max-d)/(u-d)*(f-h)+h,d=n.max}else if(d<=u&&u>n.max){if(d>n.max)continue;f=(n.max-d)/(u-d)*(f-h)+h,u=n.max}if(h<=f&&h<o.min){if(f<o.min)continue;d=(o.min-h)/(f-h)*(u-d)+d,h=o.min}else if(f<=h&&f<o.min){if(h<o.min)continue;u=(o.min-h)/(f-h)*(u-d)+d,f=o.min}if(f<=h&&h>o.max){if(f>o.max)continue;d=(o.max-h)/(f-h)*(u-d)+d,h=o.max}else if(h<=f&&f>o.max){if(h>o.max)continue;u=(o.max-h)/(f-h)*(u-d)+d,f=o.max}h==l&&d==s||k.moveTo(o.p2c(h)+e,n.p2c(d)+i),l=f,s=u,k.lineTo(o.p2c(f)+e,n.p2c(u)+i)}}k.stroke()};k.save(),k.translate(w.left,w.top),k.lineJoin="round";var a=o.lines.lineWidth,r=o.shadowSize;0<a&&0<r&&(k.lineWidth=r,k.strokeStyle="rgba(0,0,0,0.1)",l=Math.PI/18,n(o.datapoints,Math.sin(l)*(a/2+r/2),Math.cos(l)*(a/2+r/2),o.xaxis,o.yaxis),k.lineWidth=r/2,n(o.datapoints,Math.sin(l)*(a/2+r/4),Math.cos(l)*(a/2+r/4),o.xaxis,o.yaxis));k.lineWidth=a,k.strokeStyle=o.color;var l=m(o.lines,o.color,0,T);l&&(k.fillStyle=l,function(t,e,i){for(var o=t.points,n=t.pointsize,a=Math.min(Math.max(0,i.min),i.max),r=0,l=!1,s=1,c=0,h=0;!(0<n&&r>o.length+n);){var d,f,u=o[(r+=n)-n],p=o[r-n+s],m=o[r],x=o[r+s];if(l){if(0<n&&null!=u&&null==m){h=r,n=-n,s=2;continue}if(n<0&&r==c+n){k.fill(),l=!1,s=1,r=c=h+(n=-n);continue}}if(null!=u&&null!=m){if(u<=m&&u<e.min){if(m<e.min)continue;p=(e.min-u)/(m-u)*(x-p)+p,u=e.min}else if(m<=u&&m<e.min){if(u<e.min)continue;x=(e.min-u)/(m-u)*(x-p)+p,m=e.min}if(m<=u&&u>e.max){if(m>e.max)continue;p=(e.max-u)/(m-u)*(x-p)+p,u=e.max}else if(u<=m&&m>e.max){if(u>e.max)continue;x=(e.max-u)/(m-u)*(x-p)+p,m=e.max}l||(k.beginPath(),k.moveTo(e.p2c(u),i.p2c(a)),l=!0),p>=i.max&&x>=i.max?(k.lineTo(e.p2c(u),i.p2c(i.max)),k.lineTo(e.p2c(m),i.p2c(i.max))):p<=i.min&&x<=i.min?(k.lineTo(e.p2c(u),i.p2c(i.min)),k.lineTo(e.p2c(m),i.p2c(i.min))):(d=u,f=m,p<=x&&p<i.min&&x>=i.min?(u=(i.min-p)/(x-p)*(m-u)+u,p=i.min):x<=p&&x<i.min&&p>=i.min&&(m=(i.min-p)/(x-p)*(m-u)+u,x=i.min),x<=p&&p>i.max&&x<=i.max?(u=(i.max-p)/(x-p)*(m-u)+u,p=i.max):p<=x&&x>i.max&&p<=i.max&&(m=(i.max-p)/(x-p)*(m-u)+u,x=i.max),u!=d&&k.lineTo(e.p2c(d),i.p2c(p)),k.lineTo(e.p2c(u),i.p2c(p)),k.lineTo(e.p2c(m),i.p2c(x)),m!=f&&(k.lineTo(e.p2c(m),i.p2c(x)),k.lineTo(e.p2c(f),i.p2c(x))))}}}(o.datapoints,o.xaxis,o.yaxis));0<a&&n(o.datapoints,0,0,o.xaxis,o.yaxis);k.restore()}if(i.bars.show&&!function(c){var t;switch(k.save(),k.translate(w.left,w.top),k.lineWidth=c.bars.lineWidth,k.strokeStyle=c.color,c.bars.align){case"left":t=0;break;case"right":t=-c.bars.barWidth;break;default:t=-c.bars.barWidth/2}var e=c.bars.fill?function(t,e){return m(c.bars,c.color,t,e)}:null;(function(t,e,i,o,n,a){for(var r=t.points,l=t.pointsize,s=0;s<r.length;s+=l)null!=r[s]&&N(r[s],r[s+1],r[s+2],e,i,o,n,a,k,c.bars.horizontal,c.bars.lineWidth)})(c.datapoints,t,t+c.bars.barWidth,e,c.xaxis,c.yaxis),k.restore()}(i),i.points.show){r=void 0;l=void 0;a=void 0;n=void 0;o=void 0;r=i;i=function(t,e,i,o,n,a,r,l){for(var s=t.points,c=t.pointsize,h=0;h<s.length;h+=c){var d=s[h],f=s[h+1];null==d||d<a.min||d>a.max||f<r.min||f>r.max||(k.beginPath(),d=a.p2c(d),f=r.p2c(f)+o,"circle"==l?k.arc(d,f,e,0,n?Math.PI:2*Math.PI,!1):l(k,d,f,e,n),k.closePath(),i&&(k.fillStyle=i,k.fill()),k.stroke())}};k.save(),k.translate(w.left,w.top);l=r.points.lineWidth,a=r.shadowSize,n=r.points.radius,o=r.points.symbol;0==l&&(l=1e-4);0<l&&0<a&&(a=a/2,k.lineWidth=a,k.strokeStyle="rgba(0,0,0,0.1)",i(r.datapoints,n,null,a+a/2,!0,r.xaxis,r.yaxis,o),k.strokeStyle="rgba(0,0,0,0.2)",i(r.datapoints,n,null,a/2,!0,r.xaxis,r.yaxis,o));k.lineWidth=l,k.strokeStyle=r.color,i(r.datapoints,n,m(r.points,r.color),0,!1,r.xaxis,r.yaxis,o),k.restore()}}_(G.draw,[k]),t.show&&t.aboveData&&P(),C.render(),v()}function F(t,e){for(var i,o,n,a,r,l=X(),s=0;s<l.length;++s)if((i=l[s]).direction==e&&t[o=t[o=e+i.n+"axis"]||1!=i.n?o:e+"axis"]){a=t[o].from,r=t[o].to;break}return t[o]||(i=("x"==e?E:B)[0],a=t[e+"1"],r=t[e+"2"]),null!=a&&null!=r&&r<a&&(n=a,a=r,r=n),{from:a,to:r,axis:i}}function P(){k.save(),k.translate(w.left,w.top);var t,e,i=j.grid.markings;if(i)for($.isFunction(i)&&((c=S.getAxes()).xmin=c.xaxis.min,c.xmax=c.xaxis.max,c.ymin=c.yaxis.min,c.ymax=c.yaxis.max,i=i(c)),t=0;t<i.length;++t){var o,n,a,r=i[t],l=F(r,"x"),s=F(r,"y");null==l.from&&(l.from=l.axis.min),null==l.to&&(l.to=l.axis.max),null==s.from&&(s.from=s.axis.min),null==s.to&&(s.to=s.axis.max),l.to<l.axis.min||l.from>l.axis.max||s.to<s.axis.min||s.from>s.axis.max||(l.from=Math.max(l.from,l.axis.min),l.to=Math.min(l.to,l.axis.max),s.from=Math.max(s.from,s.axis.min),s.to=Math.min(s.to,s.axis.max),o=l.from===l.to,n=s.from===s.to,o&&n)||(l.from=Math.floor(l.axis.p2c(l.from)),l.to=Math.floor(l.axis.p2c(l.to)),s.from=Math.floor(s.axis.p2c(s.from)),s.to=Math.floor(s.axis.p2c(s.to)),o||n?(a=(n=r.lineWidth||j.grid.markingsLineWidth)%2?.5:0,k.beginPath(),k.strokeStyle=r.color||j.grid.markingsColor,k.lineWidth=n,o?(k.moveTo(l.to+a,s.from),k.lineTo(l.to+a,s.to)):(k.moveTo(l.from,s.to+a),k.lineTo(l.to,s.to+a)),k.stroke()):(k.fillStyle=r.color||j.grid.markingsColor,k.fillRect(l.from,s.to,l.to-l.from,s.from-s.to)))}for(var c=X(),h=j.grid.borderWidth,d=0;d<c.length;++d){var f,u,p,m=c[d],x=m.box,g=m.tickLength;if(m.show&&0!=m.ticks.length){for(k.lineWidth=1,"x"==m.direction?(f=0,u="full"==g?"top"==m.position?0:T:x.top-w.top+("top"==m.position?x.height:0)):(u=0,f="full"==g?"left"==m.position?0:M:x.left-w.left+("left"==m.position?x.width:0)),m.innermost||(k.strokeStyle=m.options.color,k.beginPath(),v=p=0,"x"==m.direction?v=M+1:p=T+1,1==k.lineWidth&&("x"==m.direction?u=Math.floor(u)+.5:f=Math.floor(f)+.5),k.moveTo(f,u),k.lineTo(f+v,u+p),k.stroke()),k.strokeStyle=m.options.tickColor,k.beginPath(),t=0;t<m.ticks.length;++t){var b=m.ticks[t].v,v=p=0;isNaN(b)||b<m.min||b>m.max||"full"==g&&("object"==typeof h&&0<h[m.position]||0<h)&&(b==m.min||b==m.max)||("x"==m.direction?(f=m.p2c(b),p="full"==g?-T:g,"top"==m.position&&(p=-p)):(u=m.p2c(b),v="full"==g?-M:g,"left"==m.position&&(v=-v)),1==k.lineWidth&&("x"==m.direction?f=Math.floor(f)+.5:u=Math.floor(u)+.5),k.moveTo(f,u),k.lineTo(f+v,u+p))}k.stroke()}}h&&(e=j.grid.borderColor,"object"==typeof h||"object"==typeof e?("object"!=typeof e&&(e={top:e,right:e,bottom:e,left:e}),0<(h="object"!=typeof h?{top:h,right:h,bottom:h,left:h}:h).top&&(k.strokeStyle=e.top,k.lineWidth=h.top,k.beginPath(),k.moveTo(0-h.left,0-h.top/2),k.lineTo(M,0-h.top/2),k.stroke()),0<h.right&&(k.strokeStyle=e.right,k.lineWidth=h.right,k.beginPath(),k.moveTo(M+h.right/2,0-h.top),k.lineTo(M+h.right/2,T),k.stroke()),0<h.bottom&&(k.strokeStyle=e.bottom,k.lineWidth=h.bottom,k.beginPath(),k.moveTo(M+h.right,T+h.bottom/2),k.lineTo(0,T+h.bottom/2),k.stroke()),0<h.left&&(k.strokeStyle=e.left,k.lineWidth=h.left,k.beginPath(),k.moveTo(0-h.left/2,T+h.bottom),k.lineTo(0-h.left/2,0),k.stroke())):(k.lineWidth=h,k.strokeStyle=j.grid.borderColor,k.strokeRect(-h/2,-h/2,M+h,T+h))),k.restore()}function N(t,e,i,o,n,a,r,l,s,c,h){var d,f,u,p,m,x,g,b,v;c?(m=!(b=x=g=!0),p=e+o,u=e+n,(f=t)<(d=i)&&(v=f,f=d,d=v,x=!(m=!0))):(b=!(m=x=g=!0),d=t+o,f=t+n,(p=e)<(u=i)&&(v=p,p=u,u=v,g=!(b=!0))),f<r.min||d>r.max||p<l.min||u>l.max||(d<r.min&&(d=r.min,m=!1),f>r.max&&(f=r.max,x=!1),u<l.min&&(u=l.min,b=!1),p>l.max&&(p=l.max,g=!1),d=r.p2c(d),u=l.p2c(u),f=r.p2c(f),p=l.p2c(p),a&&(s.fillStyle=a(u,p),s.fillRect(d,p,f-d,u-p)),0<h&&(m||x||g||b)&&(s.beginPath(),s.moveTo(d,u),m?s.lineTo(d,p):s.moveTo(d,p),g?s.lineTo(f,p):s.moveTo(f,p),x?s.lineTo(f,u):s.moveTo(f,u),b?s.lineTo(d,u):s.moveTo(d,u),s.stroke()))}function m(t,e,i,o){var n=t.fill;return n?t.fillColor?J(t.fillColor,i,o,e):((t=$.color.parse(e)).a="number"==typeof n?n:.4,t.normalize(),t.toString()):null}t&&(t.shutdown(),s.clear()),y.data("plot",S),f(W),I(),A(),j.grid.hoverable&&(h.mousemove(D),h.bind("mouseleave",L)),j.grid.clickable&&h.click(O),_(G.bindEvents,[h]);var x=[],g=null;function D(t){j.grid.hoverable&&b("plothover",t,function(t){return 0!=t.hoverable})}function L(t){j.grid.hoverable&&b("plothover",t,function(t){return!1})}function O(t){b("plotclick",t,function(t){return 0!=t.clickable})}function b(t,e,i){var o=h.offset(),n=e.pageX-o.left-w.left,a=e.pageY-o.top-w.top,r=u({left:n,top:a}),l=(r.pageX=e.pageX,r.pageY=e.pageY,function(t,e,i){for(var o,n=j.grid.mouseActiveRadius,a=n*n+1,r=null,l=H.length-1;0<=l;--l)if(i(H[l])){var s,c,h=H[l],d=h.xaxis,f=h.yaxis,u=h.datapoints.points,p=d.c2p(t),m=f.c2p(e),x=n/d.scale,g=n/f.scale,b=h.datapoints.pointsize;if(d.options.inverseTransform&&(x=Number.MAX_VALUE),f.options.inverseTransform&&(g=Number.MAX_VALUE),h.lines.show||h.points.show)for(o=0;o<u.length;o+=b){var v,k=u[o],y=u[o+1];null==k||x<k-p||k-p<-x||g<y-m||y-m<-g||(v=(v=Math.abs(d.p2c(k)-t))*v+(v=Math.abs(f.p2c(y)-e))*v)<a&&(a=v,r=[l,o/b])}if(h.bars.show&&!r){switch(h.bars.align){case"left":s=0;break;case"right":s=-h.bars.barWidth;break;default:s=-h.bars.barWidth/2}for(c=s+h.bars.barWidth,o=0;o<u.length;o+=b){var k=u[o],y=u[o+1],w=u[o+2];null!=k&&(H[l].bars.horizontal?p<=Math.max(w,k)&&p>=Math.min(w,k)&&y+s<=m&&m<=y+c:k+s<=p&&p<=k+c&&m>=Math.min(w,y)&&m<=Math.max(w,y))&&(r=[l,o/b])}}}return r?(l=r[0],o=r[1],b=H[l].datapoints.pointsize,{datapoint:H[l].datapoints.points.slice(o*b,(o+1)*b),dataIndex:o,series:H[l],seriesIndex:l}):null}(n,a,i));if(l&&(l.pageX=parseInt(l.series.xaxis.p2c(l.datapoint[0])+o.left+w.left,10),l.pageY=parseInt(l.series.yaxis.p2c(l.datapoint[1])+o.top+w.top,10)),j.grid.autoHighlight){for(var s=0;s<x.length;++s){var c=x[s];c.auto!=t||l&&c.series==l.series&&c.point[0]==l.datapoint[0]&&c.point[1]==l.datapoint[1]||Q(c.series,c.point)}l&&q(l.series,l.datapoint,t)}y.trigger(t,[r,l])}function v(){var t=j.interaction.redrawOverlayInterval;-1==t?R():g=g||setTimeout(R,t)}function R(){var t,e,i,o,n,a,r,l;for(g=null,c.save(),s.clear(),c.translate(w.left,w.top),t=0;t<x.length;++t)(i=x[t]).series.bars.show?function(t,e){var i,o="string"==typeof t.highlightColor?t.highlightColor:$.color.parse(t.color).scale("a",.5).toString(),n=o;switch(t.bars.align){case"left":i=0;break;case"right":i=-t.bars.barWidth;break;default:i=-t.bars.barWidth/2}c.lineWidth=t.bars.lineWidth,c.strokeStyle=o,N(e[0],e[1],e[2]||0,i,i+t.bars.barWidth,function(){return n},t.xaxis,t.yaxis,c,t.bars.horizontal,t.bars.lineWidth)}(i.series,i.point):(e=i.series,i=i.point,l=r=a=n=o=void 0,n=i[0],i=i[1],a=e.xaxis,r=e.yaxis,l="string"==typeof e.highlightColor?e.highlightColor:$.color.parse(e.color).scale("a",.5).toString(),n<a.min||n>a.max||i<r.min||i>r.max||(o=e.points.radius+e.points.lineWidth/2,c.lineWidth=o,c.strokeStyle=l,l=1.5*o,n=a.p2c(n),i=r.p2c(i),c.beginPath(),"circle"==e.points.symbol?c.arc(n,i,l,0,2*Math.PI,!1):e.points.symbol(c,n,i,l,!1),c.closePath(),c.stroke()));c.restore(),_(G.drawOverlay,[c])}function q(t,e,i){"number"==typeof t&&(t=H[t]),"number"==typeof e&&(o=t.datapoints.pointsize,e=t.datapoints.points.slice(o*e,o*(e+1)));var o=U(t,e);-1==o?(x.push({series:t,point:e,auto:i}),v()):i||(x[o].auto=!1)}function Q(t,e){var i;null==t&&null==e?(x=[],v()):("number"==typeof t&&(t=H[t]),"number"==typeof e&&(i=t.datapoints.pointsize,e=t.datapoints.points.slice(i*e,i*(e+1))),-1!=(i=U(t,e))&&(x.splice(i,1),v()))}function U(t,e){for(var i=0;i<x.length;++i){var o=x[i];if(o.series==t&&o.point[0]==e[0]&&o.point[1]==e[1])return i}return-1}function J(t,e,i,o){if("string"==typeof t)return t;for(var n=k.createLinearGradient(0,i,0,e),a=0,r=t.colors.length;a<r;++a){var l,s=t.colors[a];"string"!=typeof s&&(l=$.color.parse(o),null!=s.brightness&&(l=l.scale("rgb",s.brightness)),null!=s.opacity&&(l.a*=s.opacity),s=l.toString()),n.addColorStop(a/(r-1),s)}return n}}$.fn.detach||($.fn.detach=function(){return this.each(function(){this.parentNode&&this.parentNode.removeChild(this)})}),K.prototype.resize=function(t,e){if(t<=0||e<=0)throw new Error("Invalid dimensions for plot, width = "+t+", height = "+e);var i=this.element,o=this.context,n=this.pixelRatio;this.width!=t&&(i.width=t*n,i.style.width=t+"px",this.width=t),this.height!=e&&(i.height=e*n,i.style.height=e+"px",this.height=e),o.restore(),o.save(),o.scale(n,n)},K.prototype.clear=function(){this.context.clearRect(0,0,this.width,this.height)},K.prototype.render=function(){var t,e=this._textCache;for(t in e)if(u.call(e,t)){var i,o=this.getTextLayer(t),n=e[t];for(i in o.hide(),n)if(u.call(n,i)){var a,r=n[i];for(a in r)if(u.call(r,a)){for(var l,s=r[a].positions,c=0;l=s[c];c++)l.active?l.rendered||(o.append(l.element),l.rendered=!0):(s.splice(c--,1),l.rendered&&l.element.detach());0==s.length&&delete r[a]}}o.show()}},K.prototype.getTextLayer=function(t){var e=this.text[t];return null==e&&(null==this.textContainer&&(this.textContainer=$("<div class='flot-text'></div>").css({position:"absolute",top:0,left:0,bottom:0,right:0,"font-size":"smaller",color:"#545454"}).insertAfter(this.element)),e=this.text[t]=$("<div></div>").addClass(t).css({position:"absolute",top:0,left:0,bottom:0,right:0}).appendTo(this.textContainer)),e},K.prototype.getTextInfo=function(t,e,i,o,n){var a,r,l;return e=""+e,a="object"==typeof i?i.style+" "+i.variant+" "+i.weight+" "+i.size+"px/"+i.lineHeight+"px "+i.family:i,null==(l=(r=null==(r=(l=null==(l=this._textCache[t])?this._textCache[t]={}:l)[a])?l[a]={}:r)[e])&&(n=$("<div></div>").html(e).css({position:"absolute","max-width":n,top:-9999}).appendTo(this.getTextLayer(t)),"object"==typeof i?n.css({font:a,color:i.color}):"string"==typeof i&&n.addClass(i),l=r[e]={width:n.outerWidth(!0),height:n.outerHeight(!0),element:n,positions:[]},n.detach()),l},K.prototype.addText=function(t,e,i,o,n,a,r,l,s){var t=this.getTextInfo(t,o,n,a,r),c=t.positions;"center"==l?e-=t.width/2:"right"==l&&(e-=t.width),"middle"==s?i-=t.height/2:"bottom"==s&&(i-=t.height);for(var h,d=0;h=c[d];d++)if(h.x==e&&h.y==i)return void(h.active=!0);h={active:!0,rendered:!1,element:c.length?t.element.clone():t.element,x:e,y:i},c.push(h),h.element.css({top:Math.round(i),left:Math.round(e),"text-align":l})},K.prototype.removeText=function(t,e,i,o,n,a){if(null==o){var r=this._textCache[t];if(null!=r)for(var l in r)if(u.call(r,l)){var s,c=r[l];for(s in c)if(u.call(c,s))for(var h=c[s].positions,d=0;f=h[d];d++)f.active=!1}}else for(var f,h=this.getTextInfo(t,o,n,a).positions,d=0;f=h[d];d++)f.x==e&&f.y==i&&(f.active=!1)},$.plot=function(t,e,i){return new o($(t),e,i,$.plot.plugins)},$.plot.version="0.8.3",$.plot.plugins=[],$.fn.plot=function(t,e){return this.each(function(){$.plot(this,t,e)})}}(jQuery);