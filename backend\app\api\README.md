# API endpoints

This contains all endpoints in our API with descriptions/info

---

## Table of Contents

- [API endpoints](#api-endpoints)
  - [Table of Contents](#table-of-contents)
  - [Authentication](#authentication)
  - [Users](#users)
  - [Requisition](#requisition)
  - [Requisition Samples](#requisition-samples)
  - [Sample Tests](#sample-tests)
  - [Samples](#samples)
  - [Staff](#staff)
  - [Test Types](#test-types)
  - [Files](#files)
  - [Labs](#labs)
  - [Health](#health)

---

## Authentication

- **POST** `/auth/login`: Logging in as a user with username and password

- **POST** `/auth/logout`: Logout user

- **GET** `/auth/me`: Get current user info 

- **POST** `/auth/select-lab-role`: Select a role and lab for the current user session

---

## Users

- **GET** `/users`: List all users - can only do this as an Admin

- **GET** `/users/me`: Get current user information

- **GET** `/users/{user_id}`: Get a specific user's details - can only do this as an Admin

- **POST** `/users`: Create a new user - can only do this as an Admin

- **PUT** `/users/{user_id}`: Update user info - can only do this as an Admin

- **DELETE** `/users/{user_id}`: Delete a user - can only do this as an Admin

---

## Requisition

- **POST** `/requisitions`: Create requisition - Must be an Admin or Scientist
  
- **GET** `/requisitions`: List requisitions (with filters such as status, amount and archive) - If the user is a Scientist, will ONLY get the requisitions the Scientist made
  
- **GET** `/requisitions/{req_id}`: Get a certain requisition details 
  
- **PUT** `/requisitions/{req_id}`: Update or archive requisition - Only Admins can archive

---

## Requisition Samples

- **POST** `/requisitions/{req_id}/samples`: Attach samples to requisition  

- **GET** `/requisitions/{req_id}/samples`: List samples in requisition  
  
- **DELETE** `/requisitions/{req_id}/samples/{sample_id}`: Remove sample from requisition  

---

## Sample Tests

- **POST** `/requisitions/{req_id}/samples/{sample_id}/tests`: Assign a test to be run on a certain sample as apart of a certain requisition

- **GET** `/requisitions/{req_id}/samples/{sample_id}/tests`: List tests to be done on a certain sample as apart of a certain requisition
  
- **PUT** `/requisitions/{req_id}/samples/{sample_id}/tests/{test_id}`: Update the status of a test on a certain sample as apart of a certain requisition

- **DELETE** `/requisitions/{req_id}/samples/{sample_id}/tests/{test_id}`: Remove a test from a sample in a requisition

---

## Samples

- **GET** `/samples`: List all samples in our DB  
  
- **GET** `/samples/{sample_id}`: Get details about the specified sample 
  
- **POST** `/samples`: Add a new sample to our DB (must be a sample that exists in SMS)

---

## Staff

- **POST** `/staff`: Create a new staff member for the lab admin's lab

- **PUT** `/staff`: Update a staff member's role within the lab admin's lab

- **DELETE** `/staff/{staff_id}`: Delete a staff member within the lab admin's lab

---

## Staff

- **POST** `/staff`: Create a new staff member for the lab admin's lab

- **PUT** `/staff`: Update a staff member's role within the lab admin's lab

- **DELETE** `/staff/{staff_id}`: Delete a staff member within the lab admin's lab

---

## Test Types

- **GET** `/test-types`: List all test types available 
  
- **POST** `/test-types`: Create a new test that can be run on sample - can only do this as an Admin
  
- **PUT** `/test-types/{type_id}`: Update a test - can only do this as an Admin
  
- **DELETE** `/test-types/{type_id}`: Deactivate test type to not be available anymore - can only do this as an Admin. It will still exist for previous requisitions.

---

## Files

- **POST** `/files/upload`: Upload a new file as apart of a requisition, sample or test
   
- **GET** `/files/list`: List all files apart of a requisition, sample or test
  - `/files/list?requisition_id={req_id}`: Will get all files apart of a requisition
  - `/files/list?req_sample_id={req_sample_id}`: Will get all files associated to sample that is apart of a certain requisition. Remember that a sample could be apart of multiple requisitions
  - `/files/list?req_sample_test_id={req_sample_test_id}`: Will get all files associated to a certain test done on a certain sample that is apart of a certain requisition
  - Basically, a file can just be apart of a requisition. Or it can be assigned to a certain sample (inside that req). Or it could be results to a test done on a sample (inside that req)
  
- **GET** `/files/{file_id}`: Get metadata about the file
  
- **GET** `/files/{file_id}/download`: Get the file itself and prepare to be downloaded
  
- **PUT** `/files/{file_id}/publish`: Publish the file to be available to the Scientist. Otherwise it will be private only for lab personal to see

---

## Labs
- **GET** `/labs/`: Get all users working in the current user's lab - can only do this as a Lab Admin

---

## Health
- **GET** `/health`: Check if the API is running properly