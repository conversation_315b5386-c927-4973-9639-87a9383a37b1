"""
Logging middleware for the LIMS API.

This middleware logs incoming requests and outgoing responses.
It also logs exceptions that occur during request processing.

All logs are directed to a single log file (lims_api.log) with appropriate log levels.
"""

import time
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from .logger import logger

class SimpleLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware that logs incoming requests and outgoing responses.
    """
    
    def __init__(self, app):
        super().__init__(app)
        logger.info("Logging middleware initialized")
    
    async def dispatch(self, request: Request, call_next):
        # Record start time
        start_time = time.time()
        
        # Get path and method for logging
        path = request.url.path
        method = request.method
        
        # Skip logging for health checks or static files to reduce noise
        if path == "/health" or path.startswith("/static"):
            return await call_next(request)
        
        # Log the incoming request for API endpoints
        if path.startswith("/api/"):
            logger.info(f"{method} {path}")
        
        # Process the request and catch any errors
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log based on response status and endpoint
            if response.status_code >= 400:
                logger.warning(
                    f"{method} {path} - Status: {response.status_code} - "
                    f"Time: {process_time:.3f}s"
                )
            elif path.startswith("/api/") and not path.endswith("/health"):
                # Only log API calls that aren't health checks
                logger.info(
                    f"{method} {path} - Status: {response.status_code} - "
                    f"Time: {process_time:.3f}s"
                )
                
            return response
            
        except Exception as e:
            # Log exceptions
            process_time = time.time() - start_time
            logger.error(
                f"{method} {path} - Exception: {str(e)} - "
                f"Time: {process_time:.3f}s",
                exc_info=True
            )
            raise 