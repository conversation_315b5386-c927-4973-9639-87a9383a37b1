["test_requisitions.py::TestRequisitions::test_archive_requisition_admin", "test_requisitions.py::TestRequisitions::test_create_requisition_scientist", "test_requisitions.py::TestRequisitions::test_create_requisition_unauthorized", "test_requisitions.py::TestRequisitions::test_get_nonexistent_requisition", "test_requisitions.py::TestRequisitions::test_get_requisition_by_id", "test_requisitions.py::TestRequisitions::test_list_requisitions", "test_requisitions.py::TestRequisitions::test_list_requisitions_with_status_filter", "test_requisitions.py::TestRequisitions::test_update_requisition_lab_admin", "test_requisitions.py::TestRequisitions::test_update_requisition_unauthorized", "test_users.py::TestUsers::test_create_user_admin", "test_users.py::TestUsers::test_create_user_unauthorized", "test_users.py::TestUsers::test_get_current_user", "test_users.py::TestUsers::test_get_user_by_id", "test_users.py::TestUsers::test_list_users", "test_users.py::TestUsers::test_update_user"]