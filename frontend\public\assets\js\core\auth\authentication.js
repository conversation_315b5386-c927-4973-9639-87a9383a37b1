
/*
 * Authentication Middleware for Sed-LIMS
 * Centralized authentication logic that processes PAGE_AUTH_CONFIG declarations
 *
 * Features:
 * - Automatic token verification and expiration checking
 * - Role-based access control
 * - Declarative page authentication configuration
 * - Unified error handling and redirects
 */

import { parseJWT, isTokenExpired, redirectToLogin } from './auth-helpers.js';
import { redirectToPageByKey } from '../helpers/navigation-helpers.js';
import { showMessage } from '../helpers/message-helpers.js';
import { GLOBAL_CONFIGS } from '../config/global-configs.js';
import { AUTH_CONFIGS } from '../config/auth-configs.js';

// Message config maps for error handling
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS
};

$(document).ready(function() {
    processPageAuthentication();
});

// Main authentication processing function
async function processPageAuthentication() {
    try {
        // Check if page has authentication configuration
        if (!window.PAGE_AUTH_CONFIG) {
            console.warn('No PAGE_AUTH_CONFIG found - page may not require authentication');
            return;
        }

        // Verify user authentication
        const userInfo = await verifyUserAuthentication();
        if (!userInfo) {
            return; // User redirected to login
        }

        // Check role-based access if required
        if (window.PAGE_AUTH_CONFIG.requiredRole) {
            if (!checkRoleAccess(userInfo, window.PAGE_AUTH_CONFIG.requiredRole)) {
                handleAuthFailure('accessDenied');
                return;
            }
        }

        // Authentication successful - call page initialization
        if (window.PAGE_AUTH_CONFIG.onAuthSuccess) {
            await window.PAGE_AUTH_CONFIG.onAuthSuccess(userInfo);
        }

    } catch (error) {
        console.error('Authentication processing failed:', error);
        handleAuthFailure('authError');
    }
}

// Verify user authentication and token validity
async function verifyUserAuthentication() {
    const accessToken = localStorage.getItem('access_token');

    if (!accessToken) {
        redirectToLogin();
        return null;
    }

    try {
        const payload = parseJWT(accessToken);

        if (isTokenExpired(payload)) {
            clearAuthDataAndRedirect();
            return null;
        }

        // Add role and lab info to user payload
        const userInfo = {
            ...payload,
            role: localStorage.getItem('role'),
            lab: localStorage.getItem('lab')
        };

        return userInfo;

    } catch (error) {
        console.error('Token verification error:', error);
        clearAuthDataAndRedirect();
        return null;
    }
}

// Check if user has required role access
function checkRoleAccess(userInfo, requiredRole) {
    if (!userInfo.role) {
        console.warn('User role not set');
        return false;
    }

    return userInfo.role === requiredRole;
}

// Handle authentication failures
function handleAuthFailure(errorType) {
    if (window.PAGE_AUTH_CONFIG.onAuthFailure) {
        // Use custom error handler if provided
        window.PAGE_AUTH_CONFIG.onAuthFailure();
    } else {
        // Default error handling
        showDefaultAuthError(errorType);
    }
}

// Show default authentication error messages
function showDefaultAuthError(errorType) {
    const errorKey = errorType === 'accessDenied' ? 'accessDenied' : 'authError';

    showMessage('#wb-cont', 'auth.messages.errors', errorKey, MESSAGE_CONFIG_MAPS);

    // Redirect to appropriate page after showing error
    setTimeout(() => {
        if (errorType === 'accessDenied') {
            // Access denied: 3s delay (user operation feedback - allows reading error message)
            redirectToPageByKey('home');
        } else {
            // Auth error: 0ms delay (security - immediate redirect to login)
            redirectToLogin();
        }
    }, errorType === 'accessDenied' ? GLOBAL_CONFIGS.navigation.redirectDelay : 0);
}

// Clear authentication data and redirect to login
function clearAuthDataAndRedirect() {
    localStorage.clear();
    sessionStorage.clear();
    redirectToLogin();
}