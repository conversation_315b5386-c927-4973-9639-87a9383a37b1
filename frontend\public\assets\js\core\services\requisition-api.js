/*
 * Requisition API Service
 * Handles all API calls related to requisitions
 */
import { ApiBase } from './api-base.js';

// API methods for requisition operations
export class RequisitionApi {
  // Get requisitions filtered by status, lab, and user role
  static getRequisitions(status, labId, role) {
    // Determine API endpoint based on user role
    const endpoint = role === 'scientist'
      ? '/requisitions/mine'
      : `/requisitions/lab/${labId}`;

    return ApiBase.get(endpoint, { status });
  }

  static getRequisitionById(id) {
    return ApiBase.get(`/requisitions/${id}`);
  }

  static createRequisition(requisitionData) {
    return ApiBase.post('/requisitions', requisitionData);
  }

  static updateRequisitionStatus(id, status) {
    return ApiBase.put(`/requisitions/${id}/status`, { status });
  }

  static getRequisitionSamples(requisitionId) {
    return ApiBase.get(`/requisitions/${requisitionId}/samples`);
  }
}
