#Opening Doc string
from typing import Optional
import uuid
from sqlalchemy.orm import Session
from sqlalchemy import func
from ..models.test_type import TestType
from ..schemas.test import TestTypeCreate, TestTypeUpdate, TestTypeBatchUpdate
from ..schemas.user import User
from ..common.constants import User<PERSON>ole, ERROR_MESSAGES
from ..common.exceptions import NotFoundError, AccessDeniedError, ValidationError, StateError
from ..core.logging import log_info, log_error, log_warning
from app.common import constants

def get_test_type(db: Session, test_type_id: str):
    """
    Get a test type by ID.
    
    Args:
        db (Session): Database session
        test_type_id (str): ID of the test type to retrieve
        
    Returns:
        TestType: The test type if found, None otherwise
    """
    try:
        log_info(f"Retrieving test type with id: {test_type_id}")
        test_type = db.query(TestType).filter(TestType.test_type_id == test_type_id).first()
        if not test_type:
            log_warning(f"Test type not found: {test_type_id}")
            return None
        log_info(f"Successfully retrieved test type: {test_type_id}")
        return test_type
    except Exception as e:
        log_error(f"Error retrieving test type {test_type_id}: {str(e)}")
        raise ValueError(constants.ERROR_MESSAGES["test"]["get_failed"].format(error=str(e)))

def get_test_types(db: Session, skip: int = 0, limit: int = 100, lab_id: Optional[str] = None, name: Optional[str] = None):
    """
    Get a list of test types with pagination and optional lab filtering.

    Args:
        db (Session): Database session
        skip (int): Number of records to skip
        limit (int): Maximum number of records to return
        lab_id (str): Optional lab ID to filter test types by lab
        name (str): Optional name to filter test types by name (for duplicate checking)

    Returns:
        List[TestType]: List of test types

    Raises:
        ValidationError: If pagination parameters are invalid
        StateError: If an unexpected error occurs
    """
    log_info(f"Retrieving test types with pagination - skip: {skip}, limit: {limit}, lab_id: {lab_id}, name: {name}")
    try:
        if skip < 0:
            log_warning(f"Invalid skip value: {skip}")
            raise ValidationError(ERROR_MESSAGES["validation"]["skip_negative"])
        if limit < 1:
            log_warning(f"Invalid limit value: {limit}")
            raise ValidationError(ERROR_MESSAGES["validation"]["limit_positive"])

        query = db.query(TestType)

        # Filter by lab_id if provided
        if lab_id:
            query = query.filter(TestType.lab_id == lab_id)
            log_info(f"Filtering test types by lab_id: {lab_id}")

        # Filter by name if provided (case-insensitive for duplicate checking)
        if name:
            query = query.filter(TestType.name.ilike(name.strip()))
            log_info(f"Filtering test types by name: {name}")

        # Sort by timestamp (newest first)
        # Use COALESCE to prioritize updated_at, fall back to created_at if updated_at is NULL
        query = query.order_by(func.coalesce(TestType.updated_at, TestType.created_at).desc())
        log_info("Applied default sorting by timestamp (newest first)")

        test_types = query.offset(skip).limit(limit).all()
        log_info(f"Successfully retrieved {len(test_types)} test types")
        return test_types
    except ValidationError:
        raise
    except Exception as e:
        log_error(f"Error listing test types: {str(e)}", exc_info=e)
        raise StateError(ERROR_MESSAGES["test"]["list_failed"].format(error=str(e)))

def create_test_type(db: Session, test_type: TestTypeCreate, current_user: User) -> TestType:
    """
    Create a new test type. Only accessible by lab administrators.
    
    Args:
        db (Session): Database session
        test_type (TestTypeCreate): Test type data to create
        current_user (User): Current authenticated user
        
    Returns:
        TestType: Created test type
        
    Raises:
        AccessDeniedError: If user is not a lab administrator
        StateError: If an unexpected error occurs
    """
    log_info(f"Creating new test type by user: {current_user.user_id}")
    
    # Check if user has admin role
    if current_user.role != UserRole.LAB_ADMIN:
        log_warning(f"Access denied: User {current_user.user_id} attempted to create test type without admin role")
        raise AccessDeniedError(ERROR_MESSAGES["test"]["unauthorized_create"])
    
    log_info(f"Access granted: User {current_user.user_id} has admin role")
    
    try:
        # Create test type data and automatically assign current user's lab_id
        test_type_data = test_type.model_dump()

        # Get lab_id from current user - handle both User and Staff objects
        if hasattr(current_user, 'lab') and current_user.lab:
            test_type_data['lab_id'] = current_user.lab
        elif hasattr(current_user, 'lab_id') and current_user.lab_id:
            test_type_data['lab_id'] = current_user.lab_id
        else:
            test_type_data['lab_id'] = None

        db_test_type = TestType(**test_type_data)
        db.add(db_test_type)
        db.commit()
        db.refresh(db_test_type)

        lab_info = test_type_data['lab_id'] or "no lab"
        log_info(f"Successfully created test type with id: {db_test_type.test_type_id} for lab: {lab_info}")
        return db_test_type
    except Exception as e:
        db.rollback()
        log_error(f"Error creating test type: {str(e)}", exc_info=e)
        raise StateError(ERROR_MESSAGES["test"]["create_failed"].format(error=str(e)))

def update_test_type(db: Session, test_type_id: str, test_type: TestTypeUpdate, current_user: User) -> TestType:
    """
    Update an existing test type. Only accessible by lab administrators.
    
    Args:
        db (Session): Database session
        test_type_id (str): ID of the test type to update
        test_type (TestTypeUpdate): Updated test type data
        current_user (User): Current authenticated user
        
    Returns:
        TestType: Updated test type
        
    Raises:
        AccessDeniedError: If user is not a lab administrator
        NotFoundError: If test type not found
        StateError: If an unexpected error occurs
    """
    log_info(f"Updating test type {test_type_id} by user: {current_user.user_id}")
    
    # Check if user has admin role
    if current_user.role != UserRole.LAB_ADMIN:
        log_warning(f"Access denied: User {current_user.user_id} attempted to update test type without admin role")
        raise AccessDeniedError(ERROR_MESSAGES["test"]["unauthorized_update"])
    
    log_info(f"Access granted: User {current_user.user_id} has admin role")
    
    try:
        # Get the test type to update
        db_test_type = get_test_type(db, test_type_id)
        if not db_test_type:
            log_warning(f"Test type not found: {test_type_id}")
            raise NotFoundError(ERROR_MESSAGES["test"]["not_found"])
        
        # Update the test type fields with new values from the request
        update_data = test_type.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_test_type, key, value)
        
        db.commit()
        db.refresh(db_test_type)
        
        log_info(f"Successfully updated test type: {test_type_id}")
        return db_test_type
    except NotFoundError:
        raise
    except Exception as e:
        db.rollback()
        log_error(f"Error updating test type {test_type_id}: {str(e)}")
        raise StateError(ERROR_MESSAGES["test"]["update_failed"].format(error=str(e)))

def delete_test_type(db: Session, test_type_id: str, current_user: User):
    """
    Delete a test type. Only accessible by lab administrators.
    
    Args:
        db (Session): Database session
        test_type_id (str): ID of the test type to delete
        current_user (User): Current authenticated user
        
    Raises:
        AccessDeniedError: If user is not a lab administrator
        NotFoundError: If test type not found
        StateError: If an unexpected error occurs
    """
    log_info(f"Deleting test type {test_type_id} by user: {current_user.user_id}")
    
    # Check if user has admin role
    if current_user.role != UserRole.LAB_ADMIN:
        log_warning(f"Access denied: User {current_user.user_id} attempted to delete test type without admin role")
        raise AccessDeniedError(ERROR_MESSAGES["test"]["unauthorized_delete"])
    
    log_info(f"Access granted: User {current_user.user_id} has admin role")
    
    try:
        # Get the test type to delete
        db_test_type = get_test_type(db, test_type_id)
        if not db_test_type:
            log_warning(f"Test type not found: {test_type_id}")
            raise NotFoundError(ERROR_MESSAGES["test"]["not_found"])
        
        db.delete(db_test_type)
        db.commit()
        
        log_info(f"Successfully deleted test type: {test_type_id}")
    except NotFoundError:
        raise
    except Exception as e:
        db.rollback()
        log_error(f"Error deleting test type {test_type_id}: {str(e)}")
        raise StateError(ERROR_MESSAGES["test"]["delete_failed"].format(error=str(e)))

def batch_update_test_types(db: Session, batch_update: TestTypeBatchUpdate, current_user: User) -> dict:
    """
    Batch update multiple test types using atomic transaction.
    Only accessible by lab administrators.

    Either all updates succeed or all fail with database rollback.

    Args:
        db (Session): Database session
        batch_update (TestTypeBatchUpdate): Batch update data
        current_user (User): Current authenticated user

    Returns:
        dict: Simple success response with message and updated count

    Raises:
        AccessDeniedError: If user is not a lab administrator
        ValidationError: If batch is empty
        Exception: If any update fails (triggers database rollback)
    """
    log_info(f"Starting atomic batch update of {len(batch_update.updates)} test types by user: {current_user.user_id}")

    # Check if user has admin role
    if current_user.role != UserRole.LAB_ADMIN:
        log_warning(f"Access denied: User {current_user.user_id} attempted batch update without admin role")
        raise AccessDeniedError(ERROR_MESSAGES["test"]["unauthorized_update"])

    # Validate batch is not empty
    if not batch_update.updates:
        log_warning("Empty batch update request")
        raise ValidationError(ERROR_MESSAGES["test"]["batch_empty"])

    # Atomic transaction - all or nothing
    try:
        updated_count = 0
        updated_test_types = []

        # First, validate all updates and collect test types
        for update_data in batch_update.updates:
            test_type_id = update_data.get("test_type_id")
            # Create a copy to avoid modifying original data
            update_fields = {k: v for k, v in update_data.items() if k != "test_type_id"}
            test_type_update = TestTypeUpdate(**update_fields)

            # Get the test type to update (this will raise exception if not found)
            db_test_type = get_test_type(db, str(test_type_id))
            if not db_test_type:
                raise NotFoundError(ERROR_MESSAGES["test"]["not_found"])

            # Check permissions (reuse logic from update_test_type)
            if current_user.role != UserRole.LAB_ADMIN:
                raise AccessDeniedError(ERROR_MESSAGES["test"]["unauthorized_update"])

            updated_test_types.append((db_test_type, test_type_update))

        # If we get here, all validations passed - now apply updates
        for db_test_type, test_type_update in updated_test_types:
            update_data = test_type_update.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_test_type, key, value)
            updated_count += 1
            log_info(f"Updated test type {db_test_type.test_type_id} in batch (item {updated_count})")

        # Commit all changes at once
        db.commit()
        log_info(f"Batch update completed successfully: {updated_count} test types updated")

        return {
            "message": "All test types updated successfully",
            "updated_count": updated_count
        }

    except Exception as e:
        # Rollback transaction on any failure
        db.rollback()
        log_error(f"Batch update failed, transaction rolled back: {str(e)}")
        raise  # Re-raise exception to be handled by API layer