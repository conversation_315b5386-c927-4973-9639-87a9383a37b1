/*
Server file for LIMS server
*/

const express = require("express");
const path = require("path");
require('dotenv').config({ path: path.resolve(__dirname, '.env') }); // Load .env from frontend directory

//Create app
const app = express();
const port = process.env.PORT || 3000;

//Serve static files
app.use('/libraries', express.static(path.join(__dirname, 'public/libraries')));
app.use('/assets', express.static(path.join(__dirname, 'public/assets')));
app.use('/components', express.static(path.join(__dirname, 'public/components')));


// Routes for language versions
app.use('/en', express.static(path.join(__dirname, 'public/en')));
app.use('/fr', express.static(path.join(__dirname, 'public/fr')));

app.get('/', (req,res) => {
    res.redirect('/en/login.html');
});

/*
 * Environment Variable Injection Middleware
 * Automatically injects backend API URL into HTML pages
 */
app.use((req, res, next) => {
    if (req.path.endsWith('.html') || req.path === '/' || req.path.endsWith('/')) {
        const originalSend = res.send;

        // Override res.send to modify HTML content
        res.send = function(body) {
            if (typeof body === 'string' && body.includes('<head>')) {
                // Read backend API URL from environment variable with fallback
                const backendApiUrl = process.env.BACKEND_API_URL || 'http://localhost:8000';

                const envScript = `
    <script>
        // Environment configuration injected by server
        // This makes the backend URL available to all client-side JavaScript
        window.ENV_BACKEND_API_URL = '${backendApiUrl}';
    </script>`;

                // Inject the script before the closing </head> tag
                body = body.replace('</head>', envScript + '\n</head>');
            }

            // Call the original send method with modified content
            originalSend.call(this, body);
        };
    }
    next();
});

// API endpoint to provide AAD configuration to the client
app.get('/api/auth-config', (req, res) => {
    const appRedirectUri = process.env.APP_REDIRECT_URI || 'http://localhost:3000';
    
    if (!process.env.AZURE_AD_CLIENT_ID || !process.env.AZURE_AD_TENANT_ID) {
        console.error("AAD Client ID or Tenant ID missing in .env");
        return res.status(500).json({ error: "Server AAD configuration error." });
    }

    res.json({
        clientId: process.env.AZURE_AD_CLIENT_ID,
        authority: `https://login.microsoftonline.com/${process.env.AZURE_AD_TENANT_ID}`,
        redirectUri: appRedirectUri
    });
});

app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
});