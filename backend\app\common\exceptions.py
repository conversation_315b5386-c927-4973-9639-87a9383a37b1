"""
Custom exceptions for the LIMS application.

This module contains all custom exceptions used throughout the application.
These exceptions are raised by the CRUD layer and caught by the API layer
to be converted into appropriate HTTP responses.
"""

class LIMSError(Exception):
    """Base exception for all LIMS-specific errors"""
    status_code = 500  # Default status code

class NotFoundError(LIMSError):
    """Raised when a requested resource is not found"""
    status_code = 404

class AccessDeniedError(LIMSError):
    """Raised when a user doesn't have permission to access a resource"""
    status_code = 403

class ValidationError(LIMSError):
    """Raised when input validation fails"""
    status_code = 400

class StateError(LIMSError):
    """Raised when an operation is invalid due to the current state"""
    status_code = 409  # Conflict
