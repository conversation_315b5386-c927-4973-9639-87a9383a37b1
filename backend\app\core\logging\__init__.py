"""
Logging module for the LIMS API.

This module provides a simplified logging system with a single log file:
- lims_api.log: Contains all logs with standard levels (INFO, WARNING, ERROR, DEBUG)

The log file is rotated when it reaches 20MB and compressed with gzip.
"""

from .logger import (
    logger,
    log_info,
    log_warning,
    log_error
)

from .middleware import SimpleLoggingMiddleware

__all__ = [
    "logger",
    "log_info",
    "log_warning",
    "log_error",
    "SimpleLoggingMiddleware"
] 