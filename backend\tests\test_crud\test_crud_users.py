"""
Test suite for CRUD operations on User objects.

This class contains tests for the following functions:

- create_user: 
    - test_create_user_success
    - test_create_user_duplicate_email
    - test_create_user_db_add_failure
    - test_create_user_db_commit_failure
    - test_create_user_email_exists
    - test_create_user_db_error
    - test_create_user_validation_error
- get_user:
    - test_get_user_by_id
    - test_get_user_by_id_not_found
    - test_get_user_not_found
- get_user_by_email:
    - test_get_user_by_email
    - test_get_user_by_email_not_found
- get_user_by_azure_ad_id:
    - test_get_user_by_azure
    - test_get_user_by_azure_not_found
- get_users:
    - test_get_users
- update_user:
    - test_update_user
    - test_update_nonexistent_user
    - test_update_user_db_commit_failure
    - test_update_user_db_refresh_failure
    - test_update_user_not_found
    - test_update_user_db_error
- get_user_requisitions:
    - test_get_user_requisitions_no_requisitions
    - test_get_user_requisitions_user_not_found
- delete_user:
    - test_delete_user_not_found
"""
import uuid

import pytest
from sqlalchemy.orm import Session
from app.crud.sample import post_sample
from app.crud.user import (
    get_users,
    get_user,
    get_user_by_email,
    get_user_by_azure_ad_id,
    create_user,
    update_user,
    get_user_requisitions,
    delete_user
)
from app.models.requisition import Requisition
from app.schemas.user import UserCreate, UserUpdate
from app.common.constants import ERROR_MESSAGES
from app.common.exceptions import NotFoundError, AccessDeniedError, ValidationError as AppValidationError
from pydantic import ValidationError
from unittest.mock import patch, MagicMock
from sqlalchemy.exc import SQLAlchemyError
from tests.test_utils.constants import SAMPLE_TEST_CASES, USER_TEST_CASES
from tests.test_utils.helpers import TestHelpers
from app.models.staff import Staff
from app.common.constants import UserRole
from tests.test_utils.verification import UserVerifier, verify_crud_error_response
from tests.test_utils.verification.user import verify_partial_user_data


class TestUserCRUD:
    def test_create_user_success(self, db: Session, test_staff_admin):
        # Using valid test case from constants
        user_data = UserCreate(**USER_TEST_CASES["create"]["valid"])

        user = create_user(
            db, 
            user_data, 
            str(test_staff_admin.user_id), 
            str(test_staff_admin.lab_id), 
            test_staff_admin.role
        )
        # Create a verification dict excluding the password
        verification_data = {
            "email": USER_TEST_CASES["create"]["valid"]["email"],
            "is_active": USER_TEST_CASES["create"]["valid"]["is_active"]
        }
        
        # Use UserVerifier directly
        UserVerifier.verify_common_fields(user.__dict__)
        UserVerifier.verify_response(user.__dict__, verification_data)
        # Separately verify that password is hashed
        assert hasattr(user, "password")  # Password should be hashed
        assert user.password != USER_TEST_CASES["create"]["valid"]["password"]  # Password should not be plaintext

    def test_create_user_duplicate_email(self, db: Session, test_staff_admin):
        user_data = UserCreate(**USER_TEST_CASES["create"]["valid"])
        create_user(
            db, 
            user_data, 
            str(test_staff_admin.user_id), 
            str(test_staff_admin.lab_id), 
            test_staff_admin.role
        )

        # Try to create another user with the same email
        with pytest.raises(AppValidationError) as exc_info:
            create_user(
                db, 
                user_data, 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["user"]["email_exists"])

    def test_create_user_db_add_failure(self, db: Session, test_staff_admin):
        user_data = UserCreate(**USER_TEST_CASES["create"]["valid"])
        
        # Mock the database add operation to simulate an error
        db.add = MagicMock(side_effect=SQLAlchemyError("Database error"))
        
        with pytest.raises(AppValidationError) as exc_info:
            create_user(
                db, 
                user_data, 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["user"]["error_creating"])

    def test_create_user_db_commit_failure(self, db: Session, test_staff_admin):
        user_data = UserCreate(**USER_TEST_CASES["create"]["valid"])
        
        # Mock the database commit operation to simulate an error
        db.commit = MagicMock(side_effect=SQLAlchemyError("Database error"))
        
        with pytest.raises(AppValidationError) as exc_info:
            create_user(
                db, 
                user_data, 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["user"]["error_creating"])

    def test_get_user_by_id(self, db: Session, test_user, test_staff_admin):
        # Ensure test_user is in the same lab as admin
        staff_user = Staff(
            user_id=test_user.user_id,
            role=UserRole.SCIENTIST
        )
        db.add(staff_user)
        db.commit()

        user = get_user(
            db, 
            str(test_user.user_id), 
            str(test_staff_admin.user_id), 
            str(test_staff_admin.lab_id), 
            test_staff_admin.role
        )
        
        UserVerifier.verify_common_fields(user.__dict__)
        UserVerifier.verify_response(user.__dict__, {
            "user_id": test_user.user_id,
            "email": test_user.email
        })

    def test_get_user_by_id_not_found(self, db: Session, test_staff_admin):
        with pytest.raises(NotFoundError) as exc_info:
            get_user(
                db, 
                str(uuid.uuid4()), 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["user"]["not_found"])

    def test_get_user_by_email(self, db: Session, test_user):
        user = get_user_by_email(db, test_user.email)
        
        UserVerifier.verify_common_fields(user.__dict__)
        UserVerifier.verify_response(user.__dict__, {
            "email": test_user.email
        })

    def test_get_user_by_email_not_found(self, db: Session):
        with pytest.raises(NotFoundError) as exc_info:
            get_user_by_email(db, "<EMAIL>")
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["user"]["not_found"])
    
    def test_get_user_by_azure(self, db: Session, test_user):
        user = get_user_by_azure_ad_id(db, test_user.azure_ad_id)
        
        UserVerifier.verify_common_fields(user.__dict__)
        UserVerifier.verify_response(user.__dict__, {
            "azure_ad_id": test_user.azure_ad_id
        })

    def test_get_user_by_azure_not_found(self, db: Session):
        # Test getting a user by an Azure AD ID that does not exist
        user = get_user_by_azure_ad_id(db, str(uuid.uuid4()))
        assert user is None

    def test_get_users(self, db: Session, test_staff_lab_personal, test_staff_admin):
        # Ensure test_user is in the same lab as admin
        users = get_users(
            db, 
            str(test_staff_admin.user_id), 
            str(test_staff_admin.lab_id), 
            test_staff_admin.role
        )
        
        assert len(users) >= 1
        # Verify the test_user is in the results
        user_ids = [str(u.user_id) for u in users]
        assert str(test_staff_lab_personal.user_id) in user_ids
        assert str(test_staff_admin.user_id) in user_ids

    def test_update_user(self, db: Session, test_user, test_staff_admin):
        """Test updating a user"""
        # Ensure test_user is in the same lab as admin
        staff_user = Staff(
            user_id=test_user.user_id,
            role=UserRole.SCIENTIST
        )
        db.add(staff_user)
        db.commit()
        
        update_data = UserUpdate(**USER_TEST_CASES["update"]["valid"])
        updated_user = update_user(
            db, 
            str(test_user.user_id), 
            update_data, 
            str(test_staff_admin.user_id), 
            str(test_staff_admin.lab_id), 
            test_staff_admin.role
        )
        
        assert updated_user.is_active == update_data.is_active

    def test_update_nonexistent_user(self, db: Session, test_staff_admin):
        """Test updating a non-existent user"""
        update_data = UserUpdate(**USER_TEST_CASES["update"]["valid"])
        
        with pytest.raises(NotFoundError) as exc_info:
            update_user(
                db, 
                str(uuid.uuid4()), 
                update_data, 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["user"]["not_found"])

    def test_update_user_db_commit_failure(self, db: Session, test_user, test_staff_admin):
        """Test database commit failure during user update"""
        # Ensure test_user is in the same lab as admin
        staff_user = Staff(
            user_id=test_user.user_id,
            role=UserRole.SCIENTIST
        )
        db.add(staff_user)
        db.commit()
        
        update_data = UserUpdate(**USER_TEST_CASES["update"]["valid"])
        
        # Mock the database commit operation to simulate an error
        db.commit = MagicMock(side_effect=SQLAlchemyError("Database error"))
        
        with pytest.raises(AppValidationError) as exc_info:
            update_user(
                db, 
                str(test_user.user_id), 
                update_data, 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["user"]["error_updating"])

    def test_update_user_db_refresh_failure(self, db: Session, test_user, test_staff_admin):
        """Test database refresh failure during user update"""
        # Ensure test_user is in the same lab as admin
        staff_user = Staff(
            user_id=test_user.user_id,
            role=UserRole.SCIENTIST
        )
        db.add(staff_user)
        db.commit()
        
        update_data = UserUpdate(**USER_TEST_CASES["update"]["valid"])
        
        # Mock the database refresh operation to simulate an error
        db.refresh = MagicMock(side_effect=SQLAlchemyError("Database error"))
        
        with pytest.raises(AppValidationError) as exc_info:
            update_user(
                db, 
                str(test_user.user_id), 
                update_data, 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["user"]["error_updating"])

    def test_get_user_requisitions_no_requisitions(self, db: Session, test_staff_admin):
        # Create a new user without any requisitions
        user_data = UserCreate(**USER_TEST_CASES["create"]["valid2"])
        user = create_user(
            db, 
            user_data, 
            str(test_staff_admin.user_id), 
            str(test_staff_admin.lab_id), 
            test_staff_admin.role
        )
        
        # Ensure user is in the same lab as admin
        staff_user = Staff(
            user_id=user.user_id,
            role=UserRole.SCIENTIST
        )
        db.add(staff_user)
        db.commit()
        
        requisitions = get_user_requisitions(
            db, 
            str(user.user_id), 
            str(test_staff_admin.user_id), 
            str(test_staff_admin.lab_id), 
            test_staff_admin.role
        )
        assert len(requisitions) == 0

    def test_get_user_requisitions_user_not_found(self, db: Session, test_staff_admin):
        with pytest.raises(NotFoundError) as exc_info:
            get_user_requisitions(
                db, 
                str(uuid.uuid4()), 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["user"]["not_found"])

    def test_create_user_email_exists(self, db: Session, test_user, test_staff_admin):
        """Test creating a user with an email that already exists"""
        user_data = UserCreate(**{
            "email": test_user.email,
            "password": "password123",
            "is_active": True,
            "azure_ad_id": str(uuid.uuid4),
            "role": UserRole.SCIENTIST
        })
        
        with pytest.raises(AppValidationError) as exc_info:
            create_user(
                db, 
                user_data, 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["user"]["email_exists"])

    def test_create_user_db_error(self, db: Session, test_staff_admin):
        """Test database error during user creation"""
        user_data = UserCreate(**USER_TEST_CASES["create"]["valid"])
        
        with patch.object(Session, 'commit', side_effect=SQLAlchemyError("Database error")):
            with pytest.raises(AppValidationError) as exc_info:
                create_user(
                    db, 
                    user_data, 
                    str(test_staff_admin.user_id), 
                    str(test_staff_admin.lab_id), 
                    test_staff_admin.role
                )
            verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["user"]["error_creating"])

    def test_create_user_validation_error(self, db: Session, test_staff_admin):
        """Test validation error during user creation"""
        # Invalid email format
        invalid_data = {
            "email": "invalid-email",
            "password": "password123",
            "is_active": True,
            "role": UserRole.SCIENTIST
        }
        
        with pytest.raises(ValidationError):
            user = UserCreate(**invalid_data)
            create_user(
                db, 
                user, 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )

    def test_get_user_not_found(self, db: Session, test_staff_admin):
        """Test getting a non-existent user"""
        with pytest.raises(NotFoundError) as exc_info:
            get_user(
                db, 
                str(uuid.uuid4()), 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["user"]["not_found"])

    def test_update_user_not_found(self, db: Session, test_staff_admin):
        """Test updating a non-existent user"""
        update_data = UserUpdate(**USER_TEST_CASES["update"]["valid"])
        
        with pytest.raises(NotFoundError) as exc_info:
            update_user(
                db, 
                str(uuid.uuid4()), 
                update_data, 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["user"]["not_found"])

    def test_update_user_db_error(self, db: Session, test_user, test_staff_admin):
        """Test database error during user update"""
        # Ensure test_user is in the same lab as admin
        staff_user = Staff(
            user_id=test_user.user_id,
            role=UserRole.SCIENTIST
        )
        db.add(staff_user)
        db.commit()
        
        update_data = UserUpdate(**USER_TEST_CASES["update"]["valid"])
        
        with patch.object(Session, 'commit', side_effect=SQLAlchemyError("Database error")):
            with pytest.raises(AppValidationError) as exc_info:
                update_user(
                    db, 
                    str(test_user.user_id), 
                    update_data, 
                    str(test_staff_admin.user_id), 
                    str(test_staff_admin.lab_id), 
                    test_staff_admin.role
                )
            verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["user"]["error_updating"])

    def test_delete_user_not_found(self, db: Session, test_staff_admin):
        """Test deleting a non-existent user"""
        with pytest.raises(NotFoundError) as exc_info:
            delete_user(
                db, 
                str(uuid.uuid4()), 
                str(test_staff_admin.user_id), 
                str(test_staff_admin.lab_id), 
                test_staff_admin.role
            )
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["user"]["not_found"])
