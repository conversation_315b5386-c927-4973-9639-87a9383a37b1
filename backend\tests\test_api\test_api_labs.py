"""
Tests for API functions on /labs

Test Coverage:
    1. test_list_lab_users_unauthorized : User (not lab_admin) tries to access /labs
    2. test_list_lab_users_authorized : Lab Admin gets all workers that work in their lab
    3. test_list_lab_users_multiple : Testing multiple users in 1 lab
    4. ## : Unexpected Error
"""
import pytest
from unittest.mock import patch

from tests.test_utils.helpers import TestHelpers
from tests.test_utils.verification import verify_api_error_response


class TestLabs:
    #####################
    # /labs TESTS
    #####################
    def test_list_lab_users_unauthorized(self, client, user_token):
        response = client.get(
            "/labs/", 
            headers=TestHelpers.Auth.get_headers(user_token)
        )

        verify_api_error_response(
            response, 
            403, 
            "Not authorized"
        )
    
    def test_list_lab_users_authorized(self, client, admin_token, test_staff_admin, test_admin):
        response = client.get(
            "/labs/", 
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        staff = response.json()
        assert len(staff) == 1
        assert staff[0]["email"] == test_admin.email
        assert staff[0]["role"] == test_staff_admin.role
        assert staff[0]["lab"] == str(test_staff_admin.lab_id)
        assert staff[0]["is_active"] == test_admin.is_active
        assert staff[0]["user_id"] == str(test_admin.user_id)
    
    def test_list_lab_users_multiple(self, client, admin_token, test_staff_admin, test_admin, test_staff_lab_personal, test_lab_personnel):
        response = client.get(
            "/labs/", 
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        staff = response.json()
        assert len(staff) == 2
        assert staff[0]["email"] == test_admin.email
        assert staff[0]["role"] == test_staff_admin.role
        assert staff[0]["lab"] == str(test_staff_admin.lab_id)
        assert staff[1]["email"] == test_lab_personnel.email
        assert staff[1]["role"] == test_staff_lab_personal.role
        assert staff[1]["lab"] == str(test_staff_lab_personal.lab_id) 
    
    def test_list_lab_users_server_error(self,client,admin_token):
        with patch("app.crud.staff.get_users_in_lab", side_effect=Exception("Unexpected error")):
            response = client.get(
                "/labs", 
                headers=TestHelpers.Auth.get_headers(admin_token)
            )

        verify_api_error_response(
            response, 
            500, 
            "Server Error"
        )