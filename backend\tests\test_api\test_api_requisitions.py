"""
Test suite for the Requisitions API endpoints.

This class contains tests for the following endpoints:
- POST /requisitions/{lab_id}
    - test_create_requisition_scientist
    - test_create_requisition_lab_admin
    - test_create_requisition_lab_personnel
    - test_create_requisition_invalid_data
    - test_create_requisition_server_error
    - test_create_requisition_unauthorized
    - test_create_requisition_custom_dates
    - test_create_requisition_by_impossible_lab_id
    - test_create_requisition_by_invalid_lab_id
- GET /requisitions/lab/{lab_id}
    - test_list_requisitions
    - test_list_requisitions_with_status_filter
    - test_list_requisitions_by_impossible_lab
    - test_list_requisitions_by_invalid_lab_id
    - test_list_requisitions_by_role
    - test_list_requisitions_status_filter
    - test_list_requisitions_pagination
- GET /requisitions/mine
    - test_list_my_requisitions_success
    - test_list_my_requisitions_with_status_filter
    - test_list_my_requisitions_unauthorized_role
    - test_list_my_requisitions_unauthenticated
    - test_list_my_requisitions_pagination
- GET /requisitions/{req_id}
    - test_get_requisition_by_id
    - test_get_nonexistent_requisition
    - test_get_requisition_by_id_access_control
    - test_get_requisition_by_id_scientist_own_requisition
- PUT /requisitions/{req_id}
    - test_update_requisition_lab_admin
    - test_update_requisition_unauthorized
    - test_archive_requisition_admin
    - test_update_requisition_lab_personnel
    - test_update_requisition_scientist
    - test_update_requisition_status_and_archive
    - test_update_requisition_archive_by_lab_personnel
    - test_archive_requisition_by_role
- POST /requisitions/{req_id}/samples
    - test_add_samples_to_requisition_success
    - test_add_samples_to_requisition_unauthorized
    - test_add_samples_to_nonexistent_requisition
    - test_add_nonexistent_samples_to_requisition
- GET /requisitions/{req_id}/samples
    - test_get_requisition_samples_success
    - test_get_requisition_samples_unauthorized
    - test_get_samples_from_nonexistent_requisition
- DELETE /requisitions/{req_id}/samples/{sample_id}
    - test_remove_sample_from_requisition_success
    - test_remove_sample_from_requisition_unauthorized
    - test_remove_nonexistent_sample_from_requisition
    - test_remove_sample_from_nonexistent_requisition
- POST /requisitions/{req_id}/samples/{sample_id}/tests
    - test_add_tests_to_sample_lab_admin
    - test_add_tests_to_sample_lab_personnel
    - test_add_tests_to_sample_scientist_denied
    - test_add_tests_to_sample_no_test_types
    - test_add_tests_to_nonexistent_requisition
    - test_add_tests_to_nonexistent_sample
    - test_add_duplicate_tests
- GET /requisitions/{req_id}/samples/{sample_id}/tests
    - test_get_sample_tests_success
    - test_get_sample_tests_unauthorized
    - test_get_tests_nonexistent_requisition
    - test_get_tests_nonexistent_sample
    - test_get_sample_tests_no_tests
- PUT /requisitions/{req_id}/samples/{sample_id}/tests/{test_id}
    - test_update_test_status_lab_admin
    - test_update_test_status_lab_personnel
    - test_update_test_status_scientist_denied
    - test_update_test_status_nonexistent_test
    - test_update_test_status_invalid_status
- DELETE /requisitions/{req_id}/samples/{sample_id}/tests/{test_id}
    - test_remove_test_from_sample_success
    - test_remove_test_from_sample_unauthorized
    - test_remove_nonexistent_test
    - test_remove_test_from_nonexistent_requisition
    - test_remove_test_from_nonexistent_sample
"""

import uuid

import pytest
from datetime import datetime, timedelta

from app.common.constants import (
    UserRole,
    RequisitionStatus,
    PriorityLevel,
    ERROR_MESSAGES,
)
from app.database import get_db
from tests.test_utils.constants import (
    TEST_REQUISITION_DATA,
    ROLE_ACCESS_TEST_CASES,
    PAGINATION_TEST_CASES,
    ARCHIVE_PERMISSION_TEST_CASES
)
from app.schemas.requisition import RequisitionCreate, RequisitionUpdate
from tests.test_utils.helpers import TestHelpers
from tests.test_utils.verification import (
    RequisitionVerifier, 
    RequisitionSampleVerifier, 
    RequisitionSampleTestVerifier, 
    verify_api_error_response
)

class TestRequisitions:
    #
    # POST /requisitions/ endpoint tests
    #
    def test_create_requisition_scientist(self, client, user_token, test_lab):
        headers = TestHelpers.Auth.get_headers(user_token)
        response = client.post(f"/requisitions/{test_lab.lab_id}", headers=headers, json=TEST_REQUISITION_DATA["scientist"])
        
        verify_api_error_response(response, 200)
        RequisitionVerifier.verify_response(response.json(), TEST_REQUISITION_DATA["scientist"])
        RequisitionVerifier.verify_common_fields(response.json())
        RequisitionVerifier.verify_response(response.json(), {
            "status": RequisitionStatus.SUBMITTED.value,
            "is_archived": False
        })
        assert "req_id" in response.json()
        assert "submitted_by" in response.json()
        assert "created_at" in response.json()

    def test_create_requisition_lab_admin(self, client, admin_token, test_lab):
        headers = TestHelpers.Auth.get_headers(admin_token)
        response = client.post(f"/requisitions/{test_lab.lab_id}", headers=headers, json=TEST_REQUISITION_DATA["admin"])
        
        assert response.status_code == 200
        RequisitionVerifier.verify_response(response.json(), TEST_REQUISITION_DATA["admin"])

    def test_create_requisition_lab_personnel(self, client, lab_personnel_token, test_lab):
        headers = TestHelpers.Auth.get_headers(lab_personnel_token)
        response = client.post(f"/requisitions/{test_lab.lab_id}", headers=headers, json=TEST_REQUISITION_DATA["lab_personnel"])
        verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_create"])

    def test_create_requisition_invalid_data(self, client, user_token, test_lab):
        headers = TestHelpers.Auth.get_headers(user_token)
        
        # Missing required fields
        response = client.post(f"/requisitions/{test_lab.lab_id}", headers=headers, json=TEST_REQUISITION_DATA["invalid"]["missing_required"])
        verify_api_error_response(response, 422, ERROR_MESSAGES["requisition"]["field_required"])
        
        # Invalid priority value
        response = client.post(f"/requisitions/{test_lab.lab_id}", headers=headers, json=TEST_REQUISITION_DATA["invalid"]["invalid_priority"])
        verify_api_error_response(response, 422, ERROR_MESSAGES["requisition"]["invalid_priority"])

    def test_create_requisition_server_error(self, client, user_token, monkeypatch, test_lab):
        headers = TestHelpers.Auth.get_headers(user_token)
        requisition_data = {
            **TEST_REQUISITION_DATA["scientist"]
        }

        # Mock the crud function to raise an exception
        def mock_create_requisition(*args, **kwargs):
            raise Exception("Database error")

        monkeypatch.setattr(
            "app.crud.requisition.create_requisition",
            mock_create_requisition
        )

        response = client.post(f"/requisitions/{test_lab.lab_id}", headers=headers, json=requisition_data)
        verify_api_error_response(
            response, 
            500, 
            ERROR_MESSAGES["requisition"]["create_failed"].format(error=ERROR_MESSAGES["requisition"]["database_error"])
        )

    def test_create_requisition_unauthorized(self, client, test_lab):
        requisition_data = {
            **TEST_REQUISITION_DATA["scientist"],
            "deadline": (datetime.now() + timedelta(days=7)).isoformat(),
            "expected_completion_date": (datetime.now() + timedelta(days=5)).isoformat()
        }
        response = client.post(f"/requisitions/{test_lab.lab_id}", json=requisition_data)
        verify_api_error_response(response, 401, ERROR_MESSAGES["auth"]["not_authenticated"])

    def test_create_requisition_custom_dates(self, client, user_token, test_lab):
        headers = TestHelpers.Auth.get_headers(user_token)
        custom_data = {
            **TEST_REQUISITION_DATA["scientist"],
            "deadline": (datetime.now() + timedelta(days=10)).isoformat(),
            "expected_completion_date": (datetime.now() + timedelta(days=8)).isoformat()
        }
        response = client.post(f"/requisitions/{test_lab.lab_id}", headers=headers, json=custom_data)
        
        verify_api_error_response(response, 200)
        RequisitionVerifier.verify_response(response.json(), custom_data)
    
    def test_create_requisition_by_impossible_lab(self, client, user_token, test_requisitions):
        response = client.post(f"/requisitions/{uuid.uuid4()}", headers=TestHelpers.Auth.get_headers(user_token) , json=TEST_REQUISITION_DATA["admin"])
        verify_api_error_response(response, 500, ERROR_MESSAGES['requisition']['create_failed'].format(error="(psycopg2.errors.ForeignKeyViolation)"))
    
    def test_create_requisition_by_invalid_lab_id(self, client, admin_token, test_labs):
        response = client.post(f"/requisitions/{test_labs[0].lab_id}", headers=TestHelpers.Auth.get_headers(admin_token), json=TEST_REQUISITION_DATA["admin"])
        verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_lab"])

    #
    # GET /requisitions/lab endpoint tests
    #
    def test_list_requisitions(self, client, admin_token, test_requisition):
        response = client.get(f"/requisitions/lab/{test_requisition.lab_id}", headers=TestHelpers.Auth.get_headers(admin_token))
        verify_api_error_response(response, 200)
        requisitions = response.json()
        assert isinstance(requisitions, list)
        assert len(requisitions) >= 1

        # Verify each requisition
        for req in requisitions:
            RequisitionVerifier.verify_common_fields(req)

    def test_list_requisitions_with_status_filter(self, client, admin_token, test_requisition):
        response = client.get(
            f"/requisitions/lab/{test_requisition.lab_id}/?status=submitted",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        assert response.status_code == 200
        requisitions = response.json()

        # Verify each requisition
        for req in requisitions:
            RequisitionVerifier.verify_common_fields(req)
            RequisitionVerifier.verify_response(req, {
                "status": RequisitionStatus.SUBMITTED.value
            })
    
    def test_list_requisitions_by_impossible_lab(self, client, admin_token, test_requisitions):
        # Admin can only access their own lab, so accessing a random lab ID should return 403
        response = client.get(
            f"/requisitions/lab/{uuid.uuid4()}",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_lab"])
    
    def test_list_requisitions_by_invalid_lab_id(self, client, admin_token, test_requisitions):
        response = client.get(
            f"/requisitions/lab/{test_requisitions[1].lab_id}", 
            headers=TestHelpers.Auth.get_headers(admin_token)  
        )
        verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_lab"])

    @pytest.mark.parametrize("user_type,expected_count", ROLE_ACCESS_TEST_CASES)
    def test_list_requisitions_by_role(self, client, request, user_type, expected_count, test_requisitions):
        token = request.getfixturevalue(user_type)
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get(f"/requisitions/lab/{test_requisitions[0].lab_id}", headers=headers)

        if user_type == "user_token":
            verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_lab_endpoint"])
        else:
            assert response.status_code == 200
            assert len(response.json()) == expected_count

    @pytest.mark.parametrize("status", [
        RequisitionStatus.SUBMITTED.value,
        RequisitionStatus.IN_PROGRESS.value,
        RequisitionStatus.COMPLETE.value,
        "invalid_status"
    ])
    def test_list_requisitions_status_filter(self, client, admin_token, test_requisitions, status):
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get(f"/requisitions/lab/{test_requisitions[0].lab_id}?status={status}", headers=headers)

        if status == "invalid_status":
            verify_api_error_response(response, 422)
        else:
            verify_api_error_response(response, 200)
            assert all(req["status"] == status for req in response.json())

    @pytest.mark.parametrize("limit,skip,expected_status", PAGINATION_TEST_CASES)
    def test_list_requisitions_pagination(self, client, admin_token, test_requisitions, limit, skip, expected_status):
        headers = {"Authorization": f"Bearer {admin_token}"}
        params = {}
        if limit is not None:
            params['limit'] = limit
        if skip is not None:
            params['skip'] = skip

        response = client.get(f"/requisitions/lab/{test_requisitions[0].lab_id}", headers=headers, params=params)
        assert response.status_code == expected_status

        if expected_status == 200:
            results = response.json()
            if limit and limit > 0:
                assert len(results) <= limit
            if skip and skip > 0:
                # Verify skip works by comparing with unskipped results
                full_response = client.get(f"/requisitions/lab/{test_requisitions[0].lab_id}", headers=headers)
                full_results = full_response.json()
                assert results == full_results[skip:]

    #
    # GET /requisitions/mine endpoint tests
    #
    def test_list_my_requisitions_success(self, client, user_token, test_requisitions, test_user):
        """Test that a scientist can successfully retrieve their own requisitions."""
        headers = TestHelpers.Auth.get_headers(user_token)
        response = client.get("/requisitions/mine", headers=headers)
        
        verify_api_error_response(response, 200)
        my_requisitions = response.json()
        assert isinstance(my_requisitions, list)
        
        # The fixture should create requisitions for the test_user
        assert len(my_requisitions) > 0, "No requisitions found for the test user."
        
        # Verify all returned requisitions were submitted by the current user
        for req in my_requisitions:
            RequisitionVerifier.verify_common_fields(req)
            assert req["submitted_by"] == str(test_user.user_id)

    def test_list_my_requisitions_with_status_filter(self, client, user_token, test_requisitions):
        """Test filtering 'my requisitions' by status."""
        headers = TestHelpers.Auth.get_headers(user_token)
        status_to_filter = RequisitionStatus.SUBMITTED.value
        response = client.get(f"/requisitions/mine?status={status_to_filter}", headers=headers)
        
        verify_api_error_response(response, 200)
        requisitions = response.json()
        assert isinstance(requisitions, list)
        
        # Verify all returned requisitions have the correct status
        for req in requisitions:
            RequisitionVerifier.verify_common_fields(req)
            assert req["status"] == status_to_filter

    def test_list_my_requisitions_unauthorized_role(self, client, admin_token, lab_personnel_token):
        """Test that non-scientist roles cannot access the /mine endpoint."""
        # Test with Admin token
        admin_headers = TestHelpers.Auth.get_headers(admin_token)
        response = client.get("/requisitions/mine", headers=admin_headers)
        verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_mine"])
        
        # Test with Lab Personnel token
        lab_personnel_headers = TestHelpers.Auth.get_headers(lab_personnel_token)
        response = client.get("/requisitions/mine", headers=lab_personnel_headers)
        verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_mine"])

    def test_list_my_requisitions_pagination(self, client, user_token, test_requisitions):
        """Test pagination for the /mine endpoint."""
        headers = TestHelpers.Auth.get_headers(user_token)
        
        response = client.get("/requisitions/mine", headers=headers)
        assert response.status_code == 200
        total_requisitions = len(response.json())
        assert total_requisitions > 2, "Need at least 3 requisitions for this pagination test"

        # Test limit
        response_limit = client.get("/requisitions/mine?limit=2", headers=headers)
        assert response_limit.status_code == 200
        data_limit = response_limit.json()
        assert len(data_limit) == 2

        # Test skip (offset)
        response_skip = client.get("/requisitions/mine?limit=2&skip=1", headers=headers)
        assert response_skip.status_code == 200
        data_skip = response_skip.json()
        assert len(data_skip) > 0

        # Verify that the item at the skip position is the second item from the full list
        response_full = client.get("/requisitions/mine", headers=headers)
        data_full = response_full.json()
        if len(data_full) > 1:
            assert data_skip[0]['req_id'] == data_full[1]['req_id']

    def test_list_my_requisitions_sorting(self, client, user_token, test_requisitions):
        """Test sorting for the /mine endpoint."""
        headers = TestHelpers.Auth.get_headers(user_token)
        
        # Test sorting by created_at descending
        response_desc = client.get("/requisitions/mine?sort_by=created_at&sort_order=desc", headers=headers)
        assert response_desc.status_code == 200
        data_desc = response_desc.json()
        assert len(data_desc) > 1
        dates = [item['created_at'] for item in data_desc]
        assert dates == sorted(dates, reverse=True)

        # Test sorting by priority ascending
        response_asc = client.get("/requisitions/mine?sort_by=priority&sort_order=asc", headers=headers)
        assert response_asc.status_code == 200
        data_asc = response_asc.json()
        assert len(data_asc) > 1
        priority_map = {p.value: i for i, p in enumerate([PriorityLevel.LOW, PriorityLevel.MEDIUM, PriorityLevel.HIGH])}
        priorities = [item['priority'] for item in data_asc]
        assert priorities == sorted(priorities, key=lambda p: priority_map[p])

    def test_list_my_requisitions_no_results(self, client, user_token):
        """Test /mine endpoint for a scientist with no requisitions."""
        headers = TestHelpers.Auth.get_headers(user_token)

        response = client.get("/requisitions/mine", headers=headers)

        assert response.status_code == 200
        data = response.json()
        # The response should be a list (empty or with requisitions)
        assert isinstance(data, list)

    def test_list_my_requisitions_with_multiple_status_filters(self, client, user_token, test_requisitions):
        """Test filtering by multiple statuses for the /mine endpoint."""
        headers = TestHelpers.Auth.get_headers(user_token)
        
        response = client.get("/requisitions/mine?status=submitted&status=in_progress", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) > 0
        
        for req in data:
            assert req['status'] in [RequisitionStatus.SUBMITTED.value, RequisitionStatus.IN_PROGRESS.value]
        
        all_statuses = {req['status'] for req in data}
        assert RequisitionStatus.COMPLETE.value not in all_statuses

    #
    # GET /requisitions/{req_id} endpoint tests
    #
    def test_get_requisition_by_id(self, client, user_token, test_requisition):
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.get(f"/requisitions/{test_requisition.req_id}", headers=headers)
        verify_api_error_response(response, 200)
        RequisitionVerifier.verify_response(response.json(), {
            "req_id": str(test_requisition.req_id)
        })

    def test_get_nonexistent_requisition(self, client, user_token):
        headers = {"Authorization": f"Bearer {user_token}"}
        nonexistent_uuid = str(uuid.uuid4())  # Generate a random valid UUID
        response = client.get(f"/requisitions/{nonexistent_uuid}", headers=headers)
        verify_api_error_response(response, 404, ERROR_MESSAGES["requisition"]["not_found"])

    def test_get_requisition_by_id_access_control(self, client, user_token, admin_token, lab_personnel_token, test_requisitions):
        """Test access control rules for getting a requisition by ID:
        - Scientists can only access their own requisitions
        - Lab Personnel and Lab Admin can access any requisition"""
        
        # Get a requisition created by admin (not the scientist)
        admin_requisition = next(r for r in test_requisitions if r.submitted_by != test_requisitions[0].submitted_by)
        
        # Scientist trying to access someone else's requisition
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.get(f"/requisitions/{admin_requisition.req_id}", headers=headers)
        verify_api_error_response(response, 404, ERROR_MESSAGES["requisition"]["not_found"])
        
        # Lab Personnel can access any requisition
        headers = {"Authorization": f"Bearer {lab_personnel_token}"}
        response = client.get(f"/requisitions/{admin_requisition.req_id}", headers=headers)
        verify_api_error_response(response, 200)
        RequisitionVerifier.verify_response(response.json(), {
            "req_id": str(admin_requisition.req_id)
        })
        
        # Admin can access any requisition
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get(f"/requisitions/{admin_requisition.req_id}", headers=headers)
        verify_api_error_response(response, 200)
        RequisitionVerifier.verify_response(response.json(), {
            "req_id": str(admin_requisition.req_id)
        })

    def test_get_requisition_by_id_scientist_own_requisition(self, client, user_token, test_requisitions):
        """Test that scientists can access their own requisitions"""
        # Get a requisition created by the scientist
        scientist_requisition = next(r for r in test_requisitions if r.submitted_by == r.submitted_by)
        
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.get(f"/requisitions/{scientist_requisition.req_id}", headers=headers)
        verify_api_error_response(response, 200)
        RequisitionVerifier.verify_response(response.json(), {
            "req_id": str(scientist_requisition.req_id)
        })

    #
    # PUT /requisitions/{req_id} endpoint tests
    #
    def test_update_requisition_lab_admin(self, client, admin_token, test_requisition):
        headers = TestHelpers.Auth.get_headers(admin_token)
        response = client.put(
            f"/requisitions/{test_requisition.req_id}",
            headers=headers,
            json=TEST_REQUISITION_DATA["updates"]["status_change"]
        )
        verify_api_error_response(response, 200)
        RequisitionVerifier.verify_response(response.json(), {
            "status": RequisitionStatus.IN_PROGRESS.value
        })

    def test_update_requisition_unauthorized(self, client, user_token, test_requisition):
        headers = TestHelpers.Auth.get_headers(user_token)
        response = client.put(
            f"/requisitions/{test_requisition.req_id}",
            headers=headers,
            json=TEST_REQUISITION_DATA["updates"]["status_change"]
        )
        verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_modify"])

    def test_archive_requisition_admin(self, client, admin_token, test_requisition):
        headers = TestHelpers.Auth.get_headers(admin_token)
        response = client.put(
            f"/requisitions/{test_requisition.req_id}",
            headers=headers,
            json=TEST_REQUISITION_DATA["updates"]["archive"]
        )
        assert response.status_code == 200
        response_data = response.json()
        RequisitionVerifier.verify_common_fields(response_data)
        RequisitionVerifier.verify_response(response_data, {
            "is_archived": True
        })

    def test_update_requisition_lab_personnel(self, client, lab_personnel_token, test_requisition):
        headers = TestHelpers.Auth.get_headers(lab_personnel_token)
        response = client.put(
            f"/requisitions/{test_requisition.req_id}",
            headers=headers,
            json=TEST_REQUISITION_DATA["updates"]["status_change"]
        )
        verify_api_error_response(response, 200)
        RequisitionVerifier.verify_response(response.json(), {
            "status": RequisitionStatus.IN_PROGRESS.value,
            "comments": TEST_REQUISITION_DATA["updates"]["status_change"]["comments"]
        })

    def test_update_requisition_scientist(self, client, user_token, test_requisition):
        headers = TestHelpers.Auth.get_headers(user_token)
        response = client.put(
            f"/requisitions/{test_requisition.req_id}",
            headers=headers,
            json=TEST_REQUISITION_DATA["updates"]["status_change"]
        )
        assert response.status_code == 403
        assert ERROR_MESSAGES["requisition"]["unauthorized_modify"] in response.json()["detail"]

    def test_update_requisition_status_and_archive(self, client, admin_token, test_requisition):
        headers = TestHelpers.Auth.get_headers(admin_token)
        response = client.put(
            f"/requisitions/{test_requisition.req_id}",
            headers=headers,
            json=TEST_REQUISITION_DATA["updates"]["status_and_archive"]
        )
        verify_api_error_response(response, 200)
        RequisitionVerifier.verify_response(response.json(), {
            "status": RequisitionStatus.IN_PROGRESS.value,
            "is_archived": True
        })

    def test_update_requisition_archive_by_lab_personnel(self, client, lab_personnel_token, test_requisition):
        headers = TestHelpers.Auth.get_headers(lab_personnel_token)
        response = client.put(
            f"/requisitions/{test_requisition.req_id}",
            headers=headers,
            json=TEST_REQUISITION_DATA["updates"]["archive"]
        )
        assert response.status_code == 403
        assert ERROR_MESSAGES["requisition"]["unauthorized_archive"] in response.json()["detail"]

    @pytest.mark.parametrize("token_fixture,expected_status,expected_message", ARCHIVE_PERMISSION_TEST_CASES)
    def test_archive_requisition_by_role(
        self, 
        client, 
        request,
        test_requisition,
        token_fixture,
        expected_status,
        expected_message
    ):
        """Test archiving permissions for different user roles"""
        token = request.getfixturevalue(token_fixture)
        headers = {"Authorization": f"Bearer {token}"}
        update_data = {
            "is_archived": True
        }
        
        response = client.put(
            f"/requisitions/{test_requisition.req_id}",
            headers=headers,
            json=update_data
        )
        
        if expected_message:
            verify_api_error_response(response, expected_status, expected_message)
        elif expected_status == 200:
            verify_api_error_response(response, expected_status)
            RequisitionVerifier.verify_response(response.json(), {
                "is_archived": True
            })

    #
    # POST /requisitions/{req_id}/samples endpoint tests
    #
    
    def test_add_samples_to_requisition_success(self, client, lab_personnel_token, test_requisition, test_samples):
        """Test successfully adding samples to a requisition"""
        headers = TestHelpers.Auth.get_headers(lab_personnel_token)  # Use lab personnel token since they can modify any requisition
        sample_ids = [str(sample.sample_id) for sample in test_samples[:2]]
        
        response = client.post(
            f"/requisitions/{str(test_requisition.req_id)}/samples",
            headers=headers,
            json={"sample_ids": sample_ids}
        )
        
        verify_api_error_response(response, 200)
        data = response.json()
        assert len(data) == len(sample_ids)
        
        # Verify each requisition sample using the verifier
        for sample in data:
            RequisitionSampleVerifier.verify_common_fields(sample)
            RequisitionSampleVerifier.verify_response(sample, {
                "requisition_id": str(test_requisition.req_id),
                "status": RequisitionStatus.SUBMITTED.value
            })
            assert sample["sample_id"] in sample_ids

    def test_add_samples_to_requisition_unauthorized(self, client, user_token, test_requisition, test_samples):
        """Test adding samples to a requisition without proper authorization"""
        # Use scientist token on a requisition they don't own
        headers = TestHelpers.Auth.get_headers(user_token)
        sample_ids = [str(sample.sample_id) for sample in test_samples[:2]]
        
        response = client.post(
            f"/requisitions/{str(test_requisition.req_id)}/samples",
            headers=headers,
            json={"sample_ids": sample_ids}
        )
        
        verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_modify"])

    def test_add_samples_to_nonexistent_requisition(self, client, lab_personnel_token, test_samples):
        """Test adding samples to a non-existent requisition"""
        headers = TestHelpers.Auth.get_headers(lab_personnel_token)
        sample_ids = [str(sample.sample_id) for sample in test_samples[:2]]
        non_existent_req_id = str(uuid.uuid4())
        
        response = client.post(
            f"/requisitions/{non_existent_req_id}/samples",
            headers=headers,
            json={"sample_ids": sample_ids}
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["requisition"]["not_found"])

    def test_add_nonexistent_samples_to_requisition(self, client, lab_personnel_token, test_requisition):
        """Test adding non-existent samples to a requisition"""
        headers = TestHelpers.Auth.get_headers(lab_personnel_token)
        non_existent_sample_ids = [str(uuid.uuid4()) for _ in range(2)]
        
        response = client.post(
            f"/requisitions/{str(test_requisition.req_id)}/samples",
            headers=headers,
            json={"sample_ids": non_existent_sample_ids}
        )
        
        verify_api_error_response(
            response, 
            404, 
            ERROR_MESSAGES["sample"]["not_found"]
        )

    #
    # GET /requisitions/{req_id}/samples endpoint tests
    #
    def test_get_requisition_samples_success(self, client, user_token, test_requisition_with_samples):
        """Test successfully getting samples from a requisition"""
        headers = TestHelpers.Auth.get_headers(user_token)
        
        response = client.get(
            f"/requisitions/{str(test_requisition_with_samples.req_id)}/samples",
            headers=headers
        )
        
        verify_api_error_response(response, 200)
        data = response.json()
        assert len(data) > 0
        assert all(sample["requisition_id"] == str(test_requisition_with_samples.req_id) for sample in data)

    def test_get_requisition_samples_unauthorized(self, client, test_requisition_with_samples):
        """Test getting samples from a requisition without authentication"""
        response = client.get(f"/requisitions/{str(test_requisition_with_samples.req_id)}/samples")
        verify_api_error_response(response, 401, ERROR_MESSAGES["auth"]["not_authenticated"])

    def test_get_samples_from_nonexistent_requisition(self, client, user_token):
        """Test getting samples from a non-existent requisition"""
        headers = TestHelpers.Auth.get_headers(user_token)
        non_existent_req_id = str(uuid.uuid4())
        
        response = client.get(
            f"/requisitions/{non_existent_req_id}/samples",
            headers=headers
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["requisition"]["not_found"])

    #
    # DELETE /requisitions/{req_id}/samples/{sample_id} endpoint tests
    #
    def test_remove_sample_from_requisition_success(self, client, lab_personnel_token, test_requisition_with_samples):
        """Test successfully removing a sample from a requisition"""
        headers = TestHelpers.Auth.get_headers(lab_personnel_token)
        sample_id = str(test_requisition_with_samples.samples[0].sample_id)
        req_id = str(test_requisition_with_samples.req_id)
        
        response = client.delete(
            f"/requisitions/{req_id}/samples/{sample_id}",
            headers=headers
        )
        
        assert response.status_code == 200
        assert response.json() == {
            "message": ERROR_MESSAGES["requisition"]["remove_sample_success"],
            "requisition_id": req_id,
            "sample_id": sample_id
        }

    def test_remove_sample_from_requisition_unauthorized(self, client, user_token, test_requisition_with_samples):
        """Test removing a sample from a requisition without proper authorization"""
        # Use scientist token on a requisition they don't own
        headers = TestHelpers.Auth.get_headers(user_token)
        sample_id = str(test_requisition_with_samples.samples[0].sample_id)
        
        response = client.delete(
            f"/requisitions/{str(test_requisition_with_samples.req_id)}/samples/{sample_id}",
            headers=headers
        )
        
        verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_modify"])

    def test_remove_nonexistent_sample_from_requisition(self, client, lab_personnel_token, test_requisition):
        """Test removing a non-existent sample from a requisition"""
        headers = TestHelpers.Auth.get_headers(lab_personnel_token)
        non_existent_sample_id = str(uuid.uuid4())
        
        response = client.delete(
            f"/requisitions/{str(test_requisition.req_id)}/samples/{non_existent_sample_id}",
            headers=headers
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["requisition"]["sample_not_found"])

    def test_remove_sample_from_nonexistent_requisition(self, client, lab_personnel_token, test_samples):
        """Test removing a sample from a non-existent requisition"""
        headers = TestHelpers.Auth.get_headers(lab_personnel_token)
        sample_id = str(test_samples[0].sample_id)
        non_existent_req_id = str(uuid.uuid4())
        
        response = client.delete(
            f"/requisitions/{non_existent_req_id}/samples/{sample_id}",
            headers=headers
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["requisition"]["not_found"])

    #
    # POST /requisitions/{req_id}/samples/{sample_id}/tests endpoint tests
    #
    def test_add_tests_to_sample_lab_admin(self, client, admin_token, test_requisition, test_sample, test_type):
        """Test that lab admin can successfully add tests to a requisition sample."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        test_data = {
            "test_type_ids": [str(test_type.test_type_id)]
        }
        
        # First add the sample to the requisition
        db = next(client.app.dependency_overrides[get_db]())
        TestHelpers.Requisitions.add_sample_to_requisition(
            db=db,
            req_id=test_requisition.req_id,
            sample_id=test_sample.sample_id
        )
        
        response = client.post(
            f"/requisitions/{test_requisition.req_id}/samples/{test_sample.sample_id}/tests",
            headers=headers,
            json=test_data
        )
        
        verify_api_error_response(response, 200)
        result = response.json()
        assert len(result) == 1
        
        # Verify the test using the verifier
        RequisitionSampleTestVerifier.verify_common_fields(result[0])
        RequisitionSampleTestVerifier.verify_response(result[0], {
            "test_type_id": test_data["test_type_ids"][0],
            "requisition_id": str(test_requisition.req_id),
            "sample_id": str(test_sample.sample_id)
        })

    def test_add_tests_to_sample_lab_personnel(self, client, lab_personnel_token, test_requisition, test_sample, test_type):
        """Test that lab personnel can successfully add tests to a requisition sample."""
        headers = TestHelpers.Auth.get_headers(lab_personnel_token)
        test_data = {
            "test_type_ids": [str(test_type.test_type_id)]
        }
        
        # First add the sample to the requisition
        db = next(client.app.dependency_overrides[get_db]())
        TestHelpers.Requisitions.add_sample_to_requisition(
            db=db,
            req_id=test_requisition.req_id,
            sample_id=test_sample.sample_id
        )
        
        response = client.post(
            f"/requisitions/{test_requisition.req_id}/samples/{test_sample.sample_id}/tests",
            headers=headers,
            json=test_data
        )
        
        verify_api_error_response(response, 200)
        result = response.json()
        assert len(result) == 1
        
        # Verify the test using the verifier
        RequisitionSampleTestVerifier.verify_common_fields(result[0])
        RequisitionSampleTestVerifier.verify_response(result[0], {
            "test_type_id": test_data["test_type_ids"][0],
            "requisition_id": str(test_requisition.req_id),
            "sample_id": str(test_sample.sample_id)
        })

    def test_add_tests_to_sample_scientist_denied(self, client, user_token, test_requisition, test_sample, test_type):
        """Test that scientists cannot add tests to a requisition sample."""
        headers = TestHelpers.Auth.get_headers(user_token)
        test_data = {
            "test_type_ids": [str(test_type.test_type_id)]
        }
        
        # First add the sample to the requisition
        db = next(client.app.dependency_overrides[get_db]())
        TestHelpers.Requisitions.add_sample_to_requisition(
            db=db,
            req_id=test_requisition.req_id,
            sample_id=test_sample.sample_id
        )
        
        response = client.post(
            f"/requisitions/{test_requisition.req_id}/samples/{test_sample.sample_id}/tests",
            headers=headers,
            json=test_data
        )
        
        assert response.status_code == 403
        assert response.json()["detail"] == ERROR_MESSAGES["requisition"]["unauthorized_modify"]

    def test_add_tests_to_sample_no_test_types(self, client, admin_token, test_requisition, test_sample):
        """Test that providing no test types returns validation error."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        test_data = {
            "test_type_ids": []
        }
        
        # First add the sample to the requisition
        db = next(client.app.dependency_overrides[get_db]())
        TestHelpers.Requisitions.add_sample_to_requisition(
            db=db,
            req_id=test_requisition.req_id,
            sample_id=test_sample.sample_id
        )
        
        response = client.post(
            f"/requisitions/{test_requisition.req_id}/samples/{test_sample.sample_id}/tests",
            headers=headers,
            json=test_data
        )
        
        verify_api_error_response(response, 400, ERROR_MESSAGES["test"]["no_test_types"])

    def test_add_tests_to_nonexistent_requisition(self, client, admin_token, test_sample, test_type):
        """Test that adding tests to a nonexistent requisition returns not found error."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        test_data = {
            "test_type_ids": [str(test_type.test_type_id)]
        }
        
        response = client.post(
            f"/requisitions/{uuid.uuid4()}/samples/{test_sample.sample_id}/tests",
            headers=headers,
            json=test_data
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["requisition"]["not_found"])

    def test_add_tests_to_nonexistent_sample(self, client, admin_token, test_requisition, test_type):
        """Test that adding tests to a nonexistent sample returns not found error."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        test_data = {
            "test_type_ids": [str(test_type.test_type_id)]
        }
        
        response = client.post(
            f"/requisitions/{test_requisition.req_id}/samples/{uuid.uuid4()}/tests",
            headers=headers,
            json=test_data
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["requisition"]["sample_not_found"])

    def test_add_duplicate_tests(self, client, admin_token, test_requisition, test_sample, test_type):
        """Test that adding duplicate tests returns a state error."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        test_data = {
            "test_type_ids": [str(test_type.test_type_id)]
        }
        
        # First add the sample to the requisition
        db = next(client.app.dependency_overrides[get_db]())
        TestHelpers.Requisitions.add_sample_to_requisition(
            db=db,
            req_id=test_requisition.req_id,
            sample_id=test_sample.sample_id
        )
        
        # Add the test for the first time
        response = client.post(
            f"/requisitions/{test_requisition.req_id}/samples/{test_sample.sample_id}/tests",
            headers=headers,
            json=test_data
        )
        verify_api_error_response(response, 200)
        
        # Try to add the same test again
        response = client.post(
            f"/requisitions/{test_requisition.req_id}/samples/{test_sample.sample_id}/tests",
            headers=headers,
            json=test_data
        )
        
        verify_api_error_response(response, 409, ERROR_MESSAGES["test"]["already_exists"])

    #
    # GET /requisitions/{req_id}/samples/{sample_id}/tests endpoint tests
    #
    def test_get_sample_tests_success(self, client, admin_token, test_requisition, test_sample, test_type):
        """Test successfully getting tests for a sample in a requisition."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        
        # First add the sample to the requisition and add a test
        db = next(client.app.dependency_overrides[get_db]())
        TestHelpers.Requisitions.add_sample_to_requisition(
            db=db,
            req_id=test_requisition.req_id,
            sample_id=test_sample.sample_id
        )
        
        # Add test to the sample
        test_data = {
            "test_type_ids": [str(test_type.test_type_id)]
        }
        client.post(
            f"/requisitions/{test_requisition.req_id}/samples/{test_sample.sample_id}/tests",
            headers=headers,
            json=test_data
        )
        
        # Get the tests
        response = client.get(
            f"/requisitions/{test_requisition.req_id}/samples/{test_sample.sample_id}/tests",
            headers=headers
        )
        
        verify_api_error_response(response, 200)
        result = response.json()
        assert len(result) == 1
        
        # Verify the test using the verifier
        RequisitionSampleTestVerifier.verify_common_fields(result[0])
        RequisitionSampleTestVerifier.verify_response(result[0], {
            "test_type_id": str(test_type.test_type_id),
            "requisition_id": str(test_requisition.req_id),
            "sample_id": str(test_sample.sample_id)
        })

    def test_get_sample_tests_unauthorized(self, client, test_requisition, test_sample):
        """Test getting tests without authentication."""
        response = client.get(
            f"/requisitions/{test_requisition.req_id}/samples/{test_sample.sample_id}/tests"
        )
        verify_api_error_response(
            response, 
            401, 
            ERROR_MESSAGES["auth"]["not_authenticated"]
        )

    def test_get_tests_nonexistent_requisition(self, client, admin_token, test_sample):
        """Test getting tests from a nonexistent requisition."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        non_existent_req_id = str(uuid.uuid4())
        
        response = client.get(
            f"/requisitions/{non_existent_req_id}/samples/{test_sample.sample_id}/tests",
            headers=headers
        )
        
        verify_api_error_response(
            response, 
            404, 
            ERROR_MESSAGES["requisition"]["not_found"]
        )

    def test_get_tests_nonexistent_sample(self, client, admin_token, test_requisition):
        """Test getting tests for a nonexistent sample."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        non_existent_sample_id = str(uuid.uuid4())
        
        response = client.get(
            f"/requisitions/{test_requisition.req_id}/samples/{non_existent_sample_id}/tests",
            headers=headers
        )
        
        verify_api_error_response(
            response, 
            404, 
            ERROR_MESSAGES["requisition"]["sample_not_found"]
        )

    def test_get_sample_tests_no_tests(self, client, admin_token, test_requisition, test_sample):
        """Test getting tests for a sample that has no tests."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        
        # First add the sample to the requisition without any tests
        db = next(client.app.dependency_overrides[get_db]())
        TestHelpers.Requisitions.add_sample_to_requisition(
            db=db,
            req_id=test_requisition.req_id,
            sample_id=test_sample.sample_id
        )
        
        response = client.get(
            f"/requisitions/{test_requisition.req_id}/samples/{test_sample.sample_id}/tests",
            headers=headers
        )
        
        verify_api_error_response(response, 200)
        assert response.json() == []

    #
    # PUT /requisitions/{req_id}/samples/{sample_id}/tests/{test_id} endpoint tests
    #

    def test_update_test_status_lab_admin(self, client, admin_token, test_requisition_with_tests):
        """Test that lab admin can successfully update test status."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        test = test_requisition_with_tests.samples[0].tests[0]
        
        update_data = {
            "status": "in_progress"
        }
        
        response = client.put(
            f"/requisitions/{test_requisition_with_tests.req_id}/samples/{test_requisition_with_tests.samples[0].sample_id}/tests/{test.req_sample_test_id}",
            headers=headers,
            json=update_data
        )
        
        verify_api_error_response(response, 200)
        result = response.json()
        
        # Verify the test using the verifier
        RequisitionSampleTestVerifier.verify_common_fields(result)
        RequisitionSampleTestVerifier.verify_response(result, {
            "status": "in_progress"
        })
        assert result["processed_by"] is not None

    def test_update_test_status_lab_personnel(self, client, lab_personnel_token, test_requisition_with_tests):
        """Test that lab personnel can successfully update test status."""
        headers = TestHelpers.Auth.get_headers(lab_personnel_token)
        test = test_requisition_with_tests.samples[0].tests[0]
        
        update_data = {
            "status": "complete"
        }
        
        response = client.put(
            f"/requisitions/{test_requisition_with_tests.req_id}/samples/{test_requisition_with_tests.samples[0].sample_id}/tests/{test.req_sample_test_id}",
            headers=headers,
            json=update_data
        )
        
        verify_api_error_response(response, 200)
        result = response.json()
        
        # Verify the test using the verifier
        RequisitionSampleTestVerifier.verify_common_fields(result)
        RequisitionSampleTestVerifier.verify_response(result, {
            "status": "complete"
        })
        assert result["processed_by"] is not None
        assert "completed_at" in result

    def test_update_test_status_scientist_denied(self, client, user_token, test_requisition_with_tests):
        """Test that scientists cannot update test status."""
        headers = TestHelpers.Auth.get_headers(user_token)
        test = test_requisition_with_tests.samples[0].tests[0]
        
        update_data = {
            "status": "in_progress"
        }
        
        response = client.put(
            f"/requisitions/{test_requisition_with_tests.req_id}/samples/{test_requisition_with_tests.samples[0].sample_id}/tests/{test.req_sample_test_id}",
            headers=headers,
            json=update_data
        )
        
        verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_modify"])

    def test_update_test_status_nonexistent_test(self, client, admin_token, test_requisition_with_tests):
        """Test updating status of a nonexistent test."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        
        update_data = {
            "status": "in_progress"
        }
        
        response = client.put(
            f"/requisitions/{test_requisition_with_tests.req_id}/samples/{test_requisition_with_tests.samples[0].sample_id}/tests/{uuid.uuid4()}",
            headers=headers,
            json=update_data
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["test"]["not_found"])

    def test_update_test_status_invalid_status(self, client, admin_token, test_requisition_with_tests):
        """Test updating test with invalid status."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        test = test_requisition_with_tests.samples[0].tests[0]
        
        update_data = {
            "status": "invalid_status"
        }
        
        response = client.put(
            f"/requisitions/{test_requisition_with_tests.req_id}/samples/{test_requisition_with_tests.samples[0].sample_id}/tests/{test.req_sample_test_id}",
            headers=headers,
            json=update_data
        )
        
        verify_api_error_response(response, 422)

    def test_remove_test_from_sample_success(self, client, admin_token, test_requisition_with_tests):
        """Test successfully removing a test from a requisition sample."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        test = test_requisition_with_tests.samples[0].tests[0]
        req_id = str(test_requisition_with_tests.req_id)
        sample_id = str(test_requisition_with_tests.samples[0].sample_id)
        test_id = str(test.req_sample_test_id)
        
        response = client.delete(
            f"/requisitions/{req_id}/samples/{sample_id}/tests/{test_id}",
            headers=headers
        )
        
        assert response.status_code == 200
        assert response.json() == {
            "message": ERROR_MESSAGES["requisition"]["remove_test_success"],
            "requisition_id": req_id,
            "sample_id": sample_id,
            "test_id": test_id
        }

    def test_remove_test_from_sample_unauthorized(self, client, user_token, test_requisition_with_tests):
        """Test that scientists cannot remove tests from a requisition sample."""
        headers = TestHelpers.Auth.get_headers(user_token)
        test = test_requisition_with_tests.samples[0].tests[0]
        
        response = client.delete(
            f"/requisitions/{test_requisition_with_tests.req_id}/samples/{test_requisition_with_tests.samples[0].sample_id}/tests/{test.req_sample_test_id}",
            headers=headers
        )
        
        verify_api_error_response(response, 403, ERROR_MESSAGES["requisition"]["unauthorized_modify"])

    def test_remove_nonexistent_test(self, client, admin_token, test_requisition_with_tests):
        """Test removing a nonexistent test."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        
        response = client.delete(
            f"/requisitions/{test_requisition_with_tests.req_id}/samples/{test_requisition_with_tests.samples[0].sample_id}/tests/{uuid.uuid4()}",
            headers=headers
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["test"]["not_found"])

    def test_remove_test_from_nonexistent_requisition(self, client, admin_token, test_sample):
        """Test removing a test from a nonexistent requisition."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        
        response = client.delete(
            f"/requisitions/{uuid.uuid4()}/samples/{test_sample.sample_id}/tests/{uuid.uuid4()}",
            headers=headers
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["requisition"]["not_found"])

    def test_remove_test_from_nonexistent_sample(self, client, admin_token, test_requisition):
        """Test removing a test from a nonexistent sample."""
        headers = TestHelpers.Auth.get_headers(admin_token)
        
        response = client.delete(
            f"/requisitions/{test_requisition.req_id}/samples/{uuid.uuid4()}/tests/{uuid.uuid4()}",
            headers=headers
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["requisition"]["sample_not_found"])