/*
 * Authentication helper functions for Sed-LIMS
 * Core utilities for JWT handling, token management, and auth redirects
 */
import { redirectToPageByKey } from '../helpers/navigation-helpers.js';

// Parse a JWT token into its payload
export function parseJWT(token) {
    return JSON.parse(atob(token.split('.')[1]));
}

// Check if a token is expired
export function isTokenExpired(payload) {
    const exp = payload.exp;
    const now = Math.floor(Date.now() / 1000);
    return exp < now;
}

// Redirect to the login page with appropriate language prefix
export function redirectToLogin() {
    redirectToPageByKey('login', 0);
}

// Get Authorization header with current token
export function getAuthHeaders() {
    const accessToken = localStorage.getItem('access_token');
    return {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
    };
}
