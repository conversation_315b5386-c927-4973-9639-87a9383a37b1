"""
User verification helpers for API response testing.
"""

from typing import Any, Dict, Optional
from tests.test_utils.verification.base import EntityVerifier


class UserVerifier(EntityVerifier):
    """Verifier for user entity responses"""
    
    COMMON_FIELDS = ["user_id", "email", "is_active"]
    DATETIME_FIELDS = ["created_at", "last_login"]
    OPTIONAL_FIELDS = ["created_at", "last_login", "user_id", "email", "is_active"]
    TYPE_VALIDATIONS = {
        "user_id": str,
        "email": str,
        "is_active": bool
    }


def verify_partial_user_data(actual_data, expected_data):
    """Verify that the actual data contains all the expected data values."""
    UserVerifier.verify_response(actual_data, expected_data) 