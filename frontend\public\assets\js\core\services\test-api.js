/*
 * Test API Service
 * Handles all API calls related to test types management
 */
import { ApiBase } from './api-base.js';

// API methods for test types operations
export class TestApi {
    // Create a new test type
    static createTestType(testData) {
        return ApiBase.post('/test-types/', testData);
    }

    // List test types with optional filtering
    static listTestTypes(params = {}) {
        return ApiBase.get('/test-types/', params);
    }

    // Batch update multiple test types
    static batchUpdateTestTypes(updates) {
        const batchData = {
            updates: updates
        };
        return ApiBase.post('/test-types/batch-update', batchData);
    }
}
