/*
 * Global Configuration
 *
 * STRUCTURE OVERVIEW:
 * 1. FUNCTIONAL SETTINGS - Global technical configurations (form lengths, navigation delays, etc.)
 * 2. UI TEXT - Global UI text with bilingual support (common buttons, navigation, etc.)
 * 3. MESSAGES - Cross-cutting messages organized by type and category
 *
 * ADDING NEW GLOBAL CONFIGURATIONS:
 * - UI text: Add to ui.* sections with { en: '', fr: '' } format
 * - Error messages: Add to messages.errors.* with type: 'danger'
 * - Success messages: Add to messages.success.* with type: 'success'
 * - Warning messages: Add to messages.warnings.* with type: 'warning'
 * - Info messages: Add to messages.info.* with type: 'info'
 *
 * NOTE: Entity-specific configurations should go in their respective config files
 */

export const GLOBAL_CONFIGS = {
    // ===== 1. FUNCTIONAL SETTINGS =====
    // Global technical configurations

    // Standard field lengths for form validation
    form: {
        standardLengths: {
            shortName: 50,        // Standard short name field length
            description: 200      // Standard description field length
        }
    },

    // Navigation and redirect settings
    navigation: {
        redirectDelay: 3000,     // Default delay for user operations (auth operations use 0ms)
        // Common page paths for generic navigation
        pages: {
            home: 'home.html',
            manageTestTypes: 'manage-test-types.html',
            createTest: 'create-lab-test.html',
            labInfo: 'lab-info.html',
            login: 'login.html',
            roleSelection: 'role-selection.html',
            myAccount: 'my-account.html',
            viewRequisitions: 'view-requisitions.html'
        }
    },

    // Application-level configurations
    application: {
        roles: {
            SCIENTIST: "scientist",
            LAB_PERSONNEL: "lab_personnel",
            LAB_ADMIN: "lab_admin"
        }
    },

    // API configuration
    api: {
        baseUrl: (() => {
            // window.ENV_BACKEND_API_URL is automatically injected by server middleware
            return window.ENV_BACKEND_API_URL || 'http://localhost:8000';
        })()
    },

    // ===== 2. UI TEXT (Bilingual) =====
    // Global UI text with bilingual support
    ui: {
        // Common UI elements used across the application
        common: {
            loading: {
                en: 'Loading',
                fr: 'Chargement'
            },
            notAvailable: {
                en: 'N/A',
                fr: 'N/D'
            },
            noRolesAssigned: {
                en: 'No roles assigned',
                fr: 'Aucun rôle attribué'
            },
            selectRole: {
                en: 'Select a role',
                fr: 'Sélectionner un rôle'
            },
            selectLab: {
                en: 'Select a lab',
                fr: 'Sélectionner un laboratoire'
            }
        },

        // Common button text
        buttons: {
            save: {
                en: 'Save',
                fr: 'Enregistrer'
            },
            cancel: {
                en: 'Cancel',
                fr: 'Annuler'
            }
        },

        // Common status text
        status: {
            active: {
                en: 'Active',
                fr: 'Actif'
            },
            inactive: {
                en: 'Inactive',
                fr: 'Inactif'
            }
        },

        // Role display names
        roles: {
            'scientist': {
                en: 'Scientist',
                fr: 'Scientifique'
            },
            'lab_personnel': {
                en: 'Lab Personnel',
                fr: 'Personnel de laboratoire'
            },
            'lab_admin': {
                en: 'Lab Admin',
                fr: 'Administrateur de laboratoire'
            }
        },

        // Common table elements (only used elements)
        table: {
            all: {
                en: 'All',
                fr: 'Tous'
            },
            // Common table message templates (use with entity-specific terms)
            messageTemplates: {
                emptyTable: {
                    en: 'No {entity} to display',
                    fr: 'Aucun(e) {entity} à afficher'
                },
                search: {
                    en: 'Search {entity}:',
                    fr: 'Rechercher des {entity} :'
                },
                lengthMenu: {
                    en: 'Show _MENU_ {entity} per page',
                    fr: 'Afficher _MENU_ {entity} par page'
                },
                info: {
                    en: 'Showing _START_ to _END_ of _TOTAL_ {entity}',
                    fr: 'Affichage de _START_ à _END_ sur _TOTAL_ {entity}'
                }
            }
        }
    },
    
    // ===== 3. MESSAGES (Bilingual) =====
    // Cross-cutting messages organized by type and category
    messages: {
        errors: {
            // Unified server-related error messages
            server: {
                // General server connection/operation error - covers API failures, table load failures, and build failures
                connectionError: {
                    message: {
                        en: 'Unable to connect to server or load data. Please check your connection and try again.',
                        fr: 'Impossible de se connecter au serveur ou de charger les données. Veuillez vérifier votre connexion et réessayer.'
                    },
                    type: 'danger'
                }
            },

            // Common error message templates
            templates: {
                entityCreateFailed: {
                    message: {
                        en: 'Failed to create {entity}. Please try again.',
                        fr: 'Échec de la création de {entity}. Veuillez réessayer.'
                    },
                    type: 'danger'
                },
                entityUpdateFailed: {
                    message: {
                        en: 'Failed to update {entity}. Please try again.',
                        fr: 'Échec de la mise à jour de {entity}. Veuillez réessayer.'
                    },
                    type: 'danger'
                },
                entityDeleteFailed: {
                    message: {
                        en: 'Failed to delete {entity}. Please try again.',
                        fr: 'Échec de la suppression de {entity}. Veuillez réessayer.'
                    },
                    type: 'danger'
                },
                fieldRequired: {
                    message: {
                        en: '{field} is required.',
                        fr: '{field} est requis.'
                    },
                    type: 'danger'
                },
                fieldTooLong: {
                    message: {
                        en: '{field} must be {maxLength} characters or less.',
                        fr: '{field} doit contenir {maxLength} caractères ou moins.'
                    },
                    type: 'danger'
                }
            }
        },
        
        success: {
            // Common success message templates
            templates: {
                entityCreated: {
                    message: {
                        en: '{entity} created successfully!',
                        fr: '{entity} créé avec succès !'
                    },
                    type: 'success'
                },
                entityUpdated: {
                    message: {
                        en: '{entity} updated successfully!',
                        fr: '{entity} mis à jour avec succès !'
                    },
                    type: 'success'
                },
                entityDeleted: {
                    message: {
                        en: '{entity} deleted successfully!',
                        fr: '{entity} supprimé avec succès !'
                    },
                    type: 'success'
                }
            }
        },
        
        warnings: {
            // API-related warning messages
            api: {
                authentication: {
                    message: {
                        en: 'Your session has expired. Please log in again.',
                        fr: 'Votre session a expiré. Veuillez vous reconnecter.'
                    },
                    type: 'warning'
                }
            },

            // Common warning message templates
            templates: {
                duplicateName: {
                    message: {
                        en: 'A {entity} with this name already exists. Please choose a different name.',
                        fr: 'Un(e) {entity} avec ce nom existe déjà. Veuillez choisir un nom différent.'
                    },
                    type: 'warning'
                }
            }
        },
        
        info: {
            // Common operation messages
            common: {
                noChangesToSave: {
                    message: {
                        en: 'No changes have been made to save.',
                        fr: 'Aucune modification n\'a été apportée à enregistrer.'
                    },
                    type: 'info'
                },
                cancelled: {
                    message: {
                        en: 'Operation was cancelled.',
                        fr: 'L\'opération a été annulée.'
                    },
                    type: 'info'
                }
            }
        }
    }
};
