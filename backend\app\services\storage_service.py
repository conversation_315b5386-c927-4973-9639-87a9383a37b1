import asyncio
import aiohttp
import json
import os
from io import Bytes<PERSON>

from dotenv import load_dotenv
from fastapi import HTT<PERSON><PERSON>xception
from aiohttp import FormData
from app.core.logging import log_info, log_error

# Ensure we load environment variables here
load_dotenv()

class StorageService:
    def __init__(self):
        # Force reload environment variables to ensure we have the latest
        load_dotenv(override=True)
        
        self.api_url = os.getenv("GSC_LIMS_DAS_API")
        if not self.api_url:
            raise ValueError("GSC_LIMS_DAS_API environment variable not set")
            
        # Clean up the URL if needed
        self.api_url = self.api_url.strip()
        # Remove trailing slash if present
        if self.api_url.endswith('/'):
            self.api_url = self.api_url[:-1]
            
        # Add protocol if missing
        if not self.api_url.startswith('http'):
            self.api_url = f"http://{self.api_url}"

        self.username = os.getenv("DAS_USERNAME", "gsc_lims")
        self.password = os.getenv("DAS_PASSWORD", "1234")
        self.upload_endpoint = os.getenv("DAS_UPLOAD_ENDPOINT", "/uploadBinaryObject")
        self.download_endpoint = os.getenv("DAS_DOWNLOAD_ENDPOINT", "/downloadBinaryObject")
        self.delete_endpoint = os.getenv("DAS_DELETE_ENDPOINT", "/deleteBinaryObject")

        log_info(f"StorageService initialized with API URL: {self.api_url}")

    async def get_token(self):
        token_url = f"{self.api_url}/token"
        log_info(f"Attempting to get token from: {token_url}")

        if not self.api_url:
            log_error("GSC_LIMS_DAS_API environment variable not set")
            return None

        async with aiohttp.ClientSession() as session:
            payload = {
                "username": self.username,
                "password": self.password,
            }

            try:
                async with session.post(
                        token_url,
                        json=payload
                ) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        return response_data.get("access_token")
                    log_error(f"Failed to get token. Status: {response.status}")
                    return None
            except Exception as e:
                log_error(f"Exception during token retrieval: {str(e)}")
                return None

    async def upload_file(self, file_contents: bytes, filename: str, content_type: str):
        """Upload a file to storage service"""
        token = await self.get_token()
        if not token:
            raise HTTPException(status_code=500, detail="Could not get storage API token")

        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {token}"}
            form_data = FormData()

            # Add file
            form_data.add_field('file',
                                file_contents,
                                filename=filename,
                                content_type=content_type)

            # Add metadata
            metadata = {
                "filename": filename,
                "content_type": content_type,
                "size": len(file_contents)
            }
            form_data.add_field('metadata_str',
                                json.dumps(metadata),
                                content_type='application/json')

            upload_url = f"{self.api_url}{self.upload_endpoint}"

            async with session.post(upload_url, data=form_data, headers=headers) as response:
                if response.status != 201:
                    raise HTTPException(
                        status_code=500,
                        detail="Storage service upload failed"
                    )

                response_data = await response.json()
                return response_data.get("objectID")

    async def download_file(self, storage_id: str):
        """Download a file from storage service"""
        token = await self.get_token()
        if not token:
            raise HTTPException(status_code=500, detail="Could not get storage API token")

        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {token}"}
            download_url = f"{self.api_url}{self.download_endpoint}?id={storage_id}"

            async with session.get(download_url, headers=headers) as response:
                if response.status != 200:
                    raise HTTPException(
                        status_code=500,
                        detail="Failed to retrieve file from storage"
                    )
                return await response.read()

    async def delete_file(self, storage_id: str):
        """Delete a file from storage service"""
        token = await self.get_token()
        if not token:
            raise HTTPException(status_code=500, detail="Could not get storage API token")

        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {token}"}
            # Use the configured delete endpoint
            delete_url = f"{self.api_url}{self.delete_endpoint}?id={storage_id}"
            
            try:
                async with session.delete(delete_url, headers=headers) as response:
                    if response.status not in (200, 204):  # Common success status codes for DELETE
                        raise HTTPException(
                            status_code=500,
                            detail=f"Storage service deletion failed: {response.status}"
                        )
                    return True
            except Exception as e:
                log_error(f"Error deleting file from storage: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail="Failed to delete file from storage"
                )

# Create a singleton instance
storage_service = StorageService()
