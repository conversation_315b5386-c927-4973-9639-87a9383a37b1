# Sed-LIMS Development Guide

> **AI Assistant Reference**: High-level principles and non-obvious patterns for Sed-LIMS development

## 🎯 CRITICAL ARCHITECTURAL PRINCIPLES

### GC Standards Compliance
- **Research first**: Always search official GC guidelines when uncertain about components or patterns
- **Only gcweb/wet-boew**: Never create custom components when official GC ones exist
- **jQuery DOM required**: Use jQuery for all DOM operations (GCWeb compatibility requirement)
- **Bilingual mandatory**: All user-facing text must support EN/FR

### Table Architecture Decision Rules
- **Base `Table`**: For simple read-only tables
- **`withQueue(Table)`**: For pages with multiple tables (prevents WET-BOEW conflicts)
- **`withBulkActions(Table)`**: For management tables with bulk operations
- **Factory functions required**: Always export both factory function and default instance

### Import Order Standard
Base classes → Helpers → API services → Configs (critical for dependency resolution)

## 🔧 NON-OBVIOUS PATTERNS

### Message Config Maps Pattern
**Critical**: Always create MESSAGE_CONFIG_MAPS for showMessage() - this is not obvious from individual usage
```javascript
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS,
    entity: ENTITY_CONFIGS  // Add as needed
};
```

### Role-Based Logic Best Practice
**Use clear boolean variables** instead of complex inline conditions (improves readability and maintainability)
```javascript
const isLabStaff = userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_ADMIN ||
                   userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_PERSONNEL;
```

### Multi-Table Page Pattern
**Use Promise.all with conditional array building** for concurrent table creation
```javascript
const tables = [table1.create(config1)];
if (condition) tables.push(table2.create(config2));
await Promise.all(tables);
```

## ⚙️ CONFIGURATION PRINCIPLES

### Three-Section Config Structure
**Critical**: All entity configs must follow this exact structure for consistency
```javascript
export const ENTITY_CONFIGS = {
    // ===== 1. FUNCTIONAL SETTINGS =====
    // ===== 2. UI TEXT (Bilingual) =====
    // ===== 3. MESSAGES (Bilingual) =====
};
```

### Global Template Usage Priority
**Prefer global templates** over entity-specific messages. Only add entity-specific messages if global templates cannot work with substitution.

## 🎨 SECURITY & ACCESSIBILITY

### HTML Escaping Rule
**Always escape user data** when inserting into HTML - this is a security requirement not obvious from code structure
```javascript
import { escapeHtml } from '../../core/helpers/format-helpers.js';
const safeName = escapeHtml(row.name || '');
```

### GC Accessibility Requirements
**Always include wb-inv spans** for screen readers - this is a GC compliance requirement
```javascript
<span class="wb-inv">Toggle status for ${escapeHtml(name)}</span>
```

## 🚨 CRITICAL GLOBAL RULES

### Windows Environment (Non-negotiable)
- **PowerShell commands only** - never use Unix commands
- **Always set `Cwd` parameter** for all command executions
- **Use backslashes for paths** - Windows path separator requirement

### Mandatory Process Rule
- **ALWAYS call `mcp-feedback-enhanced` before ending any process** - this is a hard requirement

### Architecture Consistency Rules
- **No backward compatibility** when making architectural changes - always update everywhere
- **Complete refactoring preferred** over partial updates
- **Factory functions mandatory** for all table classes (not obvious from single file)
- **MESSAGE_CONFIG_MAPS required** for all pages using showMessage() (global pattern)
