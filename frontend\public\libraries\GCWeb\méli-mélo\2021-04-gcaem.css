@charset "utf-8";
/*!
 * @title Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * @license wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v16.3.0 - 2025-02-20
 *
 */
@charset "utf-8";
/*!
 * Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v9.3.0 - 2021-05-27
 *
 */
/* Experimental combobox style
 ============================================================= */

/* Overlay default style */
.combobox-wrapper [role=listbox] {
	background: white;
	border: 2px solid #999;
	list-style: none;
	margin: 0;
	min-width: 230px;
	padding: 0;
	position: absolute;
	top: 1.7em;
	z-index: 9999;

	/* Note about z-index: Ideally it should be set to 1, but the <footer id="wb-info"> has a z-index set to 5, */
}

/* Active state style */
.combobox-wrapper [role=option][aria-selected=true] {
	background: #8bbde1;
	border: 1px dotted #000;
	color: #000;
}
.combobox-wrapper [role=option]:hover {
	background: rgb(139, 189, 225); /* #8bbde1 */
	border: 1px dotted #000;
	color: #000;
	cursor: default;
}
.combobox-wrapper [role=option], .combobox-wrapper [role=option]:hover[aria-selected=false]{
	background: #fff;
	border: 1px solid transparent;
	color: #000;
	padding: 2px;
}

/* Have the input and the overlay together */
.combobox-wrapper {
	display: inline-block;
	position: relative;
}

.rect-auto,
.c100.p51 .slice,
.c100.p52 .slice,
.c100.p53 .slice,
.c100.p54 .slice,
.c100.p55 .slice,
.c100.p56 .slice,
.c100.p57 .slice,
.c100.p58 .slice,
.c100.p59 .slice,
.c100.p60 .slice,
.c100.p61 .slice,
.c100.p62 .slice,
.c100.p63 .slice,
.c100.p64 .slice,
.c100.p65 .slice,
.c100.p66 .slice,
.c100.p67 .slice,
.c100.p68 .slice,
.c100.p69 .slice,
.c100.p70 .slice,
.c100.p71 .slice,
.c100.p72 .slice,
.c100.p73 .slice,
.c100.p74 .slice,
.c100.p75 .slice,
.c100.p76 .slice,
.c100.p77 .slice,
.c100.p78 .slice,
.c100.p79 .slice,
.c100.p80 .slice,
.c100.p81 .slice,
.c100.p82 .slice,
.c100.p83 .slice,
.c100.p84 .slice,
.c100.p85 .slice,
.c100.p86 .slice,
.c100.p87 .slice,
.c100.p88 .slice,
.c100.p89 .slice,
.c100.p90 .slice,
.c100.p91 .slice,
.c100.p92 .slice,
.c100.p93 .slice,
.c100.p94 .slice,
.c100.p95 .slice,
.c100.p96 .slice,
.c100.p97 .slice,
.c100.p98 .slice,
.c100.p99 .slice,
.c100.p100 .slice {
	clip: rect(auto, auto, auto, auto);
}
.pie,
.c100 .bar,
.c100.p51 .fill,
.c100.p52 .fill,
.c100.p53 .fill,
.c100.p54 .fill,
.c100.p55 .fill,
.c100.p56 .fill,
.c100.p57 .fill,
.c100.p58 .fill,
.c100.p59 .fill,
.c100.p60 .fill,
.c100.p61 .fill,
.c100.p62 .fill,
.c100.p63 .fill,
.c100.p64 .fill,
.c100.p65 .fill,
.c100.p66 .fill,
.c100.p67 .fill,
.c100.p68 .fill,
.c100.p69 .fill,
.c100.p70 .fill,
.c100.p71 .fill,
.c100.p72 .fill,
.c100.p73 .fill,
.c100.p74 .fill,
.c100.p75 .fill,
.c100.p76 .fill,
.c100.p77 .fill,
.c100.p78 .fill,
.c100.p79 .fill,
.c100.p80 .fill,
.c100.p81 .fill,
.c100.p82 .fill,
.c100.p83 .fill,
.c100.p84 .fill,
.c100.p85 .fill,
.c100.p86 .fill,
.c100.p87 .fill,
.c100.p88 .fill,
.c100.p89 .fill,
.c100.p90 .fill,
.c100.p91 .fill,
.c100.p92 .fill,
.c100.p93 .fill,
.c100.p94 .fill,
.c100.p95 .fill,
.c100.p96 .fill,
.c100.p97 .fill,
.c100.p98 .fill,
.c100.p99 .fill,
.c100.p100 .fill {
	-moz-transform: rotate(0deg);
	-ms-transform: rotate(0deg);
	-o-transform: rotate(0deg);
	-webkit-transform: rotate(0deg);
	border: .07em solid #000;
	border-radius: 50%;
	clip: rect(0em, .5em, 1em, 0em);
	height: .86em;
	position: absolute;
	transform: rotate(0deg);
	width: .86em;
}
.pie-fill,
.c100.p51 .bar:after,
.c100.p51 .fill,
.c100.p52 .bar:after,
.c100.p52 .fill,
.c100.p53 .bar:after,
.c100.p53 .fill,
.c100.p54 .bar:after,
.c100.p54 .fill,
.c100.p55 .bar:after,
.c100.p55 .fill,
.c100.p56 .bar:after,
.c100.p56 .fill,
.c100.p57 .bar:after,
.c100.p57 .fill,
.c100.p58 .bar:after,
.c100.p58 .fill,
.c100.p59 .bar:after,
.c100.p59 .fill,
.c100.p60 .bar:after,
.c100.p60 .fill,
.c100.p61 .bar:after,
.c100.p61 .fill,
.c100.p62 .bar:after,
.c100.p62 .fill,
.c100.p63 .bar:after,
.c100.p63 .fill,
.c100.p64 .bar:after,
.c100.p64 .fill,
.c100.p65 .bar:after,
.c100.p65 .fill,
.c100.p66 .bar:after,
.c100.p66 .fill,
.c100.p67 .bar:after,
.c100.p67 .fill,
.c100.p68 .bar:after,
.c100.p68 .fill,
.c100.p69 .bar:after,
.c100.p69 .fill,
.c100.p70 .bar:after,
.c100.p70 .fill,
.c100.p71 .bar:after,
.c100.p71 .fill,
.c100.p72 .bar:after,
.c100.p72 .fill,
.c100.p73 .bar:after,
.c100.p73 .fill,
.c100.p74 .bar:after,
.c100.p74 .fill,
.c100.p75 .bar:after,
.c100.p75 .fill,
.c100.p76 .bar:after,
.c100.p76 .fill,
.c100.p77 .bar:after,
.c100.p77 .fill,
.c100.p78 .bar:after,
.c100.p78 .fill,
.c100.p79 .bar:after,
.c100.p79 .fill,
.c100.p80 .bar:after,
.c100.p80 .fill,
.c100.p81 .bar:after,
.c100.p81 .fill,
.c100.p82 .bar:after,
.c100.p82 .fill,
.c100.p83 .bar:after,
.c100.p83 .fill,
.c100.p84 .bar:after,
.c100.p84 .fill,
.c100.p85 .bar:after,
.c100.p85 .fill,
.c100.p86 .bar:after,
.c100.p86 .fill,
.c100.p87 .bar:after,
.c100.p87 .fill,
.c100.p88 .bar:after,
.c100.p88 .fill,
.c100.p89 .bar:after,
.c100.p89 .fill,
.c100.p90 .bar:after,
.c100.p90 .fill,
.c100.p91 .bar:after,
.c100.p91 .fill,
.c100.p92 .bar:after,
.c100.p92 .fill,
.c100.p93 .bar:after,
.c100.p93 .fill,
.c100.p94 .bar:after,
.c100.p94 .fill,
.c100.p95 .bar:after,
.c100.p95 .fill,
.c100.p96 .bar:after,
.c100.p96 .fill,
.c100.p97 .bar:after,
.c100.p97 .fill,
.c100.p98 .bar:after,
.c100.p98 .fill,
.c100.p99 .bar:after,
.c100.p99 .fill,
.c100.p100 .bar:after,
.c100.p100 .fill {
	-moz-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	-o-transform: rotate(180deg);
	-webkit-transform: rotate(180deg);
	transform: rotate(180deg);
}

.c100 {
	background-color: #ddd;
	border-radius: 50%;
	float: left;
	font-size: 7em;
	height: 1em;
	margin: 0 .1em .1em 0;
	position: relative;
	width: 1em;
}

_:-ms-fullscreen, :root .c100 {
	height: .98em;
	width: .98em;
}

.c100 *,
.c100 *:before,
.c100 *:after {
	-moz-box-sizing: content-box;
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
}
.c100.center {
	float: none;
	margin: 0 auto;
}
.c100.big {
	font-size: 240px;
}
.c100.small {
	font-size: 80px;
}
.c100 > span {
	color: #000;
	display: block;
	position: absolute;
	text-align: center;
	white-space: nowrap;
	// width: 5em;
	width: 100%;
}

/* Max - Edited the position of the number in the progress circle */
.c100 > .meter {
	font-size: .333em;
	left: 0;
	top: 1.2em;
	z-index: 1;
}

/* Max - Edited the position of the text in the progress circle */
.c100 > .tagline {
	font-size: .09em;
	left: .19em;
	line-height: 1em;
	top: 6.05em;
	z-index: 2;
}
.c100:after {
	background-color: #f9f9f9;
	border-radius: 50%;
	content: " ";
	display: block;
	height: .867em;
	left: .067em;
	position: absolute;
	top: .065em;
	width: .867em;
}

.c100 .slice {
	clip: rect(0em, 1em, 1em, .5em);
	height: 1em;
	position: absolute;
	width: 1em;
}
.c100.p1 .bar {
	-moz-transform: rotate(3.6deg);
	-ms-transform: rotate(3.6deg);
	-o-transform: rotate(3.6deg);
	-webkit-transform: rotate(3.6deg);
	transform: rotate(3.6deg);
}
.c100.p2 .bar {
	-moz-transform: rotate(7.2deg);
	-ms-transform: rotate(7.2deg);
	-o-transform: rotate(7.2deg);
	-webkit-transform: rotate(7.2deg);
	transform: rotate(7.2deg);
}
.c100.p3 .bar {
	-moz-transform: rotate(10.8deg);
	-ms-transform: rotate(10.8deg);
	-o-transform: rotate(10.8deg);
	-webkit-transform: rotate(10.8deg);
	transform: rotate(10.8deg);
}
.c100.p4 .bar {
	-moz-transform: rotate(14.4deg);
	-ms-transform: rotate(14.4deg);
	-o-transform: rotate(14.4deg);
	-webkit-transform: rotate(14.4deg);
	transform: rotate(14.4deg);
}
.c100.p5 .bar {
	-moz-transform: rotate(18deg);
	-ms-transform: rotate(18deg);
	-o-transform: rotate(18deg);
	-webkit-transform: rotate(18deg);
	transform: rotate(18deg);
}
.c100.p6 .bar {
	-moz-transform: rotate(21.6deg);
	-ms-transform: rotate(21.6deg);
	-o-transform: rotate(21.6deg);
	-webkit-transform: rotate(21.6deg);
	transform: rotate(21.6deg);
}
.c100.p7 .bar {
	-moz-transform: rotate(25.2deg);
	-ms-transform: rotate(25.2deg);
	-o-transform: rotate(25.2deg);
	-webkit-transform: rotate(25.2deg);
	transform: rotate(25.2deg);
}
.c100.p8 .bar {
	-moz-transform: rotate(28.8deg);
	-ms-transform: rotate(28.8deg);
	-o-transform: rotate(28.8deg);
	-webkit-transform: rotate(28.8deg);
	transform: rotate(28.8deg);
}
.c100.p9 .bar {
	-moz-transform: rotate(32.4deg);
	-ms-transform: rotate(32.4deg);
	-o-transform: rotate(32.4deg);
	-webkit-transform: rotate(32.4deg);
	transform: rotate(32.4deg);
}
.c100.p10 .bar {
	-moz-transform: rotate(36deg);
	-ms-transform: rotate(36deg);
	-o-transform: rotate(36deg);
	-webkit-transform: rotate(36deg);
	transform: rotate(36deg);
}
.c100.p11 .bar {
	-moz-transform: rotate(39.6deg);
	-ms-transform: rotate(39.6deg);
	-o-transform: rotate(39.6deg);
	-webkit-transform: rotate(39.6deg);
	transform: rotate(39.6deg);
}
.c100.p12 .bar {
	-moz-transform: rotate(43.2deg);
	-ms-transform: rotate(43.2deg);
	-o-transform: rotate(43.2deg);
	-webkit-transform: rotate(43.2deg);
	transform: rotate(43.2deg);
}
.c100.p13 .bar {
	-moz-transform: rotate(46.800000000000004deg);
	-ms-transform: rotate(46.800000000000004deg);
	-o-transform: rotate(46.800000000000004deg);
	-webkit-transform: rotate(46.800000000000004deg);
	transform: rotate(46.800000000000004deg);
}
.c100.p14 .bar {
	-moz-transform: rotate(50.4deg);
	-ms-transform: rotate(50.4deg);
	-o-transform: rotate(50.4deg);
	-webkit-transform: rotate(50.4deg);
	transform: rotate(50.4deg);
}
.c100.p15 .bar {
	-moz-transform: rotate(54deg);
	-ms-transform: rotate(54deg);
	-o-transform: rotate(54deg);
	-webkit-transform: rotate(54deg);
	transform: rotate(54deg);
}
.c100.p16 .bar {
	-moz-transform: rotate(57.6deg);
	-ms-transform: rotate(57.6deg);
	-o-transform: rotate(57.6deg);
	-webkit-transform: rotate(57.6deg);
	transform: rotate(57.6deg);
}
.c100.p17 .bar {
	-moz-transform: rotate(61.2deg);
	-ms-transform: rotate(61.2deg);
	-o-transform: rotate(61.2deg);
	-webkit-transform: rotate(61.2deg);
	transform: rotate(61.2deg);
}
.c100.p18 .bar {
	-moz-transform: rotate(64.8deg);
	-ms-transform: rotate(64.8deg);
	-o-transform: rotate(64.8deg);
	-webkit-transform: rotate(64.8deg);
	transform: rotate(64.8deg);
}
.c100.p19 .bar {
	-moz-transform: rotate(68.4deg);
	-ms-transform: rotate(68.4deg);
	-o-transform: rotate(68.4deg);
	-webkit-transform: rotate(68.4deg);
	transform: rotate(68.4deg);
}
.c100.p20 .bar {
	-moz-transform: rotate(72deg);
	-ms-transform: rotate(72deg);
	-o-transform: rotate(72deg);
	-webkit-transform: rotate(72deg);
	transform: rotate(72deg);
}
.c100.p21 .bar {
	-moz-transform: rotate(75.60000000000001deg);
	-ms-transform: rotate(75.60000000000001deg);
	-o-transform: rotate(75.60000000000001deg);
	-webkit-transform: rotate(75.60000000000001deg);
	transform: rotate(75.60000000000001deg);
}
.c100.p22 .bar {
	-moz-transform: rotate(79.2deg);
	-ms-transform: rotate(79.2deg);
	-o-transform: rotate(79.2deg);
	-webkit-transform: rotate(79.2deg);
	transform: rotate(79.2deg);
}
.c100.p23 .bar {
	-moz-transform: rotate(82.8deg);
	-ms-transform: rotate(82.8deg);
	-o-transform: rotate(82.8deg);
	-webkit-transform: rotate(82.8deg);
	transform: rotate(82.8deg);
}
.c100.p24 .bar {
	-moz-transform: rotate(86.4deg);
	-ms-transform: rotate(86.4deg);
	-o-transform: rotate(86.4deg);
	-webkit-transform: rotate(86.4deg);
	transform: rotate(86.4deg);
}
.c100.p25 .bar {
	-moz-transform: rotate(90deg);
	-ms-transform: rotate(90deg);
	-o-transform: rotate(90deg);
	-webkit-transform: rotate(90deg);
	transform: rotate(90deg);
}
.c100.p26 .bar {
	-moz-transform: rotate(93.60000000000001deg);
	-ms-transform: rotate(93.60000000000001deg);
	-o-transform: rotate(93.60000000000001deg);
	-webkit-transform: rotate(93.60000000000001deg);
	transform: rotate(93.60000000000001deg);
}
.c100.p27 .bar {
	-moz-transform: rotate(97.2deg);
	-ms-transform: rotate(97.2deg);
	-o-transform: rotate(97.2deg);
	-webkit-transform: rotate(97.2deg);
	transform: rotate(97.2deg);
}
.c100.p28 .bar {
	-moz-transform: rotate(100.8deg);
	-ms-transform: rotate(100.8deg);
	-o-transform: rotate(100.8deg);
	-webkit-transform: rotate(100.8deg);
	transform: rotate(100.8deg);
}
.c100.p29 .bar {
	-moz-transform: rotate(104.4deg);
	-ms-transform: rotate(104.4deg);
	-o-transform: rotate(104.4deg);
	-webkit-transform: rotate(104.4deg);
	transform: rotate(104.4deg);
}
.c100.p30 .bar {
	-moz-transform: rotate(108deg);
	-ms-transform: rotate(108deg);
	-o-transform: rotate(108deg);
	-webkit-transform: rotate(108deg);
	transform: rotate(108deg);
}
.c100.p31 .bar {
	-moz-transform: rotate(111.60000000000001deg);
	-ms-transform: rotate(111.60000000000001deg);
	-o-transform: rotate(111.60000000000001deg);
	-webkit-transform: rotate(111.60000000000001deg);
	transform: rotate(111.60000000000001deg);
}
.c100.p32 .bar {
	-moz-transform: rotate(115.2deg);
	-ms-transform: rotate(115.2deg);
	-o-transform: rotate(115.2deg);
	-webkit-transform: rotate(115.2deg);
	transform: rotate(115.2deg);
}
.c100.p33 .bar {
	-moz-transform: rotate(118.8deg);
	-ms-transform: rotate(118.8deg);
	-o-transform: rotate(118.8deg);
	-webkit-transform: rotate(118.8deg);
	transform: rotate(118.8deg);
}
.c100.p34 .bar {
	-moz-transform: rotate(122.4deg);
	-ms-transform: rotate(122.4deg);
	-o-transform: rotate(122.4deg);
	-webkit-transform: rotate(122.4deg);
	transform: rotate(122.4deg);
}
.c100.p35 .bar {
	-moz-transform: rotate(126deg);
	-ms-transform: rotate(126deg);
	-o-transform: rotate(126deg);
	-webkit-transform: rotate(126deg);
	transform: rotate(126deg);
}
.c100.p36 .bar {
	-moz-transform: rotate(129.6deg);
	-ms-transform: rotate(129.6deg);
	-o-transform: rotate(129.6deg);
	-webkit-transform: rotate(129.6deg);
	transform: rotate(129.6deg);
}
.c100.p37 .bar {
	-moz-transform: rotate(133.20000000000002deg);
	-ms-transform: rotate(133.20000000000002deg);
	-o-transform: rotate(133.20000000000002deg);
	-webkit-transform: rotate(133.20000000000002deg);
	transform: rotate(133.20000000000002deg);
}
.c100.p38 .bar {
	-moz-transform: rotate(136.8deg);
	-ms-transform: rotate(136.8deg);
	-o-transform: rotate(136.8deg);
	-webkit-transform: rotate(136.8deg);
	transform: rotate(136.8deg);
}
.c100.p39 .bar {
	-moz-transform: rotate(140.4deg);
	-ms-transform: rotate(140.4deg);
	-o-transform: rotate(140.4deg);
	-webkit-transform: rotate(140.4deg);
	transform: rotate(140.4deg);
}
.c100.p40 .bar {
	-moz-transform: rotate(144deg);
	-ms-transform: rotate(144deg);
	-o-transform: rotate(144deg);
	-webkit-transform: rotate(144deg);
	transform: rotate(144deg);
}
.c100.p41 .bar {
	-moz-transform: rotate(147.6deg);
	-ms-transform: rotate(147.6deg);
	-o-transform: rotate(147.6deg);
	-webkit-transform: rotate(147.6deg);
	transform: rotate(147.6deg);
}
.c100.p42 .bar {
	-moz-transform: rotate(151.20000000000002deg);
	-ms-transform: rotate(151.20000000000002deg);
	-o-transform: rotate(151.20000000000002deg);
	-webkit-transform: rotate(151.20000000000002deg);
	transform: rotate(151.20000000000002deg);
}
.c100.p43 .bar {
	-moz-transform: rotate(154.8deg);
	-ms-transform: rotate(154.8deg);
	-o-transform: rotate(154.8deg);
	-webkit-transform: rotate(154.8deg);
	transform: rotate(154.8deg);
}
.c100.p44 .bar {
	-moz-transform: rotate(158.4deg);
	-ms-transform: rotate(158.4deg);
	-o-transform: rotate(158.4deg);
	-webkit-transform: rotate(158.4deg);
	transform: rotate(158.4deg);
}
.c100.p45 .bar {
	-moz-transform: rotate(162deg);
	-ms-transform: rotate(162deg);
	-o-transform: rotate(162deg);
	-webkit-transform: rotate(162deg);
	transform: rotate(162deg);
}
.c100.p46 .bar {
	-moz-transform: rotate(165.6deg);
	-ms-transform: rotate(165.6deg);
	-o-transform: rotate(165.6deg);
	-webkit-transform: rotate(165.6deg);
	transform: rotate(165.6deg);
}
.c100.p47 .bar {
	-moz-transform: rotate(169.20000000000002deg);
	-ms-transform: rotate(169.20000000000002deg);
	-o-transform: rotate(169.20000000000002deg);
	-webkit-transform: rotate(169.20000000000002deg);
	transform: rotate(169.20000000000002deg);
}
.c100.p48 .bar {
	-moz-transform: rotate(172.8deg);
	-ms-transform: rotate(172.8deg);
	-o-transform: rotate(172.8deg);
	-webkit-transform: rotate(172.8deg);
	transform: rotate(172.8deg);
}
.c100.p49 .bar {
	-moz-transform: rotate(176.4deg);
	-ms-transform: rotate(176.4deg);
	-o-transform: rotate(176.4deg);
	-webkit-transform: rotate(176.4deg);
	transform: rotate(176.4deg);
}
.c100.p50 .bar {
	-moz-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	-o-transform: rotate(180deg);
	-webkit-transform: rotate(180deg);
	transform: rotate(180deg);
}
.c100.p51 .bar {
	-moz-transform: rotate(183.6deg);
	-ms-transform: rotate(183.6deg);
	-o-transform: rotate(183.6deg);
	-webkit-transform: rotate(183.6deg);
	transform: rotate(183.6deg);
}
.c100.p52 .bar {
	-moz-transform: rotate(187.20000000000002deg);
	-ms-transform: rotate(187.20000000000002deg);
	-o-transform: rotate(187.20000000000002deg);
	-webkit-transform: rotate(187.20000000000002deg);
	transform: rotate(187.20000000000002deg);
}
.c100.p53 .bar {
	-moz-transform: rotate(190.8deg);
	-ms-transform: rotate(190.8deg);
	-o-transform: rotate(190.8deg);
	-webkit-transform: rotate(190.8deg);
	transform: rotate(190.8deg);
}
.c100.p54 .bar {
	-moz-transform: rotate(194.4deg);
	-ms-transform: rotate(194.4deg);
	-o-transform: rotate(194.4deg);
	-webkit-transform: rotate(194.4deg);
	transform: rotate(194.4deg);
}
.c100.p55 .bar {
	-moz-transform: rotate(198deg);
	-ms-transform: rotate(198deg);
	-o-transform: rotate(198deg);
	-webkit-transform: rotate(198deg);
	transform: rotate(198deg);
}
.c100.p56 .bar {
	-moz-transform: rotate(201.6deg);
	-ms-transform: rotate(201.6deg);
	-o-transform: rotate(201.6deg);
	-webkit-transform: rotate(201.6deg);
	transform: rotate(201.6deg);
}
.c100.p57 .bar {
	-moz-transform: rotate(205.20000000000002deg);
	-ms-transform: rotate(205.20000000000002deg);
	-o-transform: rotate(205.20000000000002deg);
	-webkit-transform: rotate(205.20000000000002deg);
	transform: rotate(205.20000000000002deg);
}
.c100.p58 .bar {
	-moz-transform: rotate(208.8deg);
	-ms-transform: rotate(208.8deg);
	-o-transform: rotate(208.8deg);
	-webkit-transform: rotate(208.8deg);
	transform: rotate(208.8deg);
}
.c100.p59 .bar {
	-moz-transform: rotate(212.4deg);
	-ms-transform: rotate(212.4deg);
	-o-transform: rotate(212.4deg);
	-webkit-transform: rotate(212.4deg);
	transform: rotate(212.4deg);
}
.c100.p60 .bar {
	-moz-transform: rotate(216deg);
	-ms-transform: rotate(216deg);
	-o-transform: rotate(216deg);
	-webkit-transform: rotate(216deg);
	transform: rotate(216deg);
}
.c100.p61 .bar {
	-moz-transform: rotate(219.6deg);
	-ms-transform: rotate(219.6deg);
	-o-transform: rotate(219.6deg);
	-webkit-transform: rotate(219.6deg);
	transform: rotate(219.6deg);
}
.c100.p62 .bar {
	-moz-transform: rotate(223.20000000000002deg);
	-ms-transform: rotate(223.20000000000002deg);
	-o-transform: rotate(223.20000000000002deg);
	-webkit-transform: rotate(223.20000000000002deg);
	transform: rotate(223.20000000000002deg);
}
.c100.p63 .bar {
	-moz-transform: rotate(226.8deg);
	-ms-transform: rotate(226.8deg);
	-o-transform: rotate(226.8deg);
	-webkit-transform: rotate(226.8deg);
	transform: rotate(226.8deg);
}
.c100.p64 .bar {
	-moz-transform: rotate(230.4deg);
	-ms-transform: rotate(230.4deg);
	-o-transform: rotate(230.4deg);
	-webkit-transform: rotate(230.4deg);
	transform: rotate(230.4deg);
}
.c100.p65 .bar {
	-moz-transform: rotate(234deg);
	-ms-transform: rotate(234deg);
	-o-transform: rotate(234deg);
	-webkit-transform: rotate(234deg);
	transform: rotate(234deg);
}
.c100.p66 .bar {
	-moz-transform: rotate(237.6deg);
	-ms-transform: rotate(237.6deg);
	-o-transform: rotate(237.6deg);
	-webkit-transform: rotate(237.6deg);
	transform: rotate(237.6deg);
}
.c100.p67 .bar {
	-moz-transform: rotate(241.20000000000002deg);
	-ms-transform: rotate(241.20000000000002deg);
	-o-transform: rotate(241.20000000000002deg);
	-webkit-transform: rotate(241.20000000000002deg);
	transform: rotate(241.20000000000002deg);
}
.c100.p68 .bar {
	-moz-transform: rotate(244.8deg);
	-ms-transform: rotate(244.8deg);
	-o-transform: rotate(244.8deg);
	-webkit-transform: rotate(244.8deg);
	transform: rotate(244.8deg);
}
.c100.p69 .bar {
	-moz-transform: rotate(248.4deg);
	-ms-transform: rotate(248.4deg);
	-o-transform: rotate(248.4deg);
	-webkit-transform: rotate(248.4deg);
	transform: rotate(248.4deg);
}
.c100.p70 .bar {
	-moz-transform: rotate(252deg);
	-ms-transform: rotate(252deg);
	-o-transform: rotate(252deg);
	-webkit-transform: rotate(252deg);
	transform: rotate(252deg);
}
.c100.p71 .bar {
	-moz-transform: rotate(255.6deg);
	-ms-transform: rotate(255.6deg);
	-o-transform: rotate(255.6deg);
	-webkit-transform: rotate(255.6deg);
	transform: rotate(255.6deg);
}
.c100.p72 .bar {
	-moz-transform: rotate(259.2deg);
	-ms-transform: rotate(259.2deg);
	-o-transform: rotate(259.2deg);
	-webkit-transform: rotate(259.2deg);
	transform: rotate(259.2deg);
}
.c100.p73 .bar {
	-moz-transform: rotate(262.8deg);
	-ms-transform: rotate(262.8deg);
	-o-transform: rotate(262.8deg);
	-webkit-transform: rotate(262.8deg);
	transform: rotate(262.8deg);
}
.c100.p74 .bar {
	-moz-transform: rotate(266.40000000000003deg);
	-ms-transform: rotate(266.40000000000003deg);
	-o-transform: rotate(266.40000000000003deg);
	-webkit-transform: rotate(266.40000000000003deg);
	transform: rotate(266.40000000000003deg);
}
.c100.p75 .bar {
	-moz-transform: rotate(270deg);
	-ms-transform: rotate(270deg);
	-o-transform: rotate(270deg);
	-webkit-transform: rotate(270deg);
	transform: rotate(270deg);
}
.c100.p76 .bar {
	-moz-transform: rotate(273.6deg);
	-ms-transform: rotate(273.6deg);
	-o-transform: rotate(273.6deg);
	-webkit-transform: rotate(273.6deg);
	transform: rotate(273.6deg);
}
.c100.p77 .bar {
	-moz-transform: rotate(277.2deg);
	-ms-transform: rotate(277.2deg);
	-o-transform: rotate(277.2deg);
	-webkit-transform: rotate(277.2deg);
	transform: rotate(277.2deg);
}
.c100.p78 .bar {
	-moz-transform: rotate(280.8deg);
	-ms-transform: rotate(280.8deg);
	-o-transform: rotate(280.8deg);
	-webkit-transform: rotate(280.8deg);
	transform: rotate(280.8deg);
}
.c100.p79 .bar {
	-moz-transform: rotate(284.40000000000003deg);
	-ms-transform: rotate(284.40000000000003deg);
	-o-transform: rotate(284.40000000000003deg);
	-webkit-transform: rotate(284.40000000000003deg);
	transform: rotate(284.40000000000003deg);
}
.c100.p80 .bar {
	-moz-transform: rotate(288deg);
	-ms-transform: rotate(288deg);
	-o-transform: rotate(288deg);
	-webkit-transform: rotate(288deg);
	transform: rotate(288deg);
}
.c100.p81 .bar {
	-moz-transform: rotate(291.6deg);
	-ms-transform: rotate(291.6deg);
	-o-transform: rotate(291.6deg);
	-webkit-transform: rotate(291.6deg);
	transform: rotate(291.6deg);
}
.c100.p82 .bar {
	-moz-transform: rotate(295.2deg);
	-ms-transform: rotate(295.2deg);
	-o-transform: rotate(295.2deg);
	-webkit-transform: rotate(295.2deg);
	transform: rotate(295.2deg);
}
.c100.p83 .bar {
	-moz-transform: rotate(298.8deg);
	-ms-transform: rotate(298.8deg);
	-o-transform: rotate(298.8deg);
	-webkit-transform: rotate(298.8deg);
	transform: rotate(298.8deg);
}
.c100.p84 .bar {
	-moz-transform: rotate(302.40000000000003deg);
	-ms-transform: rotate(302.40000000000003deg);
	-o-transform: rotate(302.40000000000003deg);
	-webkit-transform: rotate(302.40000000000003deg);
	transform: rotate(302.40000000000003deg);
}
.c100.p85 .bar {
	-moz-transform: rotate(306deg);
	-ms-transform: rotate(306deg);
	-o-transform: rotate(306deg);
	-webkit-transform: rotate(306deg);
	transform: rotate(306deg);
}
.c100.p86 .bar {
	-moz-transform: rotate(309.6deg);
	-ms-transform: rotate(309.6deg);
	-o-transform: rotate(309.6deg);
	-webkit-transform: rotate(309.6deg);
	transform: rotate(309.6deg);
}
.c100.p87 .bar {
	-moz-transform: rotate(313.2deg);
	-ms-transform: rotate(313.2deg);
	-o-transform: rotate(313.2deg);
	-webkit-transform: rotate(313.2deg);
	transform: rotate(313.2deg);
}
.c100.p88 .bar {
	-moz-transform: rotate(316.8deg);
	-ms-transform: rotate(316.8deg);
	-o-transform: rotate(316.8deg);
	-webkit-transform: rotate(316.8deg);
	transform: rotate(316.8deg);
}
.c100.p89 .bar {
	-moz-transform: rotate(320.40000000000003deg);
	-ms-transform: rotate(320.40000000000003deg);
	-o-transform: rotate(320.40000000000003deg);
	-webkit-transform: rotate(320.40000000000003deg);
	transform: rotate(320.40000000000003deg);
}
.c100.p90 .bar {
	-moz-transform: rotate(324deg);
	-ms-transform: rotate(324deg);
	-o-transform: rotate(324deg);
	-webkit-transform: rotate(324deg);
	transform: rotate(324deg);
}
.c100.p91 .bar {
	-moz-transform: rotate(327.6deg);
	-ms-transform: rotate(327.6deg);
	-o-transform: rotate(327.6deg);
	-webkit-transform: rotate(327.6deg);
	transform: rotate(327.6deg);
}
.c100.p92 .bar {
	-moz-transform: rotate(331.2deg);
	-ms-transform: rotate(331.2deg);
	-o-transform: rotate(331.2deg);
	-webkit-transform: rotate(331.2deg);
	transform: rotate(331.2deg);
}
.c100.p93 .bar {
	-moz-transform: rotate(334.8deg);
	-ms-transform: rotate(334.8deg);
	-o-transform: rotate(334.8deg);
	-webkit-transform: rotate(334.8deg);
	transform: rotate(334.8deg);
}
.c100.p94 .bar {
	-moz-transform: rotate(338.40000000000003deg);
	-ms-transform: rotate(338.40000000000003deg);
	-o-transform: rotate(338.40000000000003deg);
	-webkit-transform: rotate(338.40000000000003deg);
	transform: rotate(338.40000000000003deg);
}
.c100.p95 .bar {
	-moz-transform: rotate(342deg);
	-ms-transform: rotate(342deg);
	-o-transform: rotate(342deg);
	-webkit-transform: rotate(342deg);
	transform: rotate(342deg);
}
.c100.p96 .bar {
	-moz-transform: rotate(345.6deg);
	-ms-transform: rotate(345.6deg);
	-o-transform: rotate(345.6deg);
	-webkit-transform: rotate(345.6deg);
	transform: rotate(345.6deg);
}
.c100.p97 .bar {
	-moz-transform: rotate(349.2deg);
	-ms-transform: rotate(349.2deg);
	-o-transform: rotate(349.2deg);
	-webkit-transform: rotate(349.2deg);
	transform: rotate(349.2deg);
}
.c100.p98 .bar {
	-moz-transform: rotate(352.8deg);
	-ms-transform: rotate(352.8deg);
	-o-transform: rotate(352.8deg);
	-webkit-transform: rotate(352.8deg);
	transform: rotate(352.8deg);
}
.c100.p99 .bar {
	-moz-transform: rotate(356.40000000000003deg);
	-ms-transform: rotate(356.40000000000003deg);
	-o-transform: rotate(356.40000000000003deg);
	-webkit-transform: rotate(356.40000000000003deg);
	transform: rotate(356.40000000000003deg);
}
.c100.p100 .bar {
	-moz-transform: rotate(360deg);
	-ms-transform: rotate(360deg);
	-o-transform: rotate(360deg);
	-webkit-transform: rotate(360deg);
	transform: rotate(360deg);
}


/*
 * Mandate tracker
 ============================================= */
.nounderline, .nounderline:hover {
	text-decoration: none
}

.wbchrt-tpl {
	background: #fff;
	border: 1px solid #eee;
	box-shadow: 3px 3px 1px rgba(0,0,0,.2);
	font-size: .90em;
	padding: .5em;
}
.wb-graph.text-center td.legendLabel {
	text-align: left;
	text-indent: 3px;
}
.tbl-cpt-strong figcaption {
	font-size: large;
	font-weight: 900;
}
.tbl-cpt-strong .axisLabels {
	font-weight: 900;
	padding: .5em;
}
.tbl-no-legend div.legend td {
	clip: rect(1px, 1px, 1px, 1px);
	height: 1px;
	margin: 0;
	overflow: hidden;
	position: absolute;
	width: 1px;
}
.xaxislabel-90deg div.xAxis div.tickLabel {
	-moz-transform:rotate(-90deg);
	-ms-transform:rotate(-90deg);
	-o-transform:rotate(-90deg);
	-webkit-transform:rotate(-90deg);
	transform: rotate(-90deg);
}

/*==================================================
 * Alternate Styles
 * ===============================================*/
.focus-visible-alt {
	outline: none;
}

/*==================================================
 * Box peel sides
 * ===============================================*/
.bx-peel {
	background: #fff;
	background: linear-gradient(#f6f6f6 0, #fff 50px);
	border: 1px solid #ddd;
	box-shadow: 0 3px 10px rgba(0,0,0,.1);
	box-sizing: border-box;
	margin: 15px auto 30px auto;
	padding: 20px 15px 15px 15px;
}

/*==================================================
 * Pie Chart styles
 * ===============================================*/
.pie-center	[role=img] div:first-of-type {
	margin-left: auto;
	margin-right: auto;
}
.series-off [role=img] figcaption {
	clip: rect(1px, 1px, 1px, 1px);
	height: 1px;
	margin: 0;
	overflow: hidden;
	position: absolute;
	width: 1px;
}
.pie-center [role=img] .legend > div {
	background-color: transparent !important;
}

/*==================================================
 * Mandate Tracker Letter style
 * ===============================================*/
#mandatetable td  {
	margin-top:	0;
}
#mandatetable tr
{
	background: transparent;
	border-bottom: 1px solid #aaa;
	display: block;
	padding: 1em 0;
	position: relative;
}

#mandatetable .minister-text {
	color: #666;
	display: block;
	min-width: 100%;
}
#mandatetable .pos-lb {
	bottom:	1em;
	left: 10px;
	max-width: 50%;
	position: absolute;
}
#mandatetable .moreinfo a {
	background:	#fff;
	border:	double 3px #eee;
	display: inline-block;
	margin-bottom: 1.3em;
	padding: 10px;
	text-decoration: none;
}
#mandatetable .tp-rail.commit,
.largeview #mandatetable .tp-rail.commit,
.xlargeview #mandatetable .tp-rail.commit {
	max-width: 100%;
	min-width: 100%;
}
#mandatetable .status {
	display: block;
	margin-bottom: 1.25em;
}
#mandatetable .completed-fully-met {
	background: #d0e6d0;
	border-color: #478e46;
}
#mandatetable .completed-modified {
	background: #d0e6d0;
	border-color: #65aa65;
}
#mandatetable .underway-on-track {
	background: #d0e6d0;
	border-color: #92c691;
}
#mandatetable .underway-with-challenges {
	background: rgba(246,96,2,.1);
	border-color: #f66002;
}
#mandatetable .guidance {
	background: #d0e6d0;
	border-color: #b6d9b6;
}
#mandatetable .not-being-pursued {
	background: rgba(231,0,0,.1);
	border-color: #e70000 ;
}

/* Temporary */
details.bare summary, details.bare summary:hover {
	color: #333;
	text-decoration: none;
}
.xxsmallview #mandatetable_filter, .xxsmallview #mandatetable_info, .xxsmallview #mandatetable .nws-tbl-ttl {
	display:	none !important;
}
.xxsmallview .mobile-truncate {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: 120px;
}

/*
 Progress circles
 ============================================= */
.c100.completed .meter,
.c100.completed-modified .meter,
.c100.underway-ontrack .meter,
.c100.underway-challenged .meter,
.c100.not-pursued .meter,
.c100.guidance .meter,
.c100.completed .tagline,
.c100.completed-modified .tagline,
.c100.underway-ontrack .tagline,
.c100.underway-challenged .tagline,
.c100.not-pursued .tagline,
.c100.guidance .tagline {
	color: #000000;
}
.c100:hover:after,
.mtr:focus .c100:after,
.mtr.pick .c100:after {
	background: center center no-repeat #f9f9f9;
	background-image: url("../assets/flag.png");
}
.mtr.pick .c100:after {
	box-shadow: inset 3px 1px 5px rgba(0,0,0,.5);
}
.c100:hover {
	cursor: pointer;
}

/* Completed fully met */
.c100.completed-fully-met .bar,
.c100.completed-fully-met .fill {
	border-color: #0f4c00 !important;
}

/* Completed modified */
.c100.completed-modified .bar,
.c100.completed-modified .fill {
	border-color: #4a9f04 !important;
}

/* Actions taken, progress made */
.c100.underway-on-track .bar,
.c100.underway-on-track .fill {
	border-color: #7fcd56 !important;
}

/* Actions taken, progress made toward ongoing goal */
.c100.guidance .bar,
.c100.guidance .fill {
	border-color: #84cf96 !important;
}

/* Actions taken, progress made, facing challenges */
.c100.underway-with-challenges .bar,
.c100.underway-with-challenges .fill {
	border-color: #f66002 !important;
}

/* Not being pursued */
.c100.not-being-pursued .bar,
.c100.not-being-pursued .fill {
	border-color: #e70000 !important;
}
.mtr.pick .c100 {
	font-size: 150px;
}

/*
 Datatables Filterbox
 ============================================= */
.tagcloud.dataTables_wrapper .dataTables_filter {
	position :relative;
}
.tagcloud.dataTables_wrapper .dataTables_filter,
.tagcloud.dataTables_wrapper .dataTables_filter label,
.tagcloud.dataTables_wrapper .dataTables_filter input {
	background: #fff;
	display: inline-block;
	margin: 0;
	width: 100%;
}
.tagcloud.dataTables_wrapper .dataTables_filter input[placeholder] {
	padding-left: 10px;
	text-indent: 20px;
}
.tagcloud.dataTables_wrapper .dataTables_filter label:before {
	content: "\e003";
	font-family: "Glyphicons Halflings";
	left: 10px;
	position: absolute;
	top: 5px;
}
.tagcloud.dataTables_wrapper .top {
	border-bottom:	1px solid #aaa;
}
.tagcloud.dataTables_wrapper .dataTables_info {
	font-size: smaller;
	padding: .5em 0;
	text-align: right;
	width:	100%;
}
.tagcloud.dataTables_wrapper .dataTables_info:after {
	content: "";
}
.tagcloud.dataTables_wrapper thead {
	clip: rect(1px, 1px, 1px, 1px);
	height: 1px;
	margin: 0;
	overflow: hidden;
	position: absolute;
	width:	1px;
}

/*
 tags formatting (extended)
 ============================================= */
[data-wbtbl-tagcloud] .tagitem {
	color: #000;
	font-size: .85em;
}
[data-wbtbl-tagcloud] .tagitem .content {
	background-color: #eee;
	border-bottom-left-radius: .25em;
	border-top-left-radius: .25em;
	padding: .38em;
}
[data-wbtbl-tagcloud] .tagitem button {
	background-color: #aaa;
	border-bottom-right-radius: .25em;
	border-top-right-radius: .25em;
	cursor: pointer;
	font-size: 1.1em;
	margin-left: 0;
	margin-top: -2px;
	opacity: 1;
	padding: .38em .7em;
}
[data-wbtbl-tagcloud] .tagitem button:hover {
	background-color: #555555;
}
.xxsmallview .tagcloud.dataTables_wrapper [data-wbtbl-tagcloud] {
	display: none;
}

/*
 News table formatting (extended)
 ============================================= */
/*
 Buttons
 ============================================= */
.nws-tbl-priority1.button a {
	background: #fff url("../assets/chart-line.svg") no-repeat scroll 10px center / 1.2em 1em;
	border:	2px double #eee;
	display: inline-block;
	margin:	.5em 0;
	padding: 5px 10px 5px 35px;
}
.accented-buttons .btn-primary {
	background-color: #278154;
	border-color: #278154;
	border-width: 3px;
	color: #fff;
}
.accented-buttons .btn-primary:hover,
.accented-buttons .btn-primary:focus,
.accented-buttons .btn-primary.focus,
.accented-buttons .btn-primary:active,
.accented-buttons .btn-primary.active,
.open > .accented-buttons .btn-primary.dropdown-toggle {
	background-color: #25724A;
}
.accented-buttons .btn-primary.pick {
	background: url("../assets/flag.png") top right no-repeat #278154;
	border-style: inset;
}

/*
 * Promotions styles
 */
@-webkit-keyframes slideInUp {
	0% {
		-webkit-transform: translate3d(0, 100%, 0);
		transform: translate3d(0, 100%, 0);
	}
	100% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
}
@keyframes slideInUp {
	0% {
		-webkit-transform: translate3d(0, 100%, 0);
		transform: translate3d(0, 100%, 0);
	}
	100% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
}
.ctr-xpr-bnr.wb-bar-b.open {
	-webkit-animation-delay: 2s;
	-webkit-animation-duration: 1s;
	-webkit-animation-fill-mode: forwards;
	-webkit-transition-timing-function: linear;
	animation-delay: 2s;
	animation-duration: 1s;
	animation-fill-mode: forwards;
	transition-timing-function: linear;
}
.ctr-xpr-bnr.wb-bar-b.open {
	-webkit-animation-name: slideInUp;
	-webkit-transform: translate3d(0, 100%, 0);
	animation-name: slideInUp;
	transform: translate3d(0, 100%, 0);
}

/*
 *	Styles
 */
.ctr-xpr-ad.bg-one,
.ctr-xpr-bnr.bg-one,
.ctr-xpr-ad.bg-one .container,
.ctr-xpr-bnr.bg-one .container {
	background: transparent;
	background-repeat: repeat-x;
}
.ctr-xpr-ad .modal-body,
.ctr-xpr-bnr .modal-body {
	padding: 0;
}
.ctr-xpr-ad.bg-one,
.ctr-xpr-bnr.bg-one {

	/* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#fcd4d4+0,f2443e+79,f2443e+100 */
	background: #fcd4d4; /* Old browsers */
	background: -moz-linear-gradient(top,	#fcd4d4 0%, #f2443e 79%, #f2443e 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(top,	#fcd4d4 0%,#f2443e 79%,#f2443e 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom,	#fcd4d4 0%,#f2443e 79%,#f2443e 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fcd4d4', endColorstr='#f2443e',GradientType=0 ); /* IE6-9 */
}
.ctr-xpr-ad.bg-one h3,
.ctr-xpr-bnr.bg-one h3,
.ctr-xpr-ad.bg-one p,
.ctr-xpr-bnr.bg-one p {
	color: #000;
}
.ctr-xpr-ad a,
.ctr-xpr-bnr a {
	text-decoration: none;
}
.ctr-xpr-ad a figure.col-centered figcaption .black,
.ctr-xpr-bnr a figure.col-centered figcaption .black {
	border-color: #222222;
	color: #222222;
	text-shadow: none;
}
.ctr-xpr-ad a figure.col-centered figcaption .xl-font,
.ctr-xpr-bnr a figure.col-centered figcaption .xl-font {
	font-size: 2.5em;
}
.ctr-xpr-ad h3,
.ctr-xpr-bnr h3,
.ctr-xpr-ad .largeOpaqueHeading,
.ctr-xpr-bnr .largeOpaqueHeading {
	font-size: 2em;
	margin-top: 20px;
	text-transform: uppercase;
}
.ctr-xpr-ad .flush-lft,
.ctr-xpr-bnr .flush-lft {
	margin-left: -15px;
}
.ctr-xpr-ad .flush-rht,
.ctr-xpr-bnr .flush-rht {
	margin-right: -15px;
}
.ctr-xpr-ad .small,
.ctr-xpr-bnr .small {
	font-size: 65% !important;
}
.ctr-xpr-ad p,
.ctr-xpr-bnr p {
	font-size: 1em;
	text-transform: uppercase;
}
.ctr-xpr-ad p.big,
.ctr-xpr-bnr p.big {
	font-size: 1.5em !important;
}
.ctr-xpr-ad p.btn,
.ctr-xpr-bnr p.btn {
	background: none;
	border: 1px solid #fff;
	border-radius: 6px;
	font-size: .9em;
	min-height: inherit;
	text-shadow: none;
}

/* Temporary */
body.secondary	.wb-inview.ctr-xpr-ad {
	display: none !important
}

.wb-dismissable-container {
	background-color: #f9f9f9;
	padding: 10px 0;
	position: relative;
}
.wb-dismissable-container .mfp-close {
	color: #fff;
	position: absolute;
	right: 0;
	top: 0;
}
.bg-clear,
.xsmallview .bg-clear,
.xxsmallview .bg-clear,
.smallview .bg-clear {
	background: none !important;
}

/* Datatables Legend/PieLabel Series helper classes
 ============================================================= */
.wb-graph.legend-hide-1 .legend table tbody tr:nth-child(1),
.wb-graph.legend-hide-2 .legend table tbody tr:nth-child(2),
.wb-graph.legend-hide-3 .legend table tbody tr:nth-child(3),
.wb-graph.legend-hide-4 .legend table tbody tr:nth-child(4),
.wb-graph.legend-hide-5 .legend table tbody tr:nth-child(5),
.wb-graph.legend-hide-6 .legend table tbody tr:nth-child(6),
.wb-graph.legend-hide-7 .legend table tbody tr:nth-child(7),
.wb-graph.legend-hide-8 .legend table tbody tr:nth-child(8),
.wb-graph.legend-hide-9 .legend table tbody tr:nth-child(9),
.wb-graph.legend-hide-10 .legend table tbody tr:nth-child(10),
.wb-graph.pieLabel-hide-1  figure .pieLabel:nth-of-type(1),
.wb-graph.pieLabel-hide-2  figure .pieLabel:nth-of-type(2),
.wb-graph.pieLabel-hide-3  figure .pieLabel:nth-of-type(3),
.wb-graph.pieLabel-hide-4  figure .pieLabel:nth-of-type(4),
.wb-graph.pieLabel-hide-5  figure .pieLabel:nth-of-type(5),
.wb-graph.pieLabel-hide-6  figure .pieLabel:nth-of-type(6),
.wb-graph.pieLabel-hide-7  figure .pieLabel:nth-of-type(7),
.wb-graph.pieLabel-hide-8  figure .pieLabel:nth-of-type(8),
.wb-graph.pieLabel-hide-9  figure .pieLabel:nth-of-type(9),
.wb-graph.pieLabel-hide-10 figure .pieLabel:nth-of-type(10) {
	clip: rect(1px, 1px, 1px, 1px);
	height: 1px;
	margin: 0;
	overflow: hidden;
	position: absolute;
	width:  1px;
}
.wb-graph.pieLabel-inverse figure .pieLabel {
	background: transparent;
	border: 0;
	color: #fff;
}

/* Datatables Legend - Target Class
 ============================================================= */
.wb-graph .legend table tr .legendColorBox div,
.wb-graph .legend table tr .legendColorBox div div {
	border-radius: 50%;
}
.wb-graph.legendcolorbox-square-1 .legend table tr:nth-of-type(1) .legendColorBox div,
.wb-graph.legendcolorbox-square-1 .legend table tr:nth-of-type(1) .legendColorBox div div,
.wb-graph.legendcolorbox-square-2 .legend table tr:nth-of-type(2) .legendColorBox div,
.wb-graph.legendcolorbox-square-2 .legend table tr:nth-of-type(2) .legendColorBox div div,
.wb-graph.legendcolorbox-square-3 .legend table tr:nth-of-type(3) .legendColorBox div,
.wb-graph.legendcolorbox-square-3 .legend table tr:nth-of-type(3) .legendColorBox div div,
.wb-graph.legendcolorbox-square-4 .legend table tr:nth-of-type(4) .legendColorBox div,
.wb-graph.legendcolorbox-square-4 .legend table tr:nth-of-type(4) .legendColorBox div div,
.wb-graph.legendcolorbox-square-5 .legend table tr:nth-of-type(5) .legendColorBox div,
.wb-graph.legendcolorbox-square-5 .legend table tr:nth-of-type(5) .legendColorBox div div,
.wb-graph.legendcolorbox-square-6 .legend table tr:nth-of-type(6) .legendColorBox div,
.wb-graph.legendcolorbox-square-6 .legend table tr:nth-of-type(6) .legendColorBox div div,
.wb-graph.legendcolorbox-square-7 .legend table tr:nth-of-type(7) .legendColorBox div,
.wb-graph.legendcolorbox-square-7 .legend table tr:nth-of-type(7) .legendColorBox div div,
.wb-graph.legendcolorbox-square-8 .legend table tr:nth-of-type(8) .legendColorBox div,
.wb-graph.legendcolorbox-square-8 .legend table tr:nth-of-type(8) .legendColorBox div div,
.wb-graph.legendcolorbox-square-9 .legend table tr:nth-of-type(9) .legendColorBox div,
.wb-graph.legendcolorbox-square-9 .legend table tr:nth-of-type(9) .legendColorBox div div,
.wb-graph.legendcolorbox-square-10 .legend table tr:nth-of-type(10) .legendColorBox div,
.wb-graph.legendcolorbox-square-10 .legend table tr:nth-of-type(10) .legendColorBox div div,
.wb-graph.legendcolorbox-square-11 .legend table tr:nth-of-type(11) .legendColorBox div,
.wb-graph.legendcolorbox-square-11 .legend table tr:nth-of-type(11) .legendColorBox div div {
	border-radius: 0;
}
