"""
Test suite for CRUD operations on TestType objects.

This class contains tests for the following functions:

- get_test_type:
    - test_get_test_type
    - test_get_test_type_not_found
- get_test_types:
    - test_get_test_types
    - test_get_test_types_with_pagination
    - test_get_test_types_validation_error
    - test_get_test_types_with_lab_filter
    - test_get_test_types_with_nonexistent_lab
- create_test_type:
    - test_create_test_type_success
    - test_create_test_type_unauthorized
    - test_create_test_type_db_error
- update_test_type:
    - test_update_test_type_success
    - test_update_test_type_not_found
    - test_update_test_type_unauthorized
    - test_update_test_type_db_error
- delete_test_type:
    - test_delete_test_type_success
    - test_delete_test_type_not_found
    - test_delete_test_type_unauthorized
    - test_delete_test_type_db_error
- batch_update_test_types:
    - test_batch_update_test_types_success
    - test_batch_update_test_types_single_success
    - test_batch_update_test_types_partial_update
    - test_batch_update_test_types_unauthorized
    - test_batch_update_test_types_empty_batch
    - test_batch_update_test_types_invalid_test_type_id
    - test_batch_update_test_types_nonexistent_test_type
    - test_batch_update_test_types_mixed_results
    - test_batch_update_test_types_database_error
"""
import uuid
import pytest
from sqlalchemy.orm import Session
from unittest.mock import patch, MagicMock
from sqlalchemy.exc import SQLAlchemyError

from app.crud.test import (
    get_test_type,
    get_test_types,
    create_test_type,
    update_test_type,
    delete_test_type,
    batch_update_test_types
)
from app.schemas.test import TestTypeCreate, TestTypeUpdate, TestTypeBatchUpdate
from app.common.constants import ERROR_MESSAGES, UserRole
from app.common.exceptions import NotFoundError, AccessDeniedError, ValidationError as AppValidationError, StateError
from tests.test_utils.constants import TEST_TYPE_DATA, TEST_TYPE_TEST_CASES
from tests.test_utils.helpers import TestHelpers
from tests.test_utils.verification import TestTypeVerifier, verify_crud_error_response


class TestTestTypeCRUD:
    def test_get_test_type(self, db: Session, test_type):
        """Test retrieving a test type by ID"""
        retrieved_test_type = get_test_type(db, test_type.test_type_id)
        
        assert retrieved_test_type is not None
        TestTypeVerifier.verify_common_fields(retrieved_test_type.__dict__)
        TestTypeVerifier.verify_response(retrieved_test_type.__dict__, {
            "test_type_id": test_type.test_type_id,
            "name": test_type.name,
            "description": test_type.description,
            "is_active": test_type.is_active
        })

    def test_get_test_type_not_found(self, db: Session):
        """Test retrieving a non-existent test type"""
        non_existent_id = str(uuid.uuid4())
        retrieved_test_type = get_test_type(db, non_existent_id)

        assert retrieved_test_type is None

    def test_get_test_types(self, db: Session, test_types):
        """Test retrieving all test types"""
        retrieved_test_types = get_test_types(db)

        assert len(retrieved_test_types) == len(test_types)
        # Verify test types are returned in the expected order
        test_type_ids = [test_type.test_type_id for test_type in retrieved_test_types]
        for test_type in test_types:
            assert test_type.test_type_id in test_type_ids

        # Verify each test type
        for test_type in retrieved_test_types:
            TestTypeVerifier.verify_common_fields(test_type.__dict__)
            # No specific values to verify, just that it's a valid test type

    def test_get_test_types_with_lab_filter(self, db: Session, test_types_multiple_labs, test_labs):
        """Test retrieving test types filtered by lab_id"""
        # Get test types for the first lab
        lab_id = str(test_labs[0].lab_id)
        retrieved_test_types = get_test_types(db, lab_id=lab_id)

        # Should only return test types for this lab
        assert len(retrieved_test_types) > 0
        for test_type in retrieved_test_types:
            assert str(test_type.lab_id) == lab_id
            TestTypeVerifier.verify_common_fields(test_type.__dict__)

    def test_get_test_types_with_nonexistent_lab(self, db: Session, test_types_multiple_labs):
        """Test retrieving test types with non-existent lab_id"""
        non_existent_lab_id = str(uuid.uuid4())
        retrieved_test_types = get_test_types(db, lab_id=non_existent_lab_id)

        # Should return empty list
        assert len(retrieved_test_types) == 0

    def test_get_test_types_with_pagination(self, db: Session, test_types):
        """Test retrieving test types with pagination"""
        # Get first test type with skip=0, limit=1
        first_page = get_test_types(db, skip=0, limit=1)
        assert len(first_page) == 1
        TestTypeVerifier.verify_common_fields(first_page[0].__dict__)
        
        # Get second test type with skip=1, limit=1
        second_page = get_test_types(db, skip=1, limit=1)
        assert len(second_page) == 1
        TestTypeVerifier.verify_common_fields(second_page[0].__dict__)
        
        # Verify first and second pages contain different test types
        assert first_page[0].test_type_id != second_page[0].test_type_id
        
        # Get all test types with a large limit
        all_test_types = get_test_types(db, skip=0, limit=100)
        assert len(all_test_types) == len(test_types)

    def test_get_test_types_validation_error(self, db: Session):
        """Test validation error when retrieving test types with invalid pagination"""
        # Test with negative skip
        with pytest.raises(AppValidationError) as exc_info:
            get_test_types(db, skip=-1)
        verify_crud_error_response(exc_info.value, 400, "Skip must be non-negative")
        
        # Test with negative limit
        with pytest.raises(AppValidationError) as exc_info:
            get_test_types(db, limit=-1)
        verify_crud_error_response(exc_info.value, 400, "Limit must be positive")

    def test_create_test_type_success(self, db: Session, test_staff_admin):
        """Test creating a test type successfully"""
        test_type_data = TestTypeCreate(
            name="New Test Type",
            description="A new test type for testing",
            is_active=True
        )

        created_test_type = create_test_type(db, test_type_data, test_staff_admin)

        assert created_test_type is not None
        TestTypeVerifier.verify_common_fields(created_test_type.__dict__)
        TestTypeVerifier.verify_response(created_test_type.__dict__, {
            "name": "New Test Type",
            "description": "A new test type for testing",
            "is_active": True
        })

        # Verify lab_id is automatically assigned from current user
        assert created_test_type.lab_id == test_staff_admin.lab_id

        # Verify the test type was actually created in the database
        retrieved_test_type = get_test_type(db, str(created_test_type.test_type_id))
        assert retrieved_test_type is not None
        assert retrieved_test_type.name == "New Test Type"
        assert retrieved_test_type.lab_id == test_staff_admin.lab_id

    def test_create_test_type_unauthorized(self, db: Session, test_staff_user):
        """Test creating a test type with unauthorized user"""
        test_type_data = TestTypeCreate(
            name="Unauthorized Test Type",
            description="This should not be created",
            is_active=True
        )
        
        with pytest.raises(AccessDeniedError) as exc_info:
            create_test_type(db, test_type_data, test_staff_user)
        verify_crud_error_response(exc_info.value, 403, ERROR_MESSAGES["test"]["unauthorized_create"])

    def test_create_test_type_db_error(self, db: Session, test_staff_admin):
        """Test database error when creating a test type"""
        test_type_data = TestTypeCreate(
            name="DB Error Test Type",
            description="This should cause a database error",
            is_active=True
        )
        
        # Mock the database commit operation to simulate an error
        db.commit = MagicMock(side_effect=SQLAlchemyError("Database error"))
        
        with pytest.raises(StateError) as exc_info:
            create_test_type(db, test_type_data, test_staff_admin)
        verify_crud_error_response(exc_info.value, 409, ERROR_MESSAGES["test"]["create_failed"].format(error="Database error"))

    def test_update_test_type_success(self, db: Session, test_type, test_staff_admin):
        """Test updating a test type successfully"""
        update_data = TestTypeUpdate(
            name="Updated Test Type",
            description="Updated description",
            is_active=False
        )
        
        updated_test_type = update_test_type(db, test_type.test_type_id, update_data, test_staff_admin)
        
        assert updated_test_type is not None
        TestTypeVerifier.verify_common_fields(updated_test_type.__dict__)
        TestTypeVerifier.verify_response(updated_test_type.__dict__, {
            "test_type_id": test_type.test_type_id,
            "name": "Updated Test Type",
            "description": "Updated description",
            "is_active": False
        })
        
        # Verify the test type was actually updated in the database
        retrieved_test_type = get_test_type(db, test_type.test_type_id)
        assert retrieved_test_type is not None
        assert retrieved_test_type.name == "Updated Test Type"
        assert retrieved_test_type.description == "Updated description"
        assert retrieved_test_type.is_active == False

    def test_update_test_type_not_found(self, db: Session, test_staff_admin):
        """Test updating a non-existent test type"""
        update_data = TestTypeUpdate(
            name="Non-existent Test Type",
            description="This should not update anything",
            is_active=False
        )
        
        with pytest.raises(NotFoundError) as exc_info:
            update_test_type(db, str(uuid.uuid4()), update_data, test_staff_admin)
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["test"]["not_found"])

    def test_update_test_type_unauthorized(self, db: Session, test_type, test_staff_user):
        """Test updating a test type with unauthorized user"""
        update_data = TestTypeUpdate(
            name="Unauthorized Update",
            description="This should not update anything",
            is_active=False
        )
        
        with pytest.raises(AccessDeniedError) as exc_info:
            update_test_type(db, test_type.test_type_id, update_data, test_staff_user)
        verify_crud_error_response(exc_info.value, 403, ERROR_MESSAGES["test"]["unauthorized_update"])

    def test_update_test_type_db_error(self, db: Session, test_type, test_staff_admin):
        """Test database error when updating a test type"""
        update_data = TestTypeUpdate(
            name="DB Error Update",
            description="This should cause a database error",
            is_active=False
        )
        
        # Mock the database commit operation to simulate an error
        db.commit = MagicMock(side_effect=SQLAlchemyError("Database error"))
        
        with pytest.raises(StateError) as exc_info:
            update_test_type(db, test_type.test_type_id, update_data, test_staff_admin)
        verify_crud_error_response(exc_info.value, 409, ERROR_MESSAGES["test"]["update_failed"].format(error="Database error"))

    def test_delete_test_type_success(self, db: Session, test_type, test_staff_admin):
        """Test deleting a test type successfully"""
        # Store test_type_id before deletion
        test_type_id = test_type.test_type_id
        
        # Delete the test type
        delete_test_type(db, test_type_id, test_staff_admin)
        
        # Verify the test type was actually deleted from the database
        retrieved_test_type = get_test_type(db, test_type_id)
        assert retrieved_test_type is None

    def test_delete_test_type_not_found(self, db: Session, test_staff_admin):
        """Test deleting a non-existent test type"""
        with pytest.raises(NotFoundError) as exc_info:
            delete_test_type(db, str(uuid.uuid4()), test_staff_admin)
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["test"]["not_found"])

    def test_delete_test_type_unauthorized(self, db: Session, test_type, test_staff_user):
        """Test deleting a test type with unauthorized user"""
        with pytest.raises(AccessDeniedError) as exc_info:
            delete_test_type(db, test_type.test_type_id, test_staff_user)
        verify_crud_error_response(exc_info.value, 403, ERROR_MESSAGES["test"]["unauthorized_delete"])

    def test_delete_test_type_db_error(self, db: Session, test_type, test_staff_admin):
        """Test database error when deleting a test type"""
        # Mock the database commit operation to simulate an error
        db.commit = MagicMock(side_effect=SQLAlchemyError("Database error"))

        with pytest.raises(StateError) as exc_info:
            delete_test_type(db, test_type.test_type_id, test_staff_admin)
        verify_crud_error_response(exc_info.value, 409, ERROR_MESSAGES["test"]["delete_failed"].format(error="Database error"))

    #################################
    # batch_update_test_types TESTS
    #################################

    def test_batch_update_test_types_success(self, db: Session, test_staff_admin):
        """Test successful batch update of multiple test types"""
        # Create test types for batch update
        test_types = TestHelpers.TestTypes.create_test_types(db, TEST_TYPE_DATA)

        # Prepare batch update data
        update_data = TEST_TYPE_TEST_CASES["batch_update"]["valid_batch"]
        batch_updates = TestHelpers.TestTypes.create_batch_update_data(test_types, update_data)
        batch_request = TestTypeBatchUpdate(updates=batch_updates)

        # Execute batch update
        result = batch_update_test_types(db, batch_request, test_staff_admin)

        # Verify batch update response using verifier
        TestTypeVerifier.verify_batch_update_response(result, expected_count=2)

        # Verify that test types were actually updated in database
        db.refresh(test_types[0])
        db.refresh(test_types[1])
        TestTypeVerifier.verify_response(test_types[0].__dict__, {
            "name": update_data[0]["name"],
            "description": update_data[0]["description"],
            "is_active": update_data[0]["is_active"]
        })
        TestTypeVerifier.verify_response(test_types[1].__dict__, {
            "name": update_data[1]["name"],
            "description": update_data[1]["description"],
            "is_active": update_data[1]["is_active"]
        })

    def test_batch_update_test_types_single_success(self, db: Session, test_staff_admin):
        """Test successful batch update with single test type"""
        # Create single test type
        test_type = TestHelpers.TestTypes.create_test_type(db, TEST_TYPE_DATA["test_type1"])

        # Prepare single item batch update
        update_data = TEST_TYPE_TEST_CASES["batch_update"]["single_item"]
        batch_updates = TestHelpers.TestTypes.create_batch_update_data([test_type], update_data)
        batch_request = TestTypeBatchUpdate(updates=batch_updates)

        # Execute batch update
        result = batch_update_test_types(db, batch_request, test_staff_admin)

        # Verify batch update response using verifier
        TestTypeVerifier.verify_batch_update_response(result, expected_count=1)

        # Verify that test type was actually updated in database
        db.refresh(test_type)
        TestTypeVerifier.verify_response(test_type.__dict__, {
            "name": update_data[0]["name"],
            "description": update_data[0]["description"],
            "is_active": update_data[0]["is_active"]
        })

    def test_batch_update_test_types_partial_update(self, db: Session, test_staff_admin):
        """Test batch update with partial field updates"""
        # Create test types
        test_types = TestHelpers.TestTypes.create_test_types(db, TEST_TYPE_DATA)

        # Prepare partial update data
        update_data = TEST_TYPE_TEST_CASES["batch_update"]["partial_batch"]
        batch_updates = TestHelpers.TestTypes.create_batch_update_data(test_types, update_data)
        batch_request = TestTypeBatchUpdate(updates=batch_updates)

        # Execute batch update
        result = batch_update_test_types(db, batch_request, test_staff_admin)

        # Verify batch update response using verifier
        TestTypeVerifier.verify_batch_update_response(result, expected_count=2)

        # Verify that test types were actually updated in database
        db.refresh(test_types[0])
        db.refresh(test_types[1])
        # First test type: only name updated
        TestTypeVerifier.verify_response(test_types[0].__dict__, {
            "name": update_data[0]["name"]
        })
        # Second test type: description and is_active updated
        TestTypeVerifier.verify_response(test_types[1].__dict__, {
            "description": update_data[1]["description"],
            "is_active": update_data[1]["is_active"]
        })

    def test_batch_update_test_types_unauthorized(self, db: Session, test_staff_user):
        """Test batch update with unauthorized user (non-admin)"""
        # Create test types
        test_types = TestHelpers.TestTypes.create_test_types(db, TEST_TYPE_DATA)

        # Prepare batch update data
        update_data = TEST_TYPE_TEST_CASES["batch_update"]["valid_batch"]
        batch_updates = TestHelpers.TestTypes.create_batch_update_data(test_types, update_data)
        batch_request = TestTypeBatchUpdate(updates=batch_updates)

        # Attempt batch update with non-admin user
        with pytest.raises(AccessDeniedError) as exc_info:
            batch_update_test_types(db, batch_request, test_staff_user)
        verify_crud_error_response(exc_info.value, 403, ERROR_MESSAGES["test"]["unauthorized_update"])

    def test_batch_update_test_types_empty_batch(self, db: Session, test_staff_admin):
        """Test batch update with empty batch"""
        # Prepare empty batch
        batch_request = TestTypeBatchUpdate(updates=[])

        # Attempt batch update with empty batch
        with pytest.raises(AppValidationError) as exc_info:
            batch_update_test_types(db, batch_request, test_staff_admin)
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["test"]["batch_empty"])

    def test_batch_update_test_types_invalid_uuid_format(self, db: Session, test_staff_admin):
        """Test batch update with invalid UUID format - should fail entire batch"""
        # Prepare batch with invalid UUID format
        invalid_batch = [{"test_type_id": "invalid-uuid-format", "name": "Updated Name"}]
        batch_request = TestTypeBatchUpdate(updates=invalid_batch)

        # Execute batch update - should fail entirely
        with pytest.raises(Exception):  # Invalid UUID should cause complete failure
            batch_update_test_types(db, batch_request, test_staff_admin)

    def test_batch_update_test_types_nonexistent_test_type(self, db: Session, test_staff_admin):
        """Test batch update with nonexistent test type ID - should fail entire batch"""
        # Prepare batch with nonexistent but valid UUID
        nonexistent_id = str(uuid.uuid4())
        nonexistent_batch = [{"test_type_id": nonexistent_id, "name": "Updated Name"}]
        batch_request = TestTypeBatchUpdate(updates=nonexistent_batch)

        # Execute batch update - should fail entirely
        with pytest.raises(Exception):  # Nonexistent ID should cause complete failure
            batch_update_test_types(db, batch_request, test_staff_admin)

    def test_batch_update_test_types_invalid_test_type_fails_all(self, db: Session, test_staff_admin):
        """Test batch update with invalid test type ID - should fail entire batch"""
        # Create one valid test type
        valid_test_type = TestHelpers.TestTypes.create_test_type(db, TEST_TYPE_DATA["test_type1"])

        # Prepare batch with one valid and one invalid update
        mixed_batch = [
            {"test_type_id": str(valid_test_type.test_type_id), "name": "Valid Update"},
            {"test_type_id": str(uuid.uuid4()), "name": "Invalid Update - Nonexistent ID"}
        ]
        batch_request = TestTypeBatchUpdate(updates=mixed_batch)

        # Store original values before attempting update
        original_name = valid_test_type.name

        # Execute batch update - should fail entirely due to atomic transaction
        with pytest.raises(Exception):  # Any exception should cause complete failure
            batch_update_test_types(db, batch_request, test_staff_admin)

        # Verify that original values are preserved (transaction rolled back)
        original_values = [{"name": original_name}]
        TestHelpers.TestTypes.verify_no_updates_applied(db, [valid_test_type], original_values)

    def test_batch_update_test_types_validation_errors(self, db: Session, test_staff_admin):
        """Test batch update with validation errors - should fail entire batch"""
        # Create test types
        test_types = TestHelpers.TestTypes.create_test_types(db, TEST_TYPE_DATA)

        # Prepare batch with invalid data types
        invalid_batch = [
            {"test_type_id": str(test_types[0].test_type_id), "name": "Valid Update"},
            {"test_type_id": str(test_types[1].test_type_id), "is_active": "not_a_boolean"}  # Invalid data type
        ]
        batch_request = TestTypeBatchUpdate(updates=invalid_batch)

        # Store original values before attempting update
        original_values = [
            {"name": test_types[0].name, "description": test_types[0].description, "is_active": test_types[0].is_active},
            {"name": test_types[1].name, "description": test_types[1].description, "is_active": test_types[1].is_active}
        ]

        # Execute batch update - should fail entirely due to validation error
        with pytest.raises(Exception):  # Validation error should cause complete failure
            batch_update_test_types(db, batch_request, test_staff_admin)

        # Verify that original values are preserved (transaction rolled back)
        TestHelpers.TestTypes.verify_no_updates_applied(db, test_types, original_values)

    def test_batch_update_test_types_database_error(self, db: Session, test_staff_admin):
        """Test batch update with database error - should fail entire batch"""
        # Create test types
        test_types = TestHelpers.TestTypes.create_test_types(db, TEST_TYPE_DATA)

        # Prepare batch update data
        update_data = TEST_TYPE_TEST_CASES["batch_update"]["valid_batch"]
        batch_updates = TestHelpers.TestTypes.create_batch_update_data(test_types, update_data)
        batch_request = TestTypeBatchUpdate(updates=batch_updates)

        # Mock database error during get_test_type
        with patch('app.crud.test.get_test_type') as mock_get:
            mock_get.side_effect = StateError(ERROR_MESSAGES["test"]["update_failed"].format(error="Database error"))

            # Execute batch update - should fail entirely and rollback
            with pytest.raises(StateError) as exc_info:
                batch_update_test_types(db, batch_request, test_staff_admin)

            # Verify the error message
            assert "Database error" in str(exc_info.value)
