!function(t,s){"object"==typeof exports&&"undefined"!=typeof module?module.exports=s():"function"==typeof define&&define.amd?define(s):t.proj4=s()}(this,function(){"use strict";function m(t,s){if(t[s])return t[s];for(var i,a=Object.keys(t),h=s.toLowerCase().replace(gt,""),e=-1;++e<a.length;)if((i=a[e]).toLowerCase().replace(gt,"")===h)return t[i]}function a(t){if("string"!=typeof t)throw new Error("not a string");this.text=t.trim(),this.level=0,this.place=0,this.root=null,this.stack=[],this.currentObject=null,this.state=1}function L(t,s,i){Array.isArray(s)&&(i.unshift(s),s=null);i=i.reduce(function(t,s){return h(s,t),t},s?{}:t);s&&(t[s]=i)}function h(t,s){if(Array.isArray(t)){var i,a=t.shift();if("PARAMETER"===a&&(a=t.shift()),1===t.length)return Array.isArray(t[0])?(s[a]={},void h(t[0],s[a])):void(s[a]=t[0]);if(t.length)if("TOWGS84"!==a)if("AXIS"===a)a in s||(s[a]=[]),s[a].push(t);else switch(Array.isArray(a)||(s[a]={}),a){case"UNIT":case"PRIMEM":case"VERT_DATUM":return s[a]={name:t[0].toLowerCase(),convert:t[1]},void(3===t.length&&h(t[2],s[a]));case"SPHEROID":case"ELLIPSOID":return s[a]={name:t[0],a:t[1],rf:t[2]},void(4===t.length&&h(t[3],s[a]));case"PROJECTEDCRS":case"PROJCRS":case"GEOGCS":case"GEOCCS":case"PROJCS":case"LOCAL_CS":case"GEODCRS":case"GEODETICCRS":case"GEODETICDATUM":case"EDATUM":case"ENGINEERINGDATUM":case"VERT_CS":case"VERTCRS":case"VERTICALCRS":case"COMPD_CS":case"COMPOUNDCRS":case"ENGINEERINGCRS":case"ENGCRS":case"FITTED_CS":case"LOCAL_DATUM":case"DATUM":return t[0]=["name",t[0]],void L(s,a,t);default:for(i=-1;++i<t.length;)if(!Array.isArray(t[i]))return h(t,s[a]);return L(s,a,t)}else s[a]=t;else s[a]=!0}else s[t]=!0}function r(t){return.017453292519943295*t}function z(h){function t(t){return t*(h.to_meter||1)}if("GEOGCS"===h.type?h.projName="longlat":"LOCAL_CS"===h.type?(h.projName="identity",h.local=!0):"object"==typeof h.PROJECTION?h.projName=Object.keys(h.PROJECTION)[0]:h.projName=h.PROJECTION,h.AXIS){for(var s="",i=0,a=h.AXIS.length;i<a;++i){var e=[h.AXIS[i][0].toLowerCase(),h.AXIS[i][1].toLowerCase()];-1!==e[0].indexOf("north")||("y"===e[0]||"lat"===e[0])&&"north"===e[1]?s+="n":-1!==e[0].indexOf("south")||("y"===e[0]||"lat"===e[0])&&"south"===e[1]?s+="s":-1!==e[0].indexOf("east")||("x"===e[0]||"lon"===e[0])&&"east"===e[1]?s+="e":-1===e[0].indexOf("west")&&("x"!==e[0]&&"lon"!==e[0]||"west"!==e[1])||(s+="w")}2===s.length&&(s+="u"),3===s.length&&(h.axis=s)}h.UNIT&&(h.units=h.UNIT.name.toLowerCase(),"metre"===h.units&&(h.units="meter"),h.UNIT.convert)&&("GEOGCS"===h.type?h.DATUM&&h.DATUM.SPHEROID&&(h.to_meter=h.UNIT.convert*h.DATUM.SPHEROID.a):h.to_meter=h.UNIT.convert);var n=h.GEOGCS;(n="GEOGCS"===h.type?h:n)&&(n.DATUM?h.datumCode=n.DATUM.name.toLowerCase():h.datumCode=n.name.toLowerCase(),"d_"===h.datumCode.slice(0,2)&&(h.datumCode=h.datumCode.slice(2)),"new_zealand_geodetic_datum_1949"!==h.datumCode&&"new_zealand_1949"!==h.datumCode||(h.datumCode="nzgd49"),"wgs_1984"!==h.datumCode&&"world_geodetic_system_1984"!==h.datumCode||("Mercator_Auxiliary_Sphere"===h.PROJECTION&&(h.sphere=!0),h.datumCode="wgs84"),"_ferro"===h.datumCode.slice(-6)&&(h.datumCode=h.datumCode.slice(0,-6)),"_jakarta"===h.datumCode.slice(-8)&&(h.datumCode=h.datumCode.slice(0,-8)),~h.datumCode.indexOf("belge")&&(h.datumCode="rnb72"),n.DATUM&&n.DATUM.SPHEROID&&(h.ellps=n.DATUM.SPHEROID.name.replace("_19","").replace(/[Cc]larke\_18/,"clrk"),"international"===h.ellps.toLowerCase().slice(0,13)&&(h.ellps="intl"),h.a=n.DATUM.SPHEROID.a,h.rf=parseFloat(n.DATUM.SPHEROID.rf,10)),n.DATUM&&n.DATUM.TOWGS84&&(h.datum_params=n.DATUM.TOWGS84),~h.datumCode.indexOf("osgb_1936")&&(h.datumCode="osgb36"),~h.datumCode.indexOf("osni_1952")&&(h.datumCode="osni52"),(~h.datumCode.indexOf("tm65")||~h.datumCode.indexOf("geodetic_datum_of_1965"))&&(h.datumCode="ire65"),"ch1903+"===h.datumCode&&(h.datumCode="ch1903"),~h.datumCode.indexOf("israel"))&&(h.datumCode="isr93"),h.b&&!isFinite(h.b)&&(h.b=h.a),[["standard_parallel_1","Standard_Parallel_1"],["standard_parallel_1","Latitude of 1st standard parallel"],["standard_parallel_2","Standard_Parallel_2"],["standard_parallel_2","Latitude of 2nd standard parallel"],["false_easting","False_Easting"],["false_easting","False easting"],["false-easting","Easting at false origin"],["false_northing","False_Northing"],["false_northing","False northing"],["false_northing","Northing at false origin"],["central_meridian","Central_Meridian"],["central_meridian","Longitude of natural origin"],["central_meridian","Longitude of false origin"],["latitude_of_origin","Latitude_Of_Origin"],["latitude_of_origin","Central_Parallel"],["latitude_of_origin","Latitude of natural origin"],["latitude_of_origin","Latitude of false origin"],["scale_factor","Scale_Factor"],["k0","scale_factor"],["latitude_of_center","Latitude_Of_Center"],["latitude_of_center","Latitude_of_center"],["lat0","latitude_of_center",r],["longitude_of_center","Longitude_Of_Center"],["longitude_of_center","Longitude_of_center"],["longc","longitude_of_center",r],["x0","false_easting",t],["y0","false_northing",t],["long0","central_meridian",r],["lat0","latitude_of_origin",r],["lat0","standard_parallel_1",r],["lat1","standard_parallel_1",r],["lat2","standard_parallel_2",r],["azimuth","Azimuth"],["alpha","azimuth",r],["srsCode","name"]].forEach(function(t){var s,i,a;s=h,i=(t=t)[0],a=t[1],!(i in s)&&a in s&&(s[i]=s[a],3===t.length)&&(s[i]=t[2](s[i]))}),h.long0||!h.longc||"Albers_Conic_Equal_Area"!==h.projName&&"Lambert_Azimuthal_Equal_Area"!==h.projName||(h.long0=h.longc),h.lat_ts||!h.lat1||"Stereographic_South_Pole"!==h.projName&&"Polar Stereographic (variant B)"!==h.projName?!h.lat_ts&&h.lat0&&"Polar_Stereographic"===h.projName&&(h.lat_ts=h.lat0,h.lat0=r(0<h.lat0?90:-90)):(h.lat0=r(0<h.lat1?90:-90),h.lat_ts=h.lat1)}function e(t){var s=this;if(2===arguments.length){var i=arguments[1];"string"==typeof i?"+"===i.charAt(0)?e[t]=bt(arguments[1]):e[t]=Pt(arguments[1]):e[t]=i}else if(1===arguments.length){if(Array.isArray(t))return t.map(function(t){Array.isArray(t)?e.apply(s,t):e(t)});if("string"==typeof t){if(t in e)return e[t]}else"EPSG"in t?e["EPSG:"+t.EPSG]=t:"ESRI"in t?e["ESRI:"+t.ESRI]=t:"IAU2000"in t?e["IAU2000:"+t.IAU2000]=t:console.log(t)}}function R(t){var s,i,a;return"string"!=typeof t?t:t in e?e[t]:(a=t,At.some(function(t){return-1<a.indexOf(t)})?function(t){if(t=m(t,"authority"))return(t=m(t,"epsg"))&&-1<Ot.indexOf(t)}(s=Pt(t))?e["EPSG:3857"]:(i=function(t){if(t=m(t,"extension"))return m(t,"proj4")}(s))?bt(i):s:"+"===t[0]?bt(t):void 0)}function B(t){return t}function T(t,s){var i=Lt.length;return t.names?((Lt[i]=t).names.forEach(function(t){Gt[t.toLowerCase()]=i}),this):(console.log(s),!0)}function D(t){var s;return 0===t.length?null:"null"===(t=(s="@"===t[0])?t.slice(1):t)?{name:"null",mandatory:!s,grid:null,isNull:!0}:{name:t,mandatory:!s,grid:es[t]||null,isNull:!1}}function M(t){return t/3600*Math.PI/180}function F(t,s,i){return String.fromCharCode.apply(null,new Uint8Array(t.buffer.slice(s,i)))}function U(t,s,i){for(var a=176,h=[],e=0;e<s.nSubgrids;e++){o=i;var n={name:F(n=t,(r=a)+8,r+16).trim(),parent:F(n,r+24,r+24+8).trim(),lowerLatitude:n.getFloat64(r+72,o),upperLatitude:n.getFloat64(r+88,o),lowerLongitude:n.getFloat64(r+104,o),upperLongitude:n.getFloat64(r+120,o),latitudeInterval:n.getFloat64(r+136,o),longitudeInterval:n.getFloat64(r+152,o),gridNodeCount:n.getInt32(r+168,o)},r=function(t,s,i,a){for(var h=s+176,e=[],n=0;n<i.gridNodeCount;n++){var r={latitudeShift:t.getFloat32(h+16*n,a),longitudeShift:t.getFloat32(h+16*n+4,a),latitudeAccuracy:t.getFloat32(h+16*n+8,a),longitudeAccuracy:t.getFloat32(h+16*n+12,a)};e.push(r)}return e}(t,a,n,i),o=Math.round(1+(n.upperLongitude-n.lowerLongitude)/n.longitudeInterval),l=Math.round(1+(n.upperLatitude-n.lowerLatitude)/n.latitudeInterval);h.push({ll:[M(n.lowerLongitude),M(n.lowerLatitude)],del:[M(n.longitudeInterval),M(n.latitudeInterval)],lim:[o,l],count:n.gridNodeCount,cvs:r.map(function(t){return[M(t.longitudeShift),M(t.latitudeShift)]})}),a+=176+16*n.gridNodeCount}return h}function p(t,s){if(!(this instanceof p))return new p(t);s=s||function(t){if(t)throw t};var i,a,h,e,n,r,o,l,M,c,u,f,d=R(t);"object"==typeof d?(i=p.projections.get(d.projName))?(d.datumCode&&"none"!==d.datumCode&&(l=m(Bt,d.datumCode))&&(d.datum_params=d.datum_params||(l.towgs84?l.towgs84.split(","):null),d.ellps=l.ellipse,d.datumName=l.datumName||d.datumCode),d.k0=d.k0||1,d.axis=d.axis||"enu",d.ellps=d.ellps||"wgs84",d.lat1=d.lat1||d.lat0,l=d.a,M=d.b,c=d.rf,u=d.ellps,f=d.sphere,l||(l=(u=(u=m(zt,u))||Rt).a,M=u.b,c=u.rf),c&&!M&&(M=(1-1/c)*l),(0===c||Math.abs(l-M)<b)&&(f=!0,M=l),M=(u={a:l,b:M,rf:c,sphere:f}).a,c=u.b,f=d.R_A,r=((n=M*M)-(c=c*c))/n,o=0,f?(n=(M*=1-r*(dt+r*(mt+r*pt)))*M,r=0):o=Math.sqrt(r),f={es:r,e:o,ep2:(n-c)/c},r=void 0===(M=d.nadgrids)?null:M.split(",").map(D),o=d.datum||(o=d.datumCode,n=d.datum_params,c=u.a,M=u.b,a=f.es,h=f.ep2,r=r,(e={}).datum_type=void 0===o||"none"===o?ut:ct,n&&(e.datum_params=n.map(parseFloat),0===e.datum_params[0]&&0===e.datum_params[1]&&0===e.datum_params[2]||(e.datum_type=y),3<e.datum_params.length)&&(0===e.datum_params[3]&&0===e.datum_params[4]&&0===e.datum_params[5]&&0===e.datum_params[6]||(e.datum_type=_,e.datum_params[3]*=ft,e.datum_params[4]*=ft,e.datum_params[5]*=ft,e.datum_params[6]=e.datum_params[6]/1e6+1)),r&&(e.datum_type=x,e.grids=r),e.a=c,e.b=M,e.es=a,e.ep2=h,e),Et(this,d),Et(this,i),this.a=u.a,this.b=u.b,this.rf=u.rf,this.sphere=u.sphere,this.es=f.es,this.e=f.e,this.ep2=f.ep2,this.datum=o,this.init(),s(null,this)):s("Could not get projection name from: "+t):s("Could not parse to valid json: "+t)}function Q(t,s,i){var a=t.x,h=t.y,e=t.z||0;if(h<-g&&-1.001*g<h)h=-g;else if(g<h&&h<1.001*g)h=g;else{if(h<-g)return{x:-1/0,y:-1/0,z:t.z};if(g<h)return{x:1/0,y:1/0,z:t.z}}return a>Math.PI&&(a-=2*Math.PI),t=Math.sin(h),h=Math.cos(h),{x:((i=i/Math.sqrt(1-s*(t*t)))+e)*h*Math.cos(a),y:(i+e)*h*Math.sin(a),z:(i*(1-s)+e)*t}}function W(t,s,i,a){var h,e,n,r,o,l,M,c,u,f,d,m=t.x,p=t.y,y=t.z||0,_=Math.sqrt(m*m+p*p),x=Math.sqrt(m*m+p*p+y*y);if(_/i<1e-12){if(f=0,x/i<1e-12)return d=-a,{x:t.x,y:t.y,z:t.z}}else f=Math.atan2(p,m);for(h=y/x,r=(e=_/x)*(1-s)*(n=1/Math.sqrt(1-s*(2-s)*e*e)),o=h*n,u=0;u++,c=s*(c=i/Math.sqrt(1-s*o*o))/(c+(d=_*r+y*o-c*(1-s*o*o))),c=(M=h*(n=1/Math.sqrt(1-c*(2-c)*e*e)))*r-(l=e*(1-c)*n)*o,r=l,o=M,1e-24<c*c&&u<30;);return{x:f,y:Math.atan(M/Math.abs(l)),z:d}}function X(t){return t===y||t===_}function H(t,s,i){if(null===t.grids||0===t.grids.length)return console.log("Grid shift grids not found"),-1;var a={x:-i.x,y:i.y},h={x:Number.NaN,y:Number.NaN},e=[];t:for(var n=0;n<t.grids.length;n++){var r=t.grids[n];if(e.push(r.name),r.isNull){h=a;break}if(null!==r.grid)for(var o=r.grid.subgrids,l=0,M=o.length;l<M;l++){var c=o[l],u=(Math.abs(c.del[1])+Math.abs(c.del[0]))/1e4,f=c.ll[0]-u,d=c.ll[1]-u,m=c.ll[0]+(c.lim[0]-1)*c.del[0]+u,u=c.ll[1]+(c.lim[1]-1)*c.del[1]+u;if(!(a.y<d||a.x<f||u<a.y||m<a.x||(h=function(t,s,i){var a={x:Number.NaN,y:Number.NaN};if(!isNaN(t.x)){var h={x:t.x,y:t.y},e=(h.x-=i.ll[0],h.y-=i.ll[1],h.x=S(h.x-Math.PI)+Math.PI,J(h,i));if(s){if(isNaN(e.x))return a;e.x=h.x-e.x,e.y=h.y-e.y;var n,r=9;do{if(n=J(e,i),isNaN(n.x)){console.log("Inverse grid shift iteration failed, presumably at grid edge.  Using first approximation.");break}}while(n={x:h.x-(n.x+e.x),y:h.y-(n.y+e.y)},e.x+=n.x,e.y+=n.y,r--&&1e-12<Math.abs(n.x)&&1e-12<Math.abs(n.y));if(r<0)return console.log("Inverse grid shift iterator failed to converge."),a;a.x=S(e.x+i.ll[0]),a.y=e.y+i.ll[1]}else isNaN(e.x)||(a.x=t.x+e.x,a.y=t.y+e.y)}return a}(a,s,c),isNaN(h.x))))break t}else if(r.mandatory)return console.log("Unable to find mandatory grid '"+r.name+"'"),-1}return isNaN(h.x)?(console.log("Failed to find a grid shift table for location '"+-a.x*w+" "+a.y*w+" tried: '"+e+"'"),-1):(i.x=-h.x,i.y=h.y,0)}function J(t,s){var i,a,h,e,n,r,t={x:t.x/s.del[0],y:t.y/s.del[1]},o=Math.floor(t.x),l=Math.floor(t.y),M=t.x-+o,t=t.y-+l,c={x:Number.NaN,y:Number.NaN};return o<0||o>=s.lim[0]||l<0||l>=s.lim[1]||(l=l*s.lim[0]+o,o=s.cvs[l][0],i=s.cvs[l][1],r=s.cvs[++l][0],a=s.cvs[l][1],l+=s.lim[0],h=s.cvs[l][0],e=s.cvs[l][1],n=s.cvs[--l][0],s=s.cvs[l][1],c.x=(l=(1-M)*(1-t))*o+(o=M*(1-t))*r+(r=(1-M)*t)*n+(n=M*t)*h,c.y=l*i+o*a+r*s+n*e),c}function K(t){if("function"==typeof Number.isFinite){if(Number.isFinite(t))return;throw new TypeError("coordinates must be finite numbers")}if("number"!=typeof t||t!=t||!isFinite(t))throw new TypeError("coordinates must be finite numbers")}function V(t,s,i,a){var h,e,n=void 0!==(i=Array.isArray(i)?Ut(i):{x:i.x,y:i.y,z:i.z,m:i.m}).z;if(Qt(i),t.datum&&s.datum&&(e=s,((h=t).datum.datum_type===y||h.datum.datum_type===_||h.datum.datum_type===x)&&"WGS84"!==e.datumCode||(e.datum.datum_type===y||e.datum.datum_type===_||e.datum.datum_type===x)&&"WGS84"!==h.datumCode)&&(i=V(t,e=new p("WGS84"),i,a),t=e),a&&"enu"!==t.axis&&(i=Ft(t,!1,i)),"longlat"===t.projName)i={x:i.x*v,y:i.y*v,z:i.z||0};else if(t.to_meter&&(i={x:i.x*t.to_meter,y:i.y*t.to_meter,z:i.z||0}),!(i=t.inverse(i)))return;if(t.from_greenwich&&(i.x+=t.from_greenwich),i=Dt(t.datum,s.datum,i))return s.from_greenwich&&(i={x:i.x-s.from_greenwich,y:i.y,z:i.z||0}),"longlat"===s.projName?i={x:i.x*w,y:i.y*w,z:i.z||0}:(i=s.forward(i),s.to_meter&&(i={x:i.x/s.to_meter,y:i.y/s.to_meter,z:i.z||0})),a&&"enu"!==s.axis?Ft(s,!0,i):(i&&!n&&delete i.z,i)}function Z(s,i,a,t){var h,e;return Array.isArray(a)?(e=V(s,i,a,t)||{x:NaN,y:NaN},2<a.length?void 0!==s.name&&"geocent"===s.name||void 0!==i.name&&"geocent"===i.name?("number"==typeof e.z?[e.x,e.y,e.z]:[e.x,e.y,a[2]]).concat(a.slice(3)):[e.x,e.y].concat(a.slice(2)):[e.x,e.y]):(h=V(s,i,a,t),2===(e=Object.keys(a)).length||e.forEach(function(t){if(void 0!==s.name&&"geocent"===s.name||void 0!==i.name&&"geocent"===i.name){if("x"===t||"y"===t||"z"===t)return}else if("x"===t||"y"===t)return;h[t]=a[t]}),h)}function Y(t){return t instanceof p?t:t.oProj||p(t)}function t(i,a,t){i=Y(i);var s=!1;return void 0===a?(a=i,i=ns,s=!0):void 0===a.x&&!Array.isArray(a)||(t=a,a=i,i=ns,s=!0),a=Y(a),t?Z(i,a,t):(t={forward:function(t,s){return Z(i,a,t,s)},inverse:function(t,s){return Z(a,i,t,s)}},s&&(t.oProj=a),t)}function $(t,s){return s=s||5,t={lat:t[1],lon:t[0]},e=t.lat,t=t.lon,n=st(e),r=st(t),h=Math.floor((t+180)/6)+1,180===t&&(h=60),56<=e&&e<64&&3<=t&&t<12&&(h=32),72<=e&&e<84&&(0<=t&&t<9?h=31:9<=t&&t<21?h=33:21<=t&&t<33?h=35:33<=t&&t<42&&(h=37)),t=st(6*(h-1)-180+3),o=6378137/Math.sqrt(1-.00669438*Math.sin(n)*Math.sin(n)),i=Math.tan(n)*Math.tan(n),a=.006739496752268451*Math.cos(n)*Math.cos(n),t=.9996*o*((r=Math.cos(n)*(r-t))+(1-i+a)*r*r*r/6+(5-18*i+i*i+72*a-.39089081163157013)*r*r*r*r*r/120)+5e5,o=.9996*(6378137*(.9983242984503243*n-.002514607064228144*Math.sin(2*n)+2639046602129982e-21*Math.sin(4*n)-3.418046101696858e-9*Math.sin(6*n))+o*Math.tan(n)*(r*r/2+(5-i+9*a+4*a*a)*r*r*r*r/24+(61-58*i+i*i+600*a-2.2240339282485886)*r*r*r*r*r*r/720)),e<0&&(o+=1e7),n={northing:Math.round(o),easting:Math.round(t),zoneNumber:h,zoneLetter:function(t){var s="Z";return t<=84&&72<=t?s="X":t<72&&64<=t?s="W":t<64&&56<=t?s="V":t<56&&48<=t?s="U":t<48&&40<=t?s="T":t<40&&32<=t?s="S":t<32&&24<=t?s="R":t<24&&16<=t?s="Q":t<16&&8<=t?s="P":t<8&&0<=t?s="N":t<0&&-8<=t?s="M":t<-8&&-16<=t?s="L":t<-16&&-24<=t?s="K":t<-24&&-32<=t?s="J":t<-32&&-40<=t?s="H":t<-40&&-48<=t?s="G":t<-48&&-56<=t?s="F":t<-56&&-64<=t?s="E":t<-64&&-72<=t?s="D":t<-72&&-80<=t&&(s="C"),s}(e)},i=s,a="00000"+n.easting,r="00000"+n.northing,n.zoneNumber+n.zoneLetter+function(t,s,i){i=ht(i);return function(t,s,i){var i=i-1,a=os.charCodeAt(i),i=ls.charCodeAt(i),t=a+t-1,s=i+s,h=!1;return us<t&&(t=t-us+Ms-1,h=!0),(t===q||a<q&&q<t||(q<t||a<q)&&h)&&t++,(t===A||a<A&&A<t||(A<t||a<A)&&h)&&++t===q&&t++,us<t&&(t=t-us+Ms-1),h=cs<s&&(s=s-cs+Ms-1,!0),(s===q||i<q&&q<s||(q<s||i<q)&&h)&&s++,(s===A||i<A&&A<s||(A<s||i<A)&&h)&&++s===q&&s++,cs<s&&(s=s-cs+Ms-1),String.fromCharCode(t)+String.fromCharCode(s)}(Math.floor(t/1e5),Math.floor(s/1e5)%20,i)}(n.easting,n.northing,n.zoneNumber)+a.substr(a.length-5,i)+r.substr(r.length-5,i);var i,a,h,e,n,r,o}function tt(t){t=at(et(t.toUpperCase()));return t.lat&&t.lon?[t.lon,t.lat]:[(t.left+t.right)/2,(t.top+t.bottom)/2]}function st(t){return t*(Math.PI/180)}function it(t){return t/Math.PI*180}function at(t){var s,i,a,h=t.northing,e=t.easting,n=t.zoneLetter,r=t.zoneNumber;return r<0||60<r?null:(e=e-5e5,h=h,n<"N"&&(h-=1e7),n=6*(r-1)-180+3,h=(r=h/.9996/6367449.145945056)+(3*(h=(1-Math.sqrt(.99330562))/(1+Math.sqrt(.99330562)))/2-27*h*h*h/32)*Math.sin(2*r)+(21*h*h/16-55*h*h*h*h/32)*Math.sin(4*r)+151*h*h*h/96*Math.sin(6*r),r=6378137/Math.sqrt(1-.00669438*Math.sin(h)*Math.sin(h)),s=Math.tan(h)*Math.tan(h),i=.006739496752268451*Math.cos(h)*Math.cos(h),a=6335439.32722994/Math.pow(1-.00669438*Math.sin(h)*Math.sin(h),1.5),e=e/(.9996*r),r=it(r=h-r*Math.tan(h)/a*(e*e/2-(5+3*s+10*i-4*i*i-.06065547077041606)*e*e*e*e/24+(61+90*s+298*i+45*s*s-1.6983531815716497-3*i*i)*e*e*e*e*e*e/720)),a=n+it(a=(e-(1+2*s+i)*e*e*e/6+(5-2*i+28*s-3*i*i+.05391597401814761+24*s*s)*e*e*e*e*e/120)/Math.cos(h)),t.accuracy?{top:(n=at({northing:t.northing+t.accuracy,easting:t.easting+t.accuracy,zoneLetter:t.zoneLetter,zoneNumber:t.zoneNumber})).lat,right:n.lon,bottom:r,left:a}:{lat:r,lon:a})}function ht(t){t%=rs;return t=0===t?rs:t}function et(t){if(t&&0===t.length)throw"MGRSPoint coverting from nothing";for(var s,i=t.length,a=null,h="",e=0;!/[A-Z]/.test(s=t.charAt(e));){if(2<=e)throw"MGRSPoint bad conversion from: "+t;h+=s,e++}var n=parseInt(h,10);if(0===e||i<e+3)throw"MGRSPoint bad conversion from: "+t;var r=t.charAt(e++);if(r<="A"||"B"===r||"Y"===r||"Z"<=r||"I"===r||"O"===r)throw"MGRSPoint zone letter "+r+" not handled: "+t;for(var a=t.substring(e,e+=2),o=ht(n),l=function(t,s){for(var i=os.charCodeAt(s-1),a=1e5,h=!1;i!==t.charCodeAt(0);){if(++i===q&&i++,i===A&&i++,us<i){if(h)throw"Bad character: "+t;i=Ms,h=!0}a+=1e5}return a}(a.charAt(0),o),M=function(t,s){if("V"<t)throw"MGRSPoint given invalid Northing "+t;for(var i=ls.charCodeAt(s-1),a=0,h=!1;i!==t.charCodeAt(0);){if(++i===q&&i++,i===A&&i++,cs<i){if(h)throw"Bad character: "+t;i=Ms,h=!0}a+=1e5}return a}(a.charAt(1),o);M<function(t){var s;switch(t){case"C":s=11e5;break;case"D":s=2e6;break;case"E":s=28e5;break;case"F":s=37e5;break;case"G":s=46e5;break;case"H":s=55e5;break;case"J":s=64e5;break;case"K":s=73e5;break;case"L":s=82e5;break;case"M":s=91e5;break;case"N":s=0;break;case"P":s=8e5;break;case"Q":s=17e5;break;case"R":s=26e5;break;case"S":s=35e5;break;case"T":s=44e5;break;case"U":s=53e5;break;case"V":s=62e5;break;case"W":s=7e6;break;case"X":s=79e5;break;default:s=-1}if(0<=s)return s;throw"Invalid zone letter: "+t}(r);)M+=2e6;a=i-e;if(a%2!=0)throw"MGRSPoint has to have an even number \nof digits after the zone letter and two 100km letters - front \nhalf for easting meters, second half for \nnorthing meters"+t;var c,u,o=a/2,i=0,a=0;return 0<o&&(c=1e5/Math.pow(10,o),u=t.substring(e,e+o),i=parseFloat(u)*c,u=t.substring(e+o),a=parseFloat(u)*c),{easting:i+l,northing:a+M,zoneLetter:r,zoneNumber:n,accuracy:c}}function n(t,s,i){if(!(this instanceof n))return new n(t,s,i);var a;Array.isArray(t)?(this.x=t[0],this.y=t[1],this.z=t[2]||0):"object"==typeof t?(this.x=t.x,this.y=t.y,this.z=t.z||0):"string"==typeof t&&void 0===s?(a=t.split(","),this.x=parseFloat(a[0],10),this.y=parseFloat(a[1],10),this.z=parseFloat(a[2],10)||0):(this.x=t,this.y=s,this.z=i||0),console.warn("proj4.Point will be removed in version 3, use proj4.toPoint")}function nt(t,s,i,a){var h;return t<b?(a.value=O,h=0):(h=Math.atan2(s,i),Math.abs(h)<=N?a.value=O:N<h&&h<=g+N?(a.value=j,h-=g):g+N<h||h<=-(g+N)?(a.value=G,h=0<=h?h-c:h+c):(a.value=Xs,h+=g)),h}function l(t,s){t+=s;return t<-c?t+=yt:c<t&&(t-=yt),t}function rt(t){var s=S(t.x-(this.long0||0)),i=t.y,a=this.am1+this.m1-f(i,h=Math.sin(i),i=Math.cos(i),this.en),h=i*s/(a*Math.sqrt(1-this.es*h*h));return t.x=a*Math.sin(h),t.y=this.am1-a*Math.cos(h),t.x=this.a*t.x+(this.x0||0),t.y=this.a*t.y+(this.y0||0),t}function ot(t){var s,i,a,h;if(t.x=(t.x-(this.x0||0))/this.a,t.y=(t.y-(this.y0||0))/this.a,i=d(t.x,t.y=this.am1-t.y),h=Xt(this.am1+this.m1-i,this.es,this.en),(s=Math.abs(h))<g)s=Math.sin(h),a=i*Math.atan2(t.x,t.y)*Math.sqrt(1-this.es*s*s)/Math.cos(h);else{if(!(Math.abs(s-g)<=fi))throw new Error;a=0}return t.x=S(a+(this.long0||0)),t.y=E(h),t}function lt(t){var s=S(t.x-(this.long0||0)),i=t.y,a=this.cphi1+this.phi1-i;return Math.abs(a)>fi?(t.x=a*Math.sin(s=s*Math.cos(i)/a),t.y=this.cphi1-a*Math.cos(s)):t.x=t.y=0,t.x=this.a*t.x+(this.x0||0),t.y=this.a*t.y+(this.y0||0),t}function Mt(t){t.x=(t.x-(this.x0||0))/this.a,t.y=(t.y-(this.y0||0))/this.a;var s=d(t.x,t.y=this.cphi1-t.y),i=this.cphi1+this.phi1-s;if(Math.abs(i)>g)throw new Error;return s=Math.abs(Math.abs(i)-g)<=fi?0:s*Math.atan2(t.x,t.y)/Math.cos(i),t.x=S(s+(this.long0||0)),t.y=E(i),t}var y=1,_=2,x=3,ct=4,ut=5,ft=484813681109536e-20,g=Math.PI/2,dt=.16666666666666666,mt=.04722222222222222,pt=.022156084656084655,b=1e-10,v=.017453292519943295,w=57.29577951308232,N=Math.PI/4,yt=2*Math.PI,c=3.14159265359,_t={greenwich:0,lisbon:-9.131906111111,paris:2.337229166667,bogota:-74.080916666667,madrid:-3.687938888889,rome:12.452333333333,bern:7.439583333333,jakarta:106.807719444444,ferro:-17.666666666667,brussels:4.367975,stockholm:18.058277777778,athens:23.7163375,oslo:10.722916666667},xt={mm:{to_meter:.001},cm:{to_meter:.01},ft:{to_meter:.3048},"us-ft":{to_meter:1200/3937},fath:{to_meter:1.8288},kmi:{to_meter:1852},"us-ch":{to_meter:20.1168402336805},"us-mi":{to_meter:1609.34721869444},km:{to_meter:1e3},"ind-ft":{to_meter:.30479841},"ind-yd":{to_meter:.91439523},mi:{to_meter:1609.344},yd:{to_meter:.9144},ch:{to_meter:20.1168},link:{to_meter:.201168},dm:{to_meter:.01},in:{to_meter:.0254},"ind-ch":{to_meter:20.11669506},"us-in":{to_meter:.025400050800101},"us-yd":{to_meter:.914401828803658}},gt=/[\s_\-\/\(\)]/g,bt=function(t){var s,i,a,h={},e=t.split("+").map(function(t){return t.trim()}).filter(function(t){return t}).reduce(function(t,s){s=s.split("=");return s.push(!0),t[s[0].toLowerCase()]=s[1],t},{}),n={proj:"projName",datum:"datumCode",rf:function(t){h.rf=parseFloat(t)},lat_0:function(t){h.lat0=t*v},lat_1:function(t){h.lat1=t*v},lat_2:function(t){h.lat2=t*v},lat_ts:function(t){h.lat_ts=t*v},lon_0:function(t){h.long0=t*v},lon_1:function(t){h.long1=t*v},lon_2:function(t){h.long2=t*v},alpha:function(t){h.alpha=parseFloat(t)*v},gamma:function(t){h.rectified_grid_angle=parseFloat(t)},lonc:function(t){h.longc=t*v},x_0:function(t){h.x0=parseFloat(t)},y_0:function(t){h.y0=parseFloat(t)},k_0:function(t){h.k0=parseFloat(t)},k:function(t){h.k0=parseFloat(t)},a:function(t){h.a=parseFloat(t)},b:function(t){h.b=parseFloat(t)},r:function(t){h.a=h.b=parseFloat(t)},r_a:function(){h.R_A=!0},zone:function(t){h.zone=parseInt(t,10)},south:function(){h.utmSouth=!0},towgs84:function(t){h.datum_params=t.split(",").map(function(t){return parseFloat(t)})},to_meter:function(t){h.to_meter=parseFloat(t)},units:function(t){t=m(xt,h.units=t);t&&(h.to_meter=t.to_meter)},from_greenwich:function(t){h.from_greenwich=t*v},pm:function(t){var s=m(_t,t);h.from_greenwich=(s||parseFloat(t))*v},nadgrids:function(t){"@null"===t?h.datumCode="none":h.nadgrids=t},axis:function(t){3===t.length&&-1!=="ewnsud".indexOf(t.substr(0,1))&&-1!=="ewnsud".indexOf(t.substr(1,1))&&-1!=="ewnsud".indexOf(t.substr(2,1))&&(h.axis=t)},approx:function(){h.approx=!0}};for(s in e)i=e[s],s in n?"function"==typeof(a=n[s])?a(i):h[a]=i:h[s]=i;return"string"==typeof h.datumCode&&"WGS84"!==h.datumCode&&(h.datumCode=h.datumCode.toLowerCase()),h},vt=/\s/,wt=/[A-Za-z]/,Nt=/[A-Za-z84_]/,Ct=/[,\]]/,St=/[\d\.E\-\+]/,Pt=(a.prototype.readCharicter=function(){var t=this.text[this.place++];if(4!==this.state)for(;vt.test(t);){if(this.place>=this.text.length)return;t=this.text[this.place++]}switch(this.state){case 1:return this.neutral(t);case 2:return this.keyword(t);case 4:return this.quoted(t);case 5:return this.afterquote(t);case 3:return this.number(t);case-1:return}},a.prototype.afterquote=function(t){if('"'===t)this.word+='"',this.state=4;else{if(!Ct.test(t))throw new Error("havn't handled \""+t+'" in afterquote yet, index '+this.place);this.word=this.word.trim(),this.afterItem(t)}},a.prototype.afterItem=function(t){return","===t?(null!==this.word&&this.currentObject.push(this.word),this.word=null,void(this.state=1)):"]"===t?(this.level--,null!==this.word&&(this.currentObject.push(this.word),this.word=null),this.state=1,this.currentObject=this.stack.pop(),void(this.currentObject||(this.state=-1))):void 0},a.prototype.number=function(t){if(!St.test(t)){if(Ct.test(t))return this.word=parseFloat(this.word),void this.afterItem(t);throw new Error("havn't handled \""+t+'" in number yet, index '+this.place)}this.word+=t},a.prototype.quoted=function(t){'"'!==t?this.word+=t:this.state=5},a.prototype.keyword=function(t){var s;if(Nt.test(t))this.word+=t;else if("["===t)(s=[]).push(this.word),this.level++,null===this.root?this.root=s:this.currentObject.push(s),this.stack.push(this.currentObject),this.currentObject=s,this.state=1;else{if(!Ct.test(t))throw new Error("havn't handled \""+t+'" in keyword yet, index '+this.place);this.afterItem(t)}},a.prototype.neutral=function(t){if(wt.test(t))this.word=t,this.state=2;else if('"'===t)this.word="",this.state=4;else if(St.test(t))this.word=t,this.state=3;else{if(!Ct.test(t))throw new Error("havn't handled \""+t+'" in neutral yet, index '+this.place);this.afterItem(t)}},a.prototype.output=function(){for(;this.place<this.text.length;)this.readCharicter();if(-1===this.state)return this.root;throw new Error('unable to parse string "'+this.text+'". State is '+this.state)},function(t){var t=new a(t).output(),s=t.shift(),i=t.shift(),i=(t.unshift(["name",i]),t.unshift(["type",s]),{});return h(t,i),z(i),i}),s=e;s("EPSG:4326","+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees"),s("EPSG:4269","+title=NAD83 (long/lat) +proj=longlat +a=6378137.0 +b=6356752.31414036 +ellps=GRS80 +datum=NAD83 +units=degrees"),s("EPSG:3857","+title=WGS 84 / Pseudo-Mercator +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs");for(var i=0;i<=60;++i)s("EPSG:"+(32600+i),"+proj=utm +zone="+i+" +datum=WGS84 +units=m"),s("EPSG:"+(32700+i),"+proj=utm +zone="+i+" +south +datum=WGS84 +units=m");s.WGS84=s["EPSG:4326"],s["EPSG:3785"]=s["EPSG:3857"],s.GOOGLE=s["EPSG:3857"],s["EPSG:900913"]=s["EPSG:3857"],s["EPSG:102113"]=s["EPSG:3857"];function Et(t,s){var i,a;if(t=t||{},s)for(a in s)void 0!==(i=s[a])&&(t[a]=i)}function o(t,s,i){return t*=s,i/Math.sqrt(1-t*t)}function kt(t){return t<0?-1:1}function C(t,s,i){return i*=t,i=Math.pow((1-i)/(1+i),.5*t),Math.tan(.5*(g-s))/i}function It(t,s){for(var i,a=.5*t,h=g-2*Math.atan(s),e=0;e<=15;e++)if(i=t*Math.sin(h),h+=i=g-2*Math.atan(s*Math.pow((1-i)/(1+i),a))-h,Math.abs(i)<=1e-10)return h;return-9999}var qt,At=["PROJECTEDCRS","PROJCRS","GEOGCS","GEOCCS","PROJCS","LOCAL_CS","GEODCRS","GEODETICCRS","GEODETICDATUM","ENGCRS","ENGINEERINGCRS"],Ot=["3857","900913","3785","102113"],S=function(t){return Math.abs(t)<=c?t:t-kt(t)*yt},jt=[{init:function(){var t=this.b/this.a;this.es=1-t*t,"x0"in this||(this.x0=0),"y0"in this||(this.y0=0),this.e=Math.sqrt(this.es),this.lat_ts?this.sphere?this.k0=Math.cos(this.lat_ts):this.k0=o(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)):this.k0||(this.k?this.k0=this.k:this.k0=1)},forward:function(t){var s,i,a=t.x,h=t.y;return 90<h*w&&h*w<-90&&180<a*w&&a*w<-180||Math.abs(Math.abs(h)-g)<=b?null:(i=this.sphere?(s=this.x0+this.a*this.k0*S(a-this.long0),this.y0+this.a*this.k0*Math.log(Math.tan(N+.5*h))):(i=Math.sin(h),h=C(this.e,h,i),s=this.x0+this.a*this.k0*S(a-this.long0),this.y0-this.a*this.k0*Math.log(h)),t.x=s,t.y=i,t)},inverse:function(t){var s,i=t.x-this.x0,a=t.y-this.y0;if(this.sphere)s=g-2*Math.atan(Math.exp(-a/(this.a*this.k0)));else{var a=Math.exp(-a/(this.a*this.k0));if(-9999===(s=It(this.e,a)))return null}return a=S(this.long0+i/(this.a*this.k0)),t.x=a,t.y=s,t},names:["Mercator","Popular Visualisation Pseudo Mercator","Mercator_1SP","Mercator_Auxiliary_Sphere","merc"]},{init:function(){},forward:B,inverse:B,names:["longlat","identity"]}],Gt={},Lt=[],u={start:function(){jt.forEach(T)},add:T,get:function(t){return!!t&&(t=t.toLowerCase(),void 0!==Gt[t]&&Lt[Gt[t]]?Lt[Gt[t]]:void 0)}},zt={MERIT:{a:6378137,rf:298.257,ellipseName:"MERIT 1983"},SGS85:{a:6378136,rf:298.257,ellipseName:"Soviet Geodetic System 85"},GRS80:{a:6378137,rf:298.*********,ellipseName:"GRS 1980(IUGG, 1980)"},IAU76:{a:6378140,rf:298.257,ellipseName:"IAU 1976"},airy:{a:6377563.396,b:6356256.91,ellipseName:"Airy 1830"},APL4:{a:6378137,rf:298.25,ellipseName:"Appl. Physics. 1965"},NWL9D:{a:6378145,rf:298.25,ellipseName:"Naval Weapons Lab., 1965"},mod_airy:{a:6377340.189,b:6356034.446,ellipseName:"Modified Airy"},andrae:{a:6377104.43,rf:300,ellipseName:"Andrae 1876 (Den., Iclnd.)"},aust_SA:{a:6378160,rf:298.25,ellipseName:"Australian Natl & S. Amer. 1969"},GRS67:{a:6378160,rf:298.*********,ellipseName:"GRS 67(IUGG 1967)"},bessel:{a:6377397.155,rf:299.1528128,ellipseName:"Bessel 1841"},bess_nam:{a:6377483.865,rf:299.1528128,ellipseName:"Bessel 1841 (Namibia)"},clrk66:{a:6378206.4,b:6356583.8,ellipseName:"Clarke 1866"},clrk80:{a:6378249.145,rf:293.4663,ellipseName:"Clarke 1880 mod."},clrk80ign:{a:6378249.2,b:6356515,rf:293.4660213,ellipseName:"Clarke 1880 (IGN)"},clrk58:{a:6378293.*********,rf:294.2606763692654,ellipseName:"Clarke 1858"},CPM:{a:6375738.7,rf:334.29,ellipseName:"Comm. des Poids et Mesures 1799"},delmbr:{a:6376428,rf:311.5,ellipseName:"Delambre 1810 (Belgium)"},engelis:{a:6378136.05,rf:298.2566,ellipseName:"Engelis 1985"},evrst30:{a:6377276.345,rf:300.8017,ellipseName:"Everest 1830"},evrst48:{a:6377304.063,rf:300.8017,ellipseName:"Everest 1948"},evrst56:{a:6377301.243,rf:300.8017,ellipseName:"Everest 1956"},evrst69:{a:6377295.664,rf:300.8017,ellipseName:"Everest 1969"},evrstSS:{a:6377298.556,rf:300.8017,ellipseName:"Everest (Sabah & Sarawak)"},fschr60:{a:6378166,rf:298.3,ellipseName:"Fischer (Mercury Datum) 1960"},fschr60m:{a:6378155,rf:298.3,ellipseName:"Fischer 1960"},fschr68:{a:6378150,rf:298.3,ellipseName:"Fischer 1968"},helmert:{a:6378200,rf:298.3,ellipseName:"Helmert 1906"},hough:{a:6378270,rf:297,ellipseName:"Hough"},intl:{a:6378388,rf:297,ellipseName:"International 1909 (Hayford)"},kaula:{a:6378163,rf:298.24,ellipseName:"Kaula 1961"},lerch:{a:6378139,rf:298.257,ellipseName:"Lerch 1979"},mprts:{a:6397300,rf:191,ellipseName:"Maupertius 1738"},new_intl:{a:6378157.5,b:6356772.2,ellipseName:"New International 1967"},plessis:{a:6376523,rf:6355863,ellipseName:"Plessis 1817 (France)"},krass:{a:6378245,rf:298.3,ellipseName:"Krassovsky, 1942"},SEasia:{a:6378155,b:6356773.3205,ellipseName:"Southeast Asia"},walbeck:{a:6376896,b:6355834.8467,ellipseName:"Walbeck"},WGS60:{a:6378165,rf:298.3,ellipseName:"WGS 60"},WGS66:{a:6378145,rf:298.25,ellipseName:"WGS 66"},WGS7:{a:6378135,rf:298.26,ellipseName:"WGS 72"}},Rt=zt.WGS84={a:6378137,rf:298.257223563,ellipseName:"WGS 84"},Bt=(zt.sphere={a:6370997,b:6370997,ellipseName:"Normal Sphere (r=6370997)"},{wgs84:{towgs84:"0,0,0",ellipse:"WGS84",datumName:"WGS84"},ch1903:{towgs84:"674.374,15.056,405.346",ellipse:"bessel",datumName:"swiss"},ggrs87:{towgs84:"-199.87,74.79,246.62",ellipse:"GRS80",datumName:"Greek_Geodetic_Reference_System_1987"},nad83:{towgs84:"0,0,0",ellipse:"GRS80",datumName:"North_American_Datum_1983"},nad27:{nadgrids:"@conus,@alaska,@ntv2_0.gsb,@ntv1_can.dat",ellipse:"clrk66",datumName:"North_American_Datum_1927"},potsdam:{towgs84:"598.1,73.7,418.2,0.202,0.045,-2.455,6.7",ellipse:"bessel",datumName:"Potsdam Rauenberg 1950 DHDN"},carthage:{towgs84:"-263.0,6.0,431.0",ellipse:"clark80",datumName:"Carthage 1934 Tunisia"},hermannskogel:{towgs84:"577.326,90.129,463.919,5.137,1.474,5.297,2.4232",ellipse:"bessel",datumName:"Hermannskogel"},militargeographische_institut:{towgs84:"577.326,90.129,463.919,5.137,1.474,5.297,2.4232",ellipse:"bessel",datumName:"Militar-Geographische Institut"},osni52:{towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"airy",datumName:"Irish National"},ire65:{towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"mod_airy",datumName:"Ireland 1965"},rassadiran:{towgs84:"-133.63,-157.5,-158.62",ellipse:"intl",datumName:"Rassadiran"},nzgd49:{towgs84:"59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993",ellipse:"intl",datumName:"New Zealand Geodetic Datum 1949"},osgb36:{towgs84:"446.448,-125.157,542.060,0.1502,0.2470,0.8421,-20.4894",ellipse:"airy",datumName:"Ordnance Survey of Great Britain 1936"},s_jtsk:{towgs84:"589,76,480",ellipse:"bessel",datumName:"S-JTSK (Ferro)"},beduaram:{towgs84:"-106,-87,188",ellipse:"clrk80",datumName:"Beduaram"},gunung_segara:{towgs84:"-403,684,41",ellipse:"bessel",datumName:"Gunung Segara Jakarta"},rnb72:{towgs84:"106.869,-52.2978,103.724,-0.33657,0.456955,-1.84218,1",ellipse:"intl",datumName:"Reseau National Belge 1972"}});for(qt in Bt){var Tt=Bt[qt];Bt[Tt.datumName]=Tt}function Dt(t,s,i){if(h=s,(a=t).datum_type===h.datum_type&&!(a.a!==h.a||5e-11<Math.abs(a.es-h.es))&&(a.datum_type===y?a.datum_params[0]===h.datum_params[0]&&a.datum_params[1]===h.datum_params[1]&&a.datum_params[2]===h.datum_params[2]:a.datum_type!==_||a.datum_params[0]===h.datum_params[0]&&a.datum_params[1]===h.datum_params[1]&&a.datum_params[2]===h.datum_params[2]&&a.datum_params[3]===h.datum_params[3]&&a.datum_params[4]===h.datum_params[4]&&a.datum_params[5]===h.datum_params[5]&&a.datum_params[6]===h.datum_params[6]))return i;if(t.datum_type===ut||s.datum_type===ut)return i;var a=t.a,h=t.es;if(t.datum_type===x){if(0!==H(t,!1,i))return;a=6378137,h=.0066943799901413165}var e,n,r,o,l,M=s.a,c=s.b,u=s.es;return s.datum_type===x&&(M=6378137,c=6356752.314,u=.0066943799901413165),h===u&&a===M&&!X(t.datum_type)&&!X(s.datum_type)||(i=Q(i,h,a),X(t.datum_type)&&(h=i,a=t.datum_type,t=t.datum_params,i=a===y?{x:h.x+t[0],y:h.y+t[1],z:h.z+t[2]}:a===_?(a=t[0],e=t[1],n=t[2],r=t[3],o=t[4],l=t[5],{x:(t=t[6])*(h.x-l*h.y+o*h.z)+a,y:t*(l*h.x+h.y-r*h.z)+e,z:t*(-o*h.x+r*h.y+h.z)+n}):void 0),X(s.datum_type)&&(a=i,l=s.datum_type,e=s.datum_params,i=l===y?{x:a.x-e[0],y:a.y-e[1],z:a.z-e[2]}:l===_?(l=e[0],t=e[1],o=e[2],r=e[3],h=e[4],n=e[5],e=e[6],{x:(l=(a.x-l)/e)+n*(t=(a.y-t)/e)-h*(a=(a.z-o)/e),y:-n*l+t+r*a,z:h*l-r*t+a}):void 0),i=W(i,u,M,c),s.datum_type!==x)||0===H(s,!0,i)?i:void 0}function Ft(t,s,i){for(var a,h,e=i.x,n=i.y,r=i.z||0,o={},l=0;l<3;l++)if(!s||2!==l||void 0!==i.z)switch(h=0===l?(a=e,-1!=="ew".indexOf(t.axis[l])?"x":"y"):1===l?(a=n,-1!=="ns".indexOf(t.axis[l])?"y":"x"):(a=r,"z"),t.axis[l]){case"e":o[h]=a;break;case"w":o[h]=-a;break;case"n":o[h]=a;break;case"s":o[h]=-a;break;case"u":void 0!==i[h]&&(o.z=a);break;case"d":void 0!==i[h]&&(o.z=-a);break;default:return null}return o}function Ut(t){var s={x:t[0],y:t[1]};return 2<t.length&&(s.z=t[2]),3<t.length&&(s.m=t[3]),s}function Qt(t){K(t.x),K(t.y)}function Wt(t){var s=[],i=(s[0]=1-t*(.25+t*(.046875+t*(.01953125+t*fs))),s[1]=t*(.75-t*(.046875+t*(.01953125+t*fs))),t*t);return s[2]=i*(.46875-t*(.013020833333333334+.007120768229166667*t)),s[3]=(i*=t)*(.3645833333333333-.005696614583333333*t),s[4]=i*t*.3076171875,s}function f(t,s,i,a){return a[0]*t-(i*=s)*(a[1]+(s*=s)*(a[2]+s*(a[3]+s*a[4])))}function Xt(t,s,i){for(var a=1/(1-s),h=t,e=20;e;--e){var n=Math.sin(h),r=1-s*n*n;if(h-=r=(f(h,n,Math.cos(h),i)-t)*(r*Math.sqrt(r))*a,Math.abs(r)<b)return h}return h}function Ht(t){return((t=Math.exp(t))-1/t)/2}function d(t,s){t=Math.abs(t),s=Math.abs(s);var i=Math.max(t,s),t=Math.min(t,s)/(i||1);return i*Math.sqrt(1+Math.pow(t,2))}function Jt(t){var s,i,a,h=Math.abs(t);return s=h*(1+h/(d(1,h)+1)),h=0==(a=(i=1+s)-1)?s:s*Math.log(i)/a,t<0?-h:h}function Kt(t,s){for(var i,a=2*Math.cos(2*s),h=t.length-1,e=t[h],n=0;0<=--h;)i=a*e-n+t[h],n=e,e=i;return s+i*Math.sin(2*s)}function Vt(t,s,i){for(var a,h,e=Math.sin(s),s=Math.cos(s),n=Ht(i),i=function(t){t=Math.exp(t);return(t+1/t)/2}(i),r=2*s*i,o=-2*e*n,l=t.length-1,M=t[l],c=0,u=0,f=0;0<=--l;)a=u,h=c,M=r*(u=M)-a-o*(c=f)+t[l],f=o*u-h+r*c;return[(r=e*i)*M-(o=s*n)*f,r*f+o*M]}function Zt(t,s){return Math.pow((1-t)/(1+t),s)}function P(t,s,i,a,h){return t*h-s*Math.sin(2*h)+i*Math.sin(4*h)-a*Math.sin(6*h)}function Yt(t){return 1-.25*t*(1+t/16*(3+1.25*t))}function $t(t){return.375*t*(1+.25*t*(1+.46875*t))}function ts(t){return.05859375*t*t*(1+.75*t)}function ss(t){return t*t*t*(35/3072)}function is(t,s,i){return s*=i,t/Math.sqrt(1-s*s)}function E(t){return Math.abs(t)<g?t:t-kt(t)*Math.PI}function as(t,s,i,a,h){for(var e,n=t/s,r=0;r<15;r++)if(n+=e=(t-(s*n-i*Math.sin(2*n)+a*Math.sin(4*n)-h*Math.sin(6*n)))/(s-2*i*Math.cos(2*n)+4*a*Math.cos(4*n)-6*h*Math.cos(6*n)),Math.abs(e)<=1e-10)return n;return NaN}function k(t,s){var i;return 1e-7<t?(1-t*t)*(s/(1-(i=t*s)*i)-.5/t*Math.log((1-i)/(1+i))):2*s}function I(t){return 1<Math.abs(t)&&(t=1<t?1:-1),Math.asin(t)}function hs(t,s){return t[0]+s*(t[1]+s*(t[2]+s*t[3]))}var es={},ns=((p.projections=u).start(),p("WGS84")),rs=6,os="AJSAJS",ls="AFAFAF",Ms=65,q=73,A=79,cs=86,us=90,u={forward:$,inverse:function(t){t=at(et(t.toUpperCase()));return t.lat&&t.lon?[t.lon,t.lat,t.lon,t.lat]:[t.left,t.bottom,t.right,t.top]},toPoint:tt},fs=(n.fromMGRS=function(t){return new n(tt(t))},n.prototype.toMGRS=function(t){return $([this.x,this.y],t)},.01068115234375),ds={init:function(){this.x0=void 0!==this.x0?this.x0:0,this.y0=void 0!==this.y0?this.y0:0,this.long0=void 0!==this.long0?this.long0:0,this.lat0=void 0!==this.lat0?this.lat0:0,this.es&&(this.en=Wt(this.es),this.ml0=f(this.lat0,Math.sin(this.lat0),Math.cos(this.lat0),this.en))},forward:function(t){var s=t.x,i=t.y,s=S(s-this.long0),a=Math.sin(i),h=Math.cos(i);if(this.es)var e=h*s,n=Math.pow(e,2),r=this.ep2*Math.pow(h,2),o=Math.pow(r,2),l=Math.abs(h)>b?Math.tan(i):0,l=Math.pow(l,2),M=Math.pow(l,2),c=1-this.es*Math.pow(a,2),c=(e/=Math.sqrt(c),f(i,a,h,this.en)),u=this.a*(this.k0*e*(1+n/6*(1-l+r+n/20*(5-18*l+M+14*r-58*l*r+n/42*(61+179*M-M*l-479*l)))))+this.x0,c=this.a*(this.k0*(c-this.ml0+a*s*e/2*(1+n/12*(5-l+9*r+4*o+n/30*(61+M-58*l+270*r-330*l*r+n/56*(1385+543*M-M*l-3111*l))))))+this.y0;else{a=h*Math.sin(s);if(Math.abs(Math.abs(a)-1)<b)return 93;if(u=.5*this.a*this.k0*Math.log((1+a)/(1-a))+this.x0,c=h*Math.cos(s)/Math.sqrt(1-Math.pow(a,2)),1<=(a=Math.abs(c))){if(b<a-1)return 93;c=0}else c=Math.acos(c);c=this.a*this.k0*((c=i<0?-c:c)-this.lat0)+this.y0}return t.x=u,t.y=c,t},inverse:function(t){var s,i,a,h,e,n,r,o,l,M=(t.x-this.x0)*(1/this.a),c=(t.y-this.y0)*(1/this.a);return e=this.es?(a=this.ml0+c/this.k0,l=Xt(a,this.es,this.en),Math.abs(l)<g?(r=Math.sin(l),s=Math.cos(l),h=Math.abs(s)>b?Math.tan(l):0,o=this.ep2*Math.pow(s,2),n=Math.pow(o,2),i=Math.pow(h,2),e=Math.pow(i,2),a=1-this.es*Math.pow(r,2),r=M*Math.sqrt(a)/this.k0,h=l-(a*=h)*(l=Math.pow(r,2))/(1-this.es)*.5*(1-l/12*(5+3*i-9*o*i+o-4*n-l/30*(61+90*i-252*o*i+45*e+46*o-l/56*(1385+3633*i+4095*e+1574*e*i)))),S(this.long0+r*(1-l/6*(1+2*i+o-l/20*(5+28*i+24*e+8*o*i+6*o-l/42*(61+662*i+1320*e+720*e*i))))/s)):(h=g*kt(c),0)):(r=.5*((n=Math.exp(M/this.k0))-1/n),o=this.lat0+c/this.k0,l=Math.cos(o),a=Math.sqrt((1-Math.pow(l,2))/(1+Math.pow(r,2))),h=Math.asin(a),c<0&&(h=-h),0==r&&0===l?0:S(Math.atan2(r,l)+this.long0)),t.x=e,t.y=h,t},names:["Fast_Transverse_Mercator","Fast Transverse Mercator"]},ms={init:function(){if(!this.approx&&(isNaN(this.es)||this.es<=0))throw new Error('Incorrect elliptical usage. Try using the +approx option in the proj string, or PROJECTION["Fast_Transverse_Mercator"] in the WKT.');this.approx&&(ds.init.apply(this),this.forward=ds.forward,this.inverse=ds.inverse),this.x0=void 0!==this.x0?this.x0:0,this.y0=void 0!==this.y0?this.y0:0,this.long0=void 0!==this.long0?this.long0:0,this.lat0=void 0!==this.lat0?this.lat0:0,this.cgb=[],this.cbg=[],this.utg=[],this.gtu=[];var t=this.es/(1+Math.sqrt(1-this.es)),t=t/(2-t),s=t,t=(this.cgb[0]=t*(2+t*(-2/3+t*(t*(116/45+t*(26/45+-2854/675*t))-2))),this.cbg[0]=t*(t*(2/3+t*(4/3+t*(-82/45+t*(32/45+4642/4725*t))))-2),this.cgb[1]=(s*=t)*(7/3+t*(t*(-227/45+t*(2704/315+2323/945*t))-1.6)),this.cbg[1]=s*(5/3+t*(-16/15+t*(-13/9+t*(904/315+-1522/945*t)))),this.cgb[2]=(s*=t)*(56/15+t*(-136/35+t*(-1262/105+73814/2835*t))),this.cbg[2]=s*(-26/15+t*(34/21+t*(1.6+-12686/2835*t))),this.cgb[3]=(s*=t)*(4279/630+t*(-332/35+-399572/14175*t)),this.cbg[3]=s*(1237/630+t*(-24832/14175*t-2.4)),this.cgb[4]=(s*=t)*(4174/315+-144838/6237*t),this.cbg[4]=s*(-734/315+109598/31185*t),this.cgb[5]=601676/22275*(s*=t),this.cbg[5]=444337/155925*s,s=Math.pow(t,2),this.Qn=this.k0/(1+t)*(1+s*(.25+s*(1/64+s/256))),this.utg[0]=t*(t*(2/3+t*(-37/96+t*(1/360+t*(81/512+-96199/604800*t))))-.5),this.gtu[0]=t*(.5+t*(-2/3+t*(5/16+t*(41/180+t*(-127/288+7891/37800*t))))),this.utg[1]=s*(-1/48+t*(-1/15+t*(437/1440+t*(-46/105+1118711/3870720*t)))),this.gtu[1]=s*(13/48+t*(t*(557/1440+t*(281/630+-1983433/1935360*t))-.6)),this.utg[2]=(s*=t)*(-17/480+t*(37/840+t*(209/4480+-5569/90720*t))),this.gtu[2]=s*(61/240+t*(-103/140+t*(15061/26880+167603/181440*t))),this.utg[3]=(s*=t)*(-4397/161280+t*(11/504+830251/7257600*t)),this.gtu[3]=s*(49561/161280+t*(-179/168+6601661/7257600*t)),this.utg[4]=(s*=t)*(-4583/161280+108847/3991680*t),this.gtu[4]=s*(34729/80640+-3418889/1995840*t),this.utg[5]=-.03233083094085698*(s*=t),this.gtu[5]=.6650675310896665*s,Kt(this.cbg,this.lat0));this.Zb=-this.Qn*(t+function(t,s){for(var i,a=2*Math.cos(s),h=t.length-1,e=t[h],n=0;0<=--h;)i=a*e-n+t[h],n=e,e=i;return Math.sin(s)*i}(this.gtu,2*t))},forward:function(t){var s,i=S(t.x-this.long0),a=t.y,a=Kt(this.cbg,a),h=Math.sin(a),e=Math.cos(a),n=Math.sin(i),r=Math.cos(i),n=(a=Math.atan2(h,r*e),i=Math.atan2(n*e,d(h,e*r)),i=Jt(Math.tan(i)),Vt(this.gtu,2*a,2*i));return a+=n[0],i+=n[1],h=Math.abs(i)<=2.623395162778?(s=this.a*(this.Qn*i)+this.x0,this.a*(this.Qn*a+this.Zb)+this.y0):s=1/0,t.x=s,t.y=h,t},inverse:function(t){var s,i,a,h,e=(t.x-this.x0)*(1/this.a),n=(t.y-this.y0)*(1/this.a);return n=(n-this.Zb)/this.Qn,e/=this.Qn,h=Math.abs(e)<=2.623395162778?(n+=(a=Vt(this.utg,2*n,2*e))[0],e+=a[1],e=Math.atan(Ht(e)),a=Math.sin(n),s=Math.cos(n),h=Math.sin(e),i=Math.cos(e),n=Math.atan2(a*i,d(h,i*s)),e=Math.atan2(h,i*s),a=S(e+this.long0),Kt(this.cgb,n)):a=1/0,t.x=a,t.y=h,t},names:["Extended_Transverse_Mercator","Extended Transverse Mercator","etmerc","Transverse_Mercator","Transverse Mercator","Gauss Kruger","Gauss_Kruger","tmerc"]},ps={init:function(){var t=function(t,s){if(void 0===t){if((t=Math.floor(30*(S(s)+Math.PI)/Math.PI)+1)<0)return 0;if(60<t)return 60}return t}(this.zone,this.long0);if(void 0===t)throw new Error("unknown utm zone");this.lat0=0,this.long0=(6*Math.abs(t)-183)*v,this.x0=5e5,this.y0=this.utmSouth?1e7:0,this.k0=.9996,ms.init.apply(this),this.forward=ms.forward,this.inverse=ms.inverse},names:["Universal Transverse Mercator System","utm"],dependsOn:"etmerc"},ys={init:function(){var t=Math.sin(this.lat0),s=Math.cos(this.lat0);s*=s,this.rc=Math.sqrt(1-this.es)/(1-this.es*t*t),this.C=Math.sqrt(1+this.es*s*s/(1-this.es)),this.phic0=Math.asin(t/this.C),this.ratexp=.5*this.C*this.e,this.K=Math.tan(.5*this.phic0+N)/(Math.pow(Math.tan(.5*this.lat0+N),this.C)*Zt(this.e*t,this.ratexp))},forward:function(t){var s=t.x,i=t.y;return t.y=2*Math.atan(this.K*Math.pow(Math.tan(.5*i+N),this.C)*Zt(this.e*Math.sin(i),this.ratexp))-g,t.x=this.C*s,t},inverse:function(t){for(var s=t.x/this.C,i=t.y,a=Math.pow(Math.tan(.5*i+N)/this.K,1/this.C),h=20;0<h&&(i=2*Math.atan(a*Zt(this.e*Math.sin(t.y),-.5*this.e))-g,!(Math.abs(i-t.y)<1e-14));--h)t.y=i;return h?(t.x=s,t.y=i,t):null},names:["gauss"]},_s={init:function(){ys.init.apply(this),this.rc&&(this.sinc0=Math.sin(this.phic0),this.cosc0=Math.cos(this.phic0),this.R2=2*this.rc,this.title||(this.title="Oblique Stereographic Alternative"))},forward:function(t){var s,i,a,h;return t.x=S(t.x-this.long0),ys.forward.apply(this,[t]),s=Math.sin(t.y),i=Math.cos(t.y),a=Math.cos(t.x),h=this.k0*this.R2/(1+this.sinc0*s+this.cosc0*i*a),t.x=h*i*Math.sin(t.x),t.y=h*(this.cosc0*s-this.sinc0*i*a),t.x=this.a*t.x+this.x0,t.y=this.a*t.y+this.y0,t},inverse:function(t){var s,i,a,h;return t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,h=(h=d(t.x,t.y))?(i=2*Math.atan2(h,this.R2),s=Math.sin(i),i=Math.cos(i),a=Math.asin(i*this.sinc0+t.y*s*this.cosc0/h),Math.atan2(t.x*s,h*this.cosc0*i-t.y*this.sinc0*s)):(a=this.phic0,0),t.x=h,t.y=a,ys.inverse.apply(this,[t]),t.x=S(t.x+this.long0),t},names:["Stereographic_North_Pole","Oblique_Stereographic","sterea","Oblique Stereographic Alternative","Double_Stereographic"]},xs={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.coslat0=Math.cos(this.lat0),this.sinlat0=Math.sin(this.lat0),this.sphere?1===this.k0&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=b&&(this.k0=.5*(1+kt(this.lat0)*Math.sin(this.lat_ts))):(Math.abs(this.coslat0)<=b&&(0<this.lat0?this.con=1:this.con=-1),this.cons=Math.sqrt(Math.pow(1+this.e,1+this.e)*Math.pow(1-this.e,1-this.e)),1===this.k0&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=b&&Math.abs(Math.cos(this.lat_ts))>b&&(this.k0=.5*this.cons*o(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts))/C(this.e,this.con*this.lat_ts,this.con*Math.sin(this.lat_ts))),this.ms1=o(this.e,this.sinlat0,this.coslat0),this.X0=2*Math.atan(this.ssfn_(this.lat0,this.sinlat0,this.e))-g,this.cosX0=Math.cos(this.X0),this.sinX0=Math.sin(this.X0))},forward:function(t){var s,i,a=t.x,h=t.y,e=Math.sin(h),n=Math.cos(h),r=S(a-this.long0);return Math.abs(Math.abs(a-this.long0)-Math.PI)<=b&&Math.abs(h+this.lat0)<=b?(t.x=NaN,t.y=NaN):this.sphere?(s=2*this.k0/(1+this.sinlat0*e+this.coslat0*n*Math.cos(r)),t.x=this.a*s*n*Math.sin(r)+this.x0,t.y=this.a*s*(this.coslat0*e-this.sinlat0*n*Math.cos(r))+this.y0):(n=2*Math.atan(this.ssfn_(h,e,this.e))-g,i=Math.cos(n),n=Math.sin(n),Math.abs(this.coslat0)<=b?(h=C(this.e,h*this.con,this.con*e),e=2*this.a*this.k0*h/this.cons,t.x=this.x0+e*Math.sin(a-this.long0),t.y=this.y0-this.con*e*Math.cos(a-this.long0)):(Math.abs(this.sinlat0)<b?(s=2*this.a*this.k0/(1+i*Math.cos(r)),t.y=s*n):(s=2*this.a*this.k0*this.ms1/(this.cosX0*(1+this.sinX0*n+this.cosX0*i*Math.cos(r))),t.y=s*(this.cosX0*n-this.sinX0*i*Math.cos(r))+this.y0),t.x=s*i*Math.sin(r)+this.x0)),t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var s,i,a,h,e=Math.sqrt(t.x*t.x+t.y*t.y);if(this.sphere)i=2*Math.atan(e/(2*this.a*this.k0)),a=this.long0,h=this.lat0,e<=b||(h=Math.asin(Math.cos(i)*this.sinlat0+t.y*Math.sin(i)*this.coslat0/e),a=S(Math.abs(this.coslat0)<b?0<this.lat0?this.long0+Math.atan2(t.x,-1*t.y):this.long0+Math.atan2(t.x,t.y):this.long0+Math.atan2(t.x*Math.sin(i),e*this.coslat0*Math.cos(i)-t.y*this.sinlat0*Math.sin(i))));else if(Math.abs(this.coslat0)<=b){if(e<=b)return h=this.lat0,a=this.long0,t.x=a,t.y=h,t;t.x*=this.con,t.y*=this.con,i=e*this.cons/(2*this.a*this.k0),h=this.con*It(this.e,i),a=this.con*S(this.con*this.long0+Math.atan2(t.x,-1*t.y))}else i=2*Math.atan(e*this.cosX0/(2*this.a*this.k0*this.ms1)),a=this.long0,e<=b?s=this.X0:(s=Math.asin(Math.cos(i)*this.sinX0+t.y*Math.sin(i)*this.cosX0/e),a=S(this.long0+Math.atan2(t.x*Math.sin(i),e*this.cosX0*Math.cos(i)-t.y*this.sinX0*Math.sin(i)))),h=-1*It(this.e,Math.tan(.5*(g+s)));return t.x=a,t.y=h,t},names:["stere","Stereographic_South_Pole","Polar Stereographic (variant B)","Polar_Stereographic"],ssfn_:function(t,s,i){return s*=i,Math.tan(.5*(g+t))*Math.pow((1-s)/(1+s),.5*i)}},gs={init:function(){var t=this.lat0,s=(this.lambda0=this.long0,Math.sin(t)),i=this.a,a=1/this.rf,a=2*a-Math.pow(a,2),h=this.e=Math.sqrt(a),i=(this.R=this.k0*i*Math.sqrt(1-a)/(1-a*Math.pow(s,2)),this.alpha=Math.sqrt(1+a/(1-a)*Math.pow(Math.cos(t),4)),this.b0=Math.asin(s/this.alpha),Math.log(Math.tan(Math.PI/4+this.b0/2))),a=Math.log(Math.tan(Math.PI/4+t/2)),t=Math.log((1+h*s)/(1-h*s));this.K=i-this.alpha*a+this.alpha*h/2*t},forward:function(t){var s=Math.log(Math.tan(Math.PI/4-t.y/2)),i=this.e/2*Math.log((1+this.e*Math.sin(t.y))/(1-this.e*Math.sin(t.y))),s=-this.alpha*(s+i)+this.K,i=2*(Math.atan(Math.exp(s))-Math.PI/4),s=this.alpha*(t.x-this.lambda0),a=Math.atan(Math.sin(s)/(Math.sin(this.b0)*Math.tan(i)+Math.cos(this.b0)*Math.cos(s))),i=Math.asin(Math.cos(this.b0)*Math.sin(i)-Math.sin(this.b0)*Math.cos(i)*Math.cos(s));return t.y=this.R/2*Math.log((1+Math.sin(i))/(1-Math.sin(i)))+this.y0,t.x=this.R*a+this.x0,t},inverse:function(t){for(var s,i=t.x-this.x0,a=t.y-this.y0,i=i/this.R,a=2*(Math.atan(Math.exp(a/this.R))-Math.PI/4),h=Math.asin(Math.cos(this.b0)*Math.sin(a)+Math.sin(this.b0)*Math.cos(a)*Math.cos(i)),i=Math.atan(Math.sin(i)/(Math.cos(this.b0)*Math.cos(i)-Math.sin(this.b0)*Math.tan(a))),a=this.lambda0+i/this.alpha,e=h,n=-1e3,r=0;1e-7<Math.abs(e-n);){if(20<++r)return;s=1/this.alpha*(Math.log(Math.tan(Math.PI/4+h/2))-this.K)+this.e*Math.log(Math.tan(Math.PI/4+Math.asin(this.e*Math.sin(e))/2)),n=e,e=2*Math.atan(Math.exp(s))-Math.PI/2}return t.x=a,t.y=e,t},names:["somerc"]},bs=1e-7,vs={init:function(){var t,s,i,a,h,e=0,n=0,r=0,o=0,l=0,M=0,c=0,u=(this.no_off=(f="object"==typeof(u=this).PROJECTION?Object.keys(u.PROJECTION)[0]:u.PROJECTION,"no_uoff"in u||"no_off"in u||-1!==["Hotine_Oblique_Mercator","Hotine_Oblique_Mercator_Azimuth_Natural_Origin"].indexOf(f)),this.no_rot="no_rot"in this,!1),f=("alpha"in this&&(u=!0),!1);if("rectified_grid_angle"in this&&(f=!0),u&&(c=this.alpha),f&&(e=this.rectified_grid_angle*v),u||f)n=this.longc;else if(r=this.long1,l=this.lat1,o=this.long2,M=this.lat2,Math.abs(l-M)<=bs||(t=Math.abs(l))<=bs||Math.abs(t-g)<=bs||Math.abs(Math.abs(this.lat0)-g)<=bs||Math.abs(Math.abs(M)-g)<=bs)throw new Error;var d=1-this.es,m=Math.sqrt(d);Math.abs(this.lat0)>b?(a=Math.sin(this.lat0),s=Math.cos(this.lat0),t=1-this.es*a*a,this.B=s*s,this.B=Math.sqrt(1+this.es*this.B*this.B/d),this.A=this.B*this.k0*m/t,(i=(s=this.B*m/(s*Math.sqrt(t)))*s-1)<=0?i=0:(i=Math.sqrt(i),this.lat0<0&&(i=-i)),this.E=i+=s,this.E*=Math.pow(C(this.e,this.lat0,a),this.B)):(this.B=1/m,this.A=this.k0,this.E=s=i=1),u||f?(u?(h=Math.asin(Math.sin(c)/s),f||(e=c)):(h=e,c=Math.asin(s*Math.sin(h))),this.lam0=n-Math.asin(.5*(i-1/i)*Math.tan(h))/this.B):(d=Math.pow(C(this.e,l,Math.sin(l)),this.B),a=Math.pow(C(this.e,M,Math.sin(M)),this.B),i=this.E/d,m=(a-d)/(a+d),u=((u=this.E*this.E)-a*d)/(u+a*d),(t=r-o)<-Math.pi?o-=yt:t>Math.pi&&(o+=yt),this.lam0=S(.5*(r+o)-Math.atan(u*Math.tan(.5*this.B*(r-o))/m)/this.B),h=Math.atan(2*Math.sin(this.B*S(r-this.lam0))/(i-1/i)),e=c=Math.asin(s*Math.sin(h))),this.singam=Math.sin(h),this.cosgam=Math.cos(h),this.sinrot=Math.sin(e),this.cosrot=Math.cos(e),this.rB=1/this.B,this.ArB=this.A*this.rB,this.BrA=1/this.ArB,this.no_off?this.u_0=0:(this.u_0=Math.abs(this.ArB*Math.atan(Math.sqrt(s*s-1)/Math.cos(c))),this.lat0<0&&(this.u_0=-this.u_0)),i=.5*h,this.v_pole_n=this.ArB*Math.log(Math.tan(N-i)),this.v_pole_s=this.ArB*Math.log(Math.tan(N+i))},forward:function(t){var s,i,a,h,e={};if(t.x=t.x-this.lam0,Math.abs(Math.abs(t.y)-g)>b){if(a=.5*((h=this.E/Math.pow(C(this.e,t.y,Math.sin(t.y)),this.B))-(i=1/h)),h=.5*(h+i),s=Math.sin(this.B*t.x),h=(a*this.singam-s*this.cosgam)/h,Math.abs(Math.abs(h)-1)<b)throw new Error;h=.5*this.ArB*Math.log((1-h)/(1+h)),i=Math.cos(this.B*t.x),a=Math.abs(i)<bs?this.A*t.x:this.ArB*Math.atan2(a*this.cosgam+s*this.singam,i)}else h=0<t.y?this.v_pole_n:this.v_pole_s,a=this.ArB*t.y;return this.no_rot?(e.x=a,e.y=h):(a-=this.u_0,e.x=h*this.cosrot+a*this.sinrot,e.y=a*this.cosrot-h*this.sinrot),e.x=this.a*e.x+this.x0,e.y=this.a*e.y+this.y0,e},inverse:function(t){var s,i,a,h={};if(t.x=(t.x-this.x0)*(1/this.a),t.y=(t.y-this.y0)*(1/this.a),t=this.no_rot?(a=t.y,t.x):(a=t.x*this.cosrot-t.y*this.sinrot,t.y*this.cosrot+t.x*this.sinrot+this.u_0),s=.5*((a=Math.exp(-this.BrA*a))-1/a),a=.5*(a+1/a),a=((i=Math.sin(this.BrA*t))*this.cosgam+s*this.singam)/a,Math.abs(Math.abs(a)-1)<b)h.x=0,h.y=a<0?-g:g;else{if(h.y=this.E/Math.sqrt((1+a)/(1-a)),h.y=It(this.e,Math.pow(h.y,1/this.B)),h.y===1/0)throw new Error;h.x=-this.rB*Math.atan2(s*this.cosgam-i*this.singam,Math.cos(this.BrA*t))}return h.x+=this.lam0,h},names:["Hotine_Oblique_Mercator","Hotine Oblique Mercator","Hotine_Oblique_Mercator_Azimuth_Natural_Origin","Hotine_Oblique_Mercator_Two_Point_Natural_Origin","Hotine_Oblique_Mercator_Azimuth_Center","Oblique_Mercator","omerc"]},ws={init:function(){var t,s,i,a,h,e;this.lat2||(this.lat2=this.lat1),this.k0||(this.k0=1),this.x0=this.x0||0,this.y0=this.y0||0,Math.abs(this.lat1+this.lat2)<b||(t=this.b/this.a,this.e=Math.sqrt(1-t*t),t=Math.sin(this.lat1),s=Math.cos(this.lat1),s=o(this.e,t,s),i=C(this.e,this.lat1,t),h=Math.sin(this.lat2),a=Math.cos(this.lat2),a=o(this.e,h,a),h=C(this.e,this.lat2,h),e=C(this.e,this.lat0,Math.sin(this.lat0)),Math.abs(this.lat1-this.lat2)>b?this.ns=Math.log(s/a)/Math.log(i/h):this.ns=t,isNaN(this.ns)&&(this.ns=t),this.f0=s/(this.ns*Math.pow(i,this.ns)),this.rh=this.a*this.f0*Math.pow(e,this.ns),this.title)||(this.title="Lambert Conformal Conic")},forward:function(t){var s=t.x,i=t.y;Math.abs(2*Math.abs(i)-Math.PI)<=b&&(i=kt(i)*(g-2*b));var a,h=Math.abs(Math.abs(i)-g);if(b<h)h=C(this.e,i,Math.sin(i)),a=this.a*this.f0*Math.pow(h,this.ns);else{if(i*this.ns<=0)return null;a=0}h=this.ns*S(s-this.long0);return t.x=this.k0*(a*Math.sin(h))+this.x0,t.y=this.k0*(this.rh-a*Math.cos(h))+this.y0,t},inverse:function(t){var s,i,a=(t.x-this.x0)/this.k0,h=this.rh-(t.y-this.y0)/this.k0,e=0<this.ns?(s=Math.sqrt(a*a+h*h),1):(s=-Math.sqrt(a*a+h*h),-1),n=0;if(0!==s&&(n=Math.atan2(e*a,e*h)),0!==s||0<this.ns){if(e=1/this.ns,a=Math.pow(s/(this.a*this.f0),e),-9999===(i=It(this.e,a)))return null}else i=-g;return h=S(n/this.ns+this.long0),t.x=h,t.y=i,t},names:["Lambert Tangential Conformal Conic Projection","Lambert_Conformal_Conic","Lambert_Conformal_Conic_1SP","Lambert_Conformal_Conic_2SP","lcc","Lambert Conic Conformal (1SP)","Lambert Conic Conformal (2SP)"]},Ns={init:function(){this.a=6377397.155,this.es=.006674372230614,this.e=Math.sqrt(this.es),this.lat0||(this.lat0=.863937979737193),this.long0||(this.long0=.4334234309119251),this.k0||(this.k0=.9999),this.s45=.785398163397448,this.s90=2*this.s45,this.fi0=this.lat0,this.e2=this.es,this.e=Math.sqrt(this.e2),this.alfa=Math.sqrt(1+this.e2*Math.pow(Math.cos(this.fi0),4)/(1-this.e2)),this.uq=1.04216856380474,this.u0=Math.asin(Math.sin(this.fi0)/this.alfa),this.g=Math.pow((1+this.e*Math.sin(this.fi0))/(1-this.e*Math.sin(this.fi0)),this.alfa*this.e/2),this.k=Math.tan(this.u0/2+this.s45)/Math.pow(Math.tan(this.fi0/2+this.s45),this.alfa)*this.g,this.k1=this.k0,this.n0=this.a*Math.sqrt(1-this.e2)/(1-this.e2*Math.pow(Math.sin(this.fi0),2)),this.s0=1.37008346281555,this.n=Math.sin(this.s0),this.ro0=this.k1*this.n0/Math.tan(this.s0),this.ad=this.s90-this.uq},forward:function(t){var s=t.x,i=t.y,s=S(s-this.long0),a=Math.pow((1+this.e*Math.sin(i))/(1-this.e*Math.sin(i)),this.alfa*this.e/2),i=2*(Math.atan(this.k*Math.pow(Math.tan(i/2+this.s45),this.alfa)/a)-this.s45),a=-s*this.alfa,s=Math.asin(Math.cos(this.ad)*Math.sin(i)+Math.sin(this.ad)*Math.cos(i)*Math.cos(a)),i=Math.asin(Math.cos(i)*Math.sin(a)/Math.cos(s)),a=this.n*i,i=this.ro0*Math.pow(Math.tan(this.s0/2+this.s45),this.n)/Math.pow(Math.tan(s/2+this.s45),this.n);return t.y=i*Math.cos(a),t.x=i*Math.sin(a),this.czech||(t.y*=-1,t.x*=-1),t},inverse:function(t){for(var s,i,a,h,e=t.x,n=(t.x=t.y,t.y=e,this.czech||(t.y*=-1,t.x*=-1),e=Math.sqrt(t.x*t.x+t.y*t.y),i=Math.atan2(t.y,t.x)/Math.sin(this.s0),e=2*(Math.atan(Math.pow(this.ro0/e,1/this.n)*Math.tan(this.s0/2+this.s45))-this.s45),s=Math.asin(Math.cos(this.ad)*Math.sin(e)-Math.sin(this.ad)*Math.cos(e)*Math.cos(i)),e=Math.asin(Math.cos(e)*Math.sin(i)/Math.cos(s)),t.x=this.long0-e/this.alfa,a=s,h=0);t.y=2*(Math.atan(Math.pow(this.k,-1/this.alfa)*Math.pow(Math.tan(s/2+this.s45),1/this.alfa)*Math.pow((1+this.e*Math.sin(a))/(1-this.e*Math.sin(a)),this.e/2))-this.s45),Math.abs(a-t.y)<1e-10&&(h=1),a=t.y,n+=1,0===h&&n<15;);return 15<=n?null:t},names:["Krovak","krovak"]},Cs={init:function(){this.sphere||(this.e0=Yt(this.es),this.e1=$t(this.es),this.e2=ts(this.es),this.e3=ss(this.es),this.ml0=this.a*P(this.e0,this.e1,this.e2,this.e3,this.lat0))},forward:function(t){var s,i,a,h,e,n,r=t.x,o=t.y,r=S(r-this.long0);return o=this.sphere?(n=this.a*Math.asin(Math.cos(o)*Math.sin(r)),this.a*(Math.atan2(Math.tan(o),Math.cos(r))-this.lat0)):(s=Math.sin(o),i=Math.cos(o),a=is(this.a,this.e,s),h=Math.tan(o)*Math.tan(o),n=a*(r=r*Math.cos(o))*(1-(r=r*r)*h*(1/6-(8-h+8*(e=this.es*i*i/(1-this.es)))*r/120)),this.a*P(this.e0,this.e1,this.e2,this.e3,o)-this.ml0+a*s/i*r*(.5+(5-h+6*e)*r/24)),t.x=n+this.x0,t.y=o+this.y0,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var s=t.x/this.a,i=t.y/this.a;if(this.sphere)var a=i+this.lat0,h=Math.asin(Math.sin(a)*Math.cos(s)),a=Math.atan2(Math.tan(s),Math.cos(a));else{var e=this.ml0/this.a+i,e=as(e,this.e0,this.e1,this.e2,this.e3);if(Math.abs(Math.abs(e)-g)<=b)return t.x=this.long0,t.y=g,i<0&&(t.y*=-1),t;var i=is(this.a,this.e,Math.sin(e)),n=i*i*i/this.a/this.a*(1-this.es),r=Math.pow(Math.tan(e),2),s=s*this.a/i,o=s*s;h=e-i*Math.tan(e)/n*s*s*(.5-(1+3*r)*s*s/24),a=s*(1-o*(r/3+(1+3*r)*r*o/15))/Math.cos(e)}return t.x=S(a+this.long0),t.y=E(h),t},names:["Cassini","Cassini_Soldner","cass"]},Ss={init:function(){var t,s,i,a=Math.abs(this.lat0);if(Math.abs(a-g)<b?this.mode=this.lat0<0?this.S_POLE:this.N_POLE:Math.abs(a)<b?this.mode=this.EQUIT:this.mode=this.OBLIQ,0<this.es)switch(this.qp=k(this.e,1),this.mmf=.5/(1-this.es),this.apa=(a=this.es,(i=[])[0]=.3333333333333333*a,i[0]+=.17222222222222222*(s=a*a),i[1]=.06388888888888888*s,i[0]+=.10257936507936508*(s*=a),i[1]+=.0664021164021164*s,i[2]=.016415012942191543*s,i),this.mode){case this.N_POLE:case this.S_POLE:this.dd=1;break;case this.EQUIT:this.rq=Math.sqrt(.5*this.qp),this.dd=1/this.rq,this.xmf=1,this.ymf=.5*this.qp;break;case this.OBLIQ:this.rq=Math.sqrt(.5*this.qp),t=Math.sin(this.lat0),this.sinb1=k(this.e,t)/this.qp,this.cosb1=Math.sqrt(1-this.sinb1*this.sinb1),this.dd=Math.cos(this.lat0)/(Math.sqrt(1-this.es*t*t)*this.rq*this.cosb1),this.ymf=(this.xmf=this.rq)/this.dd,this.xmf*=this.dd}else this.mode===this.OBLIQ&&(this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0))},forward:function(t){var s,i,a,h,e,n,r,o,l,M,c=t.x,u=t.y,c=S(c-this.long0);if(this.sphere){if(e=Math.sin(u),M=Math.cos(u),a=Math.cos(c),this.mode===this.OBLIQ||this.mode===this.EQUIT){if((i=this.mode===this.EQUIT?1+M*a:1+this.sinph0*e+this.cosph0*M*a)<=b)return null;s=(i=Math.sqrt(2/i))*M*Math.sin(c),i*=this.mode===this.EQUIT?e:this.cosph0*e-this.sinph0*M*a}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(a=-a),Math.abs(u+this.lat0)<b)return null;i=N-.5*u,s=(i=2*(this.mode===this.S_POLE?Math.cos(i):Math.sin(i)))*Math.sin(c),i*=a}}else{switch(l=o=r=0,a=Math.cos(c),h=Math.sin(c),e=Math.sin(u),n=k(this.e,e),this.mode!==this.OBLIQ&&this.mode!==this.EQUIT||(r=n/this.qp,o=Math.sqrt(1-r*r)),this.mode){case this.OBLIQ:l=1+this.sinb1*r+this.cosb1*o*a;break;case this.EQUIT:l=1+o*a;break;case this.N_POLE:l=g+u,n=this.qp-n;break;case this.S_POLE:l=u-g,n=this.qp+n}if(Math.abs(l)<b)return null;switch(this.mode){case this.OBLIQ:case this.EQUIT:l=Math.sqrt(2/l),i=this.mode===this.OBLIQ?this.ymf*l*(this.cosb1*r-this.sinb1*o*a):(l=Math.sqrt(2/(1+o*a)))*r*this.ymf,s=this.xmf*l*o*h;break;case this.N_POLE:case this.S_POLE:0<=n?(s=(l=Math.sqrt(n))*h,i=a*(this.mode===this.S_POLE?l:-l)):s=i=0}}return t.x=this.a*s+this.x0,t.y=this.a*i+this.y0,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var s,i,a,h,e,n,r,o=t.x/this.a,l=t.y/this.a;if(this.sphere){var M=0,c=0,u=Math.sqrt(o*o+l*l);if(1<(i=.5*u))return null;switch(i=2*Math.asin(i),this.mode!==this.OBLIQ&&this.mode!==this.EQUIT||(c=Math.sin(i),M=Math.cos(i)),this.mode){case this.EQUIT:i=Math.abs(u)<=b?0:Math.asin(l*c/u),o*=c,l=M*u;break;case this.OBLIQ:i=Math.abs(u)<=b?this.lat0:Math.asin(M*this.sinph0+l*c*this.cosph0/u),o*=c*this.cosph0,l=(M-Math.sin(i)*this.sinph0)*u;break;case this.N_POLE:l=-l,i=g-i;break;case this.S_POLE:i-=g}s=0!==l||this.mode!==this.EQUIT&&this.mode!==this.OBLIQ?Math.atan2(o,l):0}else{if(h=0,this.mode===this.OBLIQ||this.mode===this.EQUIT){if(o/=this.dd,l*=this.dd,(n=Math.sqrt(o*o+l*l))<b)return t.x=this.long0,t.y=this.lat0,t;e=2*Math.asin(.5*n/this.rq),r=Math.cos(e),o*=e=Math.sin(e),l=this.mode===this.OBLIQ?(h=r*this.sinb1+l*e*this.cosb1/n,a=this.qp*h,n*this.cosb1*r-l*this.sinb1*e):(a=this.qp*(h=l*e/n),n*r)}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(!(a=o*o+(l=this.mode===this.N_POLE?-l:l)*l))return t.x=this.long0,t.y=this.lat0,t;h=1-a/this.qp,this.mode===this.S_POLE&&(h=-h)}s=Math.atan2(o,l),e=Math.asin(h),n=this.apa,r=e+e,i=e+n[0]*Math.sin(r)+n[1]*Math.sin(r+r)+n[2]*Math.sin(r+r+r)}return t.x=S(this.long0+s),t.y=i,t},names:["Lambert Azimuthal Equal Area","Lambert_Azimuthal_Equal_Area","laea"],S_POLE:1,N_POLE:2,EQUIT:3,OBLIQ:4},Ps={init:function(){Math.abs(this.lat1+this.lat2)<b||(this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e3=Math.sqrt(this.es),this.sin_po=Math.sin(this.lat1),this.cos_po=Math.cos(this.lat1),this.t1=this.sin_po,this.con=this.sin_po,this.ms1=o(this.e3,this.sin_po,this.cos_po),this.qs1=k(this.e3,this.sin_po),this.sin_po=Math.sin(this.lat2),this.cos_po=Math.cos(this.lat2),this.t2=this.sin_po,this.ms2=o(this.e3,this.sin_po,this.cos_po),this.qs2=k(this.e3,this.sin_po),this.sin_po=Math.sin(this.lat0),this.cos_po=Math.cos(this.lat0),this.t3=this.sin_po,this.qs0=k(this.e3,this.sin_po),Math.abs(this.lat1-this.lat2)>b?this.ns0=(this.ms1*this.ms1-this.ms2*this.ms2)/(this.qs2-this.qs1):this.ns0=this.con,this.c=this.ms1*this.ms1+this.ns0*this.qs1,this.rh=this.a*Math.sqrt(this.c-this.ns0*this.qs0)/this.ns0)},forward:function(t){var s=t.x,i=t.y,i=(this.sin_phi=Math.sin(i),this.cos_phi=Math.cos(i),k(this.e3,this.sin_phi)),i=this.a*Math.sqrt(this.c-this.ns0*i)/this.ns0,s=this.ns0*S(s-this.long0),a=i*Math.sin(s)+this.x0,i=this.rh-i*Math.cos(s)+this.y0;return t.x=a,t.y=i,t},inverse:function(t){var s,i,a;return t.x-=this.x0,t.y=this.rh-t.y+this.y0,a=0<=this.ns0?(i=Math.sqrt(t.x*t.x+t.y*t.y),1):(i=-Math.sqrt(t.x*t.x+t.y*t.y),-1),(s=0)!==i&&(s=Math.atan2(a*t.x,a*t.y)),a=i*this.ns0/this.a,a=this.sphere?Math.asin((this.c-a*a)/(2*this.ns0)):(i=(this.c-a*a)/this.ns0,this.phi1z(this.e3,i)),i=S(s/this.ns0+this.long0),t.x=i,t.y=a,t},names:["Albers_Conic_Equal_Area","Albers","aea"],phi1z:function(t,s){var i,a,h,e=I(.5*s);if(t<b)return e;for(var n=t*t,r=1;r<=25;r++)if(e+=h=.5*(a=1-(i=t*(h=Math.sin(e)))*i)*a/Math.cos(e)*(s/(1-n)-h/a+.5/t*Math.log((1-i)/(1+i))),Math.abs(h)<=1e-7)return e;return null}},Es={init:function(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0),this.infinity_dist=1e3*this.a,this.rc=1},forward:function(t){var s,i=t.x,a=t.y,i=S(i-this.long0),h=Math.sin(a),a=Math.cos(a),e=Math.cos(i),n=0<(n=this.sin_p14*h+this.cos_p14*a*e)||Math.abs(n)<=b?(s=this.x0+ +this.a*a*Math.sin(i)/n,this.y0+ +this.a*(this.cos_p14*h-this.sin_p14*a*e)/n):(s=this.x0+this.infinity_dist*a*Math.sin(i),this.y0+this.infinity_dist*(this.cos_p14*h-this.sin_p14*a*e));return t.x=s,t.y=n,t},inverse:function(t){var s,i,a,h;return t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,a=(s=Math.sqrt(t.x*t.x+t.y*t.y))?(a=Math.atan2(s,this.rc),i=Math.sin(a),a=Math.cos(a),h=I(a*this.sin_p14+t.y*i*this.cos_p14/s),a=Math.atan2(t.x*i,s*this.cos_p14*a-t.y*this.sin_p14*i),S(this.long0+a)):(h=this.phic0,0),t.x=a,t.y=h,t},names:["gnom"]},ks={init:function(){this.sphere||(this.k0=o(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)))},forward:function(t){var s,i=t.x,a=t.y,i=S(i-this.long0);return i=this.sphere?(s=this.x0+this.a*i*Math.cos(this.lat_ts),this.y0+this.a*Math.sin(a)/Math.cos(this.lat_ts)):(a=k(this.e,Math.sin(a)),s=this.x0+this.a*this.k0*i,this.y0+this.a*a*.5/this.k0),t.x=s,t.y=i,t},inverse:function(t){var s,i;return t.x-=this.x0,t.y-=this.y0,this.sphere?(s=S(this.long0+t.x/this.a/Math.cos(this.lat_ts)),i=Math.asin(t.y/this.a*Math.cos(this.lat_ts))):(i=function(t,s){var i=1-(1-t*t)/(2*t)*Math.log((1-t)/(1+t));if(Math.abs(Math.abs(s)-i)<1e-6)return s<0?-1*g:g;for(var a,h,e,n=Math.asin(.5*s),r=0;r<30;r++)if(a=Math.sin(n),h=Math.cos(n),e=t*a,n+=h=Math.pow(1-e*e,2)/(2*h)*(s/(1-t*t)-a/(1-e*e)+.5/t*Math.log((1-e)/(1+e))),Math.abs(h)<=1e-10)return n;return NaN}(this.e,2*t.y*this.k0/this.a),s=S(this.long0+t.x/(this.a*this.k0))),t.x=s,t.y=i,t},names:["cea"]},Is={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Equidistant Cylindrical (Plate Carre)",this.rc=Math.cos(this.lat_ts)},forward:function(t){var s=t.x,i=t.y,s=S(s-this.long0),i=E(i-this.lat0);return t.x=this.x0+this.a*s*this.rc,t.y=this.y0+this.a*i,t},inverse:function(t){var s=t.x,i=t.y;return t.x=S(this.long0+(s-this.x0)/(this.a*this.rc)),t.y=E(this.lat0+(i-this.y0)/this.a),t},names:["Equirectangular","Equidistant_Cylindrical","eqc"]},qs={init:function(){this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=Yt(this.es),this.e1=$t(this.es),this.e2=ts(this.es),this.e3=ss(this.es),this.ml0=this.a*P(this.e0,this.e1,this.e2,this.e3,this.lat0)},forward:function(t){var s,i=t.x,a=t.y,i=S(i-this.long0),h=i*Math.sin(a);return a=this.sphere?Math.abs(a)<=b?(s=this.a*i,-1*this.a*this.lat0):(s=this.a*Math.sin(h)/Math.tan(a),this.a*(E(a-this.lat0)+(1-Math.cos(h))/Math.tan(a))):Math.abs(a)<=b?(s=this.a*i,-1*this.ml0):(s=(i=is(this.a,this.e,Math.sin(a))/Math.tan(a))*Math.sin(h),this.a*P(this.e0,this.e1,this.e2,this.e3,a)-this.ml0+i*(1-Math.cos(h))),t.x=s+this.x0,t.y=a+this.y0,t},inverse:function(t){var s,i,a,h,e,n,r=t.x-this.x0,o=t.y-this.y0;if(this.sphere)if(Math.abs(o+this.a*this.lat0)<=b)s=S(r/this.a+this.long0),i=0;else{for(var l,M=this.lat0+o/this.a,c=r*r/this.a/this.a+M*M,u=M,f=20;f;--f)if(u+=a=-1*(M*(u*(l=Math.tan(u))+1)-u-.5*(u*u+c)*l)/((u-M)/l-1),Math.abs(a)<=b){i=u;break}s=S(this.long0+Math.asin(r*Math.tan(u)/this.a)/Math.sin(i))}else if(Math.abs(o+this.ml0)<=b)i=0,s=S(this.long0+r/this.a);else{for(M=(this.ml0+o)/this.a,c=r*r/this.a/this.a+M*M,u=M,f=20;f;--f)if(n=this.e*Math.sin(u),h=Math.sqrt(1-n*n)*Math.tan(u),n=this.a*P(this.e0,this.e1,this.e2,this.e3,u),e=this.e0-2*this.e1*Math.cos(2*u)+4*this.e2*Math.cos(4*u)-6*this.e3*Math.cos(6*u),u-=a=(M*(h*(n=n/this.a)+1)-n-.5*h*(n*n+c))/(this.es*Math.sin(2*u)*(n*n+c-2*M*n)/(4*h)+(M-n)*(h*e-2/Math.sin(2*u))-e),Math.abs(a)<=b){i=u;break}h=Math.sqrt(1-this.es*Math.pow(Math.sin(i),2))*Math.tan(i),s=S(this.long0+Math.asin(r*h/this.a)/Math.sin(i))}return t.x=s,t.y=i,t},names:["Polyconic","poly"]},As={init:function(){this.A=[],this.A[1]=.6399175073,this.A[2]=-.1358797613,this.A[3]=.063294409,this.A[4]=-.02526853,this.A[5]=.0117879,this.A[6]=-.0055161,this.A[7]=.0026906,this.A[8]=-.001333,this.A[9]=67e-5,this.A[10]=-34e-5,this.B_re=[],this.B_im=[],this.B_re[1]=.7557853228,this.B_im[1]=0,this.B_re[2]=.249204646,this.B_im[2]=.003371507,this.B_re[3]=-.001541739,this.B_im[3]=.04105856,this.B_re[4]=-.10162907,this.B_im[4]=.01727609,this.B_re[5]=-.26623489,this.B_im[5]=-.36249218,this.B_re[6]=-.6870983,this.B_im[6]=-1.1651967,this.C_re=[],this.C_im=[],this.C_re[1]=1.3231270439,this.C_im[1]=0,this.C_re[2]=-.577245789,this.C_im[2]=-.007809598,this.C_re[3]=.508307513,this.C_im[3]=-.112208952,this.C_re[4]=-.15094762,this.C_im[4]=.18200602,this.C_re[5]=1.01418179,this.C_im[5]=1.64497696,this.C_re[6]=1.9660549,this.C_im[6]=2.5127645,this.D=[],this.D[1]=1.5627014243,this.D[2]=.5185406398,this.D[3]=-.03333098,this.D[4]=-.1052906,this.D[5]=-.0368594,this.D[6]=.007317,this.D[7]=.0122,this.D[8]=.00394,this.D[9]=-.0013},forward:function(t){for(var s=t.x,i=t.y-this.lat0,s=s-this.long0,a=i/ft*1e-5,i=s,h=1,e=0,n=1;n<=10;n++)e+=this.A[n]*(h*=a);var r,o=e,l=i,M=1,c=0,u=0,f=0;for(n=1;n<=6;n++)r=c*o+M*l,u=u+this.B_re[n]*(M=M*o-c*l)-this.B_im[n]*(c=r),f=f+this.B_im[n]*M+this.B_re[n]*c;return t.x=f*this.a+this.x0,t.y=u*this.a+this.y0,t},inverse:function(t){var s,i=t.x,a=t.y,i=i-this.x0,h=(a-this.y0)/this.a,e=i/this.a,n=1,r=0,o=0,l=0;for(p=1;p<=6;p++)s=r*h+n*e,o=o+this.C_re[p]*(n=n*h-r*e)-this.C_im[p]*(r=s),l=l+this.C_im[p]*n+this.C_re[p]*r;for(var M=0;M<this.iterations;M++){for(var c,u=o,f=l,d=h,m=e,p=2;p<=6;p++)c=f*o+u*l,u=u*o-f*l,f=c,d+=(p-1)*(this.B_re[p]*u-this.B_im[p]*f),m+=(p-1)*(this.B_im[p]*u+this.B_re[p]*f);var u=1,f=0,y=this.B_re[1],_=this.B_im[1];for(p=2;p<=6;p++)c=f*o+u*l,u=u*o-f*l,f=c,y+=p*(this.B_re[p]*u-this.B_im[p]*f),_+=p*(this.B_im[p]*u+this.B_re[p]*f);var x=y*y+_*_,o=(d*y+m*_)/x,l=(m*y-d*_)/x}var g=o,a=l,b=1,v=0;for(p=1;p<=9;p++)v+=this.D[p]*(b*=g);i=this.lat0+v*ft*1e5,a=this.long0+a;return t.x=a,t.y=i,t},names:["New_Zealand_Map_Grid","nzmg"]},Os={init:function(){},forward:function(t){var s=t.x,i=t.y,s=S(s-this.long0),s=this.x0+this.a*s,i=this.y0+this.a*Math.log(Math.tan(Math.PI/4+i/2.5))*1.25;return t.x=s,t.y=i,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var s=S(this.long0+t.x/this.a),i=2.5*(Math.atan(Math.exp(.8*t.y/this.a))-Math.PI/4);return t.x=s,t.y=i,t},names:["Miller_Cylindrical","mill"]},js={init:function(){this.sphere?(this.n=1,this.m=0,this.es=0,this.C_y=Math.sqrt((this.m+1)/this.n),this.C_x=this.C_y/(this.m+1)):this.en=Wt(this.es)},forward:function(t){var s=t.x,i=t.y,s=S(s-this.long0);if(this.sphere){if(this.m)for(var a=this.n*Math.sin(i),h=20;h;--h){var e=(this.m*i+Math.sin(i)-a)/(this.m+Math.cos(i));if(i-=e,Math.abs(e)<b)break}else i=1!==this.n?Math.asin(this.n*Math.sin(i)):i;l=this.a*this.C_x*s*(this.m+Math.cos(i)),o=this.a*this.C_y*i}else var n=Math.sin(i),r=Math.cos(i),o=this.a*f(i,n,r,this.en),l=this.a*s*r/Math.sqrt(1-this.es*n*n);return t.x=l,t.y=o,t},inverse:function(t){var s,i,a,h;return t.x-=this.x0,a=t.x/this.a,t.y-=this.y0,s=t.y/this.a,this.sphere?(s/=this.C_y,a/=this.C_x*(this.m+Math.cos(s)),this.m?s=I((this.m*s+Math.sin(s))/this.n):1!==this.n&&(s=I(Math.sin(s)/this.n)),a=S(a+this.long0),s=E(s)):(s=Xt(t.y/this.a,this.es,this.en),(h=Math.abs(s))<g?(h=Math.sin(s),i=this.long0+t.x*Math.sqrt(1-this.es*h*h)/(this.a*Math.cos(s)),a=S(i)):h-b<g&&(a=this.long0)),t.x=a,t.y=s,t},names:["Sinusoidal","sinu"]},Gs={init:function(){},forward:function(t){for(var s=t.x,i=t.y,s=S(s-this.long0),a=i,h=Math.PI*Math.sin(i);;){var e=-(a+Math.sin(a)-h)/(1+Math.cos(a));if(a+=e,Math.abs(e)<b)break}a/=2,Math.PI/2-Math.abs(i)<b&&(s=0);i=.900316316158*this.a*s*Math.cos(a)+this.x0,s=1.4142135623731*this.a*Math.sin(a)+this.y0;return t.x=i,t.y=s,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0,s=t.y/(1.4142135623731*this.a),.999999999999<Math.abs(s)&&(s=.999999999999),a=Math.asin(s);var s,i=S(this.long0+t.x/(.900316316158*this.a*Math.cos(a))),a=((i=i<-Math.PI?-Math.PI:i)>Math.PI&&(i=Math.PI),s=(2*a+Math.sin(2*a))/Math.PI,1<Math.abs(s)&&(s=1),Math.asin(s));return t.x=i,t.y=a,t},names:["Mollweide","moll"]},Ls={init:function(){Math.abs(this.lat1+this.lat2)<b||(this.lat2=this.lat2||this.lat1,this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=Yt(this.es),this.e1=$t(this.es),this.e2=ts(this.es),this.e3=ss(this.es),this.sinphi=Math.sin(this.lat1),this.cosphi=Math.cos(this.lat1),this.ms1=o(this.e,this.sinphi,this.cosphi),this.ml1=P(this.e0,this.e1,this.e2,this.e3,this.lat1),Math.abs(this.lat1-this.lat2)<b?this.ns=this.sinphi:(this.sinphi=Math.sin(this.lat2),this.cosphi=Math.cos(this.lat2),this.ms2=o(this.e,this.sinphi,this.cosphi),this.ml2=P(this.e0,this.e1,this.e2,this.e3,this.lat2),this.ns=(this.ms1-this.ms2)/(this.ml2-this.ml1)),this.g=this.ml1+this.ms1/this.ns,this.ml0=P(this.e0,this.e1,this.e2,this.e3,this.lat0),this.rh=this.a*(this.g-this.ml0))},forward:function(t){var s=t.x,i=t.y,s=(i=this.sphere?this.a*(this.g-i):(i=P(this.e0,this.e1,this.e2,this.e3,i),this.a*(this.g-i)),this.ns*S(s-this.long0)),a=this.x0+i*Math.sin(s),i=this.y0+this.rh-i*Math.cos(s);return t.x=a,t.y=i,t},inverse:function(t){t.x-=this.x0,t.y=this.rh-t.y+this.y0,i=0<=this.ns?(s=Math.sqrt(t.x*t.x+t.y*t.y),1):(s=-Math.sqrt(t.x*t.x+t.y*t.y),-1);var s,i,a,h,e=0;return(0!==s&&(e=Math.atan2(i*t.x,i*t.y)),this.sphere)?(h=S(this.long0+e/this.ns),a=E(this.g-s/this.a),t.x=h,t.y=a):(i=this.g-s/this.a,a=as(i,this.e0,this.e1,this.e2,this.e3),h=S(this.long0+e/this.ns),t.x=h,t.y=a),t},names:["Equidistant_Conic","eqdc"]},zs={init:function(){this.R=this.a},forward:function(t){var s,i=t.x,a=t.y,i=S(i-this.long0),h=(Math.abs(a)<=b&&(s=this.x0+this.R*i,M=this.y0),I(2*Math.abs(a/Math.PI))),e=((Math.abs(i)<=b||Math.abs(Math.abs(a)-g)<=b)&&(s=this.x0,M=0<=a?this.y0+Math.PI*this.R*Math.tan(.5*h):this.y0+Math.PI*this.R*-Math.tan(.5*h)),.5*Math.abs(Math.PI/i-i/Math.PI)),n=e*e,r=Math.sin(h),h=Math.cos(h),h=h/(r+h-1),r=h*(2/r-1),o=r*r,l=Math.PI*this.R*(e*(h-o)+Math.sqrt(n*(h-o)*(h-o)-(o+n)*(h*h-o)))/(o+n),i=(s=this.x0+(l=i<0?-l:l),n+h),l=Math.PI*this.R*(r*i-e*Math.sqrt((o+n)*(1+n)-i*i))/(o+n),M=0<=a?this.y0+l:this.y0-l;return t.x=s,t.y=M,t},inverse:function(t){var s,i,a,h,e,n,r;return t.x-=this.x0,t.y-=this.y0,r=Math.PI*this.R,a=(s=t.x/r)*s+(i=t.y/r)*i,r=3*(i*i/(e=-2*(n=-Math.abs(i)*(1+a))+1+2*i*i+a*a)+(2*(h=n-2*i*i+s*s)*h*h/e/e/e-9*n*h/e/e)/27)/(n=(n-h*h/3/e)/e)/(n=2*Math.sqrt(-n/3)),1<Math.abs(r)&&(r=0<=r?1:-1),r=Math.acos(r)/3,n=0<=t.y?(-n*Math.cos(r+Math.PI/3)-h/3/e)*Math.PI:-(-n*Math.cos(r+Math.PI/3)-h/3/e)*Math.PI,r=Math.abs(s)<b?this.long0:S(this.long0+Math.PI*(a-1+Math.sqrt(1+2*(s*s-i*i)+a*a))/2/s),t.x=r,t.y=n,t},names:["Van_der_Grinten_I","VanDerGrinten","vandg"]},Rs={init:function(){this.sin_p12=Math.sin(this.lat0),this.cos_p12=Math.cos(this.lat0)},forward:function(t){var s,i,a,h,e,n,r,o=t.x,l=t.y,M=Math.sin(t.y),c=Math.cos(t.y),o=S(o-this.long0);return this.sphere?Math.abs(this.sin_p12-1)<=b?(t.x=this.x0+this.a*(g-l)*Math.sin(o),t.y=this.y0-this.a*(g-l)*Math.cos(o)):Math.abs(this.sin_p12+1)<=b?(t.x=this.x0+this.a*(g+l)*Math.sin(o),t.y=this.y0+this.a*(g+l)*Math.cos(o)):(h=this.sin_p12*M+this.cos_p12*c*Math.cos(o),n=(h=Math.acos(h))?h/Math.sin(h):1,t.x=this.x0+this.a*n*c*Math.sin(o),t.y=this.y0+this.a*n*(this.cos_p12*M-this.sin_p12*c*Math.cos(o))):(n=Yt(this.es),s=$t(this.es),a=ts(this.es),r=ss(this.es),Math.abs(this.sin_p12-1)<=b?(e=this.a*P(n,s,a,r,g),i=this.a*P(n,s,a,r,l),t.x=this.x0+(e-i)*Math.sin(o),t.y=this.y0-(e-i)*Math.cos(o)):Math.abs(this.sin_p12+1)<=b?(e=this.a*P(n,s,a,r,g),i=this.a*P(n,s,a,r,l),t.x=this.x0+(e+i)*Math.sin(o),t.y=this.y0+(e+i)*Math.cos(o)):(n=M/c,s=is(this.a,this.e,this.sin_p12),a=is(this.a,this.e,M),r=Math.atan((1-this.es)*n+this.es*s*this.sin_p12/(a*c)),e=0===(l=Math.atan2(Math.sin(o),this.cos_p12*Math.tan(r)-this.sin_p12*Math.cos(o)))?Math.asin(this.cos_p12*Math.sin(r)-this.sin_p12*Math.cos(r)):Math.abs(Math.abs(l)-Math.PI)<=b?-Math.asin(this.cos_p12*Math.sin(r)-this.sin_p12*Math.cos(r)):Math.asin(Math.sin(o)*Math.cos(r)/Math.sin(l)),i=this.e*this.sin_p12/Math.sqrt(1-this.es),M=this.e*this.cos_p12*Math.cos(l)/Math.sqrt(1-this.es),t.x=this.x0+(h=s*e*(1-(n=e*e)*(a=M*M)*(1-a)/6+(c=n*e)/8*(o=i*M)*(1-2*a)+(r=c*e)/120*(a*(4-7*a)-3*i*i*(1-7*a))-r*e/48*o))*Math.sin(l),t.y=this.y0+h*Math.cos(l))),t},inverse:function(t){var s,i,a,h,e,n,r,o;return t.x-=this.x0,t.y-=this.y0,this.sphere?(o=Math.sqrt(t.x*t.x+t.y*t.y))>2*g*this.a?void 0:(h=o/this.a,r=Math.sin(h),h=Math.cos(h),s=this.long0,Math.abs(o)<=b?i=this.lat0:(i=I(h*this.sin_p12+t.y*r*this.cos_p12/o),n=Math.abs(this.lat0)-g,s=S(Math.abs(n)<=b?0<=this.lat0?this.long0+Math.atan2(t.x,-t.y):this.long0-Math.atan2(-t.x,t.y):this.long0+Math.atan2(t.x*r,o*this.cos_p12*h-t.y*this.sin_p12*r))),t.x=s,t.y=i,t):(n=Yt(this.es),h=$t(this.es),r=ts(this.es),e=ss(this.es),Math.abs(this.sin_p12-1)<=b?(a=this.a*P(n,h,r,e,g),o=Math.sqrt(t.x*t.x+t.y*t.y),i=as((a-o)/this.a,n,h,r,e),s=S(this.long0+Math.atan2(t.x,-1*t.y))):Math.abs(this.sin_p12+1)<=b?(a=this.a*P(n,h,r,e,g),o=Math.sqrt(t.x*t.x+t.y*t.y),i=as((o-a)/this.a,n,h,r,e),s=S(this.long0+Math.atan2(t.x,t.y))):(o=Math.sqrt(t.x*t.x+t.y*t.y),a=Math.atan2(t.x,t.y),n=is(this.a,this.e,this.sin_p12),h=Math.cos(a),e=-(r=this.e*this.cos_p12*h)*r/(1-this.es),r=3*this.es*(1-e)*this.sin_p12*this.cos_p12*h/(1-this.es),r=1-e*(n=(o=o/n)-e*(1+e)*Math.pow(o,3)/6-r*(1+3*e)*Math.pow(o,4)/24)*n/2-o*n*n*n/6,e=Math.asin(this.sin_p12*Math.cos(n)+this.cos_p12*Math.sin(n)*h),s=S(this.long0+Math.asin(Math.sin(a)*Math.sin(n)/Math.cos(e))),o=Math.sin(e),i=Math.atan2((o-this.es*r*this.sin_p12)*Math.tan(e),o*(1-this.es))),t.x=s,t.y=i,t)},names:["Azimuthal_Equidistant","aeqd"]},Bs={init:function(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0)},forward:function(t){var s,i,a,h=t.x,e=t.y,h=S(h-this.long0),n=Math.sin(e),e=Math.cos(e),r=Math.cos(h);return(0<(s=this.sin_p14*n+this.cos_p14*e*r)||Math.abs(s)<=b)&&(i=+this.a*e*Math.sin(h),a=this.y0+ +this.a*(this.cos_p14*n-this.sin_p14*e*r)),t.x=i,t.y=a,t},inverse:function(t){var s,i,a,h,e,n;return t.x-=this.x0,t.y-=this.y0,s=Math.sqrt(t.x*t.x+t.y*t.y),a=I(s/this.a),i=Math.sin(a),a=Math.cos(a),e=this.long0,Math.abs(s)<=b?n=this.lat0:(n=I(a*this.sin_p14+t.y*i*this.cos_p14/s),h=Math.abs(this.lat0)-g,e=Math.abs(h)<=b?S(0<=this.lat0?this.long0+Math.atan2(t.x,-t.y):this.long0-Math.atan2(-t.x,t.y)):S(this.long0+Math.atan2(t.x*i,s*this.cos_p14*a-t.y*this.sin_p14*i))),t.x=e,t.y=n,t},names:["ortho"]},Ts=1,Ds=2,Fs=3,Us=4,Qs=5,Ws=6,O=1,j=2,G=3,Xs=4,Hs={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Quadrilateralized Spherical Cube",this.lat0>=g-N/2?this.face=Qs:this.lat0<=-(g-N/2)?this.face=Ws:Math.abs(this.long0)<=N?this.face=Ts:Math.abs(this.long0)<=g+N?this.face=0<this.long0?Ds:Us:this.face=Fs,0!==this.es&&(this.one_minus_f=1-(this.a-this.b)/this.a,this.one_minus_f_squared=this.one_minus_f*this.one_minus_f)},forward:function(t){var s,i,a,h,e,n,r={x:0,y:0},o={value:0};return t.x-=this.long0,e=0!==this.es?Math.atan(this.one_minus_f_squared*Math.tan(t.y)):t.y,a=t.x,this.face===Qs?(i=g-e,s=N<=a&&a<=g+N?(o.value=O,a-g):g+N<a||a<=-(g+N)?(o.value=j,0<a?a-c:a+c):-(g+N)<a&&a<=-N?(o.value=G,a+g):(o.value=Xs,a)):this.face===Ws?(i=g+e,s=N<=a&&a<=g+N?(o.value=O,-a+g):a<N&&-N<=a?(o.value=j,-a):a<-N&&-(g+N)<=a?(o.value=G,-a-g):(o.value=Xs,0<a?c-a:-a-c)):(this.face===Ds?a=l(a,+g):this.face===Fs?a=l(a,c):this.face===Us&&(a=l(a,-g)),h=Math.sin(e),e=Math.cos(e),n=Math.sin(a),a=e*Math.cos(a),e=e*n,n=h,this.face===Ts?s=nt(i=Math.acos(a),n,e,o):this.face===Ds?s=nt(i=Math.acos(e),n,-a,o):this.face===Fs?s=nt(i=Math.acos(-a),n,-e,o):this.face===Us?s=nt(i=Math.acos(-e),n,a,o):(i=s=0,o.value=O)),h=Math.atan(12/c*(s+Math.acos(Math.sin(s)*Math.cos(N))-g)),e=Math.sqrt((1-Math.cos(i))/(Math.cos(h)*Math.cos(h))/(1-Math.cos(Math.atan(1/Math.cos(s))))),o.value===j?h+=g:o.value===G?h+=c:o.value===Xs&&(h+=1.5*c),r.x=e*Math.cos(h),r.y=e*Math.sin(h),r.x=r.x*this.a+this.x0,r.y=r.y*this.a+this.y0,t.x=r.x,t.y=r.y,t},inverse:function(t){var s,i,a,h,e,n={lam:0,phi:0},r={value:0};return t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,a=Math.atan(Math.sqrt(t.x*t.x+t.y*t.y)),s=Math.atan2(t.y,t.x),0<=t.x&&t.x>=Math.abs(t.y)?r.value=O:0<=t.y&&t.y>=Math.abs(t.x)?(r.value=j,s-=g):t.x<0&&-t.x>=Math.abs(t.y)?(r.value=G,s=s<0?s+c:s-c):(r.value=Xs,s+=g),e=c/12*Math.tan(s),h=Math.sin(e)/(Math.cos(e)-1/Math.sqrt(2)),h=Math.atan(h),(a=1-(s=Math.cos(s))*s*(s=Math.tan(a))*s*(1-Math.cos(Math.atan(1/Math.cos(h)))))<-1?a=-1:1<a&&(a=1),this.face===Qs?(i=Math.acos(a),n.phi=g-i,n.lam=r.value===O?h+g:r.value===j?h<0?h+c:h-c:r.value===G?h-g:h):this.face===Ws?(i=Math.acos(a),n.phi=i-g,n.lam=r.value===O?-h+g:r.value===j?-h:r.value===G?-h-g:h<0?-h-c:c-h):(e=(s=a)*s,a=1<=(e+=(i=1<=e?0:Math.sqrt(1-e)*Math.sin(h))*i)?0:Math.sqrt(1-e),r.value===j?(e=a,a=-i,i=e):r.value===G?(a=-a,i=-i):r.value===Xs&&(e=a,a=i,i=-e),this.face===Ds?(e=s,s=-a,a=e):this.face===Fs?(s=-s,a=-a):this.face===Us&&(e=s,s=a,a=-e),n.phi=Math.acos(-i)-g,n.lam=Math.atan2(a,s),this.face===Ds?n.lam=l(n.lam,-g):this.face===Fs?n.lam=l(n.lam,-c):this.face===Us&&(n.lam=l(n.lam,+g))),0!==this.es&&(h=n.phi<0?1:0,r=Math.tan(n.phi),e=this.b/Math.sqrt(r*r+this.one_minus_f_squared),n.phi=Math.atan(Math.sqrt(this.a*this.a-e*e)/(this.one_minus_f*e)),h)&&(n.phi=-n.phi),n.lam+=this.long0,t.x=n.lam,t.y=n.phi,t},names:["Quadrilateralized Spherical Cube","Quadrilateralized_Spherical_Cube","qsc"]},Js=[[1,22199e-21,-715515e-10,31103e-10],[.9986,-482243e-9,-24897e-9,-13309e-10],[.9954,-83103e-8,-448605e-10,-9.86701e-7],[.99,-.00135364,-59661e-9,36777e-10],[.9822,-.00167442,-449547e-11,-572411e-11],[.973,-.00214868,-903571e-10,1.8736e-8],[.96,-.00305085,-900761e-10,164917e-11],[.9427,-.00382792,-653386e-10,-26154e-10],[.9216,-.00467746,-10457e-8,481243e-11],[.8962,-.00536223,-323831e-10,-543432e-11],[.8679,-.00609363,-113898e-9,332484e-11],[.835,-.00698325,-640253e-10,9.34959e-7],[.7986,-.00755338,-500009e-10,9.35324e-7],[.7597,-.00798324,-35971e-9,-227626e-11],[.7186,-.00851367,-701149e-10,-86303e-10],[.6732,-.00986209,-199569e-9,191974e-10],[.6213,-.010418,883923e-10,624051e-11],[.5722,-.00906601,182e-6,624051e-11],[.5322,-.00677797,275608e-9,624051e-11]],Ks=[[-520417e-23,.0124,121431e-23,-845284e-16],[.062,.0124,-1.26793e-9,4.22642e-10],[.124,.0124,5.07171e-9,-1.60604e-9],[.186,.0123999,-1.90189e-8,6.00152e-9],[.248,.0124002,7.10039e-8,-2.24e-8],[.31,.0123992,-2.64997e-7,8.35986e-8],[.372,.0124029,9.88983e-7,-3.11994e-7],[.434,.0123893,-369093e-11,-4.35621e-7],[.4958,.0123198,-102252e-10,-3.45523e-7],[.5571,.0121916,-154081e-10,-5.82288e-7],[.6176,.0119938,-241424e-10,-5.25327e-7],[.6769,.011713,-320223e-10,-5.16405e-7],[.7346,.0113541,-397684e-10,-6.09052e-7],[.7903,.0109107,-489042e-10,-104739e-11],[.8435,.0103431,-64615e-9,-1.40374e-9],[.8936,.00969686,-64636e-9,-8547e-9],[.9394,.00840947,-192841e-9,-42106e-10],[.9761,.00616527,-256e-6,-42106e-10],[1,.00328947,-319159e-9,-42106e-10]],Vs=w/5,Zs=1/Vs,Ys={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.long0=this.long0||0,this.es=0,this.title=this.title||"Robinson"},forward:function(t){var s=S(t.x-this.long0),i=Math.abs(t.y),a=Math.floor(i*Vs),s=(a<0?a=0:18<=a&&(a=17),{x:hs(Js[a],i=w*(i-Zs*a))*s,y:hs(Ks[a],i)});return t.y<0&&(s.y=-s.y),s.x=s.x*this.a*.8487+this.x0,s.y=s.y*this.a*1.3523+this.y0,s},inverse:function(t){var s={x:(t.x-this.x0)/(.8487*this.a),y:Math.abs(t.y-this.y0)/(1.3523*this.a)};if(1<=s.y)s.x/=Js[18][0],s.y=t.y<0?-g:g;else{var i=Math.floor(18*s.y);for(i<0?i=0:18<=i&&(i=17);;)if(Ks[i][0]>s.y)--i;else{if(!(Ks[i+1][0]<=s.y))break;++i}var a=Ks[i],h=function(t,s,i,a){for(var h=s;a;--a){var e=t(h);if(h-=e,Math.abs(e)<i)break}return h}(function(t){return(hs(a,t)-s.y)/(a[1]+t*(2*a[2]+3*t*a[3]))},5*(s.y-a[0])/(Ks[i+1][0]-a[0]),b,100);s.x/=hs(Js[i],h),s.y=(5*i+h)*v,t.y<0&&(s.y=-s.y)}return s.x=S(s.x+this.long0),s},names:["Robinson","robin"]},$s={init:function(){this.name="geocent"},forward:function(t){return Q(t,this.es,this.a)},inverse:function(t){return W(t,this.es,this.a,this.b)},names:["Geocentric","geocentric","geocent","Geocent"]},ti=0,si=1,ii=2,ai=3,hi={h:{def:1e5,num:!0},azi:{def:0,num:!0,degrees:!0},tilt:{def:0,num:!0,degrees:!0},long0:{def:0,num:!0},lat0:{def:0,num:!0}},ei={init:function(){if(Object.keys(hi).forEach(function(t){if(void 0===this[t])this[t]=hi[t].def;else{if(hi[t].num&&isNaN(this[t]))throw new Error("Invalid parameter value, must be numeric "+t+" = "+this[t]);hi[t].num&&(this[t]=parseFloat(this[t]))}hi[t].degrees&&(this[t]=this[t]*v)}.bind(this)),Math.abs(Math.abs(this.lat0)-g)<b?this.mode=this.lat0<0?si:ti:Math.abs(this.lat0)<b?this.mode=ii:(this.mode=ai,this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0)),this.pn1=this.h/this.a,this.pn1<=0||1e10<this.pn1)throw new Error("Invalid height");this.p=1+this.pn1,this.rp=1/this.p,this.h1=1/this.pn1,this.pfact=(this.p+1)*this.h1,this.es=0;var t=this.tilt,s=this.azi;this.cg=Math.cos(s),this.sg=Math.sin(s),this.cw=Math.cos(t),this.sw=Math.sin(t)},forward:function(t){t.x-=this.long0;var s,i,a,h,e=Math.sin(t.y),n=Math.cos(t.y),r=Math.cos(t.x);switch(this.mode){case ai:i=this.sinph0*e+this.cosph0*n*r;break;case ii:i=n*r;break;case si:i=-e;break;case ti:i=e}switch(s=(i=this.pn1/(this.p-i))*n*Math.sin(t.x),this.mode){case ai:i*=this.cosph0*e-this.sinph0*n*r;break;case ii:i*=e;break;case ti:i*=-n*r;break;case si:i*=n*r}return h=1/((a=i*this.cg+s*this.sg)*this.sw*this.h1+this.cw),s=(s*this.cg-i*this.sg)*this.cw*h,i=a*h,t.x=s*this.a,t.y=i*this.a,t},inverse:function(t){t.x/=this.a,t.y/=this.a;var s={x:t.x,y:t.y},i=1/(this.pn1-t.y*this.sw),a=this.pn1*t.x*i,i=this.pn1*t.y*this.cw*i,h=(t.x=a*this.cg+i*this.sg,t.y=i*this.cg-a*this.sg,d(t.x,t.y));if(Math.abs(h)<b)s.x=0,s.y=t.y;else{var e,n=1-h*h*this.pfact;switch(n=(this.p-Math.sqrt(n))/(this.pn1/h+h/this.pn1),e=Math.sqrt(1-n*n),this.mode){case ai:s.y=Math.asin(e*this.sinph0+t.y*n*this.cosph0/h),t.y=(e-this.sinph0*Math.sin(s.y))*h,t.x*=n*this.cosph0;break;case ii:s.y=Math.asin(t.y*n/h),t.y=e*h,t.x*=n;break;case ti:s.y=Math.asin(e),t.y=-t.y;break;case si:s.y=-Math.asin(e)}s.x=Math.atan2(t.x,t.y)}return t.x=s.x+this.long0,t.y=s.y,t},names:["Tilted_Perspective","tpers"]},ni={init:function(){if(this.flip_axis="x"===this.sweep?1:0,this.h=Number(this.h),this.radius_g_1=this.h/this.a,this.radius_g_1<=0||1e10<this.radius_g_1)throw new Error;var t,s;this.radius_g=1+this.radius_g_1,this.C=this.radius_g*this.radius_g-1,0!==this.es?(s=1/(t=1-this.es),this.radius_p=Math.sqrt(t),this.radius_p2=t,this.radius_p_inv2=s,this.shape="ellipse"):(this.radius_p=1,this.radius_p2=1,this.radius_p_inv2=1,this.shape="sphere"),this.title||(this.title="Geostationary Satellite View")},forward:function(t){var s,i=t.x,a=t.y;if(i-=this.long0,"ellipse"===this.shape){var a=Math.atan(this.radius_p2*Math.tan(a)),h=this.radius_p/d(this.radius_p*Math.cos(a),Math.sin(a)),e=h*Math.cos(i)*Math.cos(a),n=h*Math.sin(i)*Math.cos(a),h=h*Math.sin(a);if((this.radius_g-e)*e-n*n-h*h*this.radius_p_inv2<0)return t.x=Number.NaN,t.y=Number.NaN,t;s=this.radius_g-e,this.flip_axis?(t.x=this.radius_g_1*Math.atan(n/d(h,s)),t.y=this.radius_g_1*Math.atan(h/s)):(t.x=this.radius_g_1*Math.atan(n/s),t.y=this.radius_g_1*Math.atan(h/d(n,s)))}else"sphere"===this.shape&&(s=Math.cos(a),e=Math.cos(i)*s,n=Math.sin(i)*s,h=Math.sin(a),s=this.radius_g-e,this.flip_axis?(t.x=this.radius_g_1*Math.atan(n/d(h,s)),t.y=this.radius_g_1*Math.atan(h/s)):(t.x=this.radius_g_1*Math.atan(n/s),t.y=this.radius_g_1*Math.atan(h/d(n,s))));return t.x=t.x*this.a,t.y=t.y*this.a,t},inverse:function(t){var s,i,a=-1,h=0,e=0;if(t.x=t.x/this.a,t.y=t.y/this.a,"ellipse"===this.shape){this.flip_axis?(e=Math.tan(t.y/this.radius_g_1),h=Math.tan(t.x/this.radius_g_1)*d(1,e)):(h=Math.tan(t.x/this.radius_g_1),e=Math.tan(t.y/this.radius_g_1)*d(1,h));var n,r=e/this.radius_p,r=h*h+r*r+a*a;if((s=(n=2*this.radius_g*a)*n-4*r*this.C)<0)return t.x=Number.NaN,t.y=Number.NaN,t;i=(-n-Math.sqrt(s))/(2*r),a=this.radius_g+i*a,h*=i,e*=i,t.x=Math.atan2(h,a),t.y=Math.atan(e*Math.cos(t.x)/a),t.y=Math.atan(this.radius_p_inv2*Math.tan(t.y))}else if("sphere"===this.shape){if(this.flip_axis?(e=Math.tan(t.y/this.radius_g_1),h=Math.tan(t.x/this.radius_g_1)*Math.sqrt(1+e*e)):(h=Math.tan(t.x/this.radius_g_1),e=Math.tan(t.y/this.radius_g_1)*Math.sqrt(1+h*h)),(s=(n=2*this.radius_g*a)*n-4*(r=h*h+e*e+a*a)*this.C)<0)return t.x=Number.NaN,t.y=Number.NaN,t;i=(-n-Math.sqrt(s))/(2*r),a=this.radius_g+i*a,h*=i,e*=i,t.x=Math.atan2(h,a),t.y=Math.atan(e*Math.cos(t.x)/a)}return t.x=t.x+this.long0,t},names:["Geostationary Satellite View","Geostationary_Satellite","geos"]},ri=1.340264,oi=-.081106,li=893e-6,Mi=.003796,ci=Math.sqrt(3)/2,ui={init:function(){this.es=0,this.long0=void 0!==this.long0?this.long0:0},forward:function(t){var s=S(t.x-this.long0),i=t.y,i=Math.asin(ci*Math.sin(i)),a=i*i,h=a*a*a;return t.x=s*Math.cos(i)/(ci*(ri+3*oi*a+h*(7*li+9*Mi*a))),t.y=i*(ri+oi*a+h*(li+Mi*a)),t.x=this.a*t.x+this.x0,t.y=this.a*t.y+this.y0,t},inverse:function(t){t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a;for(var s,i,a,h=t.y,e=0;e<12&&(h-=a=(h*(ri+oi*(s=h*h)+(i=s*s*s)*(li+Mi*s))-t.y)/(ri+3*oi*s+i*(7*li+9*Mi*s)),!(Math.abs(a)<1e-9));++e);return t.x=ci*t.x*(ri+3*oi*(s=h*h)+(i=s*s*s)*(7*li+9*Mi*s))/Math.cos(h),t.y=Math.asin(Math.sin(h)/ci),t.x=S(t.x+this.long0),t},names:["eqearth","Equal Earth","Equal_Earth"]},fi=1e-10,di={init:function(){var t;if(this.phi1=this.lat1,Math.abs(this.phi1)<fi)throw new Error;this.es?(this.en=Wt(this.es),this.m1=f(this.phi1,this.am1=Math.sin(this.phi1),t=Math.cos(this.phi1),this.en),this.am1=t/(Math.sqrt(1-this.es*this.am1*this.am1)*this.am1),this.inverse=ot,this.forward=rt):(Math.abs(this.phi1)+fi>=g?this.cphi1=0:this.cphi1=1/Math.tan(this.phi1),this.inverse=Mt,this.forward=lt)},names:["bonne","Bonne (Werner lat_1=90)"]};return t.defaultDatum="WGS84",t.Proj=p,t.WGS84=new t.Proj("WGS84"),t.Point=n,t.toPoint=Ut,t.defs=e,t.nadgrid=function(t,s){var s=new DataView(s),i=11!==(i=s).getInt32(8,!1)&&(11!==i.getInt32(8,!0)&&console.warn("Failed to detect nadgrid endian-ness, defaulting to little-endian"),!0),a=(h=i,{nFields:(a=s).getInt32(8,h),nSubgridFields:a.getInt32(24,h),nSubgrids:a.getInt32(40,h),shiftType:F(a,56,64).trim(),fromSemiMajorAxis:a.getFloat64(120,h),fromSemiMinorAxis:a.getFloat64(136,h),toSemiMajorAxis:a.getFloat64(152,h),toSemiMinorAxis:a.getFloat64(168,h)}),h={header:a,subgrids:U(s,a,i)};return es[t]=h},t.transform=V,t.mgrs=u,t.version="2.14.0",(u=t).Proj.projections.add(ds),u.Proj.projections.add(ms),u.Proj.projections.add(ps),u.Proj.projections.add(_s),u.Proj.projections.add(xs),u.Proj.projections.add(gs),u.Proj.projections.add(vs),u.Proj.projections.add(ws),u.Proj.projections.add(Ns),u.Proj.projections.add(Cs),u.Proj.projections.add(Ss),u.Proj.projections.add(Ps),u.Proj.projections.add(Es),u.Proj.projections.add(ks),u.Proj.projections.add(Is),u.Proj.projections.add(qs),u.Proj.projections.add(As),u.Proj.projections.add(Os),u.Proj.projections.add(js),u.Proj.projections.add(Gs),u.Proj.projections.add(Ls),u.Proj.projections.add(zs),u.Proj.projections.add(Rs),u.Proj.projections.add(Bs),u.Proj.projections.add(Hs),u.Proj.projections.add(Ys),u.Proj.projections.add($s),u.Proj.projections.add(ei),u.Proj.projections.add(ni),u.Proj.projections.add(ui),u.Proj.projections.add(di),t});