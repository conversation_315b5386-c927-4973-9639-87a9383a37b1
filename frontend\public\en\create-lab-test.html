<!DOCTYPE html>
<!-- Create Lab Test Page -->
<html lang="en" dir="ltr">
<head>
    <meta charset="utf-8">
    <title>Create Lab Test - Canada.ca</title>
    <meta name="description" content="Create a new laboratory test type">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- GCWeb and WET-BOEW Stylesheets -->
    <link rel="stylesheet" href="/libraries/GCWeb/css/theme.min.css">
    <link rel="stylesheet" href="/assets/css/nav.css">
</head>

<body vocab="http://schema.org/" typeof="WebPage">
    <!-- Skip Links - Required for GC Accessibility Standards -->
    <nav>
        <ul id="wb-tphp">
            <li class="wb-slc">
                <a class="wb-sl" href="#wb-cont">Skip to main content</a>
            </li>
            <li class="wb-slc visible-sm visible-md visible-lg">
                <a class="wb-sl" href="#wb-info">Skip to About this site</a>
            </li>
        </ul>
    </nav>

    <!-- Site Header Component (loaded via AJAX) -->
    <div data-wb-ajax='{ "url": "/components/en/header.html", "type": "replace" }'></div>
    <div id="navbar"></div>

    <!-- Breadcrumb Navigation -->
    <nav id="wb-bc" property="breadcrumb">
        <h2>You are here:</h2>
        <div class="container">
            <ol class="breadcrumb">
                <li><a href="/en/home.html">Home</a></li>
                <li><a href="/en/manage-test-types.html">Manage Test Types</a></li>
                <li>Create Test Type</li>
            </ol>
        </div>
    </nav>

    <!-- Main Content Section - Test Type Creation Form -->
    <main role="main" property="mainContentOfPage" class="container" id="wb-cont">
        <h1 property="name">Create Lab Test Type</h1>
        <p>Create a new laboratory test type. All fields marked with an asterisk (*) are required.</p>

        <!-- Page-level Messages Section -->
        <div data-wb-ajax='{ "url": "/components/en/message-containers.html", "type": "replace" }'></div>

        <!-- Test Type Creation Form Section -->
        <div class="wb-frmvld">
            <form id="create-test-form" action="#" method="get" novalidate aria-labelledby="wb-cont">
            <div class="row">
                <div class="col-md-12">
                    <!-- Test Status Configuration -->
                    <div class="form-group">
                        <label for="test-status"><span class="field-name">Test Status</span></label>
                        <div class="checkbox gc-chckbxrdio">
                            <input type="checkbox" id="test-status" name="test-status" checked aria-describedby="test-status-help">
                            <label for="test-status">Active</label>
                        </div>
                        <div id="test-status-help" class="help-block">
                            <p class="muted">Active test types are available for requisitions. Uncheck for inactive.</p>
                        </div>
                    </div>

                    <!-- Test Name Field -->
                    <div class="form-group">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="test-name" class="required">
                                    <span class="field-name">Test Name</span>
                                    <strong class="required">(required)</strong>
                                </label>
                                <input type="text"
                                       id="test-name"
                                       name="test-name"
                                       class="form-control input-lg"
                                       maxlength="50"
                                       required
                                       aria-describedby="test-name-help">
                                <div id="test-name-help" class="help-block">
                                    <p class="muted">Enter a unique name for the test type (maximum 50 characters).</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Description Field -->
                    <div class="form-group">
                        <label for="test-description" class="required">
                            <span class="field-name">Test Description</span>
                            <strong class="required">(required)</strong>
                        </label>
                        <textarea id="test-description"
                                  name="test-description"
                                  class="form-control input-lg"
                                  style="width: 45% !important;"
                                  rows="6"
                                  maxlength="200"
                                  required
                                  aria-describedby="test-description-help"></textarea>
                        <div id="test-description-help" class="help-block">
                            <p class="muted">Provide a description of the test type (maximum 200 characters).</p>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-group mrgn-bttm-xl">
                        <button type="submit" class="btn btn-primary mrgn-rght-md" id="save-button">
                            <span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span>
                            Save
                        </button>
                        <button type="button" class="btn btn-default" id="cancel-button">
                            <span class="glyphicon glyphicon-remove" aria-hidden="true"></span>
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
            </form>
        </div>
    </main>

    <!-- Site Footer Component (loaded via AJAX) -->
    <div data-wb-ajax='{ "url": "/components/en/footer.html", "type": "replace" }'></div>

    <!-- Core Library Scripts -->
    <script src="/libraries/wet-boew/js/jquery/2.2.4/jquery.min.js"></script>
    <script src="/libraries/wet-boew/js/wet-boew.min.js"></script>
    <script src="/libraries/GCWeb/js/theme.min.js"></script>
    <!-- Microsoft Authentication Library -->
    <script src="https://alcdn.msauth.net/browser/2.38.0/js/msal-browser.min.js"></script>

    <!-- Header-related Functionality -->
    <script src="/assets/js/core/i18n/language-switcher.js"></script>
    <script type="module" src="/assets/js/core/auth/navbar.js"></script>
    <!-- Authentication Middleware (required for authenticated pages) -->
    <script type="module" src="/assets/js/core/auth/authentication.js"></script>
    <!-- Create Test Page Functionality -->
    <script type="module" src="/assets/js/pages/lab/create-test.js"></script>
</body>
</html>