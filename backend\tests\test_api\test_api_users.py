"""Tests for the Users API endpoints.

TO DO: Update most endpoints when functions change to incorporate lab_id

Test Coverage:
1. POST /users/
    - test_create_user_unauthorized
    - test_create_user_admin
    - test_create_user_missing_required_fields (email, password)

2. GET /users/
    - test_list_users_admin
    - test_list_users_unauthorized

3. GET /users/{user_id}
    - test_get_user_by_id_admin
    - test_get_user_by_id_unauthorized
    - test_get_nonexistent_user
    - test_get_user_by_id_server_error
    - test_get_user_by_id_with_different_roles

4. GET /users/me
    - test_get_current_user

5. PUT /users/{user_id}
    - test_update_user
    - test_update_nonexistent_user
    - test_update_user_unauthorized

6. DELETE /users/{user_id}
    - test_delete_user_unauthorized
    - test_delete_user_server_error
    - test_delete_user_success

7. Role-based access control
    - test_get_user_by_id_with_different_roles
"""

import pytest
import uuid
from unittest.mock import patch

from tests.test_utils.helpers import TestHelpers
from tests.test_utils.constants import (
    USER_TEST_CASES,
    ROLE_ACCESS_CONFIGURATIONS
)
from app.common.constants import ERROR_MESSAGES, UserRole
from app.models.staff import Staff
from app.models.user import User
from tests.test_utils.verification import (
    UserVerifier,
    verify_api_error_response
)

class TestUsers:
    #
    # POST /users/ endpoint tests
    #
    def test_create_user_unauthorized(self, client, user_token, test_staff_user):
        """Test that non-admin users cannot create users"""
        response = client.post(
            "/users/", 
            headers=TestHelpers.Auth.get_headers(user_token),
            json=USER_TEST_CASES["create"]["valid"]
        )

        verify_api_error_response(
            response, 
            403, 
            ERROR_MESSAGES["user"]["unauthorized_access"]
        )

    def test_create_user_admin(self, client, admin_token, test_staff_admin):
        """Test that admin users can create users"""
        response = client.post(
            "/users/", 
            headers=TestHelpers.Auth.get_headers(admin_token), 
            json=USER_TEST_CASES["create"]["valid"]
        )
        
        assert response.status_code == 200
        UserVerifier.verify_response(response.json(), USER_TEST_CASES["create"]["valid"])

    def test_create_user_missing_required_fields(self, client, admin_token, test_staff_admin):
        """Test that user creation requires all mandatory fields"""
        headers = TestHelpers.Auth.get_headers(admin_token)
        
        # Test missing email
        response = client.post(
            "/users/", 
            headers=headers, 
            json=USER_TEST_CASES["create"]["missing_email"]
        )
        verify_api_error_response(response, 422, None)

        # Test missing password
        response = client.post(
            "/users/", 
            headers=headers, 
            json=USER_TEST_CASES["create"]["missing_password"]
        )
        verify_api_error_response(response, 422, None)

    #
    # GET /users/ endpoint tests
    #
    def test_list_users_admin(self, client, admin_token, test_staff_admin):
        """Test that admin users can list all users"""
        response = client.get(
            "/users/", 
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        assert isinstance(response.json(), list)
        if response.json():
            UserVerifier.verify_common_fields(response.json()[0])

    def test_list_users_unauthorized(self, client, user_token, test_staff_user):
        """Test that non-admin users cannot list all users"""
        response = client.get(
            "/users/", 
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        verify_api_error_response(
            response, 
            403, 
            ERROR_MESSAGES["user"]["unauthorized_access"]
        )

    #
    # GET /users/{user_id} endpoint tests
    #
    def test_get_user_by_id_admin(self, client, test_user, test_staff_admin, admin_token, db):
        """Test that admin users can get user details by ID"""
        # Ensure test_user is in the same lab as admin
        staff_user = Staff(
            user_id=test_user.user_id,
            role=UserRole.SCIENTIST
        )
        db.add(staff_user)
        db.commit()

        response = client.get(
            f"/users/{test_user.user_id}",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        UserVerifier.verify_response(response.json(), {"email": test_user.email})

    def test_get_user_by_id_unauthorized(self, client, user_token, test_user, test_staff_user):
        """Test that non-admin users cannot get user details by ID"""
        response = client.get(
            f"/users/{test_user.user_id}", 
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        verify_api_error_response(
            response, 
            403, 
            ERROR_MESSAGES["user"]["unauthorized_access"]
        )

    def test_get_nonexistent_user(self, client, admin_token, test_staff_admin):
        """Test handling of requests for non-existent users"""
        fake_user_id = str(uuid.uuid4())
        response = client.get(
            f"/users/{fake_user_id}", 
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        verify_api_error_response(
            response, 
            404, 
            ERROR_MESSAGES["user"]["not_found"]
        )

    #
    # GET /users/me endpoint tests
    #
    def test_get_current_user(self, client, user_token):
        """Test getting current user info"""
        response = client.get(
            "/users/me",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        assert response.status_code == 200
        UserVerifier.verify_response(response.json())

    #
    # PUT /users/{user_id} endpoint tests
    #
    def test_update_user(self, client, admin_token, test_user):
        """Test updating user info"""
        update_data = {
            "is_active": False
        }
    
        response = client.put(
            f"/users/{test_user.user_id}",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=update_data
        )
    
        assert response.status_code == 200
        UserVerifier.verify_response(response.json(), update_data)

    def test_update_nonexistent_user(self, client, admin_token, test_staff_admin):
        """Test handling of update requests for non-existent users"""
        fake_user_id = str(uuid.uuid4())
        response = client.put(
            f"/users/{fake_user_id}",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=USER_TEST_CASES["update"]["valid"]
        )
        verify_api_error_response(
            response,
            404,
            ERROR_MESSAGES["user"]["not_found"]
        )

    def test_update_user_unauthorized(self, client, user_token, test_user, test_staff_user):
        """Test that non-admin users cannot update users"""
        response = client.put(
            f"/users/{test_user.user_id}", 
            headers=TestHelpers.Auth.get_headers(user_token),
            json={"is_active": False}
        )
        
        verify_api_error_response(
            response, 
            403, 
            ERROR_MESSAGES["user"]["unauthorized_access"]
        )

    def test_get_user_by_id_with_different_roles(self, client, admin_token, user_token, lab_personnel_token, test_user):
        """Test access control for different roles"""
        # Admin should be able to access
        admin_response = client.get(
            f"/users/{test_user.user_id}", 
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        assert admin_response.status_code == 200
        UserVerifier.verify_response(admin_response.json(), {"email": test_user.email})
        
        # Regular user should not be able to access
        user_response = client.get(
            f"/users/{test_user.user_id}", 
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        verify_api_error_response(
            user_response, 
            403, 
            ERROR_MESSAGES["user"]["unauthorized_access"]
        )
        
        # Lab personnel should not be able to access
        personnel_response = client.get(
            f"/users/{test_user.user_id}", 
            headers=TestHelpers.Auth.get_headers(lab_personnel_token)
        )
        verify_api_error_response(
            personnel_response, 
            403, 
            ERROR_MESSAGES["user"]["unauthorized_access"]
        )

    # Test server error
    def test_get_user_by_id_server_error(self, client, admin_token, test_user):
        """Test handling of server error when getting user by ID"""
        with patch("app.crud.user.get_user", side_effect=Exception("Server error")):
            response = client.get(
                f"/users/{test_user.user_id}",
                headers=TestHelpers.Auth.get_headers(admin_token)
            )
        
        verify_api_error_response(
            response,
            500,
            "Server error"
        )

    # Test unauthorized access
    def test_delete_user_unauthorized(self, client, user_token, test_user):
        """Test that non-admin users cannot delete users"""
        response = client.delete(
            f"/users/{test_user.user_id}", 
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        verify_api_error_response(
            response, 
            403, 
            ERROR_MESSAGES["user"]["unauthorized_access"]
        )

    # Test server error
    def test_delete_user_server_error(self, client, admin_token, test_user):
        """Test handling of server error when deleting user"""
        with patch("app.crud.user.delete_user", side_effect=Exception("Server error")):
            response = client.delete(
                f"/users/{test_user.user_id}",
                headers=TestHelpers.Auth.get_headers(admin_token)
            )
        
        verify_api_error_response(
            response,
            500,
            "Server error"
        )

    def test_delete_user_success(self, client, admin_token, test_user):
        """Test successful user deletion by admin"""
        response = client.delete(
            f"/users/{test_user.user_id}",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        assert response.json() == {
            "message": ERROR_MESSAGES["user"]["delete_success"],
            "user_id": str(test_user.user_id)
        }
