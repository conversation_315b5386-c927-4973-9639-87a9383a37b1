{"test_requisitions.py::TestRequisitions::test_archive_requisition_admin": true, "test_requisitions.py::TestRequisitions::test_create_requisition_scientist": true, "test_requisitions.py::TestRequisitions::test_create_requisition_unauthorized": true, "test_requisitions.py::TestRequisitions::test_list_requisitions": true, "test_requisitions.py::TestRequisitions::test_list_requisitions_with_status_filter": true, "test_requisitions.py::TestRequisitions::test_get_requisition_by_id": true, "test_requisitions.py::TestRequisitions::test_get_nonexistent_requisition": true, "test_requisitions.py::TestRequisitions::test_update_requisition_lab_admin": true, "test_requisitions.py::TestRequisitions::test_update_requisition_unauthorized": true, "test_users.py::TestUsers::test_create_user_unauthorized": true, "test_users.py::TestUsers::test_create_user_admin": true, "test_users.py::TestUsers::test_list_users": true, "test_users.py::TestUsers::test_get_user_by_id": true, "test_users.py::TestUsers::test_get_current_user": true, "test_users.py::TestUsers::test_update_user": true}