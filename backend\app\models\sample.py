"""
Model for Sample

This script describes the DB model for Sample
"""
from sqlalchemy import Column, String, DateTime, Numeric
from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, Numeric
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func
import uuid
from .base import Base

class Sample(Base):
    __tablename__ = "samples"

    sample_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sms_number: Mapped[Optional[str]] = mapped_column(String)
    external_reference: Mapped[Optional[str]] = mapped_column(String)
    station_id: Mapped[Optional[str]] = mapped_column(String)
    depth_top: Mapped[Optional[float]] = mapped_column(Numeric)
    depth_bottom: Mapped[Optional[float]] = mapped_column(Numeric)
    sample_type: Mapped[Optional[str]] = mapped_column(String)
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, onupdate=func.now())