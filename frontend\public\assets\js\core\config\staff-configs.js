/*
 * Staff Configuration
 *
 * 1. FUNCTIONAL SETTINGS - Staff-specific technical configurations
 * 2. UI TEXT - Staff UI text with bilingual support
 * 3. MESSAGES - Staff-specific messages organized by type and category
 * 
 */

export const STAFF_CONFIGS = {
    // ===== 1. FUNCTIONAL SETTINGS =====
    // Staff-specific technical configurations
    // Form lengths and navigation delays use GLOBAL_CONFIGS standards

    // ===== 2. UI TEXT (Bilingual) =====
    // Staff UI text organized by component/feature
    ui: {
        // Staff table configurations
        staffTable: {
            containerId: 'staff-table-container',
            tableId: 'staff-table',
            entityTerms: {
                en: 'staff members',
                fr: 'membres du personnel'
            },
            // Table configuration
            columns: [
                {
                    key: 'name',
                    header: { en: 'Name', fr: 'Nom' },
                    accessor: (row) => row.email ? row.email.split('@')[0] : null 
                },
                {
                    key: 'email',
                    header: { en: 'Email', fr: '<PERSON><PERSON><PERSON>' }
                },
                {
                    key: 'role',
                    header: { en: 'Role', fr: 'Rôle' },
                    formatter: 'roleDisplay'
                }
            ],
            tableOptions: {
                order: [[0, "asc"]]
            }
        }
    }

    // ===== 3. MESSAGES (Bilingual) =====
    // Staff-specific messages use global templates
    // Most messages use global templates with entity substitution:
    // - CRUD operations: global.messages.success/errors.templates.entity* (uses {entity} placeholder)
    // - Connection errors: global.messages.errors.server.connectionError
};
