"""
Tests for API functions on /auth

Test Coverage:
    1. test_select_lab_role_single_type : User with no lab_roles (aka 1 entry in staff table) tries to switch
    2. test_select_lab_role_wrong_lab : Lab Admin without lab_id (Right role, wrong lab)
    3. test_select_lab_role_wrong_role : Wrong role for right lab
    4. test_select_lab_role_right : Right lab for right role
    5. test_login_invalid_credentials : Test login with invalid credentials
    6. test_login_inactive_user : Test login with inactive user account
    7. test_login_missing_fields : Test login with missing required fields
    8. test_azure_login_invalid_token : Test login with invalid token
    9. test_azure_login_missing_fields : Test login with missing token
    10. test_azure_user : Proper token
"""
import pytest
import uuid

from unittest.mock import patch
from tests.test_utils.helpers import TestHelpers
from app.common.constants import ERROR_MESSAGES
from app.core.security import authenticate_msal_user
from tests.test_utils.verification import verify_api_error_response

class TestAuth:
    #####################
    # /select_lab_role TESTS
    #####################
    def test_select_lab_role_single_type(self, client, user_token):
        """User that is a single-type user tries to switch"""
        response = client.post(
            "/auth/select-lab-role",
            headers=TestHelpers.Auth.get_headers(user_token),
            data={"role": "scientist"}
        )

        verify_api_error_response(
            response, 
            403, 
            ERROR_MESSAGES["auth"]["single_type"]
        )
    
    def test_select_lab_role_wrong_lab(self, client, admin_multiple_labs_token):
        """User that is multi-type tries to log into the wrong lab"""
        temp_lab = uuid.uuid4()
        response = client.post(
            "/auth/select-lab-role", 
            headers=TestHelpers.Auth.get_headers(admin_multiple_labs_token),
            data={"role": "lab_personnel", "lab_id": str(temp_lab)}
        )
        
        verify_api_error_response(
            response, 
            403, 
            ERROR_MESSAGES["auth"]["wrong_combo"]
        )
    
    def test_select_lab_role_wrong_role(self, client, admin_multiple_labs_token):
        """User that is multi-type tries to log into the wrong lab"""
        response = client.post(
            "/auth/select-lab-role", 
            headers=TestHelpers.Auth.get_headers(admin_multiple_labs_token),
            data={"role": "scientist"}
        )
        
        verify_api_error_response(
            response, 
            403, 
            ERROR_MESSAGES["auth"]["wrong_combo"]
        )
    
    def test_select_lab_role_right(self, client, admin_multiple_labs_token, test_staff_multiple_roles):
        """User that is multi-type tries to log into the wrong lab"""
        response = client.post(
            "/auth/select-lab-role", 
            headers=TestHelpers.Auth.get_headers(admin_multiple_labs_token),
            data={"role": "lab_admin", "lab_id": str(test_staff_multiple_roles[0].lab_id)}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"

    def test_login_invalid_credentials(self, client):
        """Test login with invalid credentials"""
        response = client.post(
            "/auth/login",
            data={"username": "<EMAIL>", "password": "wrongpassword"}
        )
        
        verify_api_error_response(
            response,
            401,
            "Incorrect email or password"
        )

    def test_login_inactive_user(self, client, test_user):
        """Test login with inactive user account"""
        # Update the test_user to be inactive
        test_user.is_active = False
        
        response = client.post(
            "/auth/login",
            data={"username": test_user.email, "password": "password123"}
        )
        
        verify_api_error_response(
            response,
            403,
            "No valid roles"
        )

    def test_login_missing_fields(self, client):
        """Test login with missing required fields"""
        response = client.post(
            "/auth/login",
            data={}
        )
        
        verify_api_error_response(
            response,
            422,
            None
        )

    def test_azure_login_invalid_token(self, client):
        """Test login with invalid credentials"""
        response = client.post("/auth/azure-login", data={"azure_token": "some-invalid-token"})
        
        verify_api_error_response(response, 401, ERROR_MESSAGES["auth"]["credentials_invalid"])
    
    def test_azure_login_missing_fields(self, client):
        #Test login with missing data
        response = client.post("/auth/azure-login", data={})
        verify_api_error_response(response, 422, None)

    def test_azure_user(self, client, test_staff_admin, test_admin):
        """Test successful Azure login with proper token"""
        # Mock the _validate_azure_token function imported in auth.py
        with patch("app.api.auth._validate_azure_token", return_value=(
            {"oid": test_admin.azure_ad_id, "preferred_username": test_admin.email},
            test_admin.azure_ad_id,
            test_admin.email
        )):
            response = client.post("/auth/azure-login", data={"azure_token": "mock_token"})

        assert response.status_code == 200
        json_response = response.json()
        assert "access_token" in json_response
        assert json_response["token_type"] == "bearer"
