"""
Schemas for Staff

This script input schemas for Staff. These schema help define different Staff

This file contains the following schemas
    - StaffBase: The schema is used for the base of what all staff must have, user_id and a role
    - StaffCreate: The schema for creating a new staff
    - StaffType: The schema for normal staff. It has lab_id, staff_id + user_id and a role
"""

from pydantic import BaseModel, UUID4, ConfigDict, field_validator
from typing import Optional
from ..common.constants import UserRole

class StaffBase(BaseModel):
    user_id: UUID4
    role: UserRole

    @field_validator("role")
    def validate_role(cls, value) -> Optional[UserRole]:
        if not isinstance(value, UserRole):
            raise ValueError("Invalid role")
        return value

class StaffCreate(StaffBase):
    pass

class StaffType(StaffBase):
    staff_id: UUID4
    lab_id: Optional[UUID4] = None

    model_config = ConfigDict(from_attributes=True)