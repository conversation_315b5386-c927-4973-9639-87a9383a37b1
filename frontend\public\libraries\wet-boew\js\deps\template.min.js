/*!
 * @title Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * @license wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v16.3.0 - 2025-02-20
 *
 */
((e,o,n)=>{function i(t){if(!t.content){for(var e=t.childNodes,n=o.createDocumentFragment();e[0];)n.appendChild(e[0]);t.content=n}}var a="wb-template",c="template",t=n.doc;n.tmplPolyfill=i,t.on("timerpoke.wb wb-init.wb-template",c,function(t){t=n.init(t,a,c);t&&(i(t),n.ready(e(t),a))}),n.add(c)})(jQuery,document,wb);