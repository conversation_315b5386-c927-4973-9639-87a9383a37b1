"""
Test constants and configurations for the application test suite.
This module contains all the test data organized by feature area.
"""

from typing import List
from datetime import <PERSON><PERSON><PERSON>
from app.common.constants import User<PERSON><PERSON>, RequisitionStatus, PriorityLevel, ERROR_MESSAGES

###################
# User Test Data #
###################

# Core user credentials for different roles
TEST_USER_DATA = {
    "email": "<EMAIL>",
    "password": "password123",
    "azure_ad_id": "13e357dd-ef5d-4f03-a812-b6244c296362"
}

TEST_ADMIN_DATA = {
    "email": "<EMAIL>",
    "password": "admin123",
    "azure_ad_id": "51bcb4fc-e85e-4e82-832d-29c5f34b2bf7"
}

TEST_LAB_PERSONNEL_DATA = {
    "email": "<EMAIL>",
    "password": "labpass123",
    "azure_ad_id": "e1f9f882-ca45-465f-89ea-79c18578df30"
}

# Test cases for user operations
USER_TEST_CASES = {
    "create": {
        "valid": {
            "email": "<EMAIL>",
            "password": "newpass123",
            "is_active": True,
            "azure_ad_id": "13e357dd-ef5d-4f03-a812-b6244c296363"
        },
        "valid2": {
            "email": "<EMAIL>",
            "password": "newpass456",
            "is_active": True,
            "azure_ad_id": "e1f9f882-ca45-465f-89ea-79c18578df31"
        },
        "missing_email": {
            "password": "pass123"
        },
        "missing_password": {
            "email": "<EMAIL>"
        },
        "invalid_email": {
            "email": "not_an_email",
            "password": "pass123",
            "is_active": True
        }
    },
    "update": {
        "valid": {
            "email": "<EMAIL>",
            "is_active": False
        },
        "invalid": {
            "is_active": "not_a_boolean"
        }
    }
}

#######################
# Staff Test Data #
#######################
STAFF_TEST_DATA = {
    "scientist": {
        "role": UserRole.SCIENTIST
    },
    "admin": {
        "role": UserRole.LAB_ADMIN
    },
    "lab_personnel": {
        "role": UserRole.LAB_PERSONNEL
    }
}

#######################
# Lab Test Data #
#######################
BASE_LAB_DATA = {
    "lab_name":"Test Lab"
}

TEST_LAB_DATA = {
    "lab1": {
        "lab_name": "Lab 1"
    },
    "lab2": {
        "lab_name": "Lab 2"
    }
}

#########################
# Requisition Test Data #
#########################

# Base requisition data used as a template for creating test requisitions
BASE_REQUISITION_DATA = {
    "lsa_number": "LSA123",
    "project_number": "PRJ-001",
    "project_name": "Test Project",
    "status": RequisitionStatus.SUBMITTED.value,
    "priority": PriorityLevel.MEDIUM.value,
    "comments": "Test requisition"
}

# Role-specific requisition test data
TEST_REQUISITION_DATA = {
    "scientist": {
        "lsa_number": "LSA456",
        "project_number": "PRJ-002",
        "project_name": "New Test Project",
        "priority": PriorityLevel.HIGH.value,
        "comments": "Test requisition creation",
        "status": RequisitionStatus.SUBMITTED.value
    },
    "admin": {
        "lsa_number": "LSA789",
        "project_number": "PRJ-003",
        "project_name": "Admin Project",
        "priority": PriorityLevel.MEDIUM.value,
        "status": RequisitionStatus.SUBMITTED.value
    },
    "lab_personnel": {
        "lsa_number": "LSA789",
        "project_number": "PRJ-003",
        "status": RequisitionStatus.SUBMITTED.value
    },
    "invalid": {
        "missing_required": {
            "project_number": "PRJ-002",
            "priority": PriorityLevel.HIGH.value
        },
        "invalid_priority": {
            "lsa_number": "LSA456",
            "project_number": "PRJ-002",
            "priority": "INVALID_PRIORITY"
        }
    },
    "updates": {
        "status_change": {
            "status": RequisitionStatus.IN_PROGRESS.value,
            "comments": "Updated status"
        },
        "archive": {
            "is_archived": True
        },
        "status_and_archive": {
            "status": RequisitionStatus.IN_PROGRESS.value,
            "is_archived": True,
            "comments": "Updated status and archived"
        }
    }
}

######################
# Pagination Testing #
######################

# Test cases for pagination parameters (limit, skip, expected_status)
PAGINATION_TEST_CASES = [
    (5, 0, 200),    # Valid positive limit, zero skip
    (0, 0, 422),    # Invalid zero limit
    (-1, 0, 422),   # Invalid negative limit
    (5, -1, 422),   # Invalid negative skip
    (5, 1, 200),    # Valid positive skip
    (None, None, 200),  # No pagination
]

#########################
# Role Access Controls #
#########################

# Test cases for role-based access (token_type, expected_count)
ROLE_ACCESS_TEST_CASES = [
    ("user_token", 1),      # Scientists see only their requisitions
    ("admin_token", 3),     # Admins see all requisitions
    ("lab_personnel_token", 3),  # Lab personnel see all requisitions
]

# Test cases for archive permissions
ARCHIVE_PERMISSION_TEST_CASES = [
    ("admin_token", 200, None),  # Admin can archive
    ("lab_personnel_token", 403, "Only lab administrators can archive requisitions"),
    ("user_token", 403, "Only lab administrators can archive requisitions"),
]

# Role-based access configurations for different operations
ROLE_ACCESS_CONFIGURATIONS = {
    "admin": {
        "token_fixture": "admin_token",
        "expected_status": 200,
        "can_list": True,
        "can_create": True,
        "can_update": True
    },
    "user": {
        "token_fixture": "user_token",
        "expected_status": 403,
        "can_list": False,
        "can_create": False,
        "can_update": False
    },
    "lab_personnel": {
        "token_fixture": "lab_personnel_token",
        "expected_status": 403,
        "can_list": False,
        "can_create": False,
        "can_update": False
    }
}

###################
# File Test Data #
###################

# Constants for file-related tests
FILE_TEST_CONSTANTS = {
    "TEST_FILES": {
        "base": {
            "file_name": "test.pdf",
            "storage_id": "test_storage_id",
            "file_type": "application/pdf",
            "file_size": 1024,
            "is_published": False
        },
        "unpublished": {
            "file_name": "test_file.txt",
            "file_type": "text/plain",
            "file_size": 1024,
            "storage_id": "test-storage-id",
            "is_published": False
        },
        "published": {
            "file_name": "published_file.txt",
            "file_type": "text/plain",
            "file_size": 1024,
            "storage_id": "test-storage-id-pub",
            "is_published": True
        },
        "batch": [
            {
                "file_name": f"batch_file_{i}.txt",
                "file_type": "text/plain",
                "file_size": 1024,
                "storage_id": f"test-storage-id-{i}",
                "is_published": i % 2 == 0
            }
            for i in range(5)
        ]
    },
    "MOCK_RESPONSES": {
        "token": "fake_token",
        "storage_id": "mocked_storage_id",
        "content": b"mocked_file_content"
    },
    "SPECIAL_FILENAMES": [
        "test file.pdf",
        "test-file.pdf",
        "test_file.pdf",
        "testfile123.pdf"
    ],
    "FILE_SIZES": {
        "negative": -1024,
        "zero": 0,
        "normal": 1024,
        "large": 1024 * 1024 * 1024 * 2  # 2GB
    }
}

# File test configurations for different scenarios
FILE_TEST_CONFIGURATIONS = {
    "ROLE_ACCESS": {
        "admin": {
            "can_upload": True,
            "can_delete": True,
            "can_publish": True,
            "can_see_unpublished": True
        },
        "scientist": {
            "can_upload": False,
            "can_delete": False,
            "can_publish": False,
            "can_see_unpublished": False
        }
    },
    "PAGINATION": {
        "default_limit": 10,
        "test_cases": [
            (5, 0, 200),    # limit, skip, expected_status
            (0, 0, 422),
            (-1, 0, 422),
            (5, -1, 422),
            (5, 1, 200),
            (None, None, 200)
        ]
    }
}

#######################
# Sample Test Data #
#######################

SAMPLE_TEST_DATA = {
    "sample1": {
        "sms_number": "12345",
        "external_reference": None,
        "station_id": None,
        "depth_top": None,
        "depth_bottom": None,
        "sample_type": None
    },
    "sample2": {
        "sms_number": "67890",
        "external_reference": "1",
        "station_id": "12",
        "depth_top": "100",
        "depth_bottom": "150",
        "sample_type": "ground"
    },
    "invalid_depth_sample": {
        "sms_number": "1122",
        "depth_top": 150,
        "depth_bottom": 100
    },
    "missing_sms_sample": {
        "id": "1122"
    }
}

SAMPLE_TEST_CASES = {
    "create": {
        "valid": {
            "sms_number": "12345"
        },
        "invalid_depth": {
            "sms_number": "1122",
            "depth_top": 150,
            "depth_bottom": 100
        },
        "missing_sms": {
            "id": "1122"
        }
    }
}

# Add a new section for sample verification
SAMPLE_VERIFICATION = {
    "missing_sms_error": {
        'detail': [{
            'type': 'missing',
            'loc': ['body', 'sms_number'],
            'msg': 'Field required',
            'input': {'id': '1122'}
        }]
    }
}

#########################
# Test Type Test Data #
#########################

# Test type data used for creating test instances
TEST_TYPE_DATA = {
    "test_type1": {
        "name": "Grain Size Analysis",
        "description": "Analysis of sediment grain size distribution",
        "is_active": True
    },
    "test_type2": {
        "name": "Porosity Test",
        "description": "Measurement of void space in sediment",
        "is_active": True
    }
}

# Test cases for test type operations
TEST_TYPE_TEST_CASES = {
    "create": {
        "valid": {
            "name": "New Test Type",
            "description": "A new test type for testing",
            "is_active": True
        },
        "missing_name": {
            "description": "Missing required name field",
            "is_active": True
        },
        "invalid_active": {
            "name": "Invalid Test Type",
            "description": "Has invalid active field",
            "is_active": "not_a_boolean"
        }
    },
    "update": {
        "valid": {
            "name": "Updated Test Type",
            "description": "Updated description",
            "is_active": False
        },
        "partial": {
            "name": "Partially Updated Test Type"
        },
        "invalid": {
            "is_active": "not_a_boolean"
        }
    },
    "batch_update": {
        "valid_batch": [
            {
                "name": "Batch Updated Type 1",
                "description": "Updated via batch operation 1",
                "is_active": False
            },
            {
                "name": "Batch Updated Type 2",
                "description": "Updated via batch operation 2",
                "is_active": True
            }
        ],
        "partial_batch": [
            {
                "name": "Partially Updated Batch Type 1"
            },
            {
                "description": "Only description updated",
                "is_active": False
            }
        ],
        "single_item": [
            {
                "name": "Single Batch Update",
                "description": "Single item in batch",
                "is_active": False
            }
        ],
        "empty_batch": [],
        "atomic_failure_batch": [
            {
                "name": "Valid Update That Should Rollback",
                "description": "This update should be rolled back due to atomic transaction failure"
            },
            {
                "is_active": "invalid_boolean_value"  # This will cause validation error
            }
        ]
    }
}

################################################ STAFF

STAFF_VERIFICATION = {
    "impossible_role": {
        "detail" : [
            {
                "type": "enum",
                "loc": ["body","role"],
                "msg": "Input should be 'scientist', 'lab_personnel' or 'lab_admin'",
                "input": "lab_worker",
                "ctx": {"expected": "'scientist', 'lab_personnel' or 'lab_admin'"}
            }
        ]
    },
    "missing_data": {
        'detail': [{'type': 'missing', 'loc': ['body'], 'msg': 'Field required', 'input': None}]
    }
}