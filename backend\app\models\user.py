from typing import Optional, Dict, Any
from sqlalchemy import String, <PERSON><PERSON><PERSON>
from sqlalchemy.dialects.postgresql import UUI<PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship

from . import Base
import uuid

class User(Base):
    __tablename__ = "users"

    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    email: Mapped[str] = mapped_column(
        String,
        unique=True,
        index=True
    )
    password: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True
    )
    azure_ad_id: Mapped[Optional[str]] = mapped_column(String, unique=True, nullable=True)

    # Add relationships
    # requisitions: Mapped[List["Requisition"]] = relationship(
    #     "Requisition",
    #     back_populates="submitted_by"
    # )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "user_id": str(self.user_id),
            "email": self.email,
            "is_active": self.is_active
        }