/*!
 * Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v4.0.85 - 2025-02-20
 *
 */
!function(){"use strict";wb.i18nDict={"lang-code":"hu","lang-native":"<PERSON><PERSON><PERSON>",add:"Hozzáad",all:"Mind",tphp:"Oldal teteje",load:"betöltése ...",process:"feldolgozás ...",srch:"Keresés","no-match":"Nincs találat",matches:{mixin:"[MIXIN] találat"},current:"(jelenlegi)",hide:"Elrejtés",err:"Hiba",colon:":",hyphen:" - ","full-stop":".","comma-space":", ",space:"&#32;",start:"Indítás",stop:"<PERSON><PERSON>llít<PERSON>",back:"Vissza",cancel:"Mégsem","min-ago":"egy perce","coup-mins":"pár perce","mins-ago":{mixin:"[MIXIN] perccel ezelőtt"},"hour-ago":"egy órával ezelőtt","hours-ago":{mixin:"[MIXIN] órával ezelőtt"},"days-ago":{mixin:"[MIXIN] nappal ezelőtt"},yesterday:"tegnap",nxt:"Következő","nxt-r":"Következő (jobb nyíl gomb)",prv:"Előző","prv-l":"Előző (bal nyíl gomb)",first:"Első",last:"Utolsó",page:"Jump to: Page","srch-menus":"Keresés és menük",email:"Email","menu-close":"Zárja be a menüt","overlay-close":"Zárja be az overlay","esc-key":"(escape billentyűvel)",show:"Mutat","tab-rot":{off:"Tabulálás engedélyezése",on:"Tabulálás kikapcsolása"},"tab-list":"Lap listája","tab-pnl-end1":"Vége a lap panel.","tab-pnl-end2":"Vissza a lap listája","tab-pnl-end3":"vagy továbbra is a többi oldalon.","tab-play":"Lejátszás","mmp-play":"Lejátszás",pause:"Pillanatmegállító",open:"Nyitva",close:"Bezárás",volume:"Hangerő",mute:{on:"Lenémítás",off:"Hang bekapcsolása"},cc:{off:"Feliratok kikapcsolása",on:"Feliratozás bekapcsolása"},"cc-err":"Hiba a felirat betöltése közben",fs:"Enter full screen",adesc:{on:"Narráció bekapcsolása",off:"Narráció kikapcsolása"},pos:"Jelenlegi pozíció:",dur:"Összidő:",msgYoutubeNotLoad:"Video encountered loading issues",msgYoutubeVdLoad:"Loading Youtube video","shr-txt":"Oszd","shr-pg":" meg a oldal","shr-vid":" meg a videót","shr-aud":" ezt a hangfájlt","shr-hnt":" a {s} ","shr-disc":"I jóváhagyását olyan termékek vagy szolgáltatások kifejezett vagy hallgatólagos.","frm-nosubmit":"Az adatokat nem lehet elküldeni mert ","errs-fnd":" hibák léptek fel.","err-fnd":" hiba lépett fel.","err-correct":"(Correct and resubmit)","date-hide":"A naptár elrejtése","date-show":"Válasszon egy dátumot a naptárból:","date-sel":"Kijelölt",days:["Vasárnap","Hétfő","Kedd","Szerda","Csütörtök","Péntek","Szombat"],mnths:["Január","Február","Március","Április","Május","Június","Július","Augusztus","Szeptember","Október","November","December"],cal:"Naptár","cal-format":"<span class='wb-inv'>{ddd}, {M} </span>{d}<span class='wb-inv'>, {Y}</span>",currDay:"(Mai nap)","cal-goToLnk":'Ugrás<span class="wb-inv"> erre a hónapra</span>',"cal-goToTtl":"Ugrás erre a hónapra","cal-goToMnth":"Hónap:","cal-goToYr":"Év:","cal-goToBtn":"Indít",prvMnth:"Előző hónap: ",nxtMnth:"Következő hónap: ","lb-curr":"%total%-ből %curr%","lb-xhr-err":"A tartalom betöltése nem sikerült.","lb-img-err":"A kép betöltése nem sikerült.","tbl-txt":"Táblázat","tbl-dtls":"Részletek a következő táblázatban.","chrt-cmbslc":"Combined slice","st-to-msg-bgn":"A munkamenet lejár automatikusan #min# perc #sec# másodperc.","st-to-msg-end":'Válassza a "Folytatás session" kiterjeszteni a munkamenet.',"st-msgbx-ttl":"Munkamenet időtúllépési figyelmeztetés","st-alrdy-to-msg":"Sajnos a munkamenet már lejárt. Kérjük, jelentkezzen be újra.","st-btn-cont":"Folytatás ülés","st-btn-end":"Vége ülésen már","td-toggle":"Váltás az összes","td-open":"Az összes kibontása","td-close":"Az összes összecsukása","td-ttl-open":"Az összes kibontása rétegei tartalom","td-ttl-close":"Elrejt minden rétege tartalom",sortAsc:": aktiválja a növekvő sort",sortDesc:": aktiválja csökkenő rendezési",emptyTbl:"Nem állnak rendelkezésre adatok a táblázatban",infoEntr:"Megjelenítése _START_-_END_ a _TOTAL_ bejegyzés",infoEmpty:"Megjelenítése 0-0 a 0 bejegyzés",infoFilt:"(kiszűrt összesen _MAX_ entries)",info1000:"&#160;",lenMenu:"Megjelenítés _MENU_ bejegyzés",filter:"Szűrés",tbFilterInst:"This table provides a sorting feature via the buttons across the column header row with only one instance visible at a time.","twitter-start-notice":"Start of @%username%’s X timeline","twitter-end-notice":"End of @%username%’s X timeline","twitter-skip-end":"Skip to end of @%username%’s X timeline","twitter-skip-start":"Skip to start of @%username%’s X timeline","twitter-timeline-title":"X timeline","geo-mapctrl":"@geo-mapctrl@","geo-zmin":"Nagyítás","geo-zmout":"Kicsinyítés","geo-zmwrld":"Nagyítás feltérképezésére mértékben","geo-zmfeat":"Nagyítás elem","geo-sclln":"térkép méretarány","geo-msepos":"Szélességi és hosszúsági az egér kurzor","geo-ariamap":"Térkép objektumot. A leírások A térkép funkciók az alábbi táblázat tartalmazza.","geo-ally":"<strong>Billentyűzet felhasználók:</strong> Ha a térkép a középpontban, a nyílbillentyűkkel mozgathatja a térképet, és a plusz és mínusz gombokkal a képre. A nyilak nem fog mozogni a térképen, ha a térkép kinagyított mértékben.","geo-allyttl":"Utasítás: Térkép navigáció","geo-tgllyr":"Váltás a megjelenítési réteg","geo-hdnlyr":"Ez a réteg jelenleg rejtve.","geo-bmap-url":"//geoappext.nrcan.gc.ca/arcgis/rest/services/BaseMaps/CBMT3978/MapServer/WMTS/","geo-bmap-matrix-set":"default028mm","geo-bmapttl":"BaseMaps_CBMT3978","geo-bmapurltxt":"//geoappext.nrcan.gc.ca/arcgis/rest/services/BaseMaps/CBMT_TXT_3978/MapServer/WMTS/tile/1.0.0/BaseMaps_CBMT3978/{Style}/{TileMatrixSet}/{TileMatrix}/{TileRow}/{TileCol}.jpg","geo-attrlnk":"//geogratis.gc.ca/geogratis/CBM_CBC?lang=en","geo-attrttl":"GeoGratis - Kanada alaptérkép (angol vagy francia nyelven esetén)","geo-sel":"Választ","geo-lblsel":"Ellenőrizze, hogy jelölje ki az elemet a térképen","geo-locurl-geogratis":"//geogratis.gc.ca/services/geolocation/en/locate","geo-loc-placeholder":"Adja meg a helység nevét&#44; irányítószám&#44; cím (postai)&#44; a száma NTS ...","geo-loc-label":"Elhelyezkedés","geo-aoi-north":"Észak","geo-aoi-east":"Kelet","geo-aoi-south":"Dél","geo-aoi-west":"Nyugat","geo-aoi-instructions":'Döntetlen box a térképen vagy adja meg a koordinátákat, majd kattintson a "Hozzáadás" gombra.',"geo-aoi-title":"Draw box on map or enter coordinates","geo-aoi-btndraw":"Rajzol","geo-aoi-btnclear":"Eltávolít","geo-geoloc-btn":"Nagyítás az aktuális helyre","geo-geoloc-fail":"Hely sikerült. Ügyeljen arra, hogy a helymeghatározó szolgáltatások engedélyezve vannak.","geo-geoloc-uncapable":"A honosítás nem támogatja a böngészője.","geo-lgnd-grphc":"Legend grafika térképen réteg.","wb-disable":"Switch to basic HTML version","wb-enable":"Switch to standard version","disable-notice-h":"Notice: Basic HTML","disable-notice":"You are viewing Basic HTML view. Some features may be disabled.","skip-prefix":"Skip to:",dismiss:"Dismiss","tmpl-signin":"Bejelentkezés","fltr-lbl":'Filter<span class="wb-inv"> content: results appear below as you type.</span>',"fltr-info":"Showing <span data-nbitem></span> filtered from <span data-total></span> total entries","pii-header":"Remove Personal information","pii-intro":"Some information in your form is identified as personal information and it will be replaced as follows:","pii-view-more":"What is considered personal information?","pii-view-more-info":"<p>The following types of information are considered personal information:</p><ul><li>email address</li><li>telephone number</li><li>postal code</li><li>passport number</li><li>business number</li><li>social insurance number (SIN)</li></ul>","pii-yes-btn":"Remove personal information and submit","pii-cancel-btn":"Go back and edit fields",redacted:"redacted"}}(),wb.doc.one("formLanguages.wb",function(){var e;e=function(e){return e.extend(e.validator.messages,{required:"Kötelező megadni.",maxlength:e.validator.format("Legfeljebb {0} karakter hosszú legyen."),minlength:e.validator.format("Legalább {0} karakter hosszú legyen."),rangelength:e.validator.format("Legalább {0} és legfeljebb {1} karakter hosszú legyen."),email:"Érvényes e-mail címnek kell lennie.",url:"Érvényes URL-nek kell lennie.",date:"Dátumnak kell lennie.",number:"Számnak kell lennie.",digits:"Csak számjegyek lehetnek.",equalTo:"Meg kell egyeznie a két értéknek.",range:e.validator.format("{0} és {1} közé kell esnie."),max:e.validator.format("Nem lehet nagyobb, mint {0}."),min:e.validator.format("Nem lehet kisebb, mint {0}."),creditcard:"Érvényes hitelkártyaszámnak kell lennie.",remote:"Kérem javítsa ki ezt a mezőt.",dateISO:"Kérem írjon be egy érvényes dátumot (ISO).",step:e.validator.format("A {0} egyik többszörösét adja meg.")}),e},"function"==typeof define&&define.amd?define(["jquery","../jquery.validate"],e):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery)});