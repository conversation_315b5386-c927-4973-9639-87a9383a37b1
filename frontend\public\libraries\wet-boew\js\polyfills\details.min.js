/*!
 * Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v4.0.85 - 2025-02-20
 *
 */
!function(n,i){"use strict";function r(){return null!==this.getAttribute("open")}function a(t){"boolean"==typeof t&&(t?l:c).call(this),this.summary.setAttribute("aria-expanded",t),n(this).trigger("toggle")}function t(){var t;if(t=-1!==this.className.indexOf("alert")?"alert-collapsible-state-"+this.getAttribute("id"):t)try{localStorage.setItem(t,this.open?"open":"closed")}catch(t){}}var o="wb-details",s="summary",e=i.doc,l=function(){this.setAttribute("open","open"),this.className+=" open",t.call(this)},c=function(){this.removeAttribute("open"),this.className=this.className.replace(" open",""),t.call(this)};e.on("timerpoke.wb wb-init.wb-details",s,function(t){var e,t=i.init(t,o,s);t&&(e=t.parentNode,Object.defineProperty(e,"open",{get:r,set:a}),(e.summary=t).setAttribute("aria-expanded",null!==e.getAttribute("open")),t.getAttribute("role")||t.setAttribute("role","button"),t.getAttribute("tabindex")||t.setAttribute("tabindex","0"),i.ready(n(t),o))}),e.on("click keydown toggle."+o,s,function(t){var e=t.which,i=t.currentTarget;return e&&1!==e||-1!==i.className.indexOf("wb-toggle")&&("toggle"!==t.type||t.namespace!==o)?13!==e&&32!==e||(t.preventDefault(),n(i).trigger("click")):(e=i.parentNode).open=!e.open,!0}),i.add(s)}(jQuery,(window,wb));
//# sourceMappingURL=details.min.js.map