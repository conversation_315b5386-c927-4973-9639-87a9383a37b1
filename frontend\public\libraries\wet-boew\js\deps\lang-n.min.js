PR.registerLangHandler(PR.createSimpleLexer([[PR.PR_STRING,/^(?:\'(?:[^\\\'\r\n]|\\.)*\'|\"(?:[^\\\"\r\n]|\\.)*(?:\"|$))/,null,'"'],[PR.PR_COMMENT,/^#(?:(?:define|elif|else|endif|error|ifdef|include|ifndef|line|pragma|undef|warning)\b|[^\r\n]*)/,null,"#"],[PR.PR_PLAIN,/^\s+/,null," \r\n\t "]],[[PR.PR_STRING,/^@\"(?:[^\"]|\"\")*(?:\"|$)/,null],[PR.PR_STRING,/^<#(?:[^#>])*(?:#>|$)/,null],[PR.PR_STRING,/^<(?:(?:(?:\.\.\/)*|\/?)(?:[\w-]+(?:\/[\w-]+)+)?[\w-]+\.h|[a-z]\w*)>/,null],[PR.PR_COMMENT,/^\/\/[^\r\n]*/,null],[PR.PR_COMMENT,/^\/\*[\s\S]*?(?:\*\/|$)/,null],[PR.PR_KEYWORD,new RegExp("^(?:abstract|and|as|base|catch|class|def|delegate|enum|event|extern|false|finally|fun|implements|interface|internal|is|macro|match|matches|module|mutable|namespace|new|null|out|override|params|partial|private|protected|public|ref|sealed|static|struct|syntax|this|throw|true|try|type|typeof|using|variant|virtual|volatile|when|where|with|assert|assert2|async|break|checked|continue|do|else|ensures|for|foreach|if|late|lock|new|nolate|otherwise|regexp|repeat|requires|return|surroundwith|unchecked|unless|using|while|yield)\\b"),null],[PR.PR_TYPE,/^(?:array|bool|byte|char|decimal|double|float|int|list|long|object|sbyte|short|string|ulong|uint|ufloat|ulong|ushort|void)\b/,null],[PR.PR_LITERAL,/^@[a-z_$][a-z_$@0-9]*/i,null],[PR.PR_TYPE,/^@[A-Z]+[a-z][A-Za-z_$@0-9]*/,null],[PR.PR_PLAIN,/^'?[A-Za-z_$][a-z_$@0-9]*/i,null],[PR.PR_LITERAL,new RegExp("^(?:0x[a-f0-9]+|(?:\\d(?:_\\d+)*\\d*(?:\\.\\d*)?|\\.\\d\\+)(?:e[+\\-]?\\d+)?)[a-z]*","i"),null,"0123456789"],[PR.PR_PUNCTUATION,/^.[^\s\w\.$@\'\"\`\/\#]*/,null]]),["n","nemerle"]);