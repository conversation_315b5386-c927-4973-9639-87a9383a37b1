from datetime import datetime, timedelta, timezone

import pytest
import os
from fastapi.testclient import TestClient
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator, List
from sqlalchemy.exc import SQLAlchemyError
from unittest.mock import patch, AsyncMock

from app.main import app
from app.database import Base, get_db
from app.models.file import File
from app.models.requisition import Requisition
from app.models.requisition_sample import RequisitionSample
from app.models.user import User
from app.models.sample import Sample
from app.models.labs import Lab
from app.models.staff import Staff
from app.models.test_type import TestType
from app.utils.hashing import get_password_hash
from app.common.constants import User<PERSON><PERSON>, RequisitionStatus, PriorityLevel
from app.config import settings
import uuid
from tests.test_utils.constants import TEST_USER_DATA, TEST_ADMIN_DATA, TEST_LAB_PERSONNEL_DATA, FILE_TEST_CONSTANTS, SAMPLE_TEST_DATA, TEST_REQUISITION_DATA, TEST_LAB_DATA, BASE_LAB_DATA, TEST_TYPE_DATA
from tests.test_utils.helpers import TestHelpers
from app.crud import requisition


#################################
# Database Connection Fixtures  #
#################################

@pytest.fixture(scope="session")
def engine():
    # Create test database engine
    test_db_url = settings.TEST_DATABASE_URL
    if not test_db_url:
        raise ValueError("TEST_DATABASE_URL is not configured")
    engine = create_engine(test_db_url)
    Base.metadata.create_all(bind=engine)
    yield engine

@pytest.fixture(scope="function")
def db(engine):
    # Create a fresh database session for each test
    TestingSessionLocal = sessionmaker(bind=engine, autocommit=False, autoflush=False)

    # Create a connection and start a transaction
    connection = engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)

    # Wrap everything in a nested transaction
    nested = connection.begin_nested()

    try:
        yield session
    finally:
        session.close()
        # Roll back the nested transaction
        if nested and nested.is_active:
            nested.rollback()
        # Roll back the outer transaction
        if transaction and transaction.is_active:
            transaction.rollback()
        # Close the connection
        connection.close()

############################
# Test Application Setup  #
############################

@pytest.fixture(scope="function")
def client(db, cleanup_tables):
    def override_get_db():
        try:
            yield db
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    yield TestClient(app)
    del app.dependency_overrides[get_db]

@pytest.fixture(autouse=True)
def cleanup_tables(db):
    # Automatically clean up tables after each test
    yield
    try:
        db.rollback()  # First rollback any pending transactions
        TestHelpers.Database.clear_all_tables(db)
        db.commit()
    except Exception as e:
        db.rollback()
        print(f"Warning: Cleanup failed: {e}")

@pytest.fixture(scope="session", autouse=True)
def set_test_env():
    # Set up test environment variables
    os.environ["TESTING"] = "1"
    yield
    os.environ.pop("TESTING", None)

#########################
# User Test Fixtures   #
#########################

@pytest.fixture(scope="function")
def test_user(db) -> User:
    return TestHelpers.Users.create_test_user(db, TEST_USER_DATA)

@pytest.fixture(scope="function")
def test_admin(db) -> User:
    return TestHelpers.Users.create_test_user(db, TEST_ADMIN_DATA)

@pytest.fixture(scope="function")
def test_lab_personnel(db) -> User:
    return TestHelpers.Users.create_test_user(db, TEST_LAB_PERSONNEL_DATA)

#########################
# Sample Test Fixtures  #
#########################

@pytest.fixture(scope="function")
def test_staff_user(db, test_user, test_lab) -> Staff:
    return TestHelpers.Staff.create_test_staff(db, {
        "user_id": test_user.user_id,
        "lab_id": None,
        "role": UserRole.SCIENTIST
    })

@pytest.fixture(scope="function")
def test_staff_admin(db, test_admin, test_lab) -> Staff:
    return TestHelpers.Staff.create_test_staff(db, {
        "user_id": test_admin.user_id,
        "lab_id": test_lab.lab_id,
        "role": UserRole.LAB_ADMIN
    })

@pytest.fixture(scope="function")
def test_staff_lab_personal(db, test_lab_personnel, test_lab) -> Staff:
    return TestHelpers.Staff.create_test_staff(db, {
        "user_id": test_lab_personnel.user_id,
        "lab_id": test_lab.lab_id,
        "role": UserRole.LAB_PERSONNEL
    })

@pytest.fixture(scope="function")
def test_staff_multiple_labs(db, test_admin, test_labs) -> List[Staff]:
    return TestHelpers.Staff.create_test_staff_multiple(db, [
        {
            "user_id": test_admin.user_id,
            "lab_id": test_labs[0].lab_id,
            "role": UserRole.LAB_ADMIN
        },
        {
            "user_id": test_admin.user_id,
            "lab_id": test_labs[1].lab_id,
            "role": UserRole.LAB_ADMIN
        }
    ])

@pytest.fixture(scope="function")
def test_staff_multiple_roles(db,test_admin,test_lab) -> List[Staff]:
    return TestHelpers.Staff.create_test_staff_multiple(db, [
        {
            "user_id": test_admin.user_id,
            "lab_id": test_lab.lab_id,
            "role": UserRole.LAB_ADMIN
        },
        {
            "user_id": test_admin.user_id,
            "lab_id": None,
            "role": UserRole.SCIENTIST
        }
    ])

@pytest.fixture(scope="function")
def test_requisition_with_samples(db, test_staff_user, test_samples, test_lab) -> Requisition:
    """Create a test requisition with associated samples."""
    # Create requisition first
    requisition = TestHelpers.Requisitions.create_test_requisition(
        db=db,
        user_id=test_staff_user.user_id,
        lab_id=test_lab.lab_id,
        **TEST_REQUISITION_DATA["scientist"]
    )

    # Add samples to requisition
    for sample in test_samples:
        TestHelpers.Requisitions.add_sample_to_requisition(
            db=db,
            req_id=requisition.req_id,
            sample_id=sample.sample_id
        )

    return requisition

@pytest.fixture(scope="function")
def test_staff_multiple_admins(db,test_admin,test_lab_personnel,test_lab) -> List[Staff]:
    # First, clean up any existing staff records for these users and lab
    existing_staff = db.query(Staff).filter(
        (Staff.user_id == test_admin.user_id) | 
        (Staff.user_id == test_lab_personnel.user_id)
    ).all()
    
    for staff in existing_staff:
        db.delete(staff)
    db.commit()
    
    # Now create the new staff records
    return TestHelpers.Staff.create_test_staff_multiple(db, [
        {
            "user_id": test_admin.user_id,
            "lab_id": test_lab.lab_id,
            "role": UserRole.LAB_ADMIN
        },
        {
            "user_id": test_lab_personnel.user_id,
            "lab_id": test_lab.lab_id,
            "role": UserRole.LAB_ADMIN
        }
    ])

@pytest.fixture(scope="function")
def test_staff_multiple_admins_in_multiple_labs(db,test_admin,test_lab_personnel,test_labs) -> List[Staff]:
    return TestHelpers.Staff.create_test_staff_multiple(db, [
        {
            "user_id": test_admin.user_id,
            "lab_id": test_labs[0].lab_id,
            "role": UserRole.LAB_ADMIN
        },
        {
            "user_id": test_lab_personnel.user_id,
            "lab_id": test_labs[1].lab_id,
            "role": UserRole.LAB_ADMIN
        }
    ])


#########################
# Lab Test Fixtures  #
#########################

@pytest.fixture(scope="function")
def test_lab(db) -> Lab:
    return TestHelpers.Labs.create_test_lab(db, BASE_LAB_DATA)

@pytest.fixture(scope="function")
def test_labs(db) -> List[Lab]:
    return TestHelpers.Labs.create_test_labs(db, TEST_LAB_DATA)


#########################
# Auth Token Fixtures  #
#########################

@pytest.fixture(scope="function")
def admin_token(test_admin, test_staff_admin, client) -> str:
    # Get authentication token for admin user
    response = client.post(
        "/auth/login",
        data={"username": test_admin.email, "password": "admin123"}
    )
    return response.json()["access_token"]

@pytest.fixture(scope="function")
def user_token(test_user, test_staff_user, client) -> str:
    # Get authentication token for scientist user
    response = client.post(
        "/auth/login",
        data={"username": test_user.email, "password": "password123"}
    )
    return response.json()["access_token"]

@pytest.fixture(scope="function")
def lab_personnel_token(test_lab_personnel, test_staff_lab_personal, client) -> str:
    # Get authentication token for lab personnel user
    response = client.post(
        "/auth/login",
        data={"username": test_lab_personnel.email, "password": "labpass123"}
    )
    return response.json()["access_token"]

@pytest.fixture(scope="function")
def admin_multiple_labs_token(test_admin, test_staff_multiple_labs, client) -> str:
    # Get authentication token for admin user
    response = client.post(
        "/auth/login",
        data={"username": test_admin.email, "password": "admin123"}
    )
    return response.json()["access_token"]

@pytest.fixture(scope="function")
def admin_multiple_roles_token(test_admin, test_staff_multiple_roles, client) -> str:
    # Get authentication token for admin user
    response = client.post(
        "/auth/login",
        data={"username": test_admin.email, "password": "admin123"}
    )
    return response.json()["access_token"]

##############################
# Requisition Test Fixtures #
##############################

@pytest.fixture(scope="function")
def test_requisition(db, test_staff_user, test_lab) -> Requisition:
    return TestHelpers.Requisitions.create_test_requisition(db, test_staff_user.user_id, test_lab.lab_id)

@pytest.fixture(scope="function")
def test_requisitions(db, test_staff_user, test_staff_admin, test_lab, test_labs) -> List[Requisition]:
    return TestHelpers.Requisitions.create_batch(db, [test_staff_user.user_id, test_staff_admin.user_id], [test_lab.lab_id, test_labs[0].lab_id, test_labs[1].lab_id])

@pytest.fixture
def test_requisition_with_tests(db, test_requisition_with_samples, test_types, test_staff_admin):
    """Create a requisition with samples and tests for testing."""
    return TestHelpers.Requisitions.add_tests_to_requisition(
        db,
        test_requisition_with_samples,
        test_types[0].test_type_id,
        test_staff_admin.user_id,
        test_staff_admin.role
    )

######################
# File Test Fixtures #
######################

@pytest.fixture(scope="function")
def mock_storage():
    """Unified mock storage fixture with standard responses"""
    with patch('app.services.storage_service.storage_service') as mock:
        mock_responses = FILE_TEST_CONSTANTS["MOCK_RESPONSES"]
        mock.get_token = AsyncMock(return_value=mock_responses["token"])
        mock.upload_file = AsyncMock(return_value=mock_responses["storage_id"])
        mock.download_file = AsyncMock(return_value=mock_responses["content"])
        mock.delete_file = AsyncMock(return_value=True)
        yield mock

@pytest.fixture
def test_file(db, test_staff_user) -> File:
    """Create an unpublished test file record"""
    return TestHelpers.Files.create_test_file(db, test_staff_user.user_id)

@pytest.fixture
def test_published_file(db, test_staff_user) -> File:
    """Create a published test file record"""
    file_data = FILE_TEST_CONSTANTS["TEST_FILES"]["published"].copy()
    return TestHelpers.Files.create_test_file(db, test_staff_user.user_id, file_data)

@pytest.fixture
def file_test_data():
    """Fixture providing test data for file uploads"""
    return {
        "file_name": "test.txt",
        "file_content": b"test content",
        "file_type": "text/plain"
    }

@pytest.fixture(scope="function")
def file_test_setup(db, mock_storage, client):
    """Common setup for file-related tests"""
    return {
        "db": db,
        "storage_service": mock_storage,
        "client": client
    }

#########################
# Sample Test Fixtures #
#########################

@pytest.fixture(scope="function")
def test_samples(db) -> List[Sample]:
    """Create test samples for testing."""
    samples = []
    sample_data = [SAMPLE_TEST_DATA["sample1"], SAMPLE_TEST_DATA["sample2"]]
    
    for data in sample_data:
        sample = TestHelpers.Samples.create_test_sample(db, data)
        samples.append(sample)
    
    return samples

@pytest.fixture
def test_sample(db):
    """Create a single test sample for testing."""
    return TestHelpers.Samples.create_test_sample(db, SAMPLE_TEST_DATA["sample1"])

#########################
# Test Type Fixtures   #
#########################

@pytest.fixture(scope="function")
def test_types(db) -> List[TestType]:
    """Create and return test types for testing (without lab association)."""
    return TestHelpers.TestTypes.create_test_types(db, TEST_TYPE_DATA)

@pytest.fixture(scope="function")
def test_type(db) -> TestType:
    """Create and return a single test type for testing (without lab association)."""
    return TestHelpers.TestTypes.create_test_type(db, TEST_TYPE_DATA["test_type1"])

@pytest.fixture(scope="function")
def test_types_with_lab(db, test_lab) -> List[TestType]:
    """Create and return test types associated with a specific lab."""
    return TestHelpers.TestTypes.create_test_types(db, TEST_TYPE_DATA, str(test_lab.lab_id))

@pytest.fixture(scope="function")
def test_type_with_lab(db, test_lab) -> TestType:
    """Create and return a single test type associated with a specific lab."""
    return TestHelpers.TestTypes.create_test_type(db, TEST_TYPE_DATA["test_type1"], str(test_lab.lab_id))

@pytest.fixture(scope="function")
def test_types_multiple_labs(db, test_labs) -> List[TestType]:
    """Create and return test types distributed across multiple labs."""
    return TestHelpers.TestTypes.create_test_types_for_multiple_labs(db, TEST_TYPE_DATA, test_labs)