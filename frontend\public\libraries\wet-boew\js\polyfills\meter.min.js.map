{"version": 3, "file": "meter.min.js", "sources": ["meter.js"], "names": ["$", "wb", "meter", "elm", "indicator", "$elm", "min", "parseFloat", "attr", "max", "high", "low", "optimum", "value", "textContent", "innerText", "children", "width", "offsetWidth", "length", "document", "createElement", "style", "Math", "ceil", "append<PERSON><PERSON><PERSON>", "addClass", "removeClass", "title", "trigger", "componentName", "selector", "$document", "doc", "on", "event", "eventTarget", "target", "type", "namespace", "currentTarget", "init", "ready", "add", "j<PERSON><PERSON><PERSON>", "window"], "mappings": ";;;;;;AAMA,CAAA,SAAYA,EAAWC,GACvB,aAoCS,SAARC,EAAkBC,GACjB,IAQCC,EARGC,EAAOL,EAAGG,CAAI,EACjBG,EAAMC,WAAYF,EAAKG,KAAM,KAAM,GAAK,CAAE,EAC1CC,EAAMF,WAAYF,EAAKG,KAAM,KAAM,GAAK,CAAE,EAC1CE,EAAOH,WAAYF,EAAKG,KAAM,MAAO,CAAE,EACvCG,EAAMJ,WAAYF,EAAKG,KAAM,KAAM,CAAE,EACrCI,EAAUL,WAAYF,EAAKG,KAAM,SAAU,CAAE,EAC7CK,EAAiC,OAAzBR,EAAKG,KAAM,OAAQ,EAAaD,WAAYF,EAAKG,KAAM,OAAQ,CAAE,EAAML,EAAIW,aAAgCX,EAAIY,UACvHC,EAAWb,EAAIa,SAGXb,EAAIW,YACRX,EAAIW,YAAc,GACPX,EAAIY,YACfZ,EAAIY,UAAY,IAYZF,EAAQP,EACZO,EAAQP,EACWG,EAARI,IACXA,EAAQJ,GAGI,OAARE,GAAgBA,EAAML,GAE1BD,EAAKG,KAAM,MADXG,EAAML,CACgB,EAOT,OAATI,GAAwBD,EAAPC,GAErBL,EAAKG,KAAM,OADXE,EAAOD,CACiB,EAGzBQ,EAAQd,EAAIe,cAAkBL,EAAQP,IAAUG,EAAMH,KAEtDF,EAAgC,IAApBY,EAASG,OAAeC,SAASC,cAAe,KAAM,EAAIL,EAAU,IACtEM,MAAML,MAAQM,KAAKC,KAAMP,CAAM,EAAI,KAEpB,IAApBD,EAASG,QACbhB,EAAIsB,YAAarB,CAAU,EAGvBM,GAAiBA,GAATG,EACZR,EAAKqB,SAAU,mBAAoB,EACxBf,GAAOE,GAASF,EAC3BN,EAAKqB,SAAU,kBAAmB,EAElCrB,EAAKsB,YAAa,oCAAqC,EAG1ClB,GAATI,EACJR,EAAKqB,SAAU,cAAe,EAE9BrB,EAAKsB,YAAa,cAAe,EAIlCtB,EAAKG,KAAM,CACVF,IAAKA,EACLG,IAAKA,EACLI,MAAOA,EACPe,MAAOvB,EAAKG,KAAM,OAAQ,GAAKK,CAChC,CAAE,EAAEgB,QAAS,cAAgBC,CAAc,CAC5C,CA1GD,IAAIA,EAAgB,WACnBC,EAAW,QAGXC,EAAY/B,EAAGgC,IAyGhBD,EAAUE,GAAI,mDAAkDH,EAAU,SAAUI,GACnF,IAAIC,EAAcD,EAAME,OAEJ,cAAfF,EAAMG,KACLH,EAAMI,YAAcT,GACxBK,EAAMK,gBAAkBJ,GAExBlC,EAAOkC,CAAY,GA1GJD,EA6GVA,GAxGFhC,EAAMF,EAAGwC,KAAMN,EAAOL,EAAeC,CAAS,KAGjD7B,EAAOC,CAAI,EAGXF,EAAGyC,MAAO1C,EAAGG,CAAI,EAAG2B,CAAc,GAoGrC,CAAE,EAGF7B,EAAG0C,IAAKZ,CAAS,CAEf,EAAGa,QAAQC,OAAQ5C,GAAG"}