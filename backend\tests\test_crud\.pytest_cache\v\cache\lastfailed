{"test_requisition.py::test_create_requisition": true, "test_requisition.py::test_get_requisitions": true, "test_requisitions.py::TestRequisitionCRUD::test_create_requisition_success": true, "test_requisitions.py::TestRequisitionCRUD::test_get_requisitions_scientist": true, "test_requisitions.py::TestRequisitionCRUD::test_get_requisitions_admin": true, "test_requisitions.py::TestRequisitionCRUD::test_get_requisitions_with_status": true, "test_requisitions.py::TestRequisitionCRUD::test_get_requisition_by_id": true, "test_requisitions.py::TestRequisitionCRUD::test_get_nonexistent_requisition": true, "test_requisitions.py::TestRequisitionCRUD::test_update_requisition_success": true, "test_requisitions.py::TestRequisitionCRUD::test_update_nonexistent_requisition": true, "test_requisitions.py::TestRequisitionCRUD::test_archive_requisition": true, "test_users.py": true}