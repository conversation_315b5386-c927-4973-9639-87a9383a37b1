!function(L,A,l){"use strict";function s(e){var t=e.target,l=(this.id=t.attr("id"),this.mapLayers=[],this.layerDiv=t.find(".wb-geomap-layers").attr("id","geomap-layers-"+this.id),this.legendDiv=t.find(".wb-geomap-legend").attr("id","geomap-legend-"+this.id),this.mapDiv=t.find(".wb-geomap-map").attr("id","geomap-map-"+this.id),this.settings=e.settings,this.settings.aspectRatio=this.settings.basemap&&this.settings.basemap.mapOptions&&void 0!==this.settings.basemap.mapOptions.aspectRatio?this.settings.basemap.mapOptions.aspectRatio:.8,this.map=V(this),this.legend=new f(this),t=this.addBasemap(),this.map.setView(new ol.View(t)),this.addMapLayers(),this.settings.mapExtent?this.zoomAOI(this.settings.mapExtent):t.extent&&this.map.getView().fit(t.extent,this.map.getSize()),this.mapLayers);this.map.getLayersByName=function(e){for(var t,o=l.length,a={popup:{visible:function(){return!0}},geometry:{bounds:ol.proj.transform(this.getOverlays().getArray()[0].getPosition(),"EPSG:3978","EPSG:4326")}},i=0;i!==o;i+=1)if((t=l[i]).id&&t.id===e)return[{features:[a]}]},this.loadControls(),this.accessibilize(),this.createPopup(),T.on("wb-ready.wb-geomap","#"+this.id,function(){L("#"+this.id).find(".geomap-progress").remove()})}function F(e,t){var o=this,a=[];return this.map=e,this.settings=t,this.id=this.settings.tableId||j(),this.layer=this.createOLLayer(),this.settings.accessible=void 0===this.settings.accessible||this.settings.accessible,this.settings.visible=void 0===this.settings.visible||this.settings.visible,this.settings.zoom=void 0===this.settings.zoom||this.settings.zoom,this.settings.accessible&&this.map.layerDiv.append("<div class='panel panel-default'><div class='panel-heading'><div class='panel-title' role='heading'>"+this.settings.title+"</div></div><div class='panel-body'><div data-layer='"+this.id+"' class='geomap-table-wrapper' style='display:none;'></div></div></div>"),Object.defineProperty(o,"isVisible",{get:function(){return o.visibilityState},set:function(t){o.visibilityState=t,a.forEach(function(e){return e(t)})}}),o.layer?(this.observeVisibility=function(e){a.push(e)},this.observeVisibility(function(e){var t=L("div[ data-layer='"+o.id+"' ].geomap-table-wrapper");e?(t.fadeIn(),t.parent().find(".layer-msg").remove()):(t.fadeOut(),t.parent().append("<div class='layer-msg'><p>"+I.hiddenLayer+"</p></div>").fadeIn()),o.layer.setVisible(o.isVisible)}),0!==this.map.legendDiv.length&&this.addToLegend(),this.isVisible=this.settings.visible,this):null}function v(e,t){var o;return e.getInteractions().forEach(function(e){e instanceof t&&(o=e)}),o}function N(){var e=O(l.drawColours[o],.5),t=O(l.drawColours[o],1);return(o+=1)===l.drawColours.length&&(o=0),{fill:e,stroke:t,transparent:[0,0,0,0]}}function p(){var n,p,c,g,d,o,u,m,y,h,b,f,v=N(),a=(this.createStyleFunction=function(e,t){return o=(d=e)&&d.type?d.type:"default",function(e){return"rule"===o?new a(e,t):"symbol"===o?new i:"default"===o?new l(e,t):"unique"===o?new s(e,t):"select"===o?new r(e,t):void 0}},function(e){for(var t,o,a=d.rule,i=a.length,l={EQUAL_TO:function(e,t){return String(e)===String(t[0])},GREATER_THAN:function(e,t){return e>t[0]},LESS_THAN:function(e,t){return e<t[0]},BETWEEN:function(e,t){return e>=t[0]&&e<=t[1]}},s=e&&e.getGeometry()?e.getGeometry().getType():"Polygon",r=0;r!==i;r+=1)if(o=(t=a[r]).filter,b=t.init.strokeDash||[1,0],f=t.init.strokeWidth||1,m=t.init.fillOpacity||t.init.graphicOpacity||.5,y=t.init.pointRadius||5,h=t.init.strokeColor?O(t.init.strokeColor,m):v.transparent,u=O(t.init.fillColor,m),g=t.init.graphicName||null,n=t.init.externalGraphic||null,p=t.init.graphicHeight||25,c=t.init.graphicWidth||25,l[o](e.attributes[t.field],t.value))switch(s){case"Polygon":return k({fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})});case"Point":return g?w({symbol:g,fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,lineDash:b}),radius:y}):n?x({src:n,opacity:m,size:[c,p]}):S({radius:y,fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})});case"LineString":return C({stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})});default:return k({fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})})}}),i=function(){return m=d.init.fillOpacity||d.init.graphicOpacity||1,y=d.init.pointRadius||5,h=d.init.strokeColor?O(d.init.strokeColor,m):v.transparent,u=O(d.init.fillColor,m),g=d.init.graphicName||null,n=d.init.externalGraphic||null,p=d.init.graphicHeight||25,c=d.init.graphicWidth||25,g?w({symbol:d.init.graphicName,fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,lineDash:b}),radius:y}):n?x({src:n,opacity:m,size:[c,p]}):S({radius:y,fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f})})},l=function(){return m=d.fillOpacity||d.graphicOpacity||1,u=d.fillColor?O(d.fillColor,m):v.transparent,h=d.strokeColor?O(d.strokeColor,m):v.transparent,f=d.strokeWidth||1,b=d.strokeDash||[1,0],[new ol.style.Style({image:new ol.style.Circle({fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f,lineDash:b}),radius:5}),fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})})]},s=function(e,t){var o,a,i=d.field;for(o in d.init)switch(a=d.init[o],b=a.strokeDash||[1,0],f=a.strokeWidth||1,m=a.fillOpacity||a.graphicOpacity||.5,y=a.pointRadius||5,h=a.strokeColor?O(a.strokeColor,m):v.transparent,u=a.fillColor?O(a.fillColor,m):null,p=a.graphicHeight||25,n=a.externalGraphic,c=a.graphicWidth||25,t){case"Polygon":if(e.attributes&&e.attributes[i]===o)return k({fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})});break;case"Point":if(n){if(e.attributes&&e.attributes[i]===o)return x({src:n,opacity:m,size:[c,p]})}else if(e.attributes&&e.attributes[i]===o)return S({radius:y,fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})});break;case"LineString":if(e.attributes&&e.attributes[i]===o)return C({stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})});break;default:if(e.attributes&&e.attributes[i]===o)return k({fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})})}},r=function(e,t){switch(b=d.strokeDash||[1,0],f=d.strokeWidth||1,m=d.fillOpacity||d.graphicOpacity||.5,y=d.pointRadius||5,h=d.strokeColor?O(d.strokeColor,m):v.transparent,u=d.fillColor?O(d.fillColor,m):null,p=d.graphicHeight||25,n=d.externalGraphic,c=d.graphicWidth||25,t){case"Polygon":return k({fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})});case"Point":return n?x({src:n,opacity:m,size:[c,p]}):S({radius:y,fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})});case"LineString":return C({stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})});default:return k({fill:new ol.style.Fill({color:u}),stroke:new ol.style.Stroke({color:h,width:f,lineDash:b})})}}}function w(e){return{square:[new ol.style.Style({image:new ol.style.RegularShape({fill:e.fill,stroke:e.stroke,points:4,radius:e.radius,angle:Math.PI/4})})],triangle:[new ol.style.Style({image:new ol.style.RegularShape({fill:e.fill,stroke:e.stroke,points:3,radius:e.radius,rotation:Math.PI/4,angle:0})})],star:[new ol.style.Style({image:new ol.style.RegularShape({fill:e.fill,stroke:e.stroke,points:5,radius:e.radius,radius2:.4*e.radius,angle:0})})],cross:[new ol.style.Style({image:new ol.style.RegularShape({fill:e.fill,stroke:e.stroke,points:4,radius:e.radius,radius2:0,angle:0})})],x:[new ol.style.Style({image:new ol.style.RegularShape({fill:e.fill,stroke:e.stroke,points:4,radius:e.radius,radius2:0,angle:Math.PI/4})})]}[e.symbol]}function x(e){return[new ol.style.Style({image:new ol.style.Icon({opacity:e.opacity,src:e.src,size:e.size})})]}function S(e){return[new ol.style.Style({image:new ol.style.Circle({radius:e.radius,fill:e.fill,stroke:e.stroke})})]}function k(e){return[new ol.style.Style({fill:e.fill,stroke:e.stroke})]}function C(e){return[new ol.style.Style({stroke:e.stroke})]}function O(e,t){var o=(e+"").trim(),a=null,i=o.match(/^#?(([0-9a-zA-Z]{3}){1,3})$/),t=t||1;return i?(6===(o=i[1]).length?a=[parseInt(o.substring(0,2),16),parseInt(o.substring(2,4),16),parseInt(o.substring(4,6),16),t]:3===o.length&&(a=[parseInt(o.substring(0,1)+o.substring(0,1),16),parseInt(o.substring(1,2)+o.substring(1,2),16),parseInt(o.substring(2,3)+o.substring(2,3),16),t]),a):e}function M(e,t){return"<label class='wb-inv' for='cb_"+t.getId()+"'>"+I.labelSelect+"</label><input type='checkbox' id='cb_"+t.getId()+"' class='geomap-cbx' data-map='"+e.map.id+"' data-layer='"+t.layerId+"' data-feature='"+t.getId()+"' />"}function z(e,t){return"<a href='javascript:;' data-map='"+e.map.id+"' data-layer='"+t.layerId+"' data-feature='"+t.getId()+"' class='btn btn-link geomap-zoomto' alt='"+I.zoomFeature+"' role='button'><span class='glyphicon glyphicon-zoom-in'></span></a>"}function u(e,t,o){if(t){var a=o.getOverlays().getArray()[0],i=L(A.getElementById("popup-geomap-map-"+o.id)),l="",o=E(o,t.layerId);if(t&&t.attributes){var s,r,n=t.getGeometry(),n="Point"===n.getType()?n.getCoordinates():event.mapBrowserEvent.coordinate,p=t.attributes;if(o.popupsInfo)for(s in l+=o.popupsInfo.content,p)Object.prototype.hasOwnProperty.call(p,s)&&(r=new RegExp("_"+s,"igm"),l=l.replace(r,p[s]));else for(s in p)Object.prototype.hasOwnProperty.call(p,s)&&(l+="<tr><th><strong>"+s+"</strong></th><td> "+p[s]+"</td></tr>");i.find(".popup-content").html("<h5>"+t.layerTitle+"</h5><table style='width:100%;'>"+l+"</table>"),a.setPosition(n)}else a.setPosition(void 0)}}function r(e){for(var t,o=b.length-1;-1!==o;--o)if((t=b[o]).id===e)return t}function m(e,t,o){for(var a=[],i=new ol.proj.Projection({code:"EPSG:4326"}),l=e.map.getView().getProjection(),s=D(parseFloat(t[0]),parseFloat(t[1]),parseFloat(t[2]),parseFloat(t[3])),r=s.length-1;-1!==r;--r)a.push([s[r].getCoordinates()[0],s[r].getCoordinates()[1]]);return t=new ol.Feature({geometry:new ol.geom.Polygon([a]).transform(i,l)}),o||E(e.map,"locLayer").getSource().addFeature(t),t}function a(p){var e=A.createElement("button"),t=A.createElement("div");e.innerHTML="?",e.title=I.accessTitle,e.setAttribute("type","button"),e.addEventListener("click",function(){var e=A.createElement("div"),t=A.createElement("header"),o=A.createElement("h3"),a=A.createElement("a"),i=A.createElement("div"),l=A.createAttribute("role"),s=A.createAttribute("title"),r=A.createAttribute("href"),n=(e.className="panel panel-default geomap-help-dialog ol-control ",t.className="panel-heading",o.innerHTML=I.accessTitle,o.className="panel-title",t.appendChild(o),s.value=I.dismiss,a.setAttributeNode(s),r.value="#",a.setAttributeNode(r),l.value="button",a.setAttributeNode(l),a.innerHTML="&#xd7;<span class='wb-inv'>"+I.dismiss+"</span>",a.className="btn btn-link",e.appendChild(a),i.innerHTML="<p>"+I.access+"</p>",i.className="panel-body",e.appendChild(t),e.appendChild(i),new ol.control.Control({element:e}));a.addEventListener("click",function(e){e.preventDefault(),L(e.target).closest(".geomap-help-dialog").fadeOut(function(){p.map.removeControl(n)})}),p.map.addControl(n)},!1),t.className="geomap-help-btn ol-unselectable ol-control",t.appendChild(e),ol.control.Control.call(this,{element:t}),p.map.addControl(this)}function y(d){var e,t=A.createElement("div"),u="setfocus.wb",o=I.geoLocationURL,m={quebec:"<abbr title='Quebec'>QC</abbr>","québec":"<abbr title='Québec'>QC</abbr>","british columbia":"<abbr title='British Columbia'>BC</abbr>","colombie-britannique":"<abbr title='Colombie-Britannique'>BC</abbr>",alberta:"<abbr title='Alberta'>AB</abbr>",saskatchewan:"<abbr title='Saskatchewan'>SK</abbr>",manitoba:"<abbr title='Manitoba'>MB</abbr>",ontario:"<abbr title='Ontario'>ON</abbr>","newfoundland and labrador":"<abbr title='Newfoundland and Labrador'>NL</abbr>","terre-neuve-et-labrador":"<abbr title='Terre-Neuve-et-Labrador'>NL</abbr>","prince edward island":"<abbr title='Prince Edward Island'>PE</abbr>","île-du-prince-édouard":"<abbr title='Île-du-prince-Édouard'>PE</abbr>","nova scotia":"<abbr title='Nova Scotia'>NS</abbr>","nouvelle-écosse":"<abbr title='Nouvelle-Écosse'>NS</abbr>","new brunswick":"<abbr title='New Brunswick'>NB</abbr>","nouveau-brunswick":"<abbr title='Nouveau-Brunswick'>NB</abbr>",yukon:"<abbr title='Yukon'>YT</abbr>","northwest territories":"<abbr title='Northwest Territories'>NT</abbr>","territoires du nord-ouest":"<abbr title='Territoires du Nord-Ouest'>NT</abbr>",nunavut:"<abbr title='Nunavut'>NU</abbr>"},y={"ca.gc.nrcan.geoloc.data.model.Street":"<span class='glyphicon glyphicon-road' aria-hidden='true'></span>","ca.gc.nrcan.geoloc.data.model.Intersection":"<span class='glyphicon glyphicon-road' aria-hidden='true'></span>","ca.gc.nrcan.geoloc.data.model.Geoname":"<span class='glyphicon glyphicon-map-marker' aria-hidden='true'></span>","ca.gc.nrcan.geoloc.data.model.PostalCode":"<span class='glyphicon glyphicon-envelope' aria-hidden='true'></span>","ca.gc.nrcan.geoloc.data.model.NTS":"<span class='glyphicon glyphicon-globe' aria-hidden='true'></span>"};function h(e,t){var o,a,i,l=[],s=new ol.proj.Projection({code:"EPSG:4326"}),r=d.map.getView().getProjection();if(E(d.map,"locLayer").getSource().clear(!0),e&&void 0!==e){for(e=e.split(","),i=(o=D(parseFloat(e[0]),parseFloat(e[1]),parseFloat(e[2]),parseFloat(e[3]))).length-1;-1!==i;--i)l.push([o[i].getCoordinates()[0],o[i].getCoordinates()[1]]);a=new ol.Feature({geometry:new ol.geom.Polygon([l]).transform(s,r)}),E(d.map,"locLayer").getSource().addFeature(a),d.map.getView().fit(a.getGeometry().getExtent(),d.map.getSize())}else t&&void 0!==t&&(e=0===d.map.getView().getZoom()?12:d.map.getView().getZoom(),a=new ol.Feature({geometry:new ol.geom.Point(t.split(",")).transform(s,r)}),E(d.map,"locLayer").getSource().addFeature(a),d.map.getView().setZoom(e),d.map.getView().setCenter(a.getGeometry().getCoordinates()))}function b(e){var t=e.nextSibling.firstChild;v(d.map,ol.interaction.KeyboardPan).setActive(!0),t.className+=" hide",t.innerHTML="",t.setAttribute("aria-hidden","true"),e.setAttribute("aria-expanded","false"),e.setAttribute("aria-activedescendent","")}function f(e,t){var p="",c=L("#wb-geomap-geocode-search-"+d.id),g=L("#wb-geomap-geoloc-al-"+d.id);(!t||t.length<3)&&g.empty(),L.ajax({type:"GET",url:o,data:{q:t+"*"},success:function(e){if(0<e.length){for(var t,o,a,i,l,s,r=e.length,n=0;n!==r;n+=1)i=(o=e[n]).title.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),l=o.bbox?o.bbox[0]+", "+o.bbox[1]+", "+o.bbox[2]+", "+o.bbox[3]:"",s=o.geometry&&"Point"===o.geometry.type?o.geometry.coordinates[0]+", "+o.geometry.coordinates[1]:"",t=y[o.type]||"<span class='glyphicon glyphicon-map-marker' aria-hidden='true'></span>",a=(i=function(e,t){var o=new RegExp(Object.keys(t).join("|"),"gi");return e.replace(o,function(e){return t[e.toLowerCase()]})}(i,m)).replace(/<(?:.|\n)*?>/gm,""),p+="<li id='al-opt-"+d.id+"-"+n+"' class='al-opt' data-lat-lon='"+s+"' data-bbox='"+l+"' data-type='"+o.type+"'><a href='javascript:;' tabindex='-1'>"+t+"<span class='al-val'>"+i+"</span><span class='al-lbl wb-inv' aria-hidden='true'>"+a+"</span></a></li>";g.empty().append(p),g.removeClass("hide").attr("aria-hidden","false"),c.attr("aria-expanded","true"),L(".al-opt a").on("keydown click",function(e){var t,o,a,i,l,s,r,n,p=e.target,c=e.type,g=e.which;switch(c){case"keydown":if(e.ctrlKey||e.metaKey)break;return r=g,a=(t=p).parentNode.parentNode,i=a.parentNode.previousSibling,s=L(i),event.ctrlKey||event.altKey||event.metaKey?void 0:32===r||47<r&&r<91||95<r&&r<112||159<r&&r<177||187<r&&r<223?(i.value+=String.fromCharCode(r),s.trigger(u),f(0,i.value),!1):8===r?(l=i.value,0!==(n=l.length)&&(i.value=l.substring(0,n-1),f(0,i.value)),s.trigger(u),!1):13===r?(n=t.getElementsByTagName("span"),l=n[2].innerHTML,i.value=l,h(t.parentNode.getAttribute("data-bbox"),t.parentNode.getAttribute("data-lat-lon")),s.trigger(u),b(i),!1):9===r||27===r?(s.trigger(u),b(i),!1):38===r||40===r?(38===r?(o=t.parentNode.previousSibling)||(n=a.getElementsByTagName("li"),o=n[n.length-1]):o=(o=t.parentNode.nextSibling)||a.getElementsByTagName("li")[0],o=o.getElementsByTagName("a")[0],i.setAttribute("aria-activedescendent",o.parentNode.getAttribute("id")),L(o).trigger(u),!1):void 0;case"click":if(g&&1!==g)break;return l="a"===(l=p).nodeName.toLowerCase()?l:l.parentNode,s=l.parentNode.parentNode.parentNode.previousSibling,r=L(s),n=l.getElementsByTagName("span")[2].innerHTML,s.value=n,h(l.parentNode.getAttribute("data-bbox"),l.parentNode.getAttribute("data-lat-lon")),r.trigger(u),b(s),!1}})}else g.empty(),g.addClass("hide").attr("aria-hidden","true"),g.attr("aria-expanded","false")},dataType:"json",cache:!1})}t.innerHTML="<label for='wb-geomap-geocode-search-"+d.id+"' class='wb-inv'>"+I.geoCoderLabel+"</label><input type='text' class='form-control' id='wb-geomap-geocode-search-"+d.id+"' placeholder='"+I.geoCoderPlaceholder+"' ></input><div class='wb-geomap-geoloc-al-cnt'><ul role='listbox' id='wb-geomap-geoloc-al-"+d.id+"' class='wb-geomap-geoloc-al hide' aria-hidden='true' aria-live='polite'></ul></div>",t.className="geomap-geoloc ol-unselectable ol-control",ol.control.Control.call(this,{element:t}),d.map.addControl(this),L("#wb-geomap-geocode-search-"+d.id).attr("autocomplete","off"),L("#wb-geomap-geocode-search-"+d.id).attr("role","textbox"),L("#wb-geomap-geocode-search-"+d.id).attr("aria-haspopup","true"),L("#wb-geomap-geocode-search-"+d.id).attr("aria-autocomplete","list"),L("#wb-geomap-geocode-search-"+d.id).attr("aria-owns","wb-geomap-geoloc-al-"+d.id),L("#wb-geomap-geocode-search-"+d.id).attr("aria-activedescendent",""),e=768<(t=parseFloat(L(".geomap-geoloc").parent().width()))?.6:.8,P?(L(".geomap-geoloc").css({width:t-10+"px"}),L(".wb-geomap-geoloc-al-cnt").css({width:t-10+"px"})):(L(".geomap-geoloc").css({width:t*e}),L(".wb-geomap-geoloc-al-cnt").css({width:t*e})),T.on("keydown","#wb-geomap-geocode-search-"+d.id,function(e){v(d.map,ol.interaction.KeyboardPan).setActive(!1);var t=e.which;if(!e.ctrlKey&&!e.metaKey){var o=e.target,a=o.nextSibling.firstChild,i=-1!==a.className.indexOf("hide");if(!(e.ctrlKey||e.altKey||e.metaKey))if(32===t||47<t&&t<91||95<t&&t<112||159<t&&t<177||187<t&&t<223)e.altKey||f(0,o.value+String.fromCharCode(t));else if(8!==t||e.altKey){if((38===t||40===t)&&""===o.getAttribute("aria-activedescendent"))return i&&f(),0!==(a=a.getElementsByTagName("a")).length&&(a=a[38===t?a.length-1:0],o.setAttribute("aria-activedescendent",a.parentNode.getAttribute("id")),L(a).trigger(u)),!1;i||9!==t&&27!==t&&(27!==t||e.altKey)||b(o)}else a=o.value,0!==(i=a.length)&&f(0,a.substring(0,i-1))}})}function h(e){var t,o,a,i,l,e=e||{},s=this;function r(){return o=new ol.Feature,t=new ol.Feature,o.setStyle(S({radius:6,fill:new ol.style.Fill({color:"#3399CC"}),stroke:new ol.style.Stroke({color:"#fff",width:2})})),[t,o]}L("body").append("<section id='overlay-location-error' class='wb-overlay modal-content overlay-def wb-bar-t bg-danger'><header><h2 class='modal-title'>Geolocation error.</h2></header></section>"),L("#overlay-location-error").trigger("wb-init.wb-overlay"),(a=A.createElement("button")).setAttribute("type","button"),a.setAttribute("title",I.geolocBtn),a.innerHTML="<span class='glyphicon glyphicon-screenshot'></span>",(l=A.createElement("div")).className="ol-geolocate ol-unselectable ol-control",l.appendChild(a),s.geolocation=new ol.Geolocation(e),ol.control.Control.call(this,{element:l,target:e.target}),s.geolocation.on("change:accuracyGeometry",function(){t.setGeometry(s.geolocation.getAccuracyGeometry())}),s.geolocation.on("change:position",function(){i=s.geolocation.getPosition(),o.setGeometry(i?new ol.geom.Point(i):null);var e=s.featuresOverlay.getSource().getExtent();s.getMap().getView().fit(e,s.getMap().getSize()),L(a).html("<span style='color:#3399CC;' class='glyphicon glyphicon-screenshot'></span>")}),s.geolocation.on("error",function(e){2===e.code?L("#overlay-location-error h2.modal-title").text(I.geolocUncapable):L("#overlay-location-error h2.modal-title").text(I.geolocFail),L("#overlay-location-error").trigger("open.wb-overlay")}),a.addEventListener("click",function(){L(this).html("<span style='font-size:.9em;' class='glyphicon glyphicon-refresh glyphicon-spin'></span>"),void 0===s.featuresOverlay?(s.featuresOverlay=new ol.layer.Vector({map:s.getMap(),source:new ol.source.Vector({})}),s.featuresOverlay.getSource().addFeatures(r()),s.geolocation.setTracking(!0)):0===s.featuresOverlay.getSource().getFeatures().length?(s.featuresOverlay.getSource().addFeatures(r()),s.geolocation.setTracking(!0)):(s.geolocation.setTracking(!1),s.featuresOverlay.getSource().clear(),L(this).html("<span class='glyphicon glyphicon-screenshot'></span>"))},!1)}function n(e){var t,o=r(e.getAttribute("data-map"));return e.getAttribute("data-layer")?(t=E(o.map,e.getAttribute("data-layer")),[o.map,t,e.getAttribute("data-feature")?t.getSource().getFeatureById(e.getAttribute("data-feature")):null]):[o.map,null,null]}function j(){return Math.random().toString(36).slice(2)}var c,I,i,g="wb-geomap",d="."+g,T=l.doc,o=0,b=[],P=!1,G={overlays:[],tables:[],useScaleLine:!1,useMousePosition:!1,useLegend:!1,useMapControls:!0,useGeocoder:!1,useGeolocation:!1,useAOI:!1},f=function(e){return this.map=e,this.symbolMapArray=[],this.target=L("#"+e.id+".wb-geomap").find(".wb-geomap-legend"),this.target.attr("id","geomap-legend-"+e.id),this.target.empty(),this},V=function(o){var e=o.settings.useMapControls?ol.control.defaults({attributionOptions:{collapsible:!1}}):[],t=o.settings.useMapControls?ol.interaction.defaults({mouseWheelZoom:!0}):[],a=(proj4.defs("EPSG:3978","+proj=lcc +lat_1=49 +lat_2=77 +lat_0=49 +lon_0=-95 +x_0=0 +y_0=0 +ellps=GRS80 +datum=NAD83 +units=m +no_defs"),proj4.defs("urn:ogc:def:crs:OGC:1.3:CRS84",proj4.defs("EPSG:4326")),new ol.Map({controls:e,interactions:t,logo:!1,target:o.mapDiv.attr("id")})),e=v(a,ol.interaction.MouseWheelZoom);o.settings.useMapControls&&e&&e.setActive(!1),o.mapDiv.height(o.mapDiv.width()*o.settings.aspectRatio),a.set("aspectRatio",o.settings.aspectRatio),a.id=o.id,a.once("postrender",function(){a.getLayer=function(e){var t,o,e=A.querySelector(e+" [data-geometry][data-type]"),a=e.dataset.geometry;return"wkt"===e.dataset.type&&-1!==a.indexOf("POINT")&&(a=(a=(a=a.replace(/,/,"")).substring(a.indexOf("(")+1,a.indexOf(")"))).split(" "),t=parseFloat(a[0]),o=parseFloat(a[1])),{getDataExtent:function(){return[t,o]}}},a.zoomToExtent=function(e){a.getView().setCenter(ol.proj.transform([e[0],e[1]],"EPSG:4326","EPSG:3978")),a.getView().setZoom(5)},l.ready(L("#"+o.id),g,[a])}),a.on("moveend",function(){L(o.id).trigger("wb-updated"+d,[o.map])}),o.mapDiv.append("<div id='tooltip_"+o.id+"' style='display:none;'><span class='tooltip-txt'></span></div>");return a.on("pointermove",function(e){var t;i=L("#tooltip_"+o.id),!e.dragging&&(e=a.getEventPixel(e.originalEvent),i=L("#tooltip_"+o.id),t=L("#tooltip_"+o.id+" span.tooltip-txt"),i.css({left:e[0]+"px",top:e[1]-15+"px",position:"absolute"}),e=a.forEachFeatureAtPixel(e,function(e){return e}))&&e.tooltip?(i.hide(),t.html(e.tooltip),i.show()):i.hide()}),a},D=function(e,t,o,a){var i,l=parseFloat(e),s=parseFloat(t),r=parseFloat(o),n=parseFloat(a),p=[];if(0===l.length||0===s.length||0===r.length||0===n.length)return!1;for(-180===l&&(l+=.1),180===r&&(r=-5),90===n&&(n-=3),-90===s&&(s=35),i=l;i<r;i+=.5)p.push(new ol.geom.Point([i,s]));for(p.push(new ol.geom.Point([r,s])),i=r;l<i;i-=.5)p.push(new ol.geom.Point([i,n]));return p.push(new ol.geom.Point([l,n])),p.push(new ol.geom.Point([l,s])),p},E=function(e,t){var o;return e.getLayers().forEach(function(e){t===e.id&&(o=e)}),o};T.on("click",".geomap-zoomto",function(e){var t,o,a=e.which,i="A"===e.target.tagName?e.target:L(e.target).closest("a")[0];a&&1!==a||(e.preventDefault(),a=i.getAttribute("data-map"),t=(i=(e=n(i))[2].getGeometry()).getExtent(),o=e[0].getView(),"Point"===i.getType()?(o.fit(t,e[0].getSize()),o.setZoom(10)):o.fit(t,e[0].getSize()),L("#"+a+" .wb-geomap-map").trigger("setfocus.wb"))}),T.on("geomap.wb",d,function(e){var t,o=e.target,a=o.className.split(/\s+/),i={};/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)&&(P=!0),e.currentTarget===o&&(t=L(o),I||(c=l.i18n,I={add:c("add"),close:c("close"),colon:c("colon"),err:c("err"),hiddenLayer:c("geo-hdnlyr"),toggleLayer:c("geo-tgllyr"),labelSelect:c("geo-lblsel"),select:c("geo-sel"),zoomFeature:c("geo-zmfeat"),zoomin:c("geo-zmin"),zoomout:c("geo-zmout"),zoomworld:c("geo-zmwrld"),baseMapTitle:c("geo-bmapttl"),baseMapURL:c("geo-bmap-url"),baseMapMatrixSet:c("geo-bmap-matrix-set"),scaleline:c("geo-sclln"),mouseposition:c("geo-msepos"),access:c("geo-ally"),accessTitle:c("geo-allyttl"),attribLink:c("geo-attrlnk"),attribTitle:c("geo-attrttl"),ariaMap:c("geo-ariamap"),geoLocationURL:c("geo-locurl-geogratis"),geoCoderPlaceholder:c("geo-loc-placeholder"),geoCoderLabel:c("geo-loc-label"),aoiNorth:c("geo-aoi-north"),aoiEast:c("geo-aoi-east"),aoiSouth:c("geo-aoi-south"),aoiWest:c("geo-aoi-west"),aoiInstructions:c("geo-aoi-instructions"),aoiTitle:c("geo-aoi-title"),aoiBtnDraw:c("geo-aoi-btndraw"),aoiBtnClear:c("geo-aoi-btnclear"),aoiBtnClose:c("close"),geolocBtn:c("geo-geoloc-btn"),geolocFail:c("geo-geoloc-fail"),geolocUncapable:c("geo-geoloc-uncapable"),geoLgndGrphc:c("geo-lgnd-grphc"),dismiss:c("dismiss")}),e={useScaleLine:-1!==a.indexOf("scaleline")||void 0,useMousePosition:-1!==a.indexOf("position")||void 0,useLegend:-1!==a.indexOf("legend"),useMapControls:-1===a.indexOf("static"),useGeocoder:-1!==a.indexOf("geocoder"),useGeolocation:-1!==a.indexOf("geolocation"),useAOI:-1!==a.indexOf("aoi"),useAOIOpen:-1!==a.indexOf("aoi-open")},L.extend(i,G,e,l.getData(t,g)),i.layersFile?L.ajax({url:i.layersFile,async:!0,dataType:"script",success:function(){(i=L.extend(i,wet_boew_geomap))&&i.basemap&&i.basemap.type&&"xyz"===i.basemap.type&&(i.basemap.type="osm"),o.geomap=new s({target:t,settings:i}),b.push(o.geomap)}}):(o.geomap=new s({target:t,settings:i}),b.push(o.geomap))),l.getMap=r,l.getLayer=function(e,t){return E(e,t)}}),T.on(l.resizeEvents,function(i){b.forEach(function(e){var t=L(e.map.getTargetElement()),o=t.find(".geomap-geoloc"),a=t.width();t.height(a*e.map.get("aspectRatio")),e.map.updateSize(),o&&("mediumview"===i.type||"largeview"===i.type||"xlargeview"===i.type?o.css({width:"60%"}):o.css({width:"80%"}))})}),T.on("change",".geomap-cbx",function(e){var e=e.target,t=n(e)[2],o=n(e)[0],o=v(o,ol.interaction.Select);e.checked?o.getFeatures().push(t):o.getFeatures().remove(t)}),T.on("focusin focusout mouseover mouseout",".wb-geomap-map",function(e){var t=e.currentTarget,e=e.type,o=t.className.indexOf("active"),a=r(t.getAttribute("data-map")),i=v(a.map,ol.interaction.MouseWheelZoom);a.settings.useMapControls&&i.setActive(!1),"mouseover"===e||"focusin"===e?(o&&L(t).addClass("active"),"focusin"===e&&a.settings.useMapControls&&i.setActive(!0)):0<o&&L(t).removeClass("active")}),s.prototype.addBasemap=function(){var e,t,o,a,i,l=this,s=l.settings.basemap,r=s&&0!==s.length,n={},p=[],c={},g=[];if(l.settings.attribution&&(c.attributions=[new ol.Attribution({html:l.settings.attribution.text})]),r)n.extent=s.mapOptions&&s.mapOptions.maxExtent?s.mapOptions.maxExtent.split(",").map(Number):null,n.projection=s.mapOptions&&s.mapOptions.projection?s.mapOptions.projection:"EPSG:3857",n.center=l.settings&&l.settings.center?ol.proj.transform(l.settings.center,"EPSG:4326",n.projection):s.mapOptions&&s.mapOptions.center?ol.proj.transform(s.mapOptions.center,"EPSG:4326",n.projection):s.mapOptions&&s.mapOptions.maxExtent?ol.extent.getCenter(n.extent):[0,0],n.zoom=l.settings&&l.settings.zoom?l.settings.zoom:s.mapOptions&&s.mapOptions.zoomLevel?s.mapOptions.zoomLevel:2,"wms"===s.type?((r=function(e,t){var o,a={};for(o in e)Object.prototype.hasOwnProperty.call(e,o)&&L.inArray(o,t)<0&&(a[o]=e[o]);return a}(s,["mapOptions","url"])).srs=n.projection,r.crs=n.projection,g.push(new ol.layer.Image({extent:n.extent,source:new ol.source.ImageWMS({url:s.url,params:r})}))):"esri"===s.type?(c.url=s.url.replace("/MapServer/export","/MapServer"),g.push(new ol.layer.Tile({extent:n.extent,source:new ol.source.TileArcGISRest(c)}))):"xyz"===s.type?(Array.isArray(s.url)?(L.each(s.url,function(e,t){p.push(t.replace(/\${/g,"{"))}),c.urls=p):c.url=s.url.replace(/\${/g,"{"),g.push(new ol.layer.Tile({source:new ol.source.XYZ(c)}))):"osm"===s.type?g.push(new ol.layer.Tile({source:new ol.source.OSM({attributions:[ol.source.OSM.ATTRIBUTION]})})):"mapquest"===s.type&&g.push(new ol.layer.Tile({source:new ol.source.MapQuest({layer:"sat"})}));else{for(r=ol.proj.get("EPSG:3978"),t=[38364.660062653464,22489.62831258996,13229.193125052918,7937.5158750317505,4630.2175937685215,2645.8386250105837,1587.5031750063501,926.0435187537042,529.1677250021168,317.50063500127004,185.20870375074085,111.12522225044451,66.1459656252646,38.36466006265346,22.48962831258996,13.229193125052918,7.9375158750317505,4.6302175937685215],o=5,260<(c=this.mapDiv.width())&&c<=500?o=1:500<c&&c<=725?o=2:725<c&&c<=1175?o=3:1175<c&&c<=2300&&(o=4),a=o-1;-1!==a;--a)t.shift();for(i=new Array(t.length),e=0;e<t.length;++e)i[e]=o+e;n={extent:[-275e4,-9e5,36e5,463e4],resolutions:t,projection:r},g.push(new ol.layer.Tile({source:new ol.source.WMTS({attributions:[new ol.Attribution({html:"<a href='"+I.attribLink+"'>"+I.attribTitle+"</a>"})],url:I.baseMapURL,layer:I.baseMapTitle,matrixSet:I.baseMapMatrixSet,projection:r,tileGrid:new ol.tilegrid.WMTS({extent:[-275e4,-9e5,36e5,463e4],origin:[-34655800,3931e4],resolutions:t,matrixIds:i}),style:"default"})}))}for(var d=g.length-1;-1!==d;--d)l.map.addLayer(g[d]);var u,m=n,y={};for(u in m)Object.prototype.hasOwnProperty.call(m,u)&&null!==m[u]&&(y[u]=m[u]);return y},F.prototype.addToLegend=function(){var t,e,o=this,a=this.map.legend.target,i=a.find("fieldset");0===i.length&&(i=L("<fieldset name='legend'><legend class='wb-inv'>"+I.toggleLayer+"</legend></fieldset>").appendTo(a)),e=this.isVisibile?"checked='checked'":"",(a=0===(a=a.find("ul.geomap-lgnd")).length?L("<ul class='list-unstyled geomap-lgnd'></ul>").appendTo(i):a).closest(".wb-geomap").hasClass("two-cols-legend")&&a.addClass("colcount-md-2"),t=L("<input type='checkbox' id='cb_"+this.id+"' class='geomap-lgnd-cbx' value='"+this.id+"' "+e+" data-map='"+this.map.id+"' data-layer='"+this.id+"' />"),o.observeVisibility(function(e){L("#sb_"+o.id).toggle(e),o.map.legend.refresh(),t.get(0).checked=e}),t.change(function(){o.isVisible=L(this).is(":checked")}),i=L("<label>",{for:"cb_"+this.id,text:this.settings.title}).prepend(t),e=L("<li class='checkbox geomap-lgnd-layer'>").append(i,"<div id='sb_"+this.id+"'></div>"),a.append(e),this.settings.options&&this.settings.options.legendUrl?L("#sb_"+this.id).append("<img src='"+this.settings.options.legendUrl+"' alt='"+I.geoLgndGrphc+"'/>"):this.settings.options&&this.settings.options.legendHTML?L("#sb_"+this.id).append(this.settings.options.legendHTML):"wms"!==this.settings.type&&"esritile"!==this.settings.type&&this.map.legend.symbolize(this)},s.prototype.addTabularData=function(){for(var e,t,o,a,i,l,s,r,n,p,c,g,d,u,m,y,h,b,f,v,w,x,S,k="<span class='wb-inv'>"+I.zoomFeature+"</span>",C="<span class='wb-inv'>"+I.select+"</span>",O=new ol.format.WKT,T=/<\/?[^>]+>/gi,P=/\W/g,E=this.settings.tables.length-1;-1!==E;--E)if(S=A.getElementById(this.settings.tables[E].id)){for(e=L(S).wrap("<div data-layer='"+this.settings.tables[E].id+"' class='geomap-table-wrapper'></div>"),t=this.settings.tables[E],o=[],a=[],i=S.getElementsByTagName("th"),r=(s=S.getElementsByTagName("tr")).length,n=this.settings.useMapControls,e.hasClass("wb-tables")&&void 0===e.attr("data-wb-tables")&&e.attr("data-wb-tables",'{ "order": [], "columnDefs": [ { "targets": [ 0, '+(i.length+1)+' ], "orderable": false } ] }'),S=!1!==this.settings.tables[E].visible,l=i.length-1;-1!==l;--l)a[l]=i[l].innerHTML.replace(T,"");for(x=e.find("thead tr").get(0),t.zoom&&n&&((w=A.createElement("th")).innerHTML=k,x.insertBefore(w,x.firstChild)),(w=A.createElement("th")).innerHTML=C,x.appendChild(w),x=N(),x=void 0===t.style?{strokeColor:x.stroke,fillColor:x.fill}:t.style,r=s.length-1;-1!==r;--r){for(p={},m=(c=s[r]).getAttribute("data-type"),u=c.getElementsByTagName("td"),f=0;f<u.length;f+=1)(y=(d=u[f]).getElementsByTagName("script")[0])&&y.parentNode.removeChild(y),p[a[f]]=d.innerHTML;if(null!==m){if("bbox"===m){for(h=c.getAttribute("data-geometry").split(","),b="",v=(g=D(h[0],h[1],h[2],h[3])).length-1;-1!==v;--v)b+=g[v].getCoordinates()[0]+" "+g[v].getCoordinates()[1]+", ";h="POLYGON (("+(b=b.slice(0,-2))+"))"}else"wkt"===m&&-1!==(h=c.getAttribute("data-geometry")).indexOf("POINT")&&(h=h.replace(",",""));(m=O.readFeature(h,{dataProjection:"EPSG:4326",featureProjection:this.map.getView().getProjection()})).setId(j()),m.layerId=t.id,m.layerTitle=e.attr("aria-label"),t.tooltips&&(m.tooltip=t.tooltipText?p[t.tooltipText]:p[Object.keys(p)[0]]),c.setAttribute("id",m.getId().replace(P,"_")),c.insertCell(0).innerHTML=M(this,m),n&&t.zoom&&((c=c.insertCell(-1)).classList.add("text-right"),c.innerHTML=z(this,m)),m.attributes=p,o.push(m)}}S=new F(this,{tableId:e.attr("id"),type:"wkt",visible:S,datatable:t.datatable,popupsInfo:t.popupsInfo,popups:t.popups,tooltips:t.tooltips,tooltipText:t.tooltipText,name:t.id,title:e.attr("aria-label"),features:o,style:x}),this.mapLayers.push(S)}},s.prototype.addMapLayers=function(){var e,o,a=this;a.addTabularData(),L.each(a.settings.overlays,function(e,t){o=new F(a,t),a.mapLayers.push(o)}),(e=new ol.layer.Vector({source:new ol.source.Vector,style:new ol.style.Style({fill:new ol.style.Fill({color:"rgba( 255, 0, 20, 0.1 )"}),stroke:new ol.style.Stroke({color:"#ff0033",width:2}),image:new ol.style.RegularShape({fill:new ol.style.Fill({color:"#ff0033"}),stroke:new ol.style.Stroke({color:"#ff0033",width:5}),points:4,radius:10,radius2:0,angle:0})})})).id="locLayer",a.map.addLayer(e);for(var t=a.mapLayers.length-1;-1!==t;--t)a.mapLayers[t].layer&&a.map.addLayer(a.mapLayers[t].layer)},F.prototype.populateDataTable=function(){if(this.settings.accessible){var e,t=this,o=t.settings.attributes,a=function(){var e,t=0;for(e in o)Object.prototype.hasOwnProperty.call(o,e)&&(t+=1);return t}(),a="<table aria-label='"+t.settings.title+"' id='"+t.id+'\' data-wb-tables=\'{ "order": [], "columnDefs": [ { "targets": [ 0, '+(a+1)+" ], \"orderable\": false } ] }'class='table",i="'><caption>"+t.settings.caption+"</caption>",l="<th><span class='wb-inv'>"+I.select+"</span></th>",s="",r=t.layer.getSource().getFeatures();for(e in t.settings.datatable?a+=" wb-tables":a+=" table-condensed",o)Object.prototype.hasOwnProperty.call(o,e)&&(l+="<th>"+(o[e].alias||o[e])+"</th>");for(var l="<thead><tr>"+l+(t.map.settings.useMapControls&&t.settings.zoom?"<th><span class='wb-inv'>"+I.zoomFeature+"</span></th>":"")+"</tr></thead>",n=0;n<r.length;n+=1){for(e in s+="<tr><td>"+M(t,r[n]),o=r[n].attributes)Object.prototype.hasOwnProperty.call(o,e)&&(s+="<td>"+o[e]+"</td>");s+=t.map.settings.useMapControls&&t.settings.zoom?"<td class='text-right'>"+z(t.map,r[n]):""}return a=L(a+i+l+"<tbody>"+s+"</tbody></table>"),L("div[ data-layer='"+t.id+"'].geomap-table-wrapper").append(a),t.map,L(".wb-tables").trigger("wb-init.wb-tables"),a}},F.prototype.createOLLayer=function(){var s,t,r,n,b,e,o,a,i,f,v,l,w=this,x=w.settings.attributes;return"wms"===w.settings.type?(e=(l=function(e){var t,o={};for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&"type"!==t&&"caption"!==t&&"url"!==t&&"title"!==t&&(o[t]=e[t]);return o}(w.settings)).options.opacity||1,b=new ol.layer.Image({opacity:e,visible:w.settings.visible,source:new ol.source.ImageWMS({url:w.settings.url,params:l})}),w.settings.accessible=!1):"esritile"===w.settings.type?(e={url:w.settings.url,params:w.settings.params},b=new ol.layer.Tile({visible:w.settings.visible,source:new ol.source.TileArcGISRest(e)}),w.settings.accessible=!1):"wkt"===w.settings.type?(a=new p,b=new ol.layer.Vector({visible:w.settings.visible,source:new ol.source.Vector({features:w.settings.features}),style:a.createStyleFunction(w.settings.style,w.settings.featureType)}),w.settings.accessible=!1):"kml"===w.settings.type?(o=!w.settings.style,a=new p,i=N(),b=new ol.layer.Vector({visible:w.settings.visible,source:new ol.source.Vector({url:w.settings.url,format:new ol.format.KML({extractStyles:o})})}),void 0===w.settings.style&&(w.settings.style={strokeColor:i.stroke,fillColor:i.fill}),b.getSource().once("addfeature",function(e){t=e.feature.getGeometry().getType(),o||(e=a.createStyleFunction(w.settings.style,t),b.setStyle(e))}),n=b.getSource(),r=n.on("change",function(){if("ready"===n.getState()){n.unByKey(r);for(var e=this.getFeatures(),t=0,o=e.length;t<o;t+=1){var a,i,l=e[t];for(i in w.settings.style.select&&(w.settings.style.select.type="select",a=(new p).createStyleFunction(w.settings.style.select,l.getGeometry().getType()),l.selectStyle=a),l.setId(j()),l.layerId=b.id,l.layerTitle=b.title,s={},x)Object.prototype.hasOwnProperty.call(x,i)&&(s[x[i]]=l.getProperties()[i]);l.attributes=s,w.settings.tooltips&&(l.tooltip=w.settings.tooltipText?s[w.settings.tooltipText]:s[Object.keys(s)[0]])}w.populateDataTable(),w.map.legend.symbolize(w)}},n)):"json"===w.settings.type?(f=new ol.source.Vector,a=new p,i=N(),void 0===w.settings.style&&(w.settings.style={strokeColor:i.stroke,fillColor:i.fill}),b=w.settings.cluster?new ol.layer.Vector({visible:w.settings.visible,source:new ol.source.Cluster({distance:40,source:f})}):new ol.layer.Vector({visible:w.settings.visible,source:f}),f.once("addfeature",function(){b.setStyle(a.createStyleFunction(w.settings.style,t))}),v=function(e){var o;return L.each(e,function(e,t){t.coordinates&&(o=e)}),o},L.getJSON(w.settings.url,w.settings.params,function(e){for(var t,o,a,i,l,s,r=w.settings.root,n=e[r]||e.features||e,p=0,c=(n=n instanceof Array==!1?L.map(n,function(e){return e}):n).length;p<c;p+=1)if((o=n[p])[l=l||v(o)]){if(i=o[l].coordinates[0],Object.values(o[l]).includes("Polygon")&&5===i.length){for(var g,d=[],u=0,m=(g=D(i[1][0],i[1][1],i[3][0],i[3][1])).length;u<m;u+=1){var y=g[u];d.push(y.getCoordinates())}a=new ol.geom.Polygon([d])}else Object.values(o[l]).includes("Point")?a=new ol.geom.Point([o[l].coordinates[0],o[l].coordinates[1]]):Object.values(o[l]).includes("LineString")&&(a=new ol.geom.LineString(o[l].coordinates));for(var h in i=a.transform("EPSG:4326",w.map.map.getView().getProjection()),t={},x)Object.prototype.hasOwnProperty.call(x,h)&&((s=x[h].path)?t[x[h].alias]=o[s]?o[s][h]:"":t[x[h]]=o[h]||"");(o=new ol.Feature).setId(j()),o.layerId=b.id,o.layerTitle=b.title,o.attributes=t,o.setGeometry(i),f.addFeature(o),w.settings.tooltips&&(o.tooltip=w.settings.tooltipText?t[w.settings.tooltipText]:t[Object.keys(t)[0]])}w.populateDataTable(),w.map.legend.symbolize(w)})):"geojson"!==w.settings.type&&"esrijson"!==w.settings.type&&"topojson"!==w.settings.type||(a=new p,i=N(),void 0===w.settings.style&&(w.settings.style={strokeColor:i.stroke,fillColor:i.fill}),l=w.settings.params?w.settings.url+"?"+L.param(w.settings.params):w.settings.url,(b="geojson"===w.settings.type?new ol.layer.Vector({visible:w.settings.visible,source:new ol.source.Vector({url:l,format:new ol.format.GeoJSON,strategy:ol.loadingstrategy.bbox})}):"topojson"===w.settings.type?new ol.layer.Vector({visible:w.settings.visible,source:new ol.source.Vector({url:l,format:new ol.format.TopoJSON})}):new ol.layer.Vector({visible:w.settings.visible,source:new ol.source.Vector({url:l,format:new ol.format.EsriJSON,strategy:ol.loadingstrategy.bbox})})).getSource().once("addfeature",function(e){t=e.feature.getGeometry().getType();e=a.createStyleFunction(w.settings.style,t);b.setStyle(e)}),n=b.getSource(),r=n.on("change",function(){if("ready"===n.getState()){n.unByKey(r);for(var e=this.getFeatures(),t=0,o=e.length;t<o;t+=1){var a,i=e[t];for(a in i.setId(j()),i.layerId=b.id,i.layerTitle=b.title,s={},x)Object.prototype.hasOwnProperty.call(x,a)&&(s[x[a]]=i.getProperties()[a]);i.attributes=s,w.settings.tooltips&&(i.tooltip=w.settings.tooltipText?s[w.settings.tooltipText]:s[Object.keys(s)[0]])}w.populateDataTable(),w.map.legend.symbolize(w)}},n)),b?(b.id=w.id,b.title=w.settings.title,b.datatable=w.settings.datatable,b.popupsInfo=w.settings.popupsInfo,b.popups=w.settings.popups,b):null},s.prototype.loadControls=function(){var o,l,e,t,a,i,s,r,n,p,c=this,g=c.map,d=new ol.interaction.Select({layers:c.layers});d.getFeatures().on("remove",function(e){e=e.element;e.setStyle(null),L("#cb_"+e.getId()).prop("checked",!1).closest("tr").removeClass("active")}),d.getFeatures().on("add",function(e){e=e.element;e.selectStyle&&e.setStyle(e.selectStyle),L("#cb_"+e.getId()).prop("checked",!0).closest("tr").addClass("active")}),d.on("select",function(e){var t=e.selected||this.getFeatures();t&&0<t.length&&void 0!==t[0].layerTitle&&(o=E(this.getMap(),t[0].layerId).popups,t[0].layerTitle=this.getLayer(e.selected[0]).title,o)&&u(0,t[0],g),t&&0===t.length&&u(0,null,g)},d),g.getInteractions().extend([d]),c.settings.useMapControls&&((d=A.createElement("span")).className="glyphicon glyphicon-home",d=new ol.control.ZoomToExtent({extent:g.getView().calculateExtent(g.getSize()),label:d}),g.addControl(d),d.element.setAttribute("aria-label",I.zoomworld),d.element.setAttribute("title",I.zoomworld),c.settings.useMousePosition&&(d=new ol.control.MousePosition({coordinateFormat:ol.coordinate.createStringXY(4),projection:"EPSG:4326",undefinedHTML:""}),g.addControl(d),d.element.setAttribute("aria-label",I.mouseposition),d.element.setAttribute("title",I.mouseposition)),c.settings.useScaleLine)&&(d=new ol.control.ScaleLine,g.addControl(d),d.element.setAttribute("aria-label",I.scaleline),d.element.setAttribute("title",I.scaleline)),c.settings.useGeocoder&&new y(c),c.settings.useAOI&&(l=c,r=new ol.interaction.DragBox,n=new ol.proj.Projection({code:"EPSG:4326"}),p=l.map.getView().getProjection(),r.on("boxend",function(){var e=r.getGeometry().transform(p,n).getExtent();s=m(l,e),l.map.getView().fit(s.getGeometry().getExtent(),l.map.getSize()),L("#geomap-aoi-minx-"+l.id).val(e[0].toFixed(6)),L("#geomap-aoi-maxx-"+l.id).val(e[2].toFixed(6)),L("#geomap-aoi-maxy-"+l.id).val(e[3].toFixed(6)),L("#geomap-aoi-miny-"+l.id).val(e[1].toFixed(6)),L("#geomap-aoi-extent-"+l.id).val(s.getGeometry().getExtent()).trigger("change"),L("#geomap-aoi-extent-lonlat-"+l.id).val(e[0]+", "+e[1]+", "+e[2]+", "+e[3]).trigger("change")}),r.on("boxstart",function(){E(l.map,"locLayer").getSource().clear(!0)}),l.map.addInteraction(r),r.setActive(!1),l.settings.useAOIOpen?l.mapDiv.before("<div class='geomap-aoi panel panel-default'><div id='geomap-aoi-"+l.id+"' class='panel-body'></div></div>"):l.mapDiv.before("<details class='geomap-aoi'><summary>"+I.aoiTitle+"</summary><div id='geomap-aoi-"+l.id+"'></div></details>"),L("#geomap-aoi-"+l.id).append("<fieldset id='form-aoi-"+l.id+"'><legend tabindex='-1'>"+I.aoiInstructions+"</legend><div class='row'><div class='col-md-2 form-group'><label for='geomap-aoi-maxy-"+l.id+"' class='wb-inv'>"+I.aoiNorth+"</label><div class='input-group input-group-sm'><span class='input-group-addon'>"+I.aoiNorth.charAt(0)+"</span><input type='number' id='geomap-aoi-maxy-"+l.id+"' placeholder='90' class='form-control input-sm' min='-90' max='90' step='0.000001'></input></div></div><div class='col-md-2 form-group'><label for='geomap-aoi-maxx-"+l.id+"' class='wb-inv'>"+I.aoiEast+"</label><div class='input-group input-group-sm'><span class='input-group-addon'>"+I.aoiEast.charAt(0)+"</span><input type='number' id='geomap-aoi-maxx-"+l.id+"' placeholder='180' class='form-control input-sm' min='-180' max='180' step='0.000001'></input> </div></div><div class='col-md-2 form-group'><label for='geomap-aoi-miny-"+l.id+"' class='wb-inv'>"+I.aoiSouth+"</label><div class='input-group input-group-sm'><span class='input-group-addon'>"+I.aoiSouth.charAt(0)+"</span><input type='number' id='geomap-aoi-miny-"+l.id+"' placeholder='-90' class='form-control input-sm' min='-90' max='90' step='0.000001'></input> </div></div><div class='col-md-2 form-group'><label for='geomap-aoi-minx-"+l.id+"' class='wb-inv'>"+I.aoiWest+"</label><div class='input-group input-group-sm'><span class='input-group-addon'>"+I.aoiWest.charAt(0)+"</span><input type='number' id='geomap-aoi-minx-"+l.id+"' placeholder='-180' class='form-control input-sm' min='-180' max='180' step='0.000001'></input> </div></div><div class='col-md-4'><button class='btn btn-default btn-sm' id='geomap-aoi-btn-draw-"+l.id+"'>"+I.add+"</button> <button class='btn btn-default btn-sm' id='geomap-aoi-btn-clear-"+l.id+"'>"+I.aoiBtnClear+"</button> </div></div><input type='hidden' id='geomap-aoi-extent-"+l.id+"'></input><input type='hidden' id='geomap-aoi-extent-lonlat-"+l.id+"'></input></fieldset>"),L("#geomap-aoi-btn-clear-"+l.id).after("<button id='geomap-aoi-toggle-mode-draw-"+l.id+"' href='#' class='btn btn-sm btn-default geomap-geoloc-aoi-btn' title='"+I.aoiBtnDraw+"'><i class='glyphicon glyphicon-edit'></i> "+I.aoiBtnDraw+"</button>"),T.on("click","#geomap-aoi-toggle-mode-draw-"+l.id,function(e){e.preventDefault();var e=v(l.map,ol.interaction.DragBox),t=v(l.map,ol.interaction.Select),o=r.getActive(),a=L("#geomap-aoi-"+l.id);L(this).toggleClass("active"),o||a.find("legend").trigger("setfocus.wb"),e.setActive(!o),t.setActive(o)}),T.on("click","#geomap-aoi-btn-clear-"+l.id,function(e){e.preventDefault(),L("#geomap-aoi-extent-"+l.id).val(""),L("#geomap-aoi-extent-lonlat-"+l.id).val(""),L("#geomap-aoi-minx-"+l.id).val("").parent().removeClass("has-error"),L("#geomap-aoi-miny-"+l.id).val("").parent().removeClass("has-error"),L("#geomap-aoi-maxx-"+l.id).val("").parent().removeClass("has-error"),L("#geomap-aoi-maxy-"+l.id).val("").parent().removeClass("has-error"),E(l.map,"locLayer").getSource().clear(!0)}),T.on("click","#geomap-aoi-btn-draw-"+l.id,function(e){e.preventDefault(),L("#geomap-aoi-extent-"+l.id).val(""),L("#geomap-aoi-extent-lonlat-"+l.id).val(""),L("#geomap-aoi-minx-"+l.id).parent().removeClass("has-error"),L("#geomap-aoi-maxx-"+l.id).parent().removeClass("has-error"),L("#geomap-aoi-maxy-"+l.id).parent().removeClass("has-error"),L("#geomap-aoi-miny-"+l.id).parent().removeClass("has-error"),E(l.map,"locLayer").getSource().clear(!0);var e=parseFloat(L("#geomap-aoi-minx-"+l.id).val()),t=parseFloat(L("#geomap-aoi-miny-"+l.id).val()),o=parseFloat(L("#geomap-aoi-maxx-"+l.id).val()),a=parseFloat(L("#geomap-aoi-maxy-"+l.id).val()),i=!0;if((!e||e<-180||180<e)&&(L("#geomap-aoi-minx-"+l.id).parent().addClass("has-error"),i=!1),(!o||o<-180||180<o)&&(L("#geomap-aoi-maxx-"+l.id).parent().addClass("has-error"),i=!1),(!a||a<-90||90<a)&&(L("#geomap-aoi-maxy-"+l.id).parent().addClass("has-error"),i=!1),(!t||t<-90||90<t)&&(L("#geomap-aoi-miny-"+l.id).parent().addClass("has-error"),i=!1),!1===i)return!1;i=m(l,[e,t,o,a]),l.map.getView().fit(i.getGeometry().getExtent(),l.map.getSize()),L("#geomap-aoi-extent-"+l.id).val(i.getGeometry().getExtent()).trigger("change"),L("#geomap-aoi-extent-lonlat-"+l.id).val(e+", "+t+", "+o+", "+a).trigger("change")}),l.aoiExtent)&&(e=(d=l.aoiExtent.split(","))[0].trim(),t=d[1].trim(),a=d[2].trim(),i=d[3].trim(),s=m(l,d),l.map.getView().fit(s.getGeometry().getExtent(),l.map.getSize()),L("#geomap-aoi-minx-"+l.id).val(e),L("#geomap-aoi-maxx-"+l.id).val(a),L("#geomap-aoi-maxy-"+l.id).val(i),L("#geomap-aoi-miny-"+l.id).val(t),L("#geomap-aoi-extent-"+l.id).val(s.getBounds().toBBOX()),L("#geomap-aoi-extent-lonlat-"+l.id).val(e+", "+t+", "+a+", "+i)),c.settings.useGeolocation&&c.map.addControl(new h({projection:c.map.getView().getProjection()}))},s.prototype.createPopup=function(){var t,e=L("<div id='popup-"+this.mapDiv.attr("id")+"' class='ol-popup'></div>"),o=L("<a href='#' title='"+I.dismiss+"' class='ol-popup-closer' role='button'>&#xd7;<span class='wb-inv'>"+I.dismiss+"</span></a>");e.append(o,"<div class='popup-content'></div>"),L("#"+this.mapDiv.attr("id")).append(e),t=new ol.Overlay({element:A.getElementById("popup-"+this.mapDiv.attr("id")),autoPan:!0,autoPanAnimation:{duration:250}}),o.on("click",function(e){return e.preventDefault(),t.setPosition(void 0),this.blur(),!1}),this.map.addOverlay(t)},s.prototype.accessibilize=function(){var e=this,t=e.mapDiv.find(".ol-zoom-in"),o=e.mapDiv.find(".ol-zoom-out");t.attr("aria-label",I.zoomin),t.attr("title",I.zoomin),o.attr("aria-label",I.zoomout),o.attr("title",I.zoomout),e.mapDiv.attr({tabindex:"0","data-map":e.id}),e.mapDiv.attr({role:"dialog","aria-label":I.ariaMap}),e.settings.useMapControls&&new a(e)},s.prototype.zoomAOI=function(e,t,o,a){var i,l=this.map,s=l.getView(),r="string"==typeof e;if(r&&!e.length)s.setZoom(s.getMaxZoom);else{if(r&&null!==e.substring(0,1).match(/[a-z]/gi))var n=(new ol.format.WKT).readFeature(e,{dataProjection:"EPSG:4326",featureProjection:s.getProjection()});else{if(!t&&r){if(4!==(i=e.split(" ")).length)throw"4 cardinal point must be provided";e=i[0],t=i[1],o=i[2],a=i[3]}else if(!t||!o||!a)throw"Cardinal point must be provided";i=[parseFloat(a),parseFloat(o),parseFloat(t),parseFloat(e)],n=m(this,i,!0)}r=n.getGeometry().getExtent(),s.fit(r,l.getSize())}},s.prototype.showLayer=function(e,t){var o,a,i,l=[],s=this.mapLayers,r=s.length;for(t=!!t,Array.isArray(e)?l=e:l.push(e),o=0;o!==r;o+=1)i=(a=s[o]).layer.title,e&&-1===l.indexOf(i)?i&&(a.isVisible=!t):a.isVisible=t},f.prototype.symbolize=function(e){if(e.layer){var t,o,a,i,l,s,r,n,p=e.settings.style,c=e.id,g=e.layer.getSource().getFeatures()[0],d=[],u="",m="";if(void 0!==p&&p.rule){if(o=p.rule.length)for(l=0;l!==o;l+=1){if(t=(r=p.rule[l]).filter,a=r.init,m="",n="ls_"+c+"_"+l,t&&!r.name)if(t.name)m=t.name;else switch(t){case"EQUAL_TO":m=r.field+" = "+r.value[0];break;case"GREATER_THAN":m=r.field+" > "+r.value[0];break;case"LESS_THAN":m=r.field+" < "+r.value[0];break;case"BETWEEN":m=r.field+" "+r.value[0]+" - "+r.value[1]}else r&&r.name&&(m=r.name);u+="<li><div class='geomap-legend-element'><div id='"+n+"' class='geomap-legend-symbol'></div><span class='geomap-legend-symbol-text'><small>"+m+"</small></span></div></li>",d.push({id:n,feature:g,symbolizer:a})}}else if(void 0!==p&&"unique"===p.type)for(var y in l=0,p.init)u+="<li><div class='geomap-legend-element'><div id='"+(n="ls_"+c+"_"+l)+"' class='geomap-legend-symbol'></div><span class='geomap-legend-symbol-text'><small>"+(m=(a=p.init[y]).name||y)+"</small></span></div></li>",d.push({id:n,feature:g,symbolizer:a}),l+=1;else void 0!==p&&"symbol"===p.type?u+="<li><div class='geomap-legend-element'><div id='"+(n="ls_"+c+"_0")+"' class='geomap-legend-symbol'></div><span class='geomap-legend-symbol-text'><small>"+(m=(a=p.init).name||"")+"</small></span></div></li>":(a={fillColor:p.fillColor,strokeColor:p.strokeColor,strokeWidth:p.strokeWidth,strokeDash:p.strokeDash},u+="<li><div class='geomap-legend-element'><div id='"+(n="ls_"+c+"_0")+"' class='geomap-legend-symbol'></div><span class='geomap-legend-symbol-text'><small>"+m+"</small></span></div></li>"),d.push({id:n,feature:g,symbolizer:a});for(L("#sb_"+c).closest(".wb-geomap").hasClass("legend-label-only")&&u.match(/<li>/g)&&1===u.match(/<li>/g).length?(L("#sb_"+c).html("<ul class='list-unstyled' role='presentation'>"+u+"</ul>"),L("#sb_"+c+" li").attr("role","presentation")):L("#sb_"+c).html("<ul class='list-unstyled'>"+u+"</ul>"),i=0,s=d.length;i!==s;i+=1){var h=d[i];this.getSymbol(h.id,h.feature,h.symbolizer)}}},f.prototype.getSymbol=function(e,t,o){var a,i,l,s,r,n,p=N(),t=t&&t.getGeometry()?t.getGeometry().getType():"Polygon",c=o.fillOpacity||o.graphicOpacity||1,g=o.fillColor?O(o.fillColor,c):p.transparent,d=o.pointRadius||5,u=o.strokeColor?O(o.strokeColor):p.transparent,m=o.strokeWidth||1,y=o.strokeDash||[1,0],h=o.externalGraphic||null,b=o.graphicName||null,f=o.graphicHeight||30,v=o.graphicWidth||30,p=f<2*d?2*d:f,o=v<2*d?2*d:v;switch(t){case"Polygon":a=new ol.Feature({geometry:new ol.geom.Polygon([[[-10,-7],[10,-7],[10,7],[-10,7]]])}),i=k({fill:new ol.style.Fill({color:g}),stroke:new ol.style.Stroke({color:u,width:m,lineDash:y})}),a.setStyle(i);break;case"Point":a=new ol.Feature({geometry:new ol.geom.Point([0,0])}),i=b?w({symbol:b,fill:new ol.style.Fill({color:g}),stroke:new ol.style.Stroke({color:u,lineDash:y}),radius:d}):h?x({src:h,opacity:c,size:[v,f]}):S({radius:d,fill:new ol.style.Fill({color:g}),stroke:new ol.style.Stroke({color:u,width:m,lineDash:y})}),a.setStyle(i);break;case"LineString":a=new ol.Feature({geometry:new ol.geom.LineString([[-9,-4],[-4,4],[4,-4],[9,4]])}),i=C({stroke:new ol.style.Stroke({color:u,width:m,lineDash:y})}),a.setStyle(i);break;default:a=new ol.Feature({geometry:new ol.geom.Polygon([[[-10,-7],[10,-7],[10,7],[-10,7]]])}),i=k({fill:new ol.style.Fill({color:g}),stroke:new ol.style.Stroke({color:u,width:m,lineDash:y})}),a.setStyle(i)}(t=new ol.Map({controls:[],interactions:[],layers:[new ol.layer.Vector({source:new ol.source.Vector})]}))&&(this.symbolMapArray.push(t),(l=t.getLayers().item(0).getSource()).clear(),l.addFeature(a)),t.setTarget(e),l=e,e=t,t=o,o=p,p=(p=a).getGeometry().getExtent(),s=ol.extent.getWidth(p),r=ol.extent.getHeight(p),l=L("#"+l),n=(n=1)||Math.max(s/t||0,r/o||0)||1,e.setView(new ol.View({minResolution:n,maxResolution:n,projection:new ol.proj.Projection({code:"",units:"pixels"})})),t=Math.max(t,s/n),s=Math.max(o,r/n),n=[(o=ol.extent.getCenter(p))[0]-(r=t*n/2),o[1]-(p=s*n/2),o[0]+r,o[1]+p],l.width(Math.round(t)),l.height(Math.round(s)),e.updateSize(),e.getView().fit(n,e.getSize())},f.prototype.refresh=function(){if(0!==this.symbolMapArray.length)for(var e,t=this.symbolMapArray.length-1;-1!==t;--t)e=this.symbolMapArray[t],L("#"+e.getTarget()).is(":visible")&&e.updateSize()},ol.inherits(h,ol.control.Control),ol.inherits(a,ol.control.Control),ol.inherits(y,ol.control.Control),l.doc.on("submit",".wb-geomap-filter",function(e){e.preventDefault();var e=L(this),o=A.getElementById(e.data("bind-to")).geomap;e.find("select[data-filter]").each(function(){var e=L(this),t=e.find("option:selected").val(),e=e.attr("data-filter");"aoi"===e&&o.zoomAOI(t),"layer"===e&&o.showLayer(t,!0)})}),l.doc.on("click",".wb-geomap-filter [type=reset]",function(){var e=L(this.form),t=A.getElementById(e.data("bind-to")).geomap,o=t.map,a=o.getView();o.getView().fit(a.calculateExtent(o.getSize()),o.getSize()),e.find("select[data-filter=layer] option").each(function(){this.defaultSelected&&t.showLayer(this.value,!0)}),e.find("select[data-filter=aoi] option").each(function(){this.defaultSelected&&t.zoomAOI(this.value)})})}(jQuery,(window,document),wb);