"""
Tests for all API functions dealing with Samples.

Test Coverage:
1. GET /samples
    - test_get_samples_success
    - test_get_samples_not_found
    - test_get_samples_server_error

2. GET /samples/{sms_id}
    - test_get_sample_with_sms_success
    - test_get_sample_with_sms_external_service_unreachable
    - test_get_sample_with_sms_server_error

3. POST /samples
    - test_post_sample_success
    - test_post_sample_already_exists
    - test_post_sample_depth_validation_error
    - test_post_sample_no_SMS_error
    - test_post_sample_server_error
"""

import pytest
from unittest.mock import patch

from tests.test_utils.helpers import TestHelpers
from tests.test_utils.verification import verify_api_error_response, SampleVerifier
from tests.test_utils.constants import (
    SAMPLE_TEST_DATA,
    SAMPLE_TEST_CASES,
    SAMPLE_VERIFICATION
)

from app.common.constants import ERROR_MESSAGES

class TestSamples:
    #####################
    # Test Fixtures
    #####################
    @pytest.fixture(scope="function")
    def test_sample1(self, db):
        """Create first test sample"""
        return TestHelpers.Samples.create_test_sample(db, SAMPLE_TEST_DATA["sample1"])

    @pytest.fixture(scope="function")
    def test_sample2(self, db):
        """Create second test sample"""
        return TestHelpers.Samples.create_test_sample(db, SAMPLE_TEST_DATA["sample2"])

    #####################
    # /samples TESTS
    #####################
    def test_get_samples_success(self, client, user_token, test_sample1, test_sample2):
        """Test successful retrieval of samples data"""
        response = client.get(
            "/samples", 
            headers=TestHelpers.Auth.get_headers(user_token)
        )

        assert response.status_code == 200
        samples = response.json()
        assert len(samples) == 2
        assert any(s["sms_number"] == test_sample1.sms_number for s in samples)
        assert any(s["sms_number"] == test_sample2.sms_number for s in samples)

    def test_get_samples_not_found(self, client, user_token):
        """Test case where there are no samples in the DB"""
        response = client.get(
            "/samples", 
            headers=TestHelpers.Auth.get_headers(user_token)
        )

        assert response.status_code == 200
        assert response.json() == []

    def test_get_samples_server_error(self, client, user_token):
        """Test case where an unexpected error occurs"""
        with patch("app.crud.sample.get_samples", side_effect=Exception("Unexpected error")):
            response = client.get(
                "/samples", 
                headers=TestHelpers.Auth.get_headers(user_token)
            )

        verify_api_error_response(
            response, 
            500, 
            ERROR_MESSAGES["sample"]["list_failed"].format(error="Unexpected error")
        )

    #####################
    # /samples/{sms_id} TESTS
    #####################
    def test_get_sample_with_sms_success(self, client, user_token, test_sample1, test_sample2):
        """Test successful retrieval of sample data"""
        response = client.get(
            f"/samples/{test_sample1.sms_number}", 
            headers=TestHelpers.Auth.get_headers(user_token)
        )

        assert response.status_code == 200
        assert response.json()["sms_number"] == str(test_sample1.sms_number)

    """def test_get_sample_with_sms_not_found(self, client, user_token):
        #Test case where the sample is not found in the database AND external SMS function
        response = client.get(
            "/samples/12345", 
            headers=TestHelpers.Auth.get_headers(user_token)
        )

        verify_api_error_response(
            response, 
            404, 
            SAMPLE_ERROR_MESSAGES["not_found"]
        )
    """ #Need to finish external SMS to finish this

    def test_get_sample_with_sms_external_service_unreachable(self, client, user_token):
        """Test case where external SMS service is unreachable"""
        with patch("app.crud.sample.get_sample_by_SMS", side_effect=ConnectionError("Unexpected error")):
            response = client.get(
                "/samples/12345", 
                headers=TestHelpers.Auth.get_headers(user_token)
            )

        verify_api_error_response(
            response, 
            502, 
            ERROR_MESSAGES["sample"]["sms_unreachable"]
        )

    def test_get_sample_with_sms_server_error(self, client, user_token):
        """Test case where an unexpected error occurs"""
        with patch("app.crud.sample.get_sample_by_SMS", side_effect=Exception("Unexpected error")):
            response = client.get(
                "/samples/12345", 
                headers=TestHelpers.Auth.get_headers(user_token)
            )

        verify_api_error_response(
            response, 
            500, 
            ERROR_MESSAGES["sample"]["get_failed"].format(error="Unexpected error")
        )

    #####################
    # /samples POST TESTS
    #####################
    def test_post_sample_success(self, client, user_token):
        """Test successful sample creation"""
        response = client.post(
            "/samples", 
            json=SAMPLE_TEST_CASES["create"]["valid"], 
            headers=TestHelpers.Auth.get_headers(user_token)
        )

        assert response.status_code == 200
        response_data = response.json()
        SampleVerifier.verify_common_fields(response_data)
        SampleVerifier.verify_response(response_data, {
            "sms_number": str(SAMPLE_TEST_CASES["create"]["valid"]["sms_number"])
        })

    def test_post_sample_already_exists(self, client, user_token, test_sample1):
        """Test creating a sample that already exists"""
        response = client.post(
            "/samples", 
            json=SAMPLE_TEST_CASES["create"]["valid"], 
            headers=TestHelpers.Auth.get_headers(user_token)
        )

        verify_api_error_response(
            response, 
            403,
            ERROR_MESSAGES["sample"]["already_exists"].format(
                sms_number=SAMPLE_TEST_CASES["create"]["valid"]["sms_number"]
            )
        )

    def test_post_sample_depth_validation_error(self, client, user_token):
        """Test having wrong depth values submitted"""
        response = client.post(
            "/samples", 
            json=SAMPLE_TEST_DATA["invalid_depth_sample"], 
            headers=TestHelpers.Auth.get_headers(user_token)
        )

        verify_api_error_response(
            response, 
            422, 
            ERROR_MESSAGES["sample"]["depth_validation"]
        )

    def test_post_sample_no_SMS_error(self, client, user_token):
        """Test creating a sample without SMS number"""
        response = client.post(
            "/samples", 
            json=SAMPLE_TEST_DATA["missing_sms_sample"], 
            headers=TestHelpers.Auth.get_headers(user_token)
        )

        verify_api_error_response(
            response, 
            422, 
            None
        )

    def test_post_sample_server_error(self, client, user_token):
        """Test server error during sample creation"""
        with patch("app.crud.sample.get_sample_by_SMS", side_effect=Exception("Unexpected error")):
            response = client.post(
                "/samples", 
                json=SAMPLE_TEST_CASES["create"]["valid"], 
                headers=TestHelpers.Auth.get_headers(user_token)
            )

        verify_api_error_response(
            response, 
            500, 
            ERROR_MESSAGES["sample"]["create_failed"].format(error="Unexpected error")
        )