"""
Tests for the /staff API endpoint

Can't use constants when dealing with staff since its dependent on user's uuids
    create_new_staff
        1. test_create_new_staff_wrong_user_id - user_id doesn't exist
        2. test_create_new_staff_impossible_role - role doesn't exist
        3. test_create_new_staff_no_data - No data sent with request
        4. test_create_new_staff_permission_error - A lab personal trying to create a new staff
        5. test_create_new_staff_scientist - scientist chosen
        6. test_create_new_staff_staff_exists - combo already exists
        7. test_create_new_staff_server_error - db error
        8. test_create_new_staff_proper - Everything works properly
    
    edit_staff
        1. test_edit_existing_staff_wrong_user_id - user_id doesn't exist
        2. test_edit_existing_staff_wrong_lab_id - lab_id doesn't exist
        3. test_edit_existing_staff_wrong_staff_id - staff_id doesn't exist
        4. test_edit_existing_staff_wrong_role - role doesn't exist
        5. test_edit_existing_staff_no_access_lab - trying to edit a different lab_id
        6. test_edit_existing_staff_no_data - No data sent with request
        7. test_edit_existing_staff_not_auth - Not a lab_admin
        8. test_edit_existing_staff_scientist - trying to change into a scientist
        9. test_edit_existing_staff_removing_last_admin - removing last lab_admin
        10. test_edit_existing_staff_combo_exists - combo already exists
        11. test_edit_existing_staff_db_error - DB error
        12. test_edit_existing_staff_success - Successful
    
    delete_existing_staff
        1. test_delete_existing_staff_does_not_exist - Staff does not exist
        2. test_delete_existing_staff_no_authorized - Trying to delete not as lab_admin
        3. test_delete_existing_staff_incorrect_lab - Trying to delete a staff apart of a different lab
        4. test_delete_existing_staff_delete_scientist - Trying to delete a scientist
        5. test_delete_existing_staff_last_admin - removing last admin
        6. test_delete_existing_staff_db_error - db error
        7. ## - successful
"""

import pytest
import uuid
from unittest.mock import patch
from app.common.constants import UserRole, ERROR_MESSAGES

from tests.test_utils.helpers import TestHelpers
from tests.test_utils.constants import STAFF_VERIFICATION
from tests.test_utils.verification import verify_api_error_response

class TestStaff:
    #####################
    # POST /staff TESTS
    #####################
    def test_create_new_staff_wrong_user_id(self, client, admin_token):
        temp_data = {
            "user_id": str(uuid.uuid4()),
            "role": UserRole.LAB_ADMIN
        }
        response = client.post("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 422, ERROR_MESSAGES["staff"]["missing_id"]
        )
    
    def test_create_new_staff_impossible_role(self, client, admin_token, test_staff_admin):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "role": "lab_worker"
        }
        response = client.post("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        assert response.status_code == 422
        assert response.json() == STAFF_VERIFICATION["impossible_role"]
    
    def test_create_new_staff_no_data(self, client, admin_token):
        response = client.post("/staff", headers=TestHelpers.Auth.get_headers(admin_token))

        assert response.status_code == 422
        assert response.json() == STAFF_VERIFICATION["missing_data"]
    
    def test_create_new_staff_permission_error(self, client, lab_personnel_token, test_staff_lab_personal):
        temp_data = {
            "user_id": str(test_staff_lab_personal.user_id),
            "role": "lab_admin"
        }
        response = client.post("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(lab_personnel_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["not_authorized"] 
        )
    
    def test_create_new_staff_scientist(self, client, admin_token, test_staff_admin):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "role": "scientist"
        }
        response = client.post("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["scientist_lab"] 
        )
    
    def test_create_new_staff_staff_exists(self, client, admin_token, test_staff_admin):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "role": "lab_admin"
        }
        response = client.post("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["combo_exists"] 
        )
    
    def test_create_new_staff_server_error(self, client, admin_token, test_staff_admin):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "role": "lab_personnel"
        }
        with patch("app.api.staff.create_staff", side_effect=Exception("Unexpected Error")):
            response = client.post("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 500, ERROR_MESSAGES["staff"]["server_error"] 
        )
    
    def test_create_new_staff_proper(self, client, admin_token, test_staff_admin, test_lab_personnel):
        temp_data = {
            "user_id": str(test_lab_personnel.user_id),
            "role": "lab_personnel"
        }
        response = client.post("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        assert response.status_code == 200
        assert response.json()["user_id"] == str(test_lab_personnel.user_id)
        assert response.json()["lab_id"] == str(test_staff_admin.lab_id)
        assert response.json()["role"] == "lab_personnel"


    #####################
    # PUT /staff TESTS
    #####################
    def test_edit_existing_staff_wrong_user_id(self, client, admin_token, test_staff_admin):
        temp_data = {
            "user_id": str(uuid.uuid4()),
            "lab_id": str(test_staff_admin.lab_id),
            "staff_id": str(test_staff_admin.staff_id),
            "role": test_staff_admin.role
        }
        response = client.put("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["not_found"]
        )
    
    def test_edit_existing_staff_wrong_lab_id(self, client, admin_token, test_staff_admin):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "lab_id": str(uuid.uuid4()),
            "staff_id": str(test_staff_admin.staff_id),
            "role": test_staff_admin.role
        }
        response = client.put("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))
        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["not_authorized"]
        )
    
    def test_edit_existing_staff_wrong_staff_id(self, client, admin_token, test_staff_admin):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "lab_id": str(test_staff_admin.lab_id),
            "staff_id": str(uuid.uuid4()),
            "role": "lab_personnel"
        }
        response = client.put("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["not_found"]
        )
    
    def test_edit_existing_staff_wrong_role(self, client, admin_token, test_staff_admin):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "lab_id": str(test_staff_admin.lab_id),
            "staff_id": str(test_staff_admin.staff_id),
            "role": "lab_worker"
        }
        response = client.put("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))
        assert response.status_code == 422
        assert response.json() == STAFF_VERIFICATION["impossible_role"]
    
    def test_edit_existing_staff_no_access_lab(self, client, admin_token, test_staff_admin, test_labs):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "lab_id": str(test_labs[1].lab_id),
            "staff_id": str(test_staff_admin.staff_id),
            "role": "lab_personnel"
        }
        response = client.put("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))
        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["not_authorized"]
        )
    
    def test_edit_existing_staff_no_data(self, client, admin_token, test_staff_admin):
        response = client.put("/staff", headers=TestHelpers.Auth.get_headers(admin_token))

        assert response.status_code == 422
        assert response.json() == STAFF_VERIFICATION["missing_data"]
    
    def test_edit_existing_staff_not_auth(self, client, lab_personnel_token, test_staff_admin):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "lab_id": str(test_staff_admin.lab_id),
            "staff_id": str(test_staff_admin.staff_id),
            "role": "lab_personnel"
        }
        response = client.put("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(lab_personnel_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["not_authorized"]
        )
    
    def test_edit_existing_staff_scientist(self, client, admin_token, test_staff_multiple_admins):
        temp_data = {
            "user_id": str(test_staff_multiple_admins[0].user_id),
            "lab_id": str(test_staff_multiple_admins[0].lab_id),
            "staff_id": str(test_staff_multiple_admins[0].staff_id),
            "role": "scientist"
        }
        response = client.put("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["scientist_change"]
        )
    
    def test_edit_existing_staff_removing_last_admin(self, client, admin_token, test_staff_admin):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "lab_id": str(test_staff_admin.lab_id),
            "staff_id": str(test_staff_admin.staff_id),
            "role": "lab_personnel"
        }
        response = client.put("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["last_admin"]
        )
    
    def test_edit_existing_staff_combo_exists(self, client, admin_token, test_staff_admin):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "lab_id": str(test_staff_admin.lab_id),
            "staff_id": str(test_staff_admin.staff_id),
            "role": "lab_admin"
        }
        response = client.put("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["combo_exists"]
        )
    
    def test_edit_existing_staff_db_error(self, client, admin_token, test_staff_admin):
        temp_data = {
            "user_id": str(test_staff_admin.user_id),
            "lab_id": str(test_staff_admin.lab_id),
            "staff_id": str(test_staff_admin.staff_id),
            "role": "lab_personnel"
        }
        with patch("app.api.staff.edit_staff", side_effect=Exception("Unexpected Error")):
            response = client.put("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 500, ERROR_MESSAGES["staff"]["server_error"]
        )
    
    def test_edit_existing_staff_success(self, client, admin_token, test_staff_multiple_admins):
        temp_data = {
            "user_id": str(test_staff_multiple_admins[0].user_id),
            "lab_id": str(test_staff_multiple_admins[0].lab_id),
            "staff_id": str(test_staff_multiple_admins[0].staff_id),
            "role": "lab_personnel"
        }
        response = client.put("/staff", json=temp_data, headers=TestHelpers.Auth.get_headers(admin_token))

        assert response.status_code == 200
        assert response.json()["role"] == "lab_personnel"
        assert response.json()["user_id"] == str(test_staff_multiple_admins[0].user_id)
        assert response.json()["lab_id"] == str(test_staff_multiple_admins[0].lab_id)
        assert response.json()["staff_id"] == str(test_staff_multiple_admins[0].staff_id)
    

    #####################
    # DELETE /staff/{staff_id} TESTS
    #####################
    def test_delete_existing_staff_does_not_exist(self, client, admin_token, test_staff_admin):
        url ="/staff/"+str(uuid.uuid4())
        response = client.delete(url, headers=TestHelpers.Auth.get_headers(admin_token))
 
        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["not_found"]
        )
    
    def test_delete_existing_staff_no_authorized(self, client, user_token, test_staff_multiple_admins):
        url ="/staff/"+str(test_staff_multiple_admins[0].staff_id)
        response = client.delete(url, headers=TestHelpers.Auth.get_headers(user_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["not_authorized"]
        )
    
    def test_delete_existing_staff_incorrect_lab(self, client, admin_token, test_staff_multiple_admins_in_multiple_labs):
        url ="/staff/"+str(test_staff_multiple_admins_in_multiple_labs[1].staff_id)
        response = client.delete(url, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["not_authorized"]
        )
    
    def test_delete_existing_staff_delete_scientist(self, client, admin_token, test_staff_user):
        url ="/staff/"+str(test_staff_user.staff_id)
        response = client.delete(url, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["not_authorized"]
        )
    
    def test_delete_existing_staff_last_admin(self, client, admin_token, test_staff_admin):
        url ="/staff/"+str(test_staff_admin.staff_id)
        response = client.delete(url, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 403, ERROR_MESSAGES["staff"]["last_admin"]
        )
    
    def test_delete_existing_staff_db_error(self, client, admin_token, test_staff_multiple_admins):
        url ="/staff/"+str(test_staff_multiple_admins[0].staff_id)
        with patch("app.api.staff.delete_staff", side_effect=Exception("Unexpected Error")):
            response = client.delete(url, headers=TestHelpers.Auth.get_headers(admin_token))

        verify_api_error_response(
            response, 500, ERROR_MESSAGES["staff"]["server_error"]
        )
    
    def test_delete_existing_staff_successful(self, client, admin_token, test_staff_multiple_admins):
        url ="/staff/"+str(test_staff_multiple_admins[0].staff_id)
        response = client.delete(url, headers=TestHelpers.Auth.get_headers(admin_token))

        assert response.status_code == 200
        assert response.json()["message"] == "Staff member deleted successfully"