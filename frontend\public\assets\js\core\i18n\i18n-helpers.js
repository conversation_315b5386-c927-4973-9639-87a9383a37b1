/*
 * Internationalization Helper Functions
 * Centralized utilities for handling bilingual (EN/FR) content
 */

// Helper function to get current language (defaults to 'en')
export function getCurrentLanguage() {
    return document.documentElement.lang || 'en';
}

// Helper function to get localized text from bilingual configuration
export function getLocalizedText(config, field = 'message') {
    const lang = getCurrentLanguage();
    const localizedField = config[field];

    if (typeof localizedField === 'object' && localizedField[lang]) {
        return localizedField[lang];
    }

    // Fallback to English if current language not available
    if (typeof localizedField === 'object' && localizedField.en) {
        return localizedField.en;
    }

    // Fallback to string if not an object (backward compatibility)
    return localizedField || '';
}

// Helper function to get localized role display name
export function getLocalizedRoleName(role, roleConfigs) {
    const lang = getCurrentLanguage();
    const roleConfig = roleConfigs[role];

    if (roleConfig && roleConfig[lang]) {
        return roleConfig[lang];
    }

    // Fallback to English if current language not available
    if (roleConfig && roleConfig.en) {
        return roleConfig.en;
    }

    // Fallback to role name if no configuration found
    return role;
}

// Helper function to get localized UI text
export function getLocalizedUIText(textKey, uiConfigs) {
    const lang = getCurrentLanguage();
    const textConfig = uiConfigs[textKey];

    if (textConfig && textConfig[lang]) {
        return textConfig[lang];
    }

    // Fallback to English if current language not available
    if (textConfig && textConfig.en) {
        return textConfig.en;
    }

    // Fallback to key if no configuration found
    return textKey;
}
