"""
Test suite for CRUD operations on Sample objects.

This class contains tests for the following functions:
- post_sample:
    - test_post_sample_success
    - test_post_sample_success_all
    - test_post_sample_wrong_format_validation_error
    - test_post_sample_validation_error
    - test_post_sample_database_error
    - test_create_sample_invalid_data
    - test_create_sample_already_exists
    - test_create_sample_db_error
- get_samples:
    - test_get_samples_not_found
    - test_get_samples_found
- get_sample_by_SMS:
    - test_get_sample_by_SMS_found
    - test_get_sample_by_SMS_not_found
    - test_get_sample_by_id_not_found
"""

import pytest
from unittest.mock import MagicMock, patch
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import uuid

from app.crud.sample import get_samples, get_sample_by_SMS, post_sample
from app.models.sample import Sample
from app.schemas.sample import SampleBase
from tests.test_utils.constants import SAMPLE_TEST_DATA, SAMPLE_TEST_CASES
from tests.test_utils.helpers import TestHelpers
from app.common.constants import ERROR_MESSAGES
from app.common.exceptions import ValidationError, StateError, NotFoundError
from tests.test_utils.verification import SampleVerifier, verify_crud_error_response


class TestSampleCRUD:
    #####################
    # post_sample TESTS
    #####################
    def test_post_sample_success(self, db: Session):
        # This test is just to test basic add
        # Arrange
        sample_data = SampleBase(**SAMPLE_TEST_DATA["sample1"])

        # Function
        sample = post_sample(db, sample_data)

        SampleVerifier.verify_common_fields(sample.__dict__)
        SampleVerifier.verify_response(sample.__dict__, SAMPLE_TEST_DATA["sample1"])
        SampleVerifier.verify_response(sample.__dict__, {
            "sms_number": sample_data.sms_number
        })

    def test_post_sample_success_all(self, db: Session):
        # This test is just to test a more complex add
        # Arrange
        sample_data = SampleBase(**SAMPLE_TEST_DATA["sample2"])

        # Function
        sample = post_sample(db, sample_data)

        # Verify the sample using the SampleVerifier
        SampleVerifier.verify_common_fields(sample.__dict__)
        SampleVerifier.verify_response(sample.__dict__, {
            "sms_number": sample_data.sms_number,
            "external_reference": sample_data.external_reference,
            "station_id": sample_data.station_id,
            "depth_top": sample_data.depth_top,
            "depth_bottom": sample_data.depth_bottom,
            "sample_type": sample_data.sample_type
        })

    def test_post_sample_wrong_format_validation_error(self, db: Session):
        # This Test is to test the validation
        with pytest.raises(ValidationError) as exc_info:
            sample_data = SAMPLE_TEST_CASES["create"]["missing_sms"]
            post_sample(db, sample_data)
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["sample"]["invalid_format"])

    def test_post_sample_validation_error(self, db: Session, test_sample):
        # Test duplicate SMS number
        sample_data = SampleBase(**SAMPLE_TEST_DATA["sample1"])

        with pytest.raises(ValidationError) as exc_info:
            post_sample(db, sample_data)
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["sample"]["already_exists"].format(
            sms_number=sample_data.sms_number
        ))

    def test_post_sample_database_error(self, db: Session):
        # Arrange
        sample_data = SampleBase(**SAMPLE_TEST_DATA["sample1"])

        # Mock the database error at commit
        mock_error = SQLAlchemyError("Database error")
        db.commit = MagicMock(side_effect=mock_error)

        # Function and assert
        with patch('app.crud.sample.get_sample_by_SMS', return_value=None), \
             pytest.raises(StateError) as exc_info:
            post_sample(db, sample_data)
        verify_crud_error_response(exc_info.value, 409, ERROR_MESSAGES["sample"]["create_failed"].format(
            error=str(mock_error)
        ))

    #####################
    # get_samples TESTS
    #####################
    def test_get_samples_not_found(self, db: Session):
        # This test is to simulate no samples to get
        samples = get_samples(db)
        assert len(samples) == 0

    def test_get_samples_found(self, db: Session):
        # This test is to simulate returning samples
        # Arrange
        samples_data = [SAMPLE_TEST_DATA["sample1"], SAMPLE_TEST_DATA["sample2"]]
        TestHelpers.Samples.create_batch(db, samples_data)

        # function
        samples = get_samples(db)
        
        # Assert
        assert len(samples) == 2
        for sample in samples:
            # Verify using SampleVerifier
            SampleVerifier.verify_common_fields(sample.__dict__)
            
        # Verify specific samples are in the results
        sms_numbers = [s.sms_number for s in samples]
        assert "12345" in sms_numbers
        assert "67890" in sms_numbers

    #####################
    # get_sample_by_SMS TESTS
    #####################
    def test_get_sample_by_SMS_found(self, db: Session, test_sample):
        # Function
        sample = get_sample_by_SMS(db, test_sample.sms_number)
        
        # Assert
        assert sample is not None
        SampleVerifier.verify_common_fields(sample.__dict__)
        SampleVerifier.verify_response(sample.__dict__, {
            "sms_number": test_sample.sms_number
        })

    def test_get_sample_by_SMS_not_found(self, db: Session):
        # Function and Assert
        with pytest.raises(NotFoundError) as exc_info:
            get_sample_by_SMS(db, "nonexistent")
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["sample"]["not_found"])

    def test_create_sample_invalid_data(self, db: Session):
        """Test creating a sample with invalid data"""
        with pytest.raises(ValidationError) as exc_info:
            post_sample(db, {"invalid": "data"})
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["sample"]["invalid_format"])

    def test_create_sample_already_exists(self, db: Session, test_sample):
        """Test creating a sample that already exists"""
        sample_data = SampleBase(**SAMPLE_TEST_DATA["sample1"])
        
        with pytest.raises(ValidationError) as exc_info:
            post_sample(db, sample_data)
        verify_crud_error_response(exc_info.value, 400, ERROR_MESSAGES["sample"]["already_exists"].format(
            sms_number=sample_data.sms_number
        ))

    def test_create_sample_db_error(self, db: Session):
        """Test database error during sample creation"""
        sample_data = SampleBase(**SAMPLE_TEST_DATA["sample1"])
        
        with patch.object(Session, 'commit', side_effect=SQLAlchemyError("Database error")):
            with pytest.raises(StateError) as exc_info:
                with patch('app.crud.sample.get_sample_by_SMS', return_value=None):
                    post_sample(db, sample_data)
            verify_crud_error_response(exc_info.value, 409, ERROR_MESSAGES["sample"]["create_failed"].format(
                error="Database error"
            ))

    def test_get_sample_by_id_not_found(self, db: Session):
        """Test getting a non-existent sample by ID"""
        with pytest.raises(NotFoundError) as exc_info:
            get_sample_by_SMS(db, "NONEXISTENT")
        verify_crud_error_response(exc_info.value, 404, ERROR_MESSAGES["sample"]["not_found"])
