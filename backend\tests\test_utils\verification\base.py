"""
Base verification classes for API response testing.
"""

from datetime import datetime
from uuid import UUID
from typing import Any, Dict, Optional, Type
import uuid


class EntityVerifier:
    """Base class for all entity verifiers"""
    
    COMMON_FIELDS = []
    DATETIME_FIELDS = []
    OPTIONAL_FIELDS = []
    TYPE_VALIDATIONS = {}
    
    @classmethod
    def verify_response(cls, response_data: Dict[str, Any], expected_values: Optional[Dict[str, Any]] = None) -> None:
        """
        Verify a response matches expected values.
        
        Args:
            response_data: The response data to verify
            expected_values: Optional dictionary of expected values to check against
        """
        cls.verify_common_fields(response_data)
        if expected_values:
            for key, value in expected_values.items():
                # Skip password field since it's not returned in the response
                if key == "password":
                    continue
                
                # Convert UUIDs to strings for comparison
                response_value = response_data[key]
                if hasattr(response_value, 'hex') and hasattr(uuid.UUID, 'hex'):  # Check if it's a UUID
                    assert str(response_value) == str(value), f"Expected {key} to be {value}, got {response_value}"
                else:
                    assert response_value == value, f"Expected {key} to be {value}, got {response_value}"

    @classmethod
    def verify_common_fields(cls, response_data: Dict[str, Any]) -> None:
        """
        Verify all common fields exist and have correct types.
        
        Args:
            response_data: The response data to verify
        """
        for field in cls.COMMON_FIELDS:
            assert field in response_data, f"Missing required field: {field}"
        
        for field in cls.DATETIME_FIELDS:
            if field in response_data and response_data[field]:
                # Handle both string and datetime objects
                assert isinstance(response_data[field], (str, datetime)), f"Expected {field} to be a string or datetime object"
        
        for field, expected_type in cls.TYPE_VALIDATIONS.items():
            # Skip validation for optional fields that are not present
            if field in cls.OPTIONAL_FIELDS and field not in response_data:
                continue
            
            if field in response_data and response_data[field] is not None:
                # Handle UUID objects for string fields
                if expected_type == str and isinstance(response_data[field], UUID):
                    continue
                # Handle datetime objects for string fields
                if expected_type == str and isinstance(response_data[field], datetime):
                    continue
                assert isinstance(response_data[field], expected_type), f"Expected {field} to be of type {expected_type}" 