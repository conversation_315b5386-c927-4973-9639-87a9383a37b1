/*
 * Format Helpers
 * Data formatting utilities
 */
import { getLocalizedRoleName, getLocalizedText } from '../i18n/i18n-helpers.js';
import { GLOBAL_CONFIGS } from '../config/global-configs.js';

// Escape HTML to prevent XSS attacks
export function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Format date with localization
export function formatDate(dateValue) {
    if (!dateValue) {
        return formatNotAvailable();
    }
    
    try {
        const date = new Date(dateValue);
        if (isNaN(date.getTime())) {
            return formatNotAvailable();
        }
        
        // Use browser's locale for date formatting
        return date.toLocaleDateString();
    } catch (error) {
        console.warn('Date formatting error:', error);
        return formatNotAvailable();
    }
}

// Get localized role display name
export function getRoleDisplayName(roleName) {
    return getLocalizedRoleName(roleName, GLOBAL_CONFIGS.ui.roles);
}

// Format status display text (active/inactive) with localization
export function formatStatusDisplay(isActive, statusLabels) {
    const label = isActive ? statusLabels.active : statusLabels.inactive;
    return getLocalizedText({ message: label }, 'message');
}

// Format "not available" text with localization
export function formatNotAvailable() {
    return getLocalizedText({ message: GLOBAL_CONFIGS.ui.notAvailable }, 'message');
}
