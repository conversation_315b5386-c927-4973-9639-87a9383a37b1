"""
Testing external_veification functions

This script tests all functions in external_verification

Test Coverage:
    LSA_verify:
        - test_lsa_verify_success
        - test_lsa_verify_404
        - test_lsa_verify_http_error
        - test_lsa_verify_exception
    Sample_verify:
        - test_SMS_verify_success
        - test_SMS_verify_404
        - test_SMS_verify_http_error
        - test_SMS_verify_exception
"""

import pytest
import requests
from unittest.mock import patch, MagicMock
from app.services.external_verification import LSA_verify, Sample_verify
#####################
# LSA_verify TESTS
#####################
def test_lsa_verify_success():
    #Test successful LSA verification
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.ok = True
    mock_response.json.return_value = {"key":"value"}
    with patch ("requests.get", return_value=mock_response):
        result = LSA_verify(1234)
        assert result == {"key": "value"}

def test_lsa_verify_404():
    #Test missing LSA verification
    mock_response = MagicMock()
    mock_response.ok = False
    mock_response.status_code = 404
    with patch ("requests.get", return_value=mock_response):
         with pytest.raises(ValueError, match="LSA could not be found, please enter an existing LSA number"):
            LSA_verify("invalid")

def test_lsa_verify_http_error():
    #Test general error
    mock_response = MagicMock()
    mock_response.ok = False
    mock_response.status_code = 500
    mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("HTTP error")
    with patch ("requests.get", return_value=mock_response):
        with pytest.raises(requests.exceptions.HTTPError, match="HTTP error"):
            LSA_verify("invalid")

def test_lsa_verify_exception():
    #Test connection error
    with patch("requests.get", side_effect=requests.exceptions.ConnectionError("Connection Error")):
        with pytest.raises(requests.exceptions.RequestException, match="Connection Error"):
            LSA_verify("invalid")

            
#####################
# Sample_verify TESTS
#####################
def test_SMS_verify_success():
    #Test Successful SMS verification
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.ok = True
    mock_response.json.return_value = {"key":"value"}
    with patch ("requests.get", return_value=mock_response):
        result = Sample_verify("C-1234")
        assert result == {"key": "value"}

def test_SMS_verify_404():
    #Test missing SMS verification
    mock_response = MagicMock()
    mock_response.ok = False
    mock_response.status_code = 404
    with patch ("requests.get", return_value=mock_response):
         with pytest.raises(ValueError, match="SMS c-number could not be found, please enter an existing SMS c-number."):
            Sample_verify("invalid")

def test_SMS_verify_http_error():
    #Test general HTTP error
    mock_response = MagicMock()
    mock_response.ok = False
    mock_response.status_code = 500
    mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("HTTP error")
    with patch ("requests.get", return_value=mock_response):
        with pytest.raises(requests.exceptions.HTTPError, match="HTTP error"):
            Sample_verify("invalid")

def test_SMS_verify_exception():
    #Test connection error
    with patch("requests.get", side_effect=requests.exceptions.ConnectionError("Connection Error")):
        with pytest.raises(requests.exceptions.RequestException, match="Connection Error"):
            Sample_verify("invalid")