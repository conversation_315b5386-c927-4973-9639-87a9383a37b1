/*!
 * @title Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * @license wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v16.3.0 - 2025-02-20
 *
 */
((l,e,r,t)=>{var i,a="campaign-menu",s="."+a+".gcweb-menu",n="wb-init"+s,o=t.doc,c=function(){l("#mb-pnl").remove(),l("#wb-glb-mn").remove()};l(e).on("resize",function(){"A"==r.activeElement.nodeName&&(i=r.activeElement)}),o.on(t.resizeEvents,function(){if(null!=i){var e=(e=>{let t=e.parentElement;for(;"NAV"!=t.nodeName&&"BODY"!=t.nodeName;)t=t.parentElement;return"NAV"==t.nodeName&&t})(i);if(e){var t=i.getAttribute("href");if(e.classList.contains("wb-menu")&&l(".wb-menu").is(":hidden")){for(var n,a,r=l(".gcweb-menu a[aria-expanded=true]"),o=0;o<r.length;o++)l(r).eq(o).attr("aria-expanded","false");l(s).find("button").attr("aria-expanded","true"),t.match("^#")?(n=t.substring(14),null!=(n=l(".gcweb-menu a[aria-controls='gc-sub-menu-"+n+"']"))?(n.focus(),n[0].hasAttribute("aria-haspopup")&&"true"==n[0].getAttribute("aria-haspopup")&&n.attr("aria-expanded","true")):console.warn("Unable to find equivalent link in GCWeb menu top level items")):(n=l('.gcweb-menu a[href="'+t+'"]'),1<(a=l(n).parents("ul")).length&&a.eq(0).parent().children("a").attr("aria-expanded","true"),null!=n?n.focus():console.warn("Unable to find equivalent link in GCWeb menu submenu items"))}else e.classList.contains("gcweb-menu")&&l(".gcweb-menu").is(":hidden")&&(l(".wb-menu li").removeClass("active sm-open"),l(".wb-menu ul.open").attr("aria-expanded","false"),l(".wb-menu ul.open").attr("aria-hidden","true"),l(".wb-menu ul").removeClass("open"),t.match("^#")?(a=l(i).attr("aria-controls").substring(12),null!=(n=l(".wb-menu a[href='#wet-sub-menu-"+a+"']"))?n.focus():console.warn("Unable to find equivalent link in WET megamenu top level items")):(e=l('.wb-menu a[href="'+t+'"]'),1<(a=l(e).parents("ul")).length&&((n=a.eq(0)).attr("aria-expanded","true"),n.attr("aria-hidden","false"),n.addClass("open"),n.parent().addClass("active sm-open")),null!=e?e.focus():console.warn("Unable to find equivalent link in WET megamenu submenu items")))}}}),o.on("timerpoke.wb "+n,s,function(e){var n,e=t.init(e,a,s);e&&(e=l(e),1<r.querySelectorAll(".gcweb-menu").length?(console.warn(a+" - gcweb menu already exsits on the page, hiding gcweb campaign menu and aborting"),e.addClass("hidden")):null!=r.querySelector("#wb-sm")?(console.warn(a+" - megamenu already exsits on the page, aborting"),e.addClass("hidden")):l(r).on("wb-ready.wb",(n=e,function(e){var t=n.find("> ul > li"),a="",t=(l.each(t,function(e,t){var t=t.querySelector("a"),n=t.getAttribute("href"),t=t.textContent;a+=`<li><a href="${n}">${t}</a></li>`}),r.querySelector(s+" > h2")),a=`
                <nav id="wb-sm" class="campaign-menu wb-menu visible-md visible-lg" data-trgt="mb-pnl" data-ajax-replace="${n[0].getAttribute("data-megamenu-ajax")}">
                    <div class="pnl-strt nvbar">
                        <h2>${t.textContent}</h2>
                        <ul role="menubar" class="list-inline menu">
                            ${a}
                        </ul>
                    </div>
                </nav>`;n.addClass("visible-sm visible-xs"),l(".gcweb-menu").after('<div id="mb-pnl" hidden></div>'),l(".gcweb-menu").after('<div id="wb-glb-mn" hidden><h2> </h2></div>'),n.after(a),l(".wb-menu").trigger("wb-init.wb-menu"),l(r).on("wb-ready.wb-menu",c)})),t.ready(e,a))}),t.add(s)})(jQuery,window,document,wb),((n,a,r)=>{var o="collection-sort",l="."+o,i=r.doc,s={};i.on("collection-sort",l,function(e,r){var t=e.currentTarget;function n(){t.querySelectorAll(r.section).forEach(function(e){let t=e.querySelectorAll(r.selector),n=[],a=[];t.forEach(function(e){a.push(e.parentElement);e={elm:e,sortVal:""};n.push(e)}),r.sort.forEach(function(t){n.forEach(function(e){e.sortVal=e.elm.querySelector(t.selector).innerHTML}),"numeric"===t.type?"desc"===t.order?n.sort((e,t)=>t.sortVal-e.sortVal):n.sort((e,t)=>e.sortVal-t.sortVal):"desc"===t.order?n.sort((e,t)=>t.sortVal.localeCompare(e.sortVal)):n.sort((e,t)=>e.sortVal.localeCompare(t.sortVal))}),n.forEach(function(e,t){a[t].append(e.elm)})})}r.section&&r.selector&&r.sort&&(n(),i.on("wb-contentupdated",l,function(e,t){n()}))}),i.on("timerpoke.wb wb-init.collection-sort",l,function(e){var t,e=r.init(e,o,l);e&&(e=n(e),t=n.extend(!0,{},s,a[o],r.getData(e,o)),e.trigger("collection-sort",t),r.ready(e,o))}),r.add(l)})(jQuery,window,wb),((o,n,m)=>{var l="distance-calculator",a="."+l,e=m.doc,r={};e.on("distance-calculator",a,function(e,u){var a=e.currentTarget,r=o(a);function d(e){return e*(Math.PI/180)}r.find(u.form).on("submit",function(e){var e=e.currentTarget.querySelector(u.location).value,e=encodeURIComponent(e),n=a.querySelector(u.section).querySelectorAll(u.selector),t="fr"===m.lang?"https://geogratis.gc.ca/services/geolocation/fr/locate?q=":"https://geogratis.gc.ca/services/geolocation/en/locate?q=";return o.getJSON(t+e,function(e){var s,c,t;0==e.length?console.log("Empty response from geogratis"):(s=e[0].geometry.coordinates[0],c=e[0].geometry.coordinates[1],t=e[0].title,n.forEach(function(e){var t,n,a,r,o,l,i=e.querySelector(u.target),e=e.querySelector(u.sort);null!=i&&null!=e&&null!=i.dataset.distanceCoordinates&&(o=(r=JSON.parse(i.dataset.distanceCoordinates)).longtitude,t="fr"===m.lang?" ":",",a=s,o=o,l=d((r=r.latitude)-(n=c)),o=d(o-a),a=Math.sin(l/2)*Math.sin(l/2)+Math.cos(d(n))*Math.cos(d(r))*Math.sin(o/2)*Math.sin(o/2),l=6371*(2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a))),e.innerHTML=Math.round(l),i.innerHTML=((e,t)=>{for(var n=(e=(e+="").split("."))[0],e=1<e.length?"."+e[1]:"",a=/(\d+)(\d{3})/;a.test(n);)n=n.replace(a,"$1"+t+"$2");return n+e})(Math.round(l),t))}),null!=u.name&&a.querySelectorAll(u.name).forEach(function(e){e.innerHTML=t}),"object"==typeof u.display&&void 0!==u.display.selector&&void 0!==u.display.removeClass&&null!==u.display.selector&&null!==u.display.removeClass&&a.querySelectorAll(u.display.selector).forEach(function(e){e.classList.remove(u.display.removeClass)}),r.trigger("wb-contentupdated",[{source:l}]))}),!1})}),e.on("timerpoke.wb wb-init.distance-calculator",a,function(e){var t,e=m.init(e,l,a);e&&(e=o(e),t=o.extend(!0,{},r,n[l],m.getData(e,l)),e.trigger("distance-calculator",t),m.ready(e,l))}),m.add(a)})(jQuery,window,wb);