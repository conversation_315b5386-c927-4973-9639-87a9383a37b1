"""
Core application components.
This package contains core application components like security, dependencies, and logging.
"""

from app.core.dependencies import get_db, get_current_user, enforce_role_selection
from app.core.security import create_access_token, verify_token
from app.core.logging import (
    logger,
    log_info,
    log_warning,
    log_error,
    SimpleLoggingMiddleware
)

__all__ = [
    'get_db',
    'get_current_user',
    'enforce_role_selection',
    'create_access_token',
    'verify_token',
    'logger',
    'log_info',
    'log_warning',
    'log_error',
    'SimpleLoggingMiddleware',
]
