"""
File verification helpers for API response testing.
"""

from typing import Any, Dict, Optional
from tests.test_utils.verification.base import EntityVerifier


class FileVerifier(EntityVerifier):
    """Verifier for file entity responses"""
    
    COMMON_FIELDS = ["file_id", "file_name", "file_type", "file_size", "created_at", "is_published"]
    DATETIME_FIELDS = ["created_at"]
    OPTIONAL_FIELDS = ["storage_id", "requisition_id", "uploaded_by"]
    TYPE_VALIDATIONS = {
        "file_id": str,
        "file_name": str,
        "file_type": str,
        "file_size": int,
        "is_published": bool,
        "storage_id": str,
        "requisition_id": str,
        "uploaded_by": str
    } 