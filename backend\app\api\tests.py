"""
API endpoints for test types management.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from ..database import get_db
from ..crud import test as test_crud
from ..schemas.test import TestType, TestTypeCreate, TestTypeUpdate, TestTypeBatchUpdate
from ..core.dependencies import enforce_role_selection
from ..schemas.user import User
from ..common.constants import ERROR_MESSAGES
from ..common.exceptions import NotFoundError, AccessDeniedError, ValidationError, StateError
from ..core.logging import logger, log_error, log_warning, log_info

router = APIRouter()

@router.get("/", response_model=List[TestType])
async def list_test_types(
        skip: int = Query(default=0, ge=0),
        limit: int = Query(default=100, ge=1, le=100),
        lab_id: Optional[str] = Query(default=None, description="Filter test types by lab ID"),
        name: Optional[str] = Query(default=None, description="Filter test types by name (for duplicate checking)"),
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    Retrieve a list of test types with pagination and optional filtering.

    Args:
        skip (int): Number of records to skip (for pagination)
        limit (int): Maximum number of records to return (1-100)
        lab_id (str): Optional lab ID to filter test types by lab
        name (str): Optional name to filter test types by name (for duplicate checking)
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        List[TestType]: List of test types

    Raises:
        HTTPException: If operation fails
    """
    try:
        log_info(f"User {current_user.user_id} requesting test types with lab_id filter: {lab_id}, name filter: {name}")
        return test_crud.get_test_types(db, skip=skip, limit=limit, lab_id=lab_id, name=name)
    except ValidationError as e:
        log_warning(f"Validation error in list_test_types: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error in list_test_types: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["test"]["list_failed"].format(error=str(e))
        )

@router.post("/", response_model=TestType)
async def create_test_type(
        test_type: TestTypeCreate,
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    Create a new test type. Only accessible by lab administrators.

    Args:
        test_type (TestTypeCreate): Test type data to create
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        TestType: Created test type

    Raises:
        HTTPException: 403 error if user is not a lab administrator
    """
    try:
        new_test_type = test_crud.create_test_type(db, test_type, current_user)
        
        # Simple logging for test type creation
        log_info(f"User {current_user.user_id} created test type {new_test_type.test_type_id}")
        
        return new_test_type
    except AccessDeniedError as e:
        log_warning(f"Access denied in create_test_type: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in create_test_type: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except StateError as e:
        log_error(f"State error in create_test_type: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error in create_test_type: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["test"]["create_failed"].format(error=str(e))
        )

@router.put("/{test_type_id}", response_model=TestType)
async def update_test_type(
        test_type_id: str,
        test_type: TestTypeUpdate,
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    Update an existing test type. Only accessible by lab administrators.

    Args:
        test_type_id (str): ID of the test type to update
        test_type (TestTypeUpdate): Updated test type data
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        TestType: Updated test type

    Raises:
        HTTPException: 403 error if user is not a lab administrator
    """
    try:
        updated_test_type = test_crud.update_test_type(db, test_type_id, test_type, current_user)
        
        log_info(f"User {current_user.user_id} updated test type {test_type_id}")
        
        return updated_test_type
    except NotFoundError as e:
        log_info(f"Not found error in update_test_type: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in update_test_type: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in update_test_type: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except StateError as e:
        log_error(f"State error in update_test_type: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error in update_test_type: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["test"]["update_failed"].format(error=str(e))
        )

@router.delete("/{test_type_id}")
async def delete_test_type(
        test_type_id: str,
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    Delete a test type. Only accessible by lab administrators.

    Args:
        test_type_id (str): ID of the test type to delete
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        dict: Success message with deleted test type ID

    Raises:
        HTTPException: 403 error if user is not a lab administrator
    """
    try:
        test_crud.delete_test_type(db, test_type_id, current_user)
        
        log_info(f"User {current_user.user_id} deleted test type {test_type_id}")
        
        return {"message": ERROR_MESSAGES["test"]["delete_success"], "test_type_id": test_type_id}
    except NotFoundError as e:
        log_info(f"Not found error in delete_test_type: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in delete_test_type: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except StateError as e:
        log_error(f"State error in delete_test_type: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error in delete_test_type: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["test"]["delete_failed"].format(error=str(e))
        )

@router.post("/batch-update")
async def batch_update_test_types(
        batch_update: TestTypeBatchUpdate,
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    Batch update multiple test types. Only accessible by lab administrators.
    Uses atomic transaction - either all updates succeed or all fail with database rollback.

    Args:
        batch_update (TestTypeBatchUpdate): Batch update data
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        dict: Simple success response with message and updated count

    Raises:
        HTTPException: 403 error if user is not a lab administrator
        HTTPException: 400 error if batch is empty
        HTTPException: 500 error if any update fails (with database rollback)
    """
    try:
        result = test_crud.batch_update_test_types(db, batch_update, current_user)

        log_info(f"User {current_user.user_id} completed batch update: {result['updated_count']} test types updated successfully")
        return result

    except AccessDeniedError as e:
        log_warning(f"Access denied for batch update: {str(e)}")
        raise HTTPException(
            status_code=403,
            detail=str(e)
        )
    except ValidationError as e:
        log_warning(f"Validation error in batch update: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        log_error(f"Unexpected error in batch update: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["test"]["batch_update_failed"].format(error=str(e))
        )