# Sed-LIMS Frontend

This is the frontend application for the Sed-LIMS (Sediment Laboratory Information Management System) project, built using GCWeb and WET-BOEW frameworks with jQuery.

## Introduction

The frontend follows Government of Canada web standards including accessibility requirements and provides a bilingual (English/French) interface for laboratory information management.

## Getting Started

### Prerequisites
- Node.js (for development server)
- Modern web browser with JavaScript enabled

### Installation
1. Navigate to the frontend directory
2. Install dependencies: `npm install`
3. Start development server: `npm start`
4. Access the application at `http://localhost:3000`

## Build and Test

### Development
- `npm start` - Start development server (production mode)

### Production
- Static files served directly by Express.js server
- No separate build process required

## Project Structure

### Root Directory Structure
```
frontend/
├── README.md                   # This file
├── package.json               # Node.js dependencies
├── server.js                  # Development server
├── public/                    # Static assets and pages
│   ├── assets/               # CSS, JS, and other assets
│   │   ├── css/             # Custom styles
│   │   └── js/              # JavaScript code (see detailed structure below)
│   ├── components/          # Reusable HTML components
│   │   ├── en/             # English components
│   │   └── fr/             # French components
│   ├── en/                 # English pages
│   ├── fr/                 # French pages
│   └── libraries/          # WET-BOEW and external libraries
└── node_modules/           # Node.js dependencies
```

### Key Directories
- **`/libraries`** - Contains WET-BOEW assets (hosted locally)
- **`/components`** - Reusable HTML components for pages
- **`/en` & `/fr`** - Bilingual pages that users see
- **`/assets/js`** - JavaScript code (detailed architecture below)
- **`/assets/css`** - Custom styles following GC standards

## JavaScript Architecture

The JavaScript code is organized in a layered architecture for maintainability and scalability.

### JavaScript Directory Structure

```
/assets/js/
├── core/                    # Core functionality (global)
│   ├── auth/               # Authentication & authorization
│   ├── services/           # API service layer
│   ├── config/             # Configuration files
│   ├── helpers/            # Utility functions
│   └── i18n/               # Internationalization
└── pages/                   # Page-specific code
    ├── auth/               # Authentication page functionality
    ├── home/               # Home page functionality
    ├── account/            # Account page functionality
    ├── lab/                # Lab management pages
    └── requisition/        # Requisition-related pages
```

### Architecture Layers

#### 1. **Pages Layer** (`pages/`)
- **Purpose**: Page-specific initialization and flow control
- **Responsibilities**:
  - Initialize page functionality
  - Coordinate between different layers
  - Handle page-specific business logic
- **Examples**: `login.js`, `role-selection.js`, `home.js`, `my-account.js`, `view-requisitions.js`

#### 2. **Core Layer** (`core/`)

##### Services (`core/services/`)
- **Purpose**: API communication and data management
- **Responsibilities**:
  - HTTP requests to backend
  - Authentication handling
  - Error handling
- **Examples**: `user-api.js`, `requisition-api.js`, `api-base.js`

##### Internationalization (`core/i18n/`)
- **Purpose**: Centralized bilingual (EN/FR) content management
- **Responsibilities**:
  - Language detection and switching
  - Localized text retrieval for all user-facing content
  - Role name localization
  - UI text localization
- **Examples**: `i18n-helpers.js`, `language-switcher.js`

##### Config (`core/config/`)
- **Purpose**: Centralized configuration management with optimized structure
- **Responsibilities**:
  - Global application settings and navigation
  - Authentication configurations
  - Entity-specific configurations (tests, requisitions)
  - Bilingual message templates and UI text
- **Architecture**: Domain-driven configuration files
  - `global-configs.js` - Application settings, navigation, roles, and global messages
  - `auth-configs.js` - Authentication-specific configurations and messages
  - `test-configs.js` - Test management configurations and UI text
  - `requisition-configs.js` - Requisition-specific table and UI configurations

##### Auth (`core/auth/`)
- **Purpose**: Authentication and authorization functionality
- **Responsibilities**:
  - Centralized authentication middleware
  - Token management and validation
  - Role-based access control
  - Microsoft Azure AD integration
  - Navigation bar authentication logic
  - Declarative page authentication configuration
- **Examples**: `auth-helpers.js`, `authentication.js`, `navbar.js`, `msalConfig.js`

##### Helpers (`core/helpers/`)
- **Purpose**: Utility functions and common functionality
- **Responsibilities**:
  - Message display and error handling
  - Navigation and redirect utilities
  - Standardized helper functions used across pages
- **Examples**: `message-helpers.js`, `navigation-helpers.js`

## Internationalization (i18n)

The application supports bilingual (English/French) functionality following Government of Canada standards.

### Implementation
- **Language Detection**: Uses `getCurrentLanguage()` from `i18n-helpers.js` to detect current language
- **Centralized Management**: All user-facing text stored in configuration files with bilingual format
- **Helper Functions**: Unified i18n helpers in `core/i18n/i18n-helpers.js` for consistent text retrieval
- **Language Switching**: Automatic URL switching between `/en/` and `/fr/` paths

### Bilingual Text Format
```javascript
// Standard format for all user-facing text
{
    en: 'English text',
    fr: 'French text'
}

// Example usage
import { getLocalizedText } from '../i18n/i18n-helpers.js';
const localizedTitle = getLocalizedText(errorConfig, 'title');
```

### Covered Areas
- **Error Messages**: API errors, table errors, form validation
- **User Interface**: Role names, status messages, loading text
- **Table Content**: Column headers, search prompts, pagination text
- **Success/Confirmation**: All user feedback messages

### What's NOT Internationalized
- API endpoints and technical configurations
- Console.log debug messages
- JavaScript code comments
- DOM selectors and CSS classes

### Usage Patterns
```javascript
// For general text localization (errors, messages, etc.)
import { getLocalizedText } from '../i18n/i18n-helpers.js';
const message = getLocalizedText(config, 'message');

// For role names
import { getLocalizedRoleName } from '../i18n/i18n-helpers.js';
import { GLOBAL_CONFIGS } from '../config/global-configs.js';
const roleName = getLocalizedRoleName('scientist', GLOBAL_CONFIGS.ui.roles);

// For UI text (buttons, labels, etc.)
import { getLocalizedUIText } from '../i18n/i18n-helpers.js';
import { GLOBAL_CONFIGS } from '../config/global-configs.js';
const uiText = getLocalizedUIText('selectRole', GLOBAL_CONFIGS.ui.common);

// For page authentication (new centralized approach)
window.PAGE_AUTH_CONFIG = {
    requiredRole: GLOBAL_CONFIGS.application.roles.LAB_ADMIN,
    onAuthSuccess: initializePageName,
    onAuthFailure: showAccessDeniedError
};
```

## Technical Specifications

### Frameworks & Libraries
- **GCWeb**: Government of Canada web framework
- **WET-BOEW**: Web Experience Toolkit - Bootstrap of the Government of Canada
- **jQuery**: JavaScript library for DOM manipulation and AJAX
- **Express.js**: Development server (Node.js)

### Browser Support
- Modern browsers supporting ES6+ modules
- Internet Explorer 11+ (with polyfills)
- Accessibility compliance (WCAG 2.1 AA)

### Code Standards
- **JavaScript**: ES6+ modules, jQuery-style over native JavaScript
- **CSS**: Following GC design system guidelines
- **HTML**: Semantic markup with accessibility attributes
- **Comments**: English for all code documentation

## Development Guidelines

### Code Organization
- Use ES6 modules for all JavaScript files
- Follow the simplified architecture (Pages → Core → Services)
- Keep configuration centralized in `core/config/`
- Use internationalization helpers for all user-facing text

### Naming Conventions
- **Files**: kebab-case (e.g., `user-helpers.js`)
- **Functions**: camelCase (e.g., `getUserInfo()`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `USER_ROLE_CONFIGS`)
- **CSS Classes**: Follow GCWeb/WET-BOEW conventions

### Best Practices
- Always use bilingual text configurations
- Implement proper error handling with user-friendly messages
- Follow accessibility guidelines (ARIA labels, keyboard navigation)
- Use semantic HTML elements
- Test functionality across different user roles

## Configuration Management

### Optimized Configuration Architecture

Configuration system with bilingual support and GC standards compliance:

```
/core/config/
├── global-configs.js       # Application settings, navigation, roles, and global messages
├── auth-configs.js         # Authentication-specific configurations and messages
├── test-configs.js         # Test management configurations and UI text
└── requisition-configs.js  # Requisition-specific table and UI configurations
```

### Environment-Based Configuration
- Use `.env` files for environment-specific settings
- Localhost fallback when `.env` is missing
- Server middleware automatically injects environment variables into HTML pages

### Role-Based Access Control
- Three user roles: Scientist, Lab Personnel, Lab Admin
- Role constants defined in `GLOBAL_CONFIGS.application.roles`
- Centralized authentication middleware with declarative page configuration
- Navigation and functionality adapted based on user role
- Consistent with backend role definitions

## Deployment

### Development
- Local development server on `localhost:3000`
- Backend API on `localhost:8000`
- Hot reload for development efficiency

### Production
- Docker containers on Azure
- Environment variables managed through Azure portal
- Separate domains for frontend and backend
