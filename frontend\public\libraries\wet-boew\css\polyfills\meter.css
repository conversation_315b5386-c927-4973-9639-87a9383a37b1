@charset "UTF-8";
/*
 * Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * wet-boew.github.io/wet-boew/License-eng.html / wet-boew.github.io/wet-boew/Licence-fra.html
 */
meter {
	border: 1px outset;
	display: block;
	height: 20px;
	overflow: hidden;
	width: 100px;
}
meter div {
	background: #b4e391;
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #b4e391), color-stop(35%, #4a0), to(#b4e391));
	background-image: linear-gradient(to bottom, #b4e391 0, #4a0 35%, #b4e391 100%);
	border-right: 1px solid #000;
	display: block;
	height: 20px;
}
meter.meterValueTooHigh div, meter.meterValueTooLow div {
	background: #ffd65e;
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #ffd65e), color-stop(35%, #fbff47), to(#febf04));
	background-image: linear-gradient(to bottom, #ffd65e 0, #fbff47 35%, #febf04 100%);
}
meter.meterIsMaxed {
	border-right: 0 none !important;
}