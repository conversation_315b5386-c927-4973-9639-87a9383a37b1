/*!
 * @title Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * @license wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v16.3.0 - 2025-02-20
 *
 */
((o,r,t)=>{var d="wb-bonjour",i="."+d,e=t.doc,a={};e.on("nom.de.votre.evenement",i,function(e,n){e=e.currentTarget,e=o(e);e.append("Bonjour le monde"),n&&n.surpassetoi&&e.prepend("Surpasse toi")}),e.on("timerpoke.wb wb-init.wb-bonjour",i,function(e){var n,e=t.init(e,d,i);e&&(e=o(e),n=o.extend(!0,{},a,r[d],t.getData(e,d)),e.trigger("nom.de.votre.evenement",n),t.ready(e,d))}),t.add(i)})(jQuery,window,wb),((o,r,t)=>{var d="wb-bonjour",i="."+d,e=t.doc,a={};e.on("nom.de.votre.evenement",i,function(e,n){e=e.currentTarget,e=o(e);e.append("Bonjour le monde"),n&&n.surpassetoi&&e.prepend("Surpasse toi")}),e.on("timerpoke.wb wb-init.wb-bonjour",i,function(e){var n,e=t.init(e,d,i);e&&(e=o(e),n=o.extend(!0,{},a,r[d],t.getData(e,d)),e.trigger("nom.de.votre.evenement",n),t.ready(e,d))}),t.add(i)})(jQuery,window,wb);