#Opening Doc string
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from .database import init_db, create_initial_admin
from .database import engine, Base
from .config import settings
from .api import health, auth, users, samples, tests, requisitions, files, labs, staff
from .core.logging import SimpleLoggingMiddleware, logger

from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()
logger.info("Environment variables loaded")

# Log configuration settings (without sensitive data)
logger.info(f"Project name: {settings.PROJECT_NAME}")
logger.info(f"CORS origins: {settings.BACKEND_CORS_ORIGINS}")
logger.info(f"Token expiry: {settings.ACCESS_TOKEN_EXPIRE_MINUTES} minutes")


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup logic
    logger.info(f"Starting {settings.PROJECT_NAME}")
    
    # Initialize database and create admin user
    init_db(logger=logger)
    create_initial_admin(logger=logger)
    
    logger.info("Database initialization complete")
    
    yield   # Application runs here
    
    # Shutdown logic
    logger.info(f"Shutting down {settings.PROJECT_NAME}")

# Create FastAPI app with lifespan and settings
app = FastAPI(
    title=settings.PROJECT_NAME,
    lifespan=lifespan
)

# Set up CORS
origins = settings.BACKEND_CORS_ORIGINS
logger.info(f"CORS origins type: {type(origins)}, value: {origins}")

# Ensure we have both versions of each origin (with and without trailing slash)
processed_origins = []
for origin in origins:
    origin_str = str(origin).rstrip('/')
    processed_origins.append(origin_str)  # Without trailing slash
    if not origin_str.endswith('/'):
        processed_origins.append(f"{origin_str}/")  # With trailing slash

logger.info(f"Processed CORS origins: {processed_origins}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=processed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add logging middleware
app.add_middleware(SimpleLoggingMiddleware)

# Add routers
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(auth.router, prefix="/auth", tags=["auth"])
app.include_router(users.router, prefix="/users", tags=["users"])
app.include_router(requisitions.router, prefix="/requisitions", tags=["requisitions"])
app.include_router(samples.router, prefix="/samples", tags=["samples"])
app.include_router(tests.router, prefix="/test-types", tags=["test-types"])
app.include_router(files.router, prefix="/files", tags=["files"])
app.include_router(labs.router, prefix="/labs", tags=["labs"])
app.include_router(staff.router, prefix="/staff", tags=["staff"])

# Create database tables
Base.metadata.create_all(bind=engine)

@app.get("/")
async def root():
    return {
        "project": settings.PROJECT_NAME,
        "message": "Welcome to LIMS API"
    }