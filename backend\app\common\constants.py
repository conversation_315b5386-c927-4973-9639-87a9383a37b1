"""
Application-wide constants and enumerations.
Contains core business logic constants used throughout the application.
"""

from enum import Enum

class UserRole(str, Enum):
    """User role definitions for access control"""
    SCIENTIST = "scientist"
    LAB_PERSONNEL = "lab_personnel"
    LAB_ADMIN = "lab_admin"

class RequisitionStatus(str, Enum):
    """Valid states for a requisition"""
    SUBMITTED = "submitted"
    IN_PROGRESS = "in_progress"
    COMPLETE = "complete"

class PriorityLevel(str, Enum):
    """Priority levels for requisitions"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class RequisitionSampleStatus(str, Enum):
    """Valid states for a requisition sample"""
    SUBMITTED = "submitted"
    IN_PROGRESS = "in_progress"
    COMPLETE = "complete"

class SampleTestStatus(str, Enum):
    """Valid states for a test"""
    SUBMITTED = "submitted"
    IN_PROGRESS = "in_progress"  
    COMPLETE = "complete"

# Error messages grouped by feature
ERROR_MESSAGES = {
    "requisition": {
        # Not Found errors
        "not_found": "Requisition not found",
        "samples_not_found": "One or more samples not found",
        "sample_not_found": "Sample not found in requisition",
        "test_not_found": "Test not found in the requisition sample",
        
        # Permission errors
        "unauthorized_create": "Only scientists and admin can create requisitions",
        "unauthorized_modify": "Only lab personnel and admin can modify requisitions",
        "unauthorized_archive": "Only lab administrators can archive requisitions",
        "unauthorized_access": "Access denied for this requisition",
        "unauthorized_lab": "Can only edit or view requisitions that are apart of your own lab",
        "unauthorized_mine": "Only scientists can access this endpoint.",
        "unauthorized_lab_endpoint": "Lab personnel should use lab-specific endpoint.",
        
        # Operation errors
        "create_failed": "Error creating requisition: {error}",
        "update_failed": "Error updating requisition: {error}",
        "fetch_failed": "Error fetching requisition: {error}",
        "list_failed": "Error listing requisitions: {error}",
        "add_samples_failed": "Error adding samples to requisition: {error}",
        "remove_sample_failed": "Error removing sample from requisition: {error}",
        "fetch_samples_failed": "Error fetching samples for requisition: {error}",
        "database_error": "Database error",
        "get_sample_tests_failed": "Failed to get tests for sample: {error}",
        "remove_test_failed": "Failed to remove test: {error}",
        "update_test_failed": "Failed to update test status: {error}",
        
        # Validation errors
        "field_required": "Field required",
        "invalid_priority": "Input should be 'low', 'medium' or 'high'",
        "duplicate_samples": "Duplicate sample IDs provided",
        "samples_already_added": "One or more samples are already added to this requisition",
        "missing_sample_ids": "sample_ids field is required",
        "invalid_data": "Invalid requisition data provided",

        # Test validation messages
        "response_missing_field": "Response should include {field} field",
        "field_should_not_be_none": "{field} should not be None",
        "req_name_format_invalid": "req_name should contain LSA number",
        "req_name_prefix_invalid": "req_name should start with 'LSA-'",
        "status_filter_mismatch": "Requisition status {status} not in filter {filter}",
        "lab_filter_mismatch": "Requisition lab_id {lab_id} doesn't match filter {filter_id}",
        "admin_access_validation": "Admin should see requisitions from multiple users",
        "status_variety_validation": "Should see requisitions with different statuses",
        "lab_access_validation": "Lab personnel should only see requisitions from their lab",
        "empty_result_validation": "Should return empty list for non-existent lab",

        # Success messages
        "remove_sample_success": "Sample removed from requisition successfully",
        "remove_test_success": "Test removed from sample successfully"
    },
    "sample": {
        "list_failed": "Failed to retrieve samples: {error}",
        "get_failed": "Failed to retrieve sample: {error}",
        "create_failed": "Failed to create sample: {error}",
        "not_found": "Sample not found",
        "already_exists": "Sample with SMS number {sms_number} already exists",
        "invalid_format": "Invalid sample data format",
        "sms_unreachable": "External SMS service is unreachable",
        "not_found_in_requisition": "Sample not found in requisition",
        "depth_validation": "Depth Bottom cannot be less than Depth Top"
    },
    "test": {
        "already_exists": "One or more tests already exist for this sample",
        "not_found": "Test type not found",
        "invalid_test_types": "One or more test types are invalid",
        "no_test_types": "At least one test type must be provided",
        "add_tests_failed": "Error adding tests to sample: {error}",
        "update_failed": "Failed to update test status: {error}",
        "get_tests_failed": "Error getting tests for sample: {error}",
        
        # Permission errors
        "unauthorized_create": "Only lab administrators can create test types",
        "unauthorized_update": "Only lab administrators can update test types",
        "unauthorized_delete": "Only lab administrators can delete test types",
        
        # Operation errors
        "create_failed": "Error creating test type: {error}",
        "update_failed": "Error updating test type: {error}",
        "delete_failed": "Error deleting test type: {error}",
        "list_failed": "Error listing test types: {error}",

        # Batch operation errors
        "batch_empty": "Batch update cannot be empty",
        "batch_update_failed": "Error in batch update operation: {error}",

        # Success messages
        "delete_success": "Test type deleted successfully"
    },
    "user": {
        # Not Found errors
        "not_found": "User not found",
        "email_exists": "Email already registered",
        
        # Permission errors
        "unauthorized_list": "Only lab administrators can list users",
        "unauthorized_create": "Only lab administrators can create users",
        "unauthorized_modify": "Only lab administrators can modify users",
        "unauthorized_view": "Only lab administrators can view user details",
        "unauthorized_access": "Access denied for this user",
        
        # Validation errors
        "invalid_update": "Invalid user update data",
        "error_creating": "Error creating user",
        "error_updating": "Error updating user",
        "error_deleting": "Error deleting user",
        "error_retrieving": "Error retrieving user: {error}",
        
        # Success messages
        "delete_success": "User deleted successfully"
    },
    "auth": {
        "not_authenticated": "Not authenticated",
        "unauthorized": "Not authorized to perform this action",
        "single_type": "User cannot select different roles",
        "wrong_combo": "Invalid lab and role combination for this user",
        # Role selection errors
        "role_not_set": "User has not chosen a role - Please go to /auth/select-lab-role",
        "lab_not_set": "User has not chosen a lab - Please go to /auth/select-lab-role",
        "invalid_role_lab": "Invalid role/lab combination - Please go to /auth/select-lab-role",
        "non_scientist_no_lab": "Invalid role without lab - Please go to /auth/select-lab-role",
        "credentials_invalid": "Could not validate credentials",
        "no_valid_roles": "No valid roles",
        # Authentication errors
        "login_error": "An unexpected error occurred during login",
        "logout_error": "An unexpected error occurred during logout",
        "role_selection_error": "An unexpected error occurred while selecting role",
        "logout_success": "Successfully logged out",
        "unexpected_error": "An unexpected error occurred"
    },
    "validation": {
        "skip_negative": "Skip must be non-negative",
        "limit_positive": "Limit must be positive",
        "invalid_uuid": "Input should be a valid UUID"
    },
    "file": {
        # Not Found errors
        "not_found": "File not found",
        
        # Permission errors
        "unauthorized_upload": "Scientists cannot upload files",
        "unauthorized_access": "Access denied for this file",
        "unauthorized_download": "Scientists cannot access unpublished files",
        "unauthorized_publish": "Only lab administrators can publish/unpublish files",
        "unauthorized_delete": "Scientists cannot delete files",
        
        # Operation errors
        "upload_failed": "Error uploading file: {error}",
        "download_failed": "Error downloading file: {error}",
        "list_failed": "Error listing files: {error}",
        "delete_failed": "Error deleting file: {error}",
        "publish_failed": "Error publishing file: {error}",
        "storage_error": "Storage service error: {error}",
        "metadata_failed": "Error retrieving file metadata: {error}",
        "uploaded_by_required": "uploaded_by is required",
        "uploaded_by_invalid": "Invalid uploaded_by provided",
        "invalid_uuid_format": "Invalid UUID format: {error}",
        "delete_success": "File deleted successfully"
    },
    "staff" : {
        "combo_exists": "This Staff member already exists",
        "scientist_change": "Cannot change role into scientist",
        "not_found": "Staff is not found",
        "last_admin": "Must have at least 1 lab admin remaining",
        "not_authorized": "Not authorized",
        "missing_id": "User_id or lab_id doesn't exist in the database",
        "server_error": "Server Error",
        "invalid_role": "Invalid role type. Must be string or UserRole enum",
        "scientist_lab": "Scientists can not have a lab id",
        "non_scientist_lab": "Lab Admins and Lab Personnel must have a lab_id",
        "combo_not_exists": "Combination does not exist"
    }
}

# Database operation messages
DATABASE_MESSAGES = {
    "tables_created": "Database tables created successfully",
    "tables_creation_error": "Error creating database tables: {error}",
    "admin_created": "Admin user created",
    "admin_exists": "Admin user already exists",
    "admin_creation_error": "Error creating admin user: {error}"
}