import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from .models.base import Base
from .config import settings
from app.utils.hashing import get_password_hash
from app.common.constants import UserRole, DATABASE_MESSAGES
import uuid

# Use the main database URL
SQLALCHEMY_DATABASE_URL = settings.DATABASE_URL

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    json_serializer=lambda obj: obj
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db(logger=None):
    """
    Initialize the database.
    
    Args:
        logger: A logging object. If not provided, will use print().
    """
    # Import all models here
    from app.models import (
        User,
        Requisition,
        Sample,
        TestType,
        RequisitionSample,
        RequisitionSampleTest,
        File,
        Staff,
        Lab
    )
    try:
        Base.metadata.create_all(bind=engine)
        # Log using the provided logger or fall back to print
        if logger:
            logger.info(DATABASE_MESSAGES["tables_created"])
        else:
            print(DATABASE_MESSAGES["tables_created"])
    except Exception as e:
        error_msg = DATABASE_MESSAGES["tables_creation_error"].format(error=str(e))
        if logger:
            logger.error(error_msg)
        else:
            print(error_msg)
        raise

def create_initial_admin(logger=None):
    from app.models.user import User
    from app.models.staff import Staff
    db = SessionLocal()
    try:
        default_email = settings.DEFAULT_ADMIN_EMAIL
        default_password = settings.DEFAULT_ADMIN_PASSWORD

        admin = db.query(User).filter(User.email == default_email).first()
        if not admin:
            user_id = uuid.uuid4()
            admin = User(
                user_id=user_id,
                email=default_email,
                password=get_password_hash(default_password),
                is_active=True
            )
            staff = Staff(
                staff_id = uuid.uuid4(),
                user_id = user_id,
                role=UserRole.LAB_ADMIN
            )

            db.add(admin)
            db.commit()
            #Admin needs to be created first  for foreign key to work
            db.add(staff)
            db.commit()
            
            # Log using the provided logger or fall back to print
            if logger:
                logger.info(DATABASE_MESSAGES["admin_created"])
            else:
                print(DATABASE_MESSAGES["admin_created"])
        else:
            if logger:
                logger.info(DATABASE_MESSAGES["admin_exists"])
            else:
                print(DATABASE_MESSAGES["admin_exists"])
    except Exception as e:
        error_msg = DATABASE_MESSAGES["admin_creation_error"].format(error=str(e))
        if logger:
            logger.error(error_msg)
        else:
            print(error_msg)
    finally:
        db.close()