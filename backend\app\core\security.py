#Opening Doc string
from datetime import datetime, timedelta, timezone
from typing import Optional
from jose import jwt, exceptions
from jose.exceptions import JW<PERSON>rror
from jwt import PyJWKClient
import os
from passlib.context import CryptContext

from .logging import log_warning
from ..config import settings
from sqlalchemy.orm import Session
from ..crud.user import get_user_by_email, get_user_by_azure_ad_id
from ..utils.hashing import verify_password
from ..common.exceptions import NotFoundError
from ..core.logging import logger, log_error

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def authenticate_user(db: Session, email: str, password: str):
    try:
        user = get_user_by_email(db, email)
        # User was created via Azure AD JIT and does not have a password set for local authentication.
        if user.password is None:
            log_warning(f"Attempt to authenticate user {email} with no password set (likely AAD user).")
            return False
        if not verify_password(password, user.password):
            return False
        return user
    except NotFoundError:
        return False
    except Exception as e:
        log_error(f"Error during authentication for {email}: {str(e)}", exc_info=True)
        return False

def authenticate_msal_user(db: Session, id_token:str):
    """
    Authenticates a user using an MSAL ID token.
    Validates the token, extracts claims, and attempts to find the user in the local DB by Azure AD Object ID.

    Args:
        db (Session): The database session.
        id_token (str): The Azure AD ID token.

    Returns:
        tuple: (UserModel|None, dict|None)
            - The UserModel instance if found in the DB, otherwise None.
            - A dictionary of token claims if the token is valid, otherwise None.
    """
    tenant_id = os.getenv("TENANT_ID")
    client_id = os.getenv("CLIENT_ID")

    if not tenant_id or not client_id:
        log_error("Azure AD TENANT_ID or CLIENT_ID not configured.")
        return None, None

    try:
        jwks_client = PyJWKClient(f"https://login.microsoftonline.com/{tenant_id}/discovery/v2.0/keys")
        signing_key = jwks_client.get_signing_key_from_jwt(id_token)
        
        token_claims = jwt.decode(
            id_token,
            signing_key.key,
            algorithms=["RS256"],
            audience=client_id,
            issuer=f"https://login.microsoftonline.com/{tenant_id}/v2.0",
            options={"verify_exp": True, "verify_signature": True}
        )

        try:
            db_user = get_user_by_azure_ad_id(db, token_claims['oid'])
            return db_user, token_claims
        except NotFoundError:
            return None, token_claims 

    except jwt.ExpiredSignatureError:
        log_warning(f"Azure AD token expired (partial: {id_token[:10]}...).", exc_info=True)
        return None, None
    except exceptions.JWTError as e:
        log_warning(f"Invalid Azure AD token (partial: {id_token[:10]}...): {str(e)}", exc_info=True)
        return None, None
    except Exception as e:
        log_error(f"Unexpected error during Azure AD authentication for token (partial: {id_token[:10]}...): {str(e)}", exc_info=True)
        return None, None 

    

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm="HS256")
    
    # Log token creation (without the actual token)
    email = to_encode.get("sub", "unknown")
    expiry_minutes = (expire - datetime.now(timezone.utc)).total_seconds() / 60
    logger.info(f"Created token for {email} with {expiry_minutes:.1f} minutes expiry")
    
    return encoded_jwt

def verify_token(token: str):
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        return payload
    except JWTError as e:
        # Log token verification failure
        email = None
        try:
            # Try to extract email from token without verification (This is just for logging purposes
            unverified_payload = jwt.decode(token, options={"verify_signature": False})
            email = unverified_payload.get("sub")
        except:
            pass
        
        logger.warning(
            f"JWT verification error for user {email or 'unknown'}: {str(e)}"
        )
        return None