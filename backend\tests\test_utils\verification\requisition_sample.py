"""
Verification utilities for RequisitionSample entities.
"""

from typing import Any, Dict
from uuid import UUID
from datetime import datetime
from tests.test_utils.verification.base import EntityVerifier


class RequisitionSampleVerifier(EntityVerifier):
    """Verifier for RequisitionSample entities"""
    
    COMMON_FIELDS = [
        "req_sample_id",
        "requisition_id",
        "sample_id",
        "status",
        "created_at"
    ]
    
    DATETIME_FIELDS = [
        "created_at"
    ]
    
    OPTIONAL_FIELDS = [
        "survey_id"
    ]
    
    TYPE_VALIDATIONS = {
        "req_sample_id": str,
        "requisition_id": str,
        "sample_id": str,
        "survey_id": str,
        "status": str,
        "created_at": str
    }