<!--- When Mircosoft login is completed sign in button can just be that and this page doens't need to exist-->
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="utf-8">
    <title data-wb-i18n="page-title">Sign in Page</title>
    <meta name="description" content="Some description here">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- GCWeb and WET-BOEW Stylesheets -->
    <link rel="stylesheet" href="/libraries/GCWeb/css/theme.min.css">
    <link rel="stylesheet" href="/assets/css/nav.css">
</head>

<body vocab="http://schema.org/" typeof="WebPage">
    <!-- GCWeb Header -->
    <div data-wb-ajax='{ "url": "/components/en/header.html", "type": "replace" }'></div>
    <div id="navbar"></div>

    <!-- Main Content -->
    <main role="main" property="mainContentOfPage" class="container">
        <h1 property="name">Sign in below</h1>

        <!-- Page-level Messages Section -->
        <div data-wb-ajax='{ "url": "/components/en/message-containers.html", "type": "replace" }'></div>

        <form class="form-inline" role="form" id="login-form">
            <div class="form-group">
            	<label class="wb-inv" for="input-email">Email address</label>
                <input type="email" class="form-control" id="input-email" placeholder="Enter NRCAN email" />
            </div>
            <div class="form-group">
                <label class="wb-inv" for="input-password">Password</label>
                <input type="password" class="form-control" id="input-password" placeholder="Password" autocomplete="current-password" />
            </div>
            <button type="submit" class="btn btn-default">Sign in</button>
            <button type="button" class="btn btn-primary" id="azure-login">Sign in with Microsoft</button>
        </form>
    </main>
    <br/>
    <br/>

    <!-- Footer -->
    <div data-wb-ajax='{ "url": "/components/en/footer.html", "type": "replace" }'></div>

    <!-- Scripts -->
    <script src="../libraries/wet-boew/js/jquery/2.2.4/jquery.min.js"></script>
    <script src="../libraries/wet-boew/js/wet-boew.min.js"></script>
    <script src="../libraries/GCWeb/js/theme.min.js"></script>
    <!-- For headers -->
	<script src="../assets/js/core/i18n/language-switcher.js"></script>
    <script type="module" src="../assets/js/core/auth/navbar.js"></script>
	<!-- Page specific-->
    <script src="https://alcdn.msauth.net/browser/2.38.0/js/msal-browser.min.js"></script>
    <script type="module" src="../assets/js/pages/auth/login.js"></script>
</body>
</html>
