"""
Helper functions for authentication logic.

This module contains helper functions that are used by the authentication API.
These functions are extracted from the main API file to improve code organization and maintainability.
"""

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException
from typing import Optional, List, Dict, Any, Union, Tuple

from ..database import get_db
from ..core.security import authenticate_msal_user, create_access_token
from ..schemas.user import UserCreate
from ..schemas.staff import StaffCreate
from ..models.user import User as UserModel
from ..crud.user import (
    create_user_jit,
    get_user_by_email as crud_get_user_by_email,
)
from ..crud.staff import get_staff_with_lab_names_by_user_id, create_staff
from ..common.exceptions import NotFoundError, ValidationError, AccessDeniedError
from ..common.constants import UserRole, ERROR_MESSAGES
from ..core.logging import log_error, log_warning, log_info
from ..schemas.user import User

def _validate_azure_token(db: Session, azure_token: str) -> Tuple[Optional[Dict[str, Any]], str, Optional[str]]:
    """
    Validate Azure AD token and extract key information
    
    Args:
        db: Database session
        azure_token: Azure AD ID token
        
    Returns:
        Tuple[Dict, str, str]: Token claims, Azure OID, user email
        If validation fails, token claims will be None
    
    Raises:
        HTTPException: 401/400 error codes on validation failure
    """
    db_user, token_claims = authenticate_msal_user(db, azure_token)

    if not token_claims:
        log_warning(f"Azure AD token validation failed for token (partial: {azure_token[:10]}...)")
        raise HTTPException(
            status_code=401,
            detail=ERROR_MESSAGES["auth"]["credentials_invalid"],
            headers={"WWW-Authenticate": "Bearer"},
        )

    azure_oid = token_claims.get('oid')
    email_from_aad = token_claims.get('preferred_username') or token_claims.get('email') or token_claims.get('upn')
    log_info(f"AAD token claims processed for email: {email_from_aad}")

    if not azure_oid:
        log_error(f"Azure AD OID missing from token (partial: {azure_token[:10]}...)")
        raise HTTPException(
            status_code=400,
            detail=ERROR_MESSAGES["auth"]["credentials_invalid"]
        )

    if not email_from_aad:
        log_error(f"Email not found in Azure AD token for OID {azure_oid}")
        raise HTTPException(
            status_code=400,
            detail=ERROR_MESSAGES["auth"]["credentials_invalid"]
        )
        
    return token_claims, azure_oid, email_from_aad


def _handle_existing_user(db: Session, db_user: UserModel, email_from_aad: str) -> UserModel:
    """
    Handle existing user (found by Azure AD ID)
    
    Args:
        db: Database session
        db_user: User object from database
        email_from_aad: Email from Azure AD
        
    Returns:
        UserModel: Updated user object
        
    Raises:
        ValidationError: On email conflict or update failure
    """
    # Update user email to match Azure AD
    log_info(f"Existing Azure AD user {db_user.email} (OID: {db_user.azure_ad_id}) logging in")
    db_user.email = email_from_aad
    try:
        db.commit()
        db.refresh(db_user)
        return db_user
    except IntegrityError:
        db.rollback()
        # Check if email conflicts with another user
        try:
            conflicting_user = crud_get_user_by_email(db, email_from_aad)
            if conflicting_user and conflicting_user.user_id != db_user.user_id:
                log_error(f"Email {email_from_aad} conflicts with another user")
                raise ValidationError(ERROR_MESSAGES["user"]["email_exists"])
        except NotFoundError:
            pass
        log_error(f"IntegrityError when updating email for user {db_user.email} to {email_from_aad}", exc_info=True)
        raise ValidationError(ERROR_MESSAGES["user"]["error_updating"])
    except Exception as e:
        db.rollback()
        log_error(f"Error updating user {db_user.azure_ad_id}: {str(e)}", exc_info=True)
        raise ValidationError(ERROR_MESSAGES["user"]["error_updating"])


def _handle_user_linking(db: Session, azure_oid: str, email_from_aad: str) -> Optional[UserModel]:
    """
    Attempt to link local user with Azure AD account
    
    Args:
        db: Database session
        azure_oid: Azure AD object ID
        email_from_aad: Email from Azure AD
        
    Returns:
        Optional[UserModel]: Linked user object, or None if linking not possible
        
    Raises:
        ValidationError: When email is already linked to another Azure AD account
        IntegrityError: When database update fails
    """
    try:
        # Check if a user exists with the same email but no azure_ad_id
        local_user_by_email = crud_get_user_by_email(db, email_from_aad)
        
        if local_user_by_email:
            if not local_user_by_email.azure_ad_id:
                # Link existing local user with Azure AD
                log_info(f"Linking local user {email_from_aad} with Azure AD")
                local_user_by_email.azure_ad_id = azure_oid
                db.commit()
                db.refresh(local_user_by_email)
                return local_user_by_email
            elif local_user_by_email.azure_ad_id == azure_oid:
                # Edge case - should have been caught earlier
                log_warning(f"User {email_from_aad} already linked to this Azure AD account")
                return local_user_by_email
            else:
                # Email exists but is linked to another Azure AD account
                log_error(f"Email {email_from_aad} linked to different Azure AD account")
                raise ValidationError(ERROR_MESSAGES["user"]["email_exists"])
        return None
    except NotFoundError:
        log_info(f"No local user found for email {email_from_aad}. Proceeding with new user creation flow.")
        return None
    except ValidationError:
        raise
    except IntegrityError as e:
        db.rollback()
        log_error(f"Error linking user: {str(e)}", exc_info=True)
        raise ValidationError(ERROR_MESSAGES["user"]["error_updating"])
    except Exception as e:
        db.rollback()
        log_error(f"User linking error: {str(e)}", exc_info=True)
        raise ValidationError(ERROR_MESSAGES["user"]["error_updating"])


def _create_new_user(db: Session, azure_oid: str, email_from_aad: str) -> UserModel:
    """
    Create new user with default permissions
    
    Args:
        db: Database session
        azure_oid: Azure AD object ID
        email_from_aad: Email from Azure AD
        
    Returns:
        UserModel: Newly created user
        
    Raises:
        ValidationError: When user creation fails
    """
    log_info(f"Creating new user for Azure AD (Email: {email_from_aad})")
    user_data = UserCreate(
        email=email_from_aad,
        azure_ad_id=azure_oid,
        is_active=True
    )
    try:
        # Create user and assign default scientist role
        new_user = create_user_jit(db, user_in=user_data)
        staff_data = StaffCreate(user_id=str(new_user.user_id), role=UserRole.SCIENTIST)
        create_staff(db=db, new_Staff=staff_data, lab_id=None)
        log_info(f"New user {new_user.email} created with scientist role")
        return new_user
    except IntegrityError:
        db.rollback()
        # Handle race condition - check if user was created by concurrent request
        try: 
            user = crud_get_user_by_email(db, email_from_aad)
            if user and user.azure_ad_id == azure_oid:
                log_warning(f"User {email_from_aad} created by concurrent request")
                return user
            else:
                # This could be a race condition where another user with the same email but different OID was created.
                log_error(f"Failed to create user {email_from_aad}. A user with this email may already exist with a different Azure OID.", exc_info=True)
                raise ValidationError(ERROR_MESSAGES["user"]["error_creating"])
        except NotFoundError:
            # This means the user was not created by a concurrent request, so the IntegrityError is for another reason.
            log_error(f"IntegrityError when creating user {email_from_aad}, but it's not a race condition.", exc_info=True)
            raise ValidationError(ERROR_MESSAGES["user"]["error_creating"])
    except ValueError as e:
        db.rollback()
        log_error(f"Staff provisioning error: {str(e)}", exc_info=True)
        raise ValidationError(ERROR_MESSAGES["user"]["error_creating"])
    except Exception as e:
        db.rollback()
        log_error(f"JIT provisioning error: {str(e)}", exc_info=True)
        raise ValidationError(ERROR_MESSAGES["user"]["error_creating"])


def _generate_user_token(db: Session, user: UserModel) -> str:
    """
    Get user roles and generate access token.

    This function fetches the user's roles and labs, then creates a JWT access token.
    If the user has only one role, it's automatically selected in the token.
    
    Args:
        db: Database session
        user: User object
        
    Returns:
        str: JWT access token
        
    Raises:
        HTTPException: When user has no valid roles
    """
    # Get user roles
    user_roles_with_labs = get_staff_with_lab_names_by_user_id(db, str(user.user_id))
    
    # Format lab roles data to include lab_name
    formatted_lab_roles = []
    for staff, lab_name_from_db in user_roles_with_labs:
        formatted_lab_roles.append({
            "lab": str(staff.lab_id) if staff.lab_id else None,
            "role": staff.role,
            "lab_name": lab_name_from_db
        })

    if not formatted_lab_roles:
        log_id = f" (OID: {user.azure_ad_id})" if user.azure_ad_id else ""
        log_error(f"User {user.email}{log_id} has no roles after login/provisioning")
        raise HTTPException(
            status_code=403, 
            detail=ERROR_MESSAGES["auth"]["no_valid_roles"]
        )
    
    token_data = {
        "lab_roles": formatted_lab_roles,
        "sub": user.email,
        "user_id": str(user.user_id),
        "azure_ad_id": user.azure_ad_id,
        "lab": None,
        "role": None
    }
    
    # If user has only one role, set it directly in the token
    if len(formatted_lab_roles) == 1:
        selected_role = formatted_lab_roles[0]["role"]
        selected_lab_id = formatted_lab_roles[0]["lab"]
        token_data["role"] = selected_role
        token_data["lab"] = selected_lab_id
        log_id = f" (OID: {user.azure_ad_id})" if user.azure_ad_id else ""
        log_info(f"User {user.email}{log_id} logged in with role {selected_role} in lab {selected_lab_id or 'None'}")
    else:
        log_id = f" (OID: {user.azure_ad_id})" if user.azure_ad_id else ""
        log_info(f"User {user.email}{log_id} logged in with multiple roles, needs to select one.")
            
    return create_access_token(data=token_data)


def validate_lab_requisition_access(user: User, lab_id: str) -> None:
    """
    Validate if user can access lab requisitions endpoint.

    Scientists should use the /mine endpoint instead of lab-specific endpoints.
    Lab personnel can only access requisitions from their assigned lab.

    Args:
        user: Current authenticated user
        lab_id: Lab ID being accessed

    Raises:
        AccessDeniedError: If user doesn't have permission to access the lab endpoint
    """
    if user.role == UserRole.SCIENTIST:
        log_warning(f"Scientist user {user.user_id} attempted to access lab endpoint")
        raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_lab_endpoint"])

    if user.role in [UserRole.LAB_ADMIN, UserRole.LAB_PERSONNEL]:
        if str(user.lab) != lab_id:
            log_warning(f"User {user.user_id} attempted to access lab {lab_id} but belongs to lab {user.lab}")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_lab"])
    else:
        log_error(f"Unknown user role: {user.role} for user {user.user_id}")
        raise AccessDeniedError(ERROR_MESSAGES["auth"]["unauthorized"])



