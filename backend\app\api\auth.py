"""
API Authentication Responses 

This Script handles all API responses for authentication
    POST /login            # Login as a user with username/password. Returns an access token
    POST /auth/azure-login # Login using Azure AD authentication. Returns an access token
    POST /logout           # Logout the user
    GET /me                # Get current user information
    POST /select-lab-role  # A logged-in user can choose their role and lab

"""
from fastapi import APIRouter, Depends, HTTPException, status, Request, Form
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from ..database import get_db
from ..core.security import authenticate_user, create_access_token, authenticate_msal_user
from ..core.dependencies import get_current_user, enforce_role_selection
from ..schemas.user import User
from ..models.user import User as UserModel
from ..crud.user import get_user_by_azure_ad_id
from ..crud.staff import get_combination
from ..common.exceptions import ValidationError
from ..common.constants import UserRole, ERROR_MESSAGES
from fastapi import Form
from typing import Optional
from ..core.logging import log_error, log_warning, log_info
from ..utils.auth_helpers import (
    _validate_azure_token,
    _handle_existing_user,
    _handle_user_linking,
    _create_new_user,
    _generate_user_token
)

router = APIRouter()


@router.post("/azure-login")
async def azure_login(
    request: Request,
    azure_token: str = Form(...),
    db: Session = Depends(get_db)
):
    """
    Authenticate a user using MSAL ID token and return a JWT access token.
    
    Note:
        This endpoint handles authentication via Microsoft Azure AD.
        It supports JIT (Just-In-Time) user provisioning for new users
        and will automatically create accounts with default scientist role.
        If the user already exists, their details will be synced with Azure AD.
        
    Args:
        request (Request): The FastAPI request object
        azure_token (str): The Azure AD ID token from client
        db (Session): Database session dependency
        
    Returns:
        dict: Contains access token and token type
            - access_token (str): JWT access token
            - token_type (str): Token type (bearer)
            
    Raises:
        HTTPException: Various error codes based on authentication issues
    """
    email_from_aad = None
    try:
        token_claims, azure_oid, email_from_aad = _validate_azure_token(db, azure_token)
        
        if not token_claims:
            raise HTTPException(
                status_code=401,
                detail=ERROR_MESSAGES["auth"]["credentials_invalid"],
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        db_user = get_user_by_azure_ad_id(db, azure_oid)
        
        if db_user:
            user_to_log_in = _handle_existing_user(db, db_user, email_from_aad)
        else:
            linked_user = _handle_user_linking(db, azure_oid, email_from_aad)
            if linked_user:
                user_to_log_in = linked_user
            else:
                user_to_log_in = _create_new_user(db, azure_oid, email_from_aad)
        
        access_token = _generate_user_token(db, user_to_log_in)
        
        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException as http_exc:
        # Re-raise HTTP exceptions with their original status codes
        log_error(f"HTTP error during Azure login for {email_from_aad}: {str(http_exc)}", exc_info=True)
        raise
    except Exception as e:
        # Log unexpected errors
        log_error(f"Unexpected error during Azure login for {email_from_aad}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=ERROR_MESSAGES["auth"]["unexpected_error"])


@router.post("/login")
async def login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    Authenticate a user and return a JWT access token.

    Note:
        The access token contains information about the users current role and lab
        they are currently working in. If the user has multiple role/lab combinations
        to choose form, they must go to a different endpoint to get access to other endpoints.
        If there is only 1 combination, then they proceed as normal.

    Args:
        form_data (OAuth2PasswordRequestForm): Form containing username and password
        db (Session): Database session dependency

    Returns:
        dict: Contains access token and token type
            - access_token (str): JWT access token
            - token_type (str): Token type (bearer)

    Raises:
        HTTPException: 401 error if credentials are invalid
    """
    try:
        user = authenticate_user(db, form_data.username, form_data.password)
        if not user:
            log_warning(f"Login failed for user {form_data.username}: incorrect credentials")
            raise HTTPException(
                status_code=401,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Generate token using the centralized function
        access_token = _generate_user_token(db, user)
        
        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException as http_exc:
        # Re-raise HTTP exceptions with their original status codes
        log_error(f"HTTP error during login for {form_data.username}: {str(http_exc)}", exc_info=True)
        raise
    except Exception as e:
        # Log unexpected errors
        log_error(f"Unexpected error during login for {form_data.username}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=ERROR_MESSAGES["auth"]["unexpected_error"])


@router.post("/logout")
async def logout(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """
    Logout endpoint for client-side logout functionality.
    
    Note:
        For JWT-based authentication, this is primarily for client-side cleanup
        as tokens are stateless and managed client-side.
    
    Returns:
        dict: A message confirming successful logout
            - message (str): Success confirmation message
    """
    try:
        log_info(f"User {current_user.email} logged out")
        
        # For JWT, client-side logout is sufficient
        return {"message": ERROR_MESSAGES["auth"]["logout_success"]}
    except HTTPException as http_exc:
        # Re-raise HTTP exceptions with their original status codes
        log_error(f"HTTP error during logout for {current_user.email}: {str(http_exc)}", exc_info=True)
        raise
    except Exception as e:
        # Log unexpected errors
        log_error(f"Unexpected error during logout for {current_user.email}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["auth"]["logout_error"]
        )

@router.get("/me", response_model=User)
async def read_users_me(current_user: User = Depends(enforce_role_selection)):
    """
    Get details of the currently authenticated user.
    
    Args:
        current_user (User): Current authenticated user, injected by dependency
    
    Returns:
        User: The user's profile information
    
    Raises:
        HTTPException: 401 error if no valid authentication token is provided
    """
    return dict(current_user)

@router.post("/select-lab-role")
async def select_lab_role(
    request: Request,
    role: UserRole = Form(...),
    lab_id: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
    ):
    """
    Returns a new token with role and lab_id attached to it

    This was done to allow for users to choose & change what role and lab they are currently working in.
    If the user only has 1 role (for 1 lab) this endpoint will return an error, as there is nothing to change

    ### Response Model
    - Returns a new access token with the selected role & lab_id set

    ### Errors:
    - 403 Forbidden: If the user is signed in
    - 400 : User trying to switch into an impossible role/lab

    """
    try:
        if not current_user.lab_roles or len(current_user.lab_roles) <= 1:
            log_warning(f"Role change failed for user {current_user.email}: only has one role")
            raise HTTPException(status_code=403, detail=ERROR_MESSAGES["auth"]["single_type"])
        
        #Validate role/lab combination
        combination = get_combination(db, str(current_user.user_id), lab_id, role)
        if combination is None or not combination:
            log_warning(f"Role change failed for user {current_user.email}: invalid lab/role combination. Lab ID: {lab_id}, Role: {role}")
            raise HTTPException(status_code=403, detail=ERROR_MESSAGES["auth"]["wrong_combo"])
        
        # Get lab_id and role strings from the combination
        selected_lab_id_str = str(combination[0].lab_id) if combination[0].lab_id is not None else None
        selected_role_str = combination[0].role
        
        # lab_roles_data from current_user.lab_roles should already contain lab_name
        lab_roles_data_for_new_token = current_user.lab_roles or []

        # Construct token data directly
        token_data = {
            "lab_roles": lab_roles_data_for_new_token,
            "lab": selected_lab_id_str,
            "role": selected_role_str,
            "sub": current_user.email,
            "user_id": str(current_user.user_id),
            "azure_ad_id": current_user.azure_ad_id
        }
        
        access_token = create_access_token(data=token_data)
        
        log_info(f"User {current_user.email} changed role to {selected_role_str} in lab {selected_lab_id_str or 'None'}")
        
        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException as http_exc:
        # Re-raise HTTP exceptions with their original status codes
        log_error(f"HTTP error during role selection for {current_user.email}: {str(http_exc)}", exc_info=True)
        raise
    except Exception as e:
        # Log unexpected errors
        log_error(f"Unexpected error during role selection for {current_user.email}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["auth"]["role_selection_error"]
        )
        
