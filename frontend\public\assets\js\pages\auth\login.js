/*
This page handles the log in logic 

Works still to be done:
    Proper error notification to user

Future work:
    IFF it gets approved: https://wet-boew.github.io/GCWeb/sites/authentication/activeusersession-en.html 

*/

import { msalInstancePromise } from "../../core/auth/msalConfig.js";
import { showMessage } from '../../core/helpers/message-helpers.js';
import { parseJWT } from '../../core/auth/auth-helpers.js';
import { redirectToPageByKey, getCurrentLanguagePrefix } from '../../core/helpers/navigation-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS
};

$(document).ready(function () {
    initializeLoginPage();
});

// Initialize login page functionality
function initializeLoginPage() {
    setupLoginForm();
    setupAzureLogin();
}

// Handle successful login response
function handleLoginSuccess(response) {
    // Save the access token
    localStorage.setItem("access_token", response.access_token);

    // Decode the JWT to extract user information
    const payload = parseJWT(response.access_token);

    // Session-specific data (to be cleared at session end)
    localStorage.setItem("lab", payload.lab);
    localStorage.setItem("role", payload.role);
    localStorage.setItem("lab_roles", JSON.stringify(payload.lab_roles));

    // Persistent data (can stay in localStorage)
    localStorage.setItem("sub", payload.sub);

    return payload;
}

// Setup regular login form
function setupLoginForm() {
    $("#login-form").submit(function (e) {
        e.preventDefault(); // Prevent default form submit behavior


        //Detect language from URL
        const langPrefix = getCurrentLanguagePrefix();

        //Email and password verification is handled by WET_BOEW library
        const email = $("#input-email").val();
        const password = $("#input-password").val();

        if (!email || !password) {
            showMessage('#page-error-container', 'auth.messages.warnings', 'missingCredentials', MESSAGE_CONFIG_MAPS);
            return;
        }

        // Send the login request
        $.ajax({
            url: `${GLOBAL_CONFIGS.api.baseUrl}/auth/login`,
            method: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                username: email,
                password: password
            },
            success: function (response) {
                console.log("Login successful:", response);

                if (response.access_token) {
                    const payload = handleLoginSuccess(response);

                    // Determine redirect based on user's role assignment
                    if (payload.role) {
                        // User has a lab and role already assigned (single combination)
                        redirectToPageByKey('home', 0);

                        /// FUTURE WORK
                        /// DO we get /auth/me to get the users name for https://wet-boew.github.io/GCWeb/sites/authentication/activeusersession-en.html
                        // BUT we that hasn't been approved yet anyways

                    } else if (payload.lab_roles && payload.lab_roles.length > 0) {
                        // User has multiple lab-role combinations; redirect to selection page
                        redirectToPageByKey('roleSelection', 0);
                    } else {
                        // User has no roles - show appropriate error message
                        showMessage('#page-error-container', 'auth.messages.warnings', 'noRolesFound', MESSAGE_CONFIG_MAPS);
                    }
                } else {
                    // Something has gone horribly wrong, can sign in but nothing was returned -> 500 error page
                    showMessage('#page-error-container', 'auth.messages.errors', 'noTokenReturned', MESSAGE_CONFIG_MAPS);
                }
            },
            error: function (jqXHR) {
                // Differentiate between connection errors and authentication errors
                if (jqXHR.status === 0) {
                    // Network connection failed or server is down
                    showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
                } else if (jqXHR.status === 401) {
                    // Authentication failed - invalid credentials
                    showMessage('#page-error-container', 'auth.messages.errors', 'loginFailed', MESSAGE_CONFIG_MAPS);
                } else if (jqXHR.status >= 500) {
                    // Server internal error
                    showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
                } else {
                    // Other errors - default to login failed
                    showMessage('#page-error-container', 'auth.messages.errors', 'loginFailed', MESSAGE_CONFIG_MAPS);
                }
            }
        });
    });
}

// Setup Azure AD login
function setupAzureLogin() {
    $("#azure-login").click(async function () {
        //Lets sign in using Microsoft
        try {
            const langPrefix = getCurrentLanguagePrefix();

            const msalInstance = await msalInstancePromise;

            //The actual pop up
            const loginResponse = await msalInstance.loginPopup({
                scopes: ["openid", "profile", "email"]
            });


            const idToken = loginResponse.idToken;

            if (!idToken){
                showMessage('#page-error-container', 'auth.messages.errors', 'azureTokenMissing', MESSAGE_CONFIG_MAPS);
                return;
            }

            //Set the azure token
            localStorage.setItem("azure_id_token", idToken)
            localStorage.setItem("name", loginResponse.idTokenClaims.name)

            //Lets talk to our backend now
            $.ajax({
                url: `${GLOBAL_CONFIGS.api.baseUrl}/auth/azure-login`,
                method: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    azure_token: idToken
                },
                success: function (response) {
                    const payload = handleLoginSuccess(response);

                    // Determine redirect based on user's role assignment
                    if (payload.lab && payload.role) {
                        // User has a lab and role already assigned (single combination)
                        redirectToPageByKey('home', 0);

                    } else if (payload.lab_roles && payload.lab_roles.length > 0) {
                        // User has multiple lab-role combinations; redirect to selection page
                        redirectToPageByKey('roleSelection', 0);
                    } else {
                        // User has no roles - show appropriate error message
                        showMessage('#page-error-container', 'auth.messages.warnings', 'noRolesFound', MESSAGE_CONFIG_MAPS);
                    }
                },
                error: function (jqXHR) {
                    // Error happened, clear everything that might have been set
                    localStorage.clear();
                    sessionStorage.clear();

                    // Differentiate between connection and authentication errors for AAD backend request
                    if (jqXHR.status === 0) {
                        // Network connection failed or server is down
                        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
                    } else if (jqXHR.status === 401) {
                        // Authentication failed with backend
                        showMessage('#page-error-container', 'auth.messages.errors', 'loginFailed', MESSAGE_CONFIG_MAPS);
                    } else if (jqXHR.status >= 500) {
                        // Server internal error
                        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
                    } else {
                        // Other errors - default to login failed
                        showMessage('#page-error-container', 'auth.messages.errors', 'loginFailed', MESSAGE_CONFIG_MAPS);
                    }
                }
            })
        }
        catch (error) {
            // Clear everything from storage in case anything was set
            localStorage.clear();
            sessionStorage.clear();

            // Handle different types of AAD popup errors
            if (error.errorCode === 'user_cancelled' || error.name === 'BrowserAuthError') {
                // User cancelled the popup - show informational message
                console.log('User cancelled Azure login popup');
                showMessage('#page-warning-container', 'auth.messages.info', 'azureUserCancelled', MESSAGE_CONFIG_MAPS);
            } else if (error.name === 'NetworkError' || (error.message && error.message.includes('network'))) {
                // Network connection issues with AAD service
                console.error('Azure login network error:', error);
                showMessage('#page-error-container', 'auth.messages.warnings', 'azureConnectionError', MESSAGE_CONFIG_MAPS);
            } else if (error.errorCode && error.errorCode.includes('service')) {
                // AAD service-related errors
                console.error('Azure login service error:', error);
                showMessage('#page-error-container', 'auth.messages.warnings', 'azureServiceError', MESSAGE_CONFIG_MAPS);
            } else {
                // Other technical errors - show generic AAD error
                console.error('Azure login error:', error);
                showMessage('#page-error-container', 'auth.messages.errors', 'azureLoginFailed', MESSAGE_CONFIG_MAPS);
            }
        }
    });
}
