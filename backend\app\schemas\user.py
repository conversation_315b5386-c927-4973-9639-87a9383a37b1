from pydantic import BaseModel, EmailStr, UUID4, ConfigDict, field_validator
from typing import Optional, List, Dict, Any
from ..common.constants import UserRole

"""
We decided to not have a separate pydantic model for Staff and User
    1) We would need 2 separate get-er functions to get information about the current user. 
    We would also need an additional checker function making sure that the user_ids match up between the 2

    2) While signed in - you are only ever interacting with the system as 1 role for (potentially) 1 lab.
    Having what user, what role and what lab is interacting with the system in 1 place allows for better understanding/simpler code

    3) When returning any user info, labs+roles should be returned as well.
    If we had a staff and user model, we would need an user_staff model for returning this information anyways.
    Although having 1 model does make for a slightly more complicated user creation
"""
class UserBase(BaseModel):
    email: EmailStr
    is_active: bool = True
    azure_ad_id: str

class UserCreate(UserBase):
    password: Optional[str] = None
    
    # Role validation is now handled through the Staff table

class UserUpdate(BaseModel):
    is_active: Optional[bool] = None

class User(UserBase):
    user_id: UUID4
    role: Optional[UserRole] = None  # Currently selected role for the active session
    lab: Optional[UUID4] = None      # Currently selected lab for the active session
    lab_roles: Optional[List[Dict[str, Any]]] = None  # List of all roles a user has across different labs

    model_config = ConfigDict(from_attributes=True)

    def dict(self, *args, **kwargs):
        d = super().model_dump(*args, **kwargs)
        d['user_id'] = str(d['user_id'])  # Convert UUID to string
        return d