/*!
 * Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v4.0.85 - 2025-02-20
 *
 */ /*! Modernizr (Custom Build) | MIT & BSD */
/*! @license DOMPurify 3.1.7 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.1.7/LICENSE */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).DOMPurify=t()}(this,function(){"use strict";const{"entries":We,"setPrototypeOf":o,"isFrozen":s,"getPrototypeOf":c,"getOwnPropertyDescriptor":d}=Object;let{"freeze":Ve,"seal":e,"create":Qe}=Object,{"apply":i,"construct":r}="undefined"!=typeof Reflect&&Reflect;Ve=Ve||function(e){return e},e=e||function(e){return e},i=i||function(e,t,a){return e.apply(t,a)},r=r||function(e,t){return new e(...t)};const $e=l(Array.prototype.forEach),Ge=l(Array.prototype.pop),Je=l(Array.prototype.push),Ke=l(String.prototype.toLowerCase),Ze=l(String.prototype.toString),Xe=l(String.prototype.match),et=l(String.prototype.replace),vt=l(String.prototype.indexOf),xt=l(String.prototype.trim),tt=l(Object.prototype.hasOwnProperty),at=l(RegExp.prototype.test),rt=(n=TypeError,function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];return r(n,t)});var n;function l(n){return function(e){for(var t=arguments.length,a=new Array(1<t?t-1:0),r=1;r<t;r++)a[r-1]=arguments[r];return i(n,e,a)}}function nt(t,a,e){var r,n=2<arguments.length&&void 0!==e?e:Ke;o&&o(t,null);let i=a.length;for(;i--;){let e=a[i];"string"==typeof e&&(r=n(e))!==e&&(s(a)||(a[i]=r),e=r),t[e]=!0}return t}function it(e){var t,a,r=Qe(null);for([t,a]of We(e))tt(e,t)&&(Array.isArray(a)?r[t]=function(t){for(let e=0;e<t.length;e++)tt(t,e)||(t[e]=null);return t}(a):a&&"object"==typeof a&&a.constructor===Object?r[t]=it(a):r[t]=a);return r}function ot(e,t){for(;null!==e;){var a=d(e,t);if(a){if(a.get)return l(a.get);if("function"==typeof a.value)return l(a.value)}e=c(e)}return function(){return null}}const st=Ve(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),lt=Ve(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),ct=Ve(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Tt=Ve(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),dt=Ve(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),At=Ve(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),ut=Ve(["#text"]),pt=Ve(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),ft=Ve(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),gt=Ve(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ht=Ve(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]);var t=e(/\{\{[\w\W]*|[\w\W]*\}\}/gm),a=e(/<%[\w\W]*|[\w\W]*%>/gm),u=e(/\${[\w\W]*}/gm),p=e(/^data-[\-\w.\u00B7-\uFFFF]/),f=e(/^aria-[\-\w]+$/);const mt=e(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i);var g=e(/^(?:\w+script|data):/i),h=e(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g);const bt=e(/^html$/i);var m=e(/^[a-z][.\w]*(-[.\w]+)+$/i),yt=Object.freeze({"__proto__":null,"MUSTACHE_EXPR":t,"ERB_EXPR":a,"TMPLIT_EXPR":u,"DATA_ATTR":p,"ARIA_ATTR":f,"IS_ALLOWED_URI":mt,"IS_SCRIPT_OR_DATA":g,"ATTR_WHITESPACE":h,"DOCTYPE_NAME":bt,"CUSTOM_ELEMENT":m});const wt={"element":1,"attribute":2,"text":3,"cdataSection":4,"entityReference":5,"entityNode":6,"progressingInstruction":7,"comment":8,"document":9,"documentType":10,"documentFragment":11,"notation":12};return function R(e){e=0<arguments.length&&void 0!==e?e:"undefined"==typeof window?null:window;const c=e=>R(e);if(c.version="3.1.7",c.removed=[],!e||!e.document||e.document.nodeType!==wt.document)return c.isSupported=!1,c;let n=e.document;const s=n,_=s.currentScript,{"DocumentFragment":z,"HTMLTemplateElement":B,"Node":l,"Element":U,"NodeFilter":t,"NamedNodeMap":F=e.NamedNodeMap||e.MozNamedAttrMap,"HTMLFormElement":H,"DOMParser":q,"trustedTypes":d}=e;e=U.prototype;const Y=ot(e,"cloneNode"),W=ot(e,"remove"),V=ot(e,"nextSibling"),Q=ot(e,"childNodes"),o=ot(e,"parentNode");"function"==typeof B&&(e=n.createElement("template")).content&&e.content.ownerDocument&&(n=e.content.ownerDocument);let u,p="";const{"implementation":i,"createNodeIterator":$,"createDocumentFragment":G,"getElementsByTagName":J}=n,K=s.importNode;let r={};c.isSupported="function"==typeof We&&"function"==typeof o&&i&&void 0!==i.createHTMLDocument;const{"MUSTACHE_EXPR":f,"ERB_EXPR":g,"TMPLIT_EXPR":Z,"DATA_ATTR":X,"ARIA_ATTR":ee,"IS_SCRIPT_OR_DATA":te,"ATTR_WHITESPACE":ae,"CUSTOM_ELEMENT":re}=yt;let ne=yt.IS_ALLOWED_URI,h=null;const ie=nt({},[...st,...lt,...ct,...dt,...ut]);let m=null;const oe=nt({},[...pt,...ft,...gt,...ht]);let b=Object.seal(Qe(null,{"tagNameCheck":{"writable":!0,"configurable":!1,"enumerable":!0,"value":null},"attributeNameCheck":{"writable":!0,"configurable":!1,"enumerable":!0,"value":null},"allowCustomizedBuiltInElements":{"writable":!0,"configurable":!1,"enumerable":!0,"value":!1}})),y=null,se=null,le=!0,ce=!0,de=!1,ue=!0,w=!1,pe=!0,v=!1,fe=!1,ge=!1,x=!1,T=!1,A=!1,he=!0,me=!1;const be="user-content-";let ye=!0,k=!1,a={},C=null;const we=nt({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ve=null;const xe=nt({},["audio","video","img","source","image","track"]);let Te=null;const Ae=nt({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),E="http://www.w3.org/1998/Math/MathML",j="http://www.w3.org/2000/svg",S="http://www.w3.org/1999/xhtml";let N=S,ke,Ce=null;const Ee=nt({},[E,j,S],Ze);let O=null;const je=["application/xhtml+xml","text/html"];let D=null,M=null;function Se(e){return e instanceof RegExp||e instanceof Function}function Ne(){let e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};if(!M||M!==e){if(e=it(e=e&&"object"==typeof e?e:{}),O=-1===je.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,D="application/xhtml+xml"===O?Ze:Ke,h=tt(e,"ALLOWED_TAGS")?nt({},e.ALLOWED_TAGS,D):ie,m=tt(e,"ALLOWED_ATTR")?nt({},e.ALLOWED_ATTR,D):oe,Ce=tt(e,"ALLOWED_NAMESPACES")?nt({},e.ALLOWED_NAMESPACES,Ze):Ee,Te=tt(e,"ADD_URI_SAFE_ATTR")?nt(it(Ae),e.ADD_URI_SAFE_ATTR,D):Ae,ve=tt(e,"ADD_DATA_URI_TAGS")?nt(it(xe),e.ADD_DATA_URI_TAGS,D):xe,C=tt(e,"FORBID_CONTENTS")?nt({},e.FORBID_CONTENTS,D):we,y=tt(e,"FORBID_TAGS")?nt({},e.FORBID_TAGS,D):{},se=tt(e,"FORBID_ATTR")?nt({},e.FORBID_ATTR,D):{},a=!!tt(e,"USE_PROFILES")&&e.USE_PROFILES,le=!1!==e.ALLOW_ARIA_ATTR,ce=!1!==e.ALLOW_DATA_ATTR,de=e.ALLOW_UNKNOWN_PROTOCOLS||!1,ue=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,w=e.SAFE_FOR_TEMPLATES||!1,pe=!1!==e.SAFE_FOR_XML,v=e.WHOLE_DOCUMENT||!1,x=e.RETURN_DOM||!1,T=e.RETURN_DOM_FRAGMENT||!1,A=e.RETURN_TRUSTED_TYPE||!1,ge=e.FORCE_BODY||!1,he=!1!==e.SANITIZE_DOM,me=e.SANITIZE_NAMED_PROPS||!1,ye=!1!==e.KEEP_CONTENT,k=e.IN_PLACE||!1,ne=e.ALLOWED_URI_REGEXP||mt,N=e.NAMESPACE||S,b=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Se(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(b.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Se(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(b.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(b.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),w&&(ce=!1),T&&(x=!0),a&&(h=nt({},ut),m=[],!0===a.html&&(nt(h,st),nt(m,pt)),!0===a.svg&&(nt(h,lt),nt(m,ft),nt(m,ht)),!0===a.svgFilters&&(nt(h,ct),nt(m,ft),nt(m,ht)),!0===a.mathMl)&&(nt(h,dt),nt(m,gt),nt(m,ht)),e.ADD_TAGS&&nt(h=h===ie?it(h):h,e.ADD_TAGS,D),e.ADD_ATTR&&nt(m=m===oe?it(m):m,e.ADD_ATTR,D),e.ADD_URI_SAFE_ATTR&&nt(Te,e.ADD_URI_SAFE_ATTR,D),e.FORBID_CONTENTS&&nt(C=C===we?it(C):C,e.FORBID_CONTENTS,D),ye&&(h["#text"]=!0),v&&nt(h,["html","head","body"]),h.table&&(nt(h,["tbody"]),delete y.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw rt('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw rt('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');u=e.TRUSTED_TYPES_POLICY,p=u.createHTML("")}else null!==(u=void 0===u?function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let a=null;var r="data-tt-policy-suffix",t="dompurify"+((a=t&&t.hasAttribute(r)?t.getAttribute(r):a)?"#"+a:"");try{return e.createPolicy(t,{"createHTML"(e){return e},"createScriptURL"(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+t+" could not be created."),null}}(d,_):u)&&"string"==typeof p&&(p=u.createHTML(""));Ve&&Ve(e),M=e}}function Oe(e){let t=null,a=null;ge?e="<remove></remove>"+e:(r=Xe(e,/^[\r\n\t ]+/),a=r&&r[0]),"application/xhtml+xml"===O&&N===S&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var r=u?u.createHTML(e):e;if(N===S)try{t=(new q).parseFromString(r,O)}catch(e){}if(!t||!t.documentElement){t=i.createDocument(N,"template",null);try{t.documentElement.innerHTML=ke?p:r}catch(e){}}return r=t.body||t.documentElement,e&&a&&r.insertBefore(n.createTextNode(a),r.childNodes[0]||null),N===S?J.call(t,v?"html":"body")[0]:v?t.documentElement:r}function De(e){return $.call(e.ownerDocument||e,e,t.SHOW_ELEMENT|t.SHOW_COMMENT|t.SHOW_TEXT|t.SHOW_PROCESSING_INSTRUCTION|t.SHOW_CDATA_SECTION,null)}function Me(e){return"function"==typeof l&&e instanceof l}function Ie(t){let a=null;if(P("beforeSanitizeElements",t,null),!qe(t)){var e=D(t.nodeName);if(P("uponSanitizeElement",t,{"tagName":e,"allowedTags":h}),(!t.hasChildNodes()||Me(t.firstElementChild)||!at(/<[/\w]/g,t.innerHTML)||!at(/<[/\w]/g,t.textContent))&&!(t.nodeType===wt.progressingInstruction||pe&&t.nodeType===wt.comment&&at(/<[/\w]/g,t.data))){if(h[e]&&!y[e])return t instanceof U&&!function(e){let t=o(e);t&&t.tagName||(t={"namespaceURI":N,"tagName":"template"});var a=Ke(e.tagName),r=Ke(t.tagName);return Ce[e.namespaceURI]&&(e.namespaceURI===j?t.namespaceURI===S?"svg"===a:t.namespaceURI===E?"svg"===a&&("annotation-xml"===r||ze[r]):Boolean(Fe[a]):e.namespaceURI===E?t.namespaceURI===S?"math"===a:t.namespaceURI===j?"math"===a&&Be[r]:Boolean(He[a]):e.namespaceURI===S?!(t.namespaceURI===j&&!Be[r]||t.namespaceURI===E&&!ze[r]||He[a])&&(Ue[a]||!Fe[a]):"application/xhtml+xml"===O&&Ce[e.namespaceURI])}(t)||("noscript"===e||"noembed"===e||"noframes"===e)&&at(/<\/no(script|embed|frames)/i,t.innerHTML)?(I(t),!0):(w&&t.nodeType===wt.text&&(a=t.textContent,$e([f,g,Z],e=>{a=et(a,e," ")}),t.textContent!==a)&&(Je(c.removed,{"element":t.cloneNode()}),t.textContent=a),P("afterSanitizeElements",t,null),!1);if(!y[e]&&Le(e)){if(b.tagNameCheck instanceof RegExp&&at(b.tagNameCheck,e))return;if(b.tagNameCheck instanceof Function&&b.tagNameCheck(e))return}if(ye&&!C[e]){var r=o(t)||t.parentNode,n=Q(t)||t.childNodes;if(n&&r)for(let e=n.length-1;0<=e;--e){var i=Y(n[e],!0);i.__removalCount=(t.__removalCount||0)+1,r.insertBefore(i,V(t))}}}}return I(t),1}function Le(e){return"annotation-xml"!==e&&Xe(e,re)}function Pe(a){P("beforeSanitizeAttributes",a,null);var r=a.attributes;if(r){var n={"attrName":"","attrValue":"","keepAttr":!0,"allowedAttributes":m};let e=r.length;for(;e--;){var{"name":i,"namespaceURI":o,"value":s}=r[e],l=D(i);let t="value"===i?s:xt(s);if(n.attrName=l,n.attrValue=t,n.keepAttr=!0,n.forceKeepAttr=void 0,P("uponSanitizeAttribute",a,n),t=n.attrValue,!n.forceKeepAttr&&(L(i,a),n.keepAttr))if(!ue&&at(/\/>/i,t))L(i,a);else if(w&&$e([f,g,Z],e=>{t=et(t,e," ")}),s=D(a.nodeName),Ye(s,l,t))if(!me||"id"!==l&&"name"!==l||(L(i,a),t=be+t),pe&&at(/((--!?|])>)|<\/(style|title)/i,t))L(i,a);else{if(u&&"object"==typeof d&&"function"==typeof d.getAttributeType&&!o)switch(d.getAttributeType(s,l)){case"TrustedHTML":t=u.createHTML(t);break;case"TrustedScriptURL":t=u.createScriptURL(t)}try{o?a.setAttributeNS(o,i,t):a.setAttribute(i,t),qe(a)?I(a):Ge(c.removed)}catch(e){}}}P("afterSanitizeAttributes",a,null)}}function Re(e){var t,a=De(e);for(P("beforeSanitizeShadowDOM",e,null);t=a.nextNode();)P("uponSanitizeShadowNode",t,null),Ie(t)||(t.content instanceof z&&Re(t.content),Pe(t));P("afterSanitizeShadowDOM",e,null)}const _e=n.createElement("form"),ze=nt({},["mi","mo","mn","ms","mtext"]),Be=nt({},["annotation-xml"]),Ue=nt({},["title","style","font","a","script"]),Fe=nt({},[...lt,...ct,...Tt]),He=nt({},[...dt,...At]),I=function(t){Je(c.removed,{"element":t});try{o(t).removeChild(t)}catch(e){W(t)}},L=function(e,t){try{Je(c.removed,{"attribute":t.getAttributeNode(e),"from":t})}catch(e){Je(c.removed,{"attribute":null,"from":t})}if(t.removeAttribute(e),"is"===e&&!m[e])if(x||T)try{I(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},qe=function(e){return e instanceof H&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof F)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},P=function(e,t,a){r[e]&&$e(r[e],e=>{e.call(c,t,a,M)})},Ye=function(e,t,a){if(he&&("id"===t||"name"===t)&&(a in n||a in _e))return!1;if((!ce||se[t]||!at(X,t))&&(!le||!at(ee,t)))if(!m[t]||se[t]){if(!(Le(e)&&(b.tagNameCheck instanceof RegExp&&at(b.tagNameCheck,e)||b.tagNameCheck instanceof Function&&b.tagNameCheck(e))&&(b.attributeNameCheck instanceof RegExp&&at(b.attributeNameCheck,t)||b.attributeNameCheck instanceof Function&&b.attributeNameCheck(t))||"is"===t&&b.allowCustomizedBuiltInElements&&(b.tagNameCheck instanceof RegExp&&at(b.tagNameCheck,a)||b.tagNameCheck instanceof Function&&b.tagNameCheck(a))))return!1}else if(!Te[t]&&!at(ne,et(a,ae,""))&&("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==vt(a,"data:")||!ve[e])&&(!de||at(te,et(a,ae,"")))&&a)return!1;return!0};return c.sanitize=function(e){var t,a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};let r=null,n=null;if("string"!=typeof(e=(ke=!e)?"\x3c!--\x3e":e)&&!Me(e)){if("function"!=typeof e.toString)throw rt("toString is not a function");if("string"!=typeof(e=e.toString()))throw rt("dirty is not a string, aborting")}if(!c.isSupported)return e;if(fe||Ne(a),c.removed=[],k="string"!=typeof e&&k){if(e.nodeName){a=D(e.nodeName);if(!h[a]||y[a])throw rt("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)(a=(r=Oe("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType===wt.element&&"BODY"===a.nodeName||"HTML"===a.nodeName?r=a:r.appendChild(a);else{if(!x&&!w&&!v&&-1===e.indexOf("<"))return u&&A?u.createHTML(e):e;if(!(r=Oe(e)))return x?null:A?p:""}r&&ge&&I(r.firstChild);for(var i=De(k?e:r);t=i.nextNode();)Ie(t)||(t.content instanceof z&&Re(t.content),Pe(t));if(k)return e;if(x){if(T)for(n=G.call(r.ownerDocument);r.firstChild;)n.appendChild(r.firstChild);else n=r;return n=m.shadowroot||m.shadowrootmode?K.call(s,n,!0):n}let o=v?r.outerHTML:r.innerHTML;return v&&h["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&at(bt,r.ownerDocument.doctype.name)&&(o="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+o),w&&$e([f,g,Z],e=>{o=et(o,e," ")}),u&&A?u.createHTML(o):o},c.setConfig=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};Ne(e),fe=!0},c.clearConfig=function(){M=null,fe=!1},c.isValidAttribute=function(e,t,a){return M||Ne({}),e=D(e),t=D(t),Ye(e,t,a)},c.addHook=function(e,t){"function"==typeof t&&(r[e]=r[e]||[],Je(r[e],t))},c.removeHook=function(e){if(r[e])return Ge(r[e])},c.removeHooks=function(e){r[e]&&(r[e]=[])},c.removeAllHooks=function(){r={}},c}()}),function(c,t,a){"use strict";c.extend=c.fn.extend=function(){var e,t,a,r,n,i=arguments[0]||{},o=1,s=arguments.length,l=!1;for("boolean"==typeof i&&(l=i,i=arguments[o]||{},o++),"object"!=typeof i&&"function"!=typeof i&&(i={}),o===s&&(i=this,o--);o<s;o++)if(void 0!==(e=arguments[o])||null!==e)for(t in e)a=e[t],"__proto__"!==t&&i!==a&&(l&&a&&(c.isPlainObject(a)||(r=Array.isArray(a)))?(n=i[t],n=r&&!Array.isArray(n)?[]:r||c.isPlainObject(n)?n:{},r=!1,i[t]=c.extend(l,n,a)):void 0!==a&&(i[t]=a));return i},c.htmlPrefilter=function(e){return e},t.addHook("beforeSanitizeAttributes",function(e){"A"===e.tagName&&e.getAttribute("target")&&"_blank"===e.getAttribute("target")&&e.getAttribute("rel")&&e.relList.contains("noreferrer")&&e.setAttribute("data-wb-external-link","true")}),t.addHook("afterSanitizeAttributes",function(e){"A"===e.tagName&&e.getAttribute("data-wb-external-link")&&(e.setAttribute("target","_blank"),e.removeAttribute("data-wb-external-link"))});function r(e){return a.DataTable&&-1!==p.indexOf(e)?e:t.sanitize(e)}var n=c.parseHTML,i=c.fn.append,o=c.fn.prepend,s=c.fn.before,l=c.fn.after,d=c.fn.replaceWith,u=c.fn.init,p=["<tbody/>","<tr/>","<td />","<td/>"];c.parseHTML=function(e,t,a){return n(r(e),t,a)},c.domManip=null,c.append=c.fn.append=function(){var e=arguments,t=e[0];return"string"==typeof t&&(t=r(t),e[0]=t),i.apply(this,e)},c.prepend=c.fn.prepend=function(){var e=arguments,t=e[0];return"string"==typeof t&&(t=r(t),e[0]=t),o.apply(this,e)},c.before=c.fn.before=function(){var e=arguments,t=e[0];return"string"==typeof t&&(t=r(t),e[0]=t),s.apply(this,e)},c.after=c.fn.after=function(){var e=arguments,t=e[0];return"string"==typeof t&&(t=r(t),e[0]=t),l.apply(this,e)},c.replaceWith=c.fn.replaceWith=function(){var e=arguments,t=e[0];return"string"==typeof t&&(t=r(t),e[0]=t),d.apply(this,e)},c.fn.init=function(e,t,a){return"string"==typeof e&&(e=r(e)),new u(e,t,a)},c.html=function(e){return c.html(r(e))}}(jQuery,DOMPurify,window),window.Modernizr=function(r,d,u){function e(e){s.cssText=e}function p(e,t){return typeof e===t}function f(e,t){for(var a in e){a=e[a];if(!~(""+a).indexOf("-")&&s[a]!==u)return"pfx"!=t||a}return!1}function t(e,t,a){var r=e.charAt(0).toUpperCase()+e.slice(1),n=(e+" "+b.join(r+" ")+r).split(" ");if(p(t,"string")||void 0===t)return f(n,t);var i,o=(e+" "+y.join(r+" ")+r).split(" "),s=t,l=a;for(i in o){var c=s[o[i]];if(c!==u)return!1===l?o[i]:p(c,"function")?c.bind(l||s):c}return!1}function n(e,t,a,r){var n,i,o,s=d.createElement("div"),l=d.body,c=l||d.createElement("body");if(parseInt(a,10))for(;a--;)(i=d.createElement("div")).id=r?r[a]:h+(a+1),s.appendChild(i);return n=["&#173;",'<style id="s',h,'">',e,"</style>"].join(""),s.id=h,(l?s:c).innerHTML+=n,c.appendChild(s),l||(c.style.background="",c.style.overflow="hidden",o=g.style.overflow,g.style.overflow="hidden",g.appendChild(c)),n=t(s,e),l?s.parentNode.removeChild(s):(c.parentNode.removeChild(c),g.style.overflow=o),!!n}var a,i,o={},g=d.documentElement,h="modernizr",s=d.createElement(h).style,l=d.createElement("input"),c=" -webkit- -moz- -o- -ms- ".split(" "),m="Webkit Moz O ms",b=m.split(" "),y=m.toLowerCase().split(" "),w="http://www.w3.org/2000/svg",v={},x={},T={},A=[],k=A.slice,C={}.hasOwnProperty,E=void 0!==C&&void 0!==C.call?function(e,t){return C.call(e,t)}:function(e,t){return t in e&&void 0===e.constructor.prototype[t]};for(i in Function.prototype.bind||(Function.prototype.bind=function(a){var r=this;if("function"!=typeof r)throw new TypeError;var n=k.call(arguments,1),i=function(){var e,t;return this instanceof i?((e=function(){}).prototype=r.prototype,e=new e,t=r.apply(e,n.concat(k.call(arguments))),Object(t)===t?t:e):r.apply(a,n.concat(k.call(arguments)))};return i}),v.backgroundsize=function(){return t("backgroundSize")},v.borderimage=function(){return t("borderImage")},v.csstransitions=function(){return t("transition")},v.fontface=function(){var r;return n('@font-face {font-family:"font";src:url("https://")}',function(e,t){var a=d.getElementById("smodernizr"),a=a.sheet||a.styleSheet,a=a?a.cssRules&&a.cssRules[0]?a.cssRules[0].cssText:a.cssText||"":"";r=/src/i.test(a)&&0===a.indexOf(t.split(" ")[0])}),r},v.svg=function(){return!!d.createElementNS&&!!d.createElementNS(w,"svg").createSVGRect},v)E(v,i)&&(a=i.toLowerCase(),o[a]=v[i](),A.push((o[a]?"":"no-")+a));return o.input||(o.input=function(e){for(var t=0,a=e.length;t<a;t++)T[e[t]]=e[t]in l;return T.list&&(T.list=!!d.createElement("datalist")&&!!r.HTMLDataListElement),T}("autocomplete autofocus list placeholder max min multiple pattern required step".split(" ")),o.inputtypes=function(e){for(var t,a,r,n=0,i=e.length;n<i;n++)l.setAttribute("type",a=e[n]),(t="text"!==l.type)&&(l.value=":)",l.style.cssText="position:absolute;visibility:hidden;",/^range$/.test(a)&&l.style.WebkitAppearance!==u?(g.appendChild(l),t=(r=d.defaultView).getComputedStyle&&"textfield"!==r.getComputedStyle(l,null).WebkitAppearance&&0!==l.offsetHeight,g.removeChild(l)):/^(search|tel)$/.test(a)||(t=/^(url|email)$/.test(a)?l.checkValidity&&!1===l.checkValidity():":)"!=l.value)),x[e[n]]=!!t;return x}("search tel url email datetime date month week time datetime-local number range color".split(" "))),o.addTest=function(e,t){if("object"==typeof e)for(var a in e)E(e,a)&&o.addTest(a,e[a]);else{if(e=e.toLowerCase(),o[e]!==u)return o;t="function"==typeof t?t():t,g.className+=" "+(t?"":"no-")+e,o[e]=t}return o},e(""),l=null,o._version="2.8.3",o._prefixes=c,o._domPrefixes=y,o._cssomPrefixes=b,o.mq=function(e){var t,a=r.matchMedia||r.msMatchMedia;return a?a(e)&&a(e).matches||!1:(n("@media "+e+" { #"+h+" { position: absolute; } }",function(e){t="absolute"==(r.getComputedStyle?getComputedStyle(e,null):e.currentStyle).position}),t)},o.testProp=function(e){return f([e])},o.testAllProps=t,o.testStyles=n,g.className=g.className.replace(/(^|\s)no-js(\s|$)/,"$1$2")+(" js "+A.join(" ")),o}(this,this.document),function(e,f){function u(e){return"[object Function]"==n.call(e)}function g(e){return"string"==typeof e}function p(){}function h(e){return!e||"loaded"==e||"complete"==e||"uninitialized"==e}function m(){var e=w.shift();v=1,e?e.t?b(function(){("c"==e.t?E.injectCss:E.injectJs)(e.s,0,e.a,e.x,e.e,1)},0):(e(),m()):v=0}function t(e,t,a,r,n){return v=0,t=t||"j",g(e)?(o="c"==t?k:A,s=e,t=t,l=this.i++,a=a,r=r,n=(n=n)||E.errorTimeout,c=f.createElement(o),u=d=0,p={"t":t,"s":s,"e":a,"a":r,"x":n},1===C[s]&&(u=1,C[s]=[]),"object"==o?c.data=s:(c.src=s,c.type=o),c.width=c.height="0",c.onerror=c.onload=c.onreadystatechange=function(){i.call(this,u)},w.splice(l,0,p),"img"!=o&&(u||2===C[s]?(T.insertBefore(c,x?null:y),b(i,n)):C[s].push(c))):(w.splice(this.i++,0,e),1==w.length&&m()),this;function i(e){if(!d&&h(c.readyState)&&(p.r=d=1,v||m(),c.onload=c.onreadystatechange=null,e))for(var t in"img"!=o&&b(function(){T.removeChild(c)},50),C[s])C[s].hasOwnProperty(t)&&C[s][t].onload()}var o,s,l,c,d,u,p}function s(){var e=E;return e.loader={"load":t,"i":0},e}var a,r=f.documentElement,b=e.setTimeout,y=f.getElementsByTagName("script")[0],n={}.toString,w=[],v=0,i="MozAppearance"in r.style,x=i&&!!f.createRange().compareNode,T=x?r:y.parentNode,r=e.opera&&"[object Opera]"==n.call(e.opera),r=!!f.attachEvent&&!r,A=i?"object":r?"script":"img",k=r?"script":A,o=Array.isArray||function(e){return"[object Array]"==n.call(e)},l=[],C={},c={"timeout":function(e,t){return t.length&&(e.timeout=t[0]),e}},E=function(e){function d(e,t,a,r,n){var i=function(e){for(var t,a,e=e.split("!"),r=l.length,n=e.pop(),i=e.length,n={"url":n,"origUrl":n,"prefixes":e},o=0;o<i;o++)a=e[o].split("="),(t=c[a.shift()])&&(n=t(n,a));for(o=0;o<r;o++)n=l[o](n);return n}(e),o=i.autoCallback;i.url.split(".").pop().split("?").shift(),i.bypass||(t=t&&(u(t)?t:t[e]||t[r]||t[e.split("/").pop().split("?")[0]]),i.instead?i.instead(e,t,a,r,n):(C[i.url]?i.noexec=!0:C[i.url]=1,a.load(i.url,i.forceCSS||!i.forceJS&&"css"==i.url.split(".").pop().split("?").shift()?"c":void 0,i.noexec,i.attrs,i.timeout),(u(t)||u(o))&&a.load(function(){s(),t&&t(i.origUrl,n,r),o&&o(i.origUrl,n,r),C[i.url]=2})))}function t(e,t){function a(a,e){if(a){if(g(a))d(a,s=e?s:function(){var e=[].slice.call(arguments);l.apply(this,e),c()},t,0,i);else if(Object(a)===a)for(n in r=function(){var e,t=0;for(e in a)a.hasOwnProperty(e)&&t++;return t}(),a)a.hasOwnProperty(n)&&(e||--r||(u(s)?s=function(){var e=[].slice.call(arguments);l.apply(this,e),c()}:s[n]=function(t){return function(){var e=[].slice.call(arguments);t&&t.apply(this,e),c()}}(l[n])),d(a[n],s,t,n,i))}else e||c()}var r,n,i=!!e.test,o=e.load||e.both,s=e.callback||p,l=s,c=e.complete||p;a(i?e.yep:e.nope,!!o),o&&a(o)}var a,r,n=this.yepnope.loader;if(g(e))d(e,0,n,0);else if(o(e))for(a=0;a<e.length;a++)g(r=e[a])?d(r,0,n,0):o(r)?E(r):Object(r)===r&&t(r,n);else Object(e)===e&&t(e,n)};E.addPrefix=function(e,t){c[e]=t},E.addFilter=function(e){l.push(e)},E.errorTimeout=1e4,null==f.readyState&&f.addEventListener&&(f.readyState="loading",f.addEventListener("DOMContentLoaded",a=function(){f.removeEventListener("DOMContentLoaded",a,0),f.readyState="complete"},0)),e.yepnope=s(),e.yepnope.executeStack=m,e.yepnope.injectJs=function(e,t,a,r,n,i){var o,s,l=f.createElement("script"),r=r||E.errorTimeout;for(s in l.src=e,a)l.setAttribute(s,a[s]);t=i?m:t||p,l.onreadystatechange=l.onload=function(){!o&&h(l.readyState)&&(o=1,t(),l.onload=l.onreadystatechange=null)},b(function(){o||t(o=1)},r),n?l.onload():y.parentNode.insertBefore(l,y)},e.yepnope.injectCss=function(e,t,a,r,n,i){var o,t=i?m:t||p;for(o in(r=f.createElement("link")).href=e,r.rel="stylesheet",r.type="text/css",a)r.setAttribute(o,a[o]);n||(y.parentNode.insertBefore(r,y),b(t,0))}}(this,document),Modernizr.load=function(){yepnope.apply(window,[].slice.call(arguments,0))},Modernizr.addTest("details",function(){var e,t,a=document,r=a.createElement("details");return"open"in r&&(a=a.body||(t=a.documentElement,e=!0,t.insertBefore(a.createElement("body"),t.firstElementChild||t.firstChild)),r.innerHTML="<summary>a</summary>b",r.style.display="block",a.appendChild(r),t=r.offsetHeight,r.open=!0,t=t!=r.offsetHeight,a.removeChild(r),e&&a.parentNode.removeChild(a),t)}),Modernizr.addTest("progressbar",function(){return void 0!==document.createElement("progress").max}),Modernizr.addTest("meter",function(){return void 0!==document.createElement("meter").max}),Modernizr.addTest("mathml",function(){var e,t,a,r=!1;return document.createElementNS&&(e="http://www.w3.org/1998/Math/MathML",(t=document.createElement("div")).style.position="absolute",(a=t.appendChild(document.createElementNS(e,"math")).appendChild(document.createElementNS(e,"mfrac"))).appendChild(document.createElementNS(e,"mi")).appendChild(document.createTextNode("xx")),a.appendChild(document.createElementNS(e,"mi")).appendChild(document.createTextNode("yy")),document.body.appendChild(t),r=t.offsetHeight>t.offsetWidth),r}),Modernizr.addTest("cors",!!(window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest)),String.prototype.includes||(String.prototype.includes=function(e){return-1!==this.indexOf(e)}),String.prototype.replaceAll||(String.prototype.replaceAll=function(e,t){return"[object regexp]"===Object.prototype.toString.call(e).toLowerCase()?this.replace(e,t):this.replace(new RegExp(e,"g"),t)}),function(s,n,l){"use strict";function e(e){var t=l.createElement("a");return t.href=e,{"href":t.href,"absolute":t.href,"host":t.host,"hostname":t.hostname,"port":t.port,"pathname":t.pathname.replace(/^([^/])/,"/$1"),"protocol":t.protocol,"hash":t.hash,"search":t.search,"params":(e=t.search.replace(/(^\?)/,""))?e.split("&").map(function(e){return this[(e=e.split("="))[0]]=decodeURIComponent(e[1]),this}.bind({}))[0]:{}}}var t,i=0,a=s("script[src*='wet-boew.js'],script[src*='wet-boew.min.js'],script[data-wb-core]").last(),r=function(t){let a=l.documentElement.lang;if(2<a.length){let e=["pt-BR","zh-Hans"];t[0].hasAttribute("data-wb-core")&&t[0].hasAttribute("data-lang-long")&&(t=t.attr("data-lang-long").split(" "),e=t.concat(e)),-1===e.indexOf(a)&&(a=a.substring(0,2))}return a}(a),o=((t={}).home=(a=a).prop("src").split("?")[0].split("/").slice(0,-1).join("/"),t.asset=t.home+"/../assets",t.template=t.home+"/assets/templates",t.dep=t.home+"/deps",t.js=t.home,t.css=t.home.substring(0,t.home.length-2)+"css",t.mode=a.prop("src").indexOf(".min")<0?"":".min",a[0].hasAttribute("data-wb-core")&&s.extend(t,{"home":a.attr("data-home"),"asset":a.attr("data-asset"),"template":a.attr("data-template"),"dep":a.attr("data-dep"),"js":a.attr("data-js"),"css":a.attr("data-css"),"mode":a.attr("data-mode")}),t),a=function(){for(var e=3,t=l.createElement("div"),a=t.getElementsByTagName("i");t.innerHTML="\x3c!--[if gt IE "+(e+=1)+"]><i></i><![endif]--\x3e",a[0];);return 4<e?e:void 0}(),c=e(n.location.href),d=function(){var e="false";try{e=localStorage.getItem("wbdisable")||e}catch(e){}return"string"==typeof(e=c.params.wbdisable||e)?"true"===e.toLowerCase():Boolean(e)}(),u={"/":o.home,"/assets":o.asset,"/templates":o.template,"/deps":o.dep,"lang":r,"mode":o.mode,"doc":s(l),"win":s(n),"html":s("html"),"pageUrlParts":c,"getUrlParts":e,"isDisabled":d,"isStarted":!1,"isReady":!1,"ignoreHashChange":!1,"initQueue":0,"supportsDetails":function(){return"open"in l.createElement("details")},"getPath":function(e){return Object.prototype.hasOwnProperty.call(this,e)?this[e]:void 0},"getMode":function(){return this.mode},"getId":function(){var t,a="wb-auto-",r=[];return i||(l.querySelectorAll("[id^='"+a+"']").forEach(function(e){-1!==(t=e.id.substring(a.length)).search(/^\d+$/)&&r.push(t),console.error("wb.getId: ID '"+e.id+"' isn't supposed to be hardcoded in the page. Please remove it or change its prefix to something different than '"+a+"'.")}),i=r.length?Math.max.apply(null,r):i),a+(i+=1)},"init":function(e,t,a,r){var n=e.target,i=!!n,n=i?n:e,t=t+"-inited",o=n===l;if(!i||o||e.currentTarget===n&&-1===n.className.indexOf(t))return this.initQueue+=1,this.remove(a),o||(n.className+=" "+t,r)||n.id||(n.id=u.getId()),n},"ready":function(e,t,a){e?(e.find(u.allSelectors).addClass("wb-init").filter(":not(#"+e.attr("id")+" .wb-init .wb-init)").trigger("timerpoke.wb"),e.trigger("wb-ready."+t,a),--this.initQueue):this.doc.trigger("wb-ready."+t,a),!this.isReady&&this.isStarted&&this.initQueue<1&&(e=new Event("wet-boew-ready"),this.isReady=!0,this.doc.trigger("wb-ready.wb"),this.doc[0].dispatchEvent(e))},"other":!a,"desktop":void 0===n.orientation,"ie":!!a,"ie6":6===a,"ie7":7===a,"ie8":8===a,"ie9":9===a,"ielt7":a<7,"ielt8":a<8,"ielt9":a<9,"ielt10":a<10,"ie11":navigator.userAgent.includes("Trident/7."),"selectors":[],"resizeEvents":"xxsmallview.wb xsmallview.wb smallview.wb mediumview.wb largeview.wb xlargeview.wb","drawColours":["#8d201c","#EE8310","#2a7da6","#5a306b","#285228","#154055","#555555","#f6d200","#d73d38","#418541","#87aec9","#23447e","#999999"],"sessionGUID":function(){var e=sessionStorage.getItem("wb-session-GUID");return e||(e=u.guid(),sessionStorage.setItem("wb-session-GUID",e)),e},"add":function(e){var t,a=!1,r=u.selectors.length;if(u.isDisabled&&"#wb-tphp"!==e)return 0;for(t=0;t!==r;t+=1)if(u.selectors[t]===e){a=!0;break}a||u.selectors.push(e)},"remove":function(e){for(var t=this.selectors.length,a=0;a!==t;a+=1)if(this.selectors[a]===e){this.selectors.splice(a,1);break}},"timerpoke":function(e){var t,a,r,n,i=u.selectors.slice(0),o=i.length;if(e){for(r=s(),n=0;n!==o;n+=1)t=i[n],0!==(a=s(t)).length?r=r.add(a):u.remove(t);a=r.filter(":not(.wb-init .wb-init)").addClass("wb-init")}else a=s(i.join(", "));a.trigger("timerpoke.wb")},"start":function(){u.allSelectors=u.selectors.join(", "),u.timerpoke(!0),this.isStarted=!0,this.ready(),setInterval(u.timerpoke,500)},"i18nDict":{},"i18n":function(e,t,a){var r=u.i18nDict;switch(("string"==typeof e&&""!==e)|("string"==typeof t&&""!==t)<<1|("string"==typeof a&&""!==a)<<2){case 1:return r[e];case 3:return r[e][t];case 7:return r[e][t].replace("[MIXIN]",a);default:return""}},"hashString":function(e){var t,a=0;if(0!==e.length)for(t=0;t<e.length;t++)a=(a<<5)-a+e.charCodeAt(t),a&=a;return a},"stripWhitespace":function(e){return e.replace(/\s+/g,"")},"whenLibReady":function(e,t){e()?t():setTimeout(function(){u.whenLibReady(e,t)},50)}};n.wb=u,yepnope.addPrefix("site",function(e){return e.url=o.js+"/"+e.url,e}),yepnope.addPrefix("plyfll",function(e){var t,a=e.url;return d&&-1===a.indexOf("svg")?e.bypass=!0:o.mode||(a=a.replace(".min","")),t=-1!==a.indexOf(".css")?(e.forceCSS=!0,o.css):o.js,e.url=t+"/polyfills/"+a,e}),yepnope.addPrefix("i18n",function(e){return e.url=o.js+"/"+e.url+r+o.mode+".js",e}),yepnope.addPrefix("mthjx",function(e){return e.url=o.js+"/MathJax/"+e.url,e}),u.modernizrLoad=Modernizr.load,Modernizr.load=function(e){for(var t,a,r,n=(e=Array.isArray(e)?e:[e]).length,i=0;i!==n;i+=1)a=(t=e[i]).testReady,r=t.complete,a&&r&&(t.complete=u.whenLibReady(a,r));u.modernizrLoad(e)},Modernizr.load([{"test":Modernizr.details,"nope":["plyfll!details.min.js","plyfll!details.min.css"]},{"test":Modernizr.input.list,"nope":["plyfll!datalist.min.js","plyfll!datalist.min.css"]},{"test":Modernizr.inputtypes.date,"nope":["plyfll!datepicker.min.js","plyfll!datepicker.min.css"]},{"test":Modernizr.inputtypes.range,"nope":["plyfll!slider.min.js","plyfll!slider_wrapper.min.js","plyfll!slider.min.css"],"callback":function(e){"slider.min.js"===e&&n.fdSlider.onDomReady()}},{"test":Modernizr.progressbar,"nope":["plyfll!progress.min.js","plyfll!progress.min.css"]},{"test":Modernizr.mathml,"complete":function(){var t="wb-math",a="math",e=l.getElementsByTagName(a),r=u.doc;if(e.length&&l.body.removeChild(e[e.length-1].parentNode),!Modernizr.mathml){let e=new Boolean(n.navigator.msSaveOrOpenBlob);r.one("timerpoke.wb wb-init."+t,a,function(){u.init(l,t,a),n.MathJax={"options":{"enableMenu":!1}},e&&(Modernizr.load("timeout=500!https://cdnjs.cloudflare.com/polyfill/v3/polyfill.min.js?features=es6"),n.MathJax.chtml={"fontURL":"https://cdn.jsdelivr.net/npm/mathjax@3/es5/output/chtml/fonts/woff-v2"}),Modernizr.load([{"load":["timeout=500!https://cdn.jsdelivr.net/npm/mathjax@3/es5/mml-chtml.js","plyfll!mathml.min.css"],"complete":function(){setTimeout(function(){e&&!n.MathJax.startup&&(n.MathJax.chtml.fontURL=o.js+"/MathJax/output/chtml/fonts/woff-v2"),Modernizr.load([{"test":n.MathJax.startup,"nope":"mthjx!mml-chtml.js","complete":function(){Modernizr.load([{"test":n.MathJax.startup,"nope":"mthjx!MathJax.js?config=Accessible","complete":function(){u.ready(r,t)}}])}}])},100)}}])}),u.add(a)}}},{"test":Modernizr.meter,"nope":["plyfll!meter.min.js","plyfll!meter.min.css"]},{"test":Modernizr.touch,"yep":"plyfll!mobile.min.js"},{"test":Modernizr.svg,"nope":"plyfll!svg.min.js"},{"load":"i18n!i18n/","testReady":function(){return u.i18nDict.tphp},"complete":function(){u.start()}}])}(jQuery,window,document),function(o,e){e.getData=function(t,a){var e,t=t.jquery?t[0]:t,r=t.getAttribute("data-"+a);if(r)try{e=JSON.parse(r),o.data(t,a,e)}catch(e){console.info(t),o.error("Bad JSON array in data-"+a+" attribute")}return e},e.download=function(e,t,a){var r=URL.createObjectURL(e),n=document.createElement("a");t=t||"unnamed",n.textContent=a||"",n.download=t,n.hidden=!0,document.body.appendChild(n),window.navigator.msSaveOrOpenBlob?(n.addEventListener("click",function(){window.navigator.msSaveOrOpenBlob(e,t)}),n.setAttribute("target","_blank")):n.href=r,n.click(),setTimeout(function(){document.body.removeChild(n)},1),setTimeout(function(){"string"==typeof r?URL.revokeObjectURL(r):r.remove()},4e4)},e.shuffleDOM=function(e){for(var a=e.get(),t=o.map(a,function(){var e=Math.floor(Math.random()*a.length),t=o(a[e]).clone(!0)[0];return a.splice(e,1),t}),r=e.length,n=0;n<r;n++)o(e[n]).replaceWith(o(t[n]));return o(t)},e.pickElements=function(t,e){var a,r,n,i=t.size();if(i<(e=e||1))return t.pushStack(t);if(1===e)return t.filter(":eq("+Math.floor(Math.random()*i)+")");for(a=t.get(),r=0;r<i-1;r++)n=Math.floor(Math.random()*(i-r))+r,a[n]=a.splice(r,1,a[n])[0];return a=a.slice(0,e),t.filter(function(e){return-1<o.inArray(t.get(e),a)})},e.addSkipLink=function(e,t,a,r){var n,i=document.getElementById("wb-tphp"),o=document.createElement("li"),s=document.createElement(a?"button":"a");for(n in o.className="wb-slc",s.className="wb-sl",t)s.setAttribute(n,t[n]);return s.appendChild(document.createTextNode(e)),o.appendChild(s),r?i.appendChild(o):i.insertBefore(o,i.childNodes[2]),!0}}(jQuery,wb),function(d,i){"use strict";d.jqEscape=function(e){return e.replace(/([;&,\.\+\*\~':"\\\!\^\/#$%@\[\]\(\)=>\|])/g,"\\$1")},d.formattedNumCompareRegEx=/(<[^>]*>|[^\d.])/g,d.formattedNumCompare=function(e,t){for(var a,r=d.formattedNumCompareRegEx,n=-1===e.indexOf("-")?1:-1,i=("-"===e||""===e?"0":e.replace(r,"")).split("."),o=-1===t.indexOf("-")?1:-1,s=("-"===t||""===t?"0":t.replace(r,"")).split("."),l=i.length,c=0;c!==l&&0==(a=parseInt(i[c],10)*n-parseInt(s[c],10)*o);c+=1);return a},d.i18nTextCompare=function(e,t){return d.normalizeDiacritics(e).localeCompare(d.normalizeDiacritics(t))},d.normalizeDiacritics=function(e){for(var t,a={"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Œ":"OE","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","ẞ":"SS","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","œ":"oe","ɶ":"oe","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ſ":"s","ẛ":"s","ß":"ss","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","０":"0","₀":"0","⓪":"0","⁰":"0","¹":"1","⑴":"1","₁":"1","❶":"1","⓵":"1","⒈":"1","①":"1","１":"1","²":"2","❷":"2","⑵":"2","２":"2","₂":"2","⓶":"2","②":"2","⒉":"2","³":"3","３":"3","⒊":"3","⑶":"3","₃":"3","❸":"3","⓷":"3","③":"3","⓸":"4","④":"4","⒋":"4","４":"4","⁴":"4","₄":"4","❹":"4","⑷":"4","⒌":"5","₅":"5","⓹":"5","⑸":"5","❺":"5","⑤":"5","５":"5","⁵":"5","⑹":"6","⁶":"6","６":"6","❻":"6","₆":"6","⑥":"6","⓺":"6","⒍":"6","７":"7","⁷":"7","❼":"7","⓻":"7","⒎":"7","₇":"7","⑺":"7","⑦":"7","⑧":"8","⒏":"8","⓼":"8","⑻":"8","⁸":"8","８":"8","❽":"8","₈":"8","⓽":"9","９":"9","⒐":"9","❾":"9","⑼":"9","₉":"9","⑨":"9","⁹":"9"},r=e.split(""),n=r.length,i=!1,o=0;o!==n;o+=1)t=r[o],Object.prototype.hasOwnProperty.call(a,t)&&(r[o]=a[t],i=!0);return i?r.join(""):e},d.string={"pad":function(e,t){for(var a=e+"",r=t-a.length,n=0;n!==r;n+=1)a="0"+a;return a},"base64ToArrayBuffer":function(e){for(var t=i.atob(e),a=t.length,r=new Uint8Array(a),n=0;n<a;n++)r[n]=t.charCodeAt(n);return r.buffer},"arrayBufferToBase64":function(e){for(var t="",a=new Uint8Array(e),r=a.byteLength,n=0;n<r;n++)t+=String.fromCharCode(a[n]);return i.btoa(t)},"fromHexString":function(e){return null===e?null:Uint8Array.from(e.match(/.{1,2}/g).map(function(e){return parseInt(e,16)}))},"toHexString":function(e){return e.reduce(function(e,t){return e+t.toString(16).padStart(2,"0")},"")}},d.date={"convert":function(e){var t=e.constructor;switch(t){case Date:return t;case Array:return new Date(e[0],e[1],e[2]);case Number:case String:return new Date(e);default:return"object"==typeof e?new Date(e.year,e.month,e.date):NaN}},"compare":function(e,t){var a=d.date.convert;return isFinite(e=a(e).valueOf())&&isFinite(t=a(t).valueOf())?(t<e)-(e<t):NaN},"toDateISO":function(e,t){var e=d.date.convert(e),a=d.string.pad;return e.getFullYear()+"-"+a(e.getMonth()+1,2,"0")+"-"+a(e.getDate(),2,"0")+(t?" "+a(e.getHours(),2,"0")+":"+a(e.getMinutes(),2,"0"):"")},"fromDateISO":function(e){var t=null;return t=e&&/\d{4}-\d{2}-\d{2}/.test(e)?new Date(e.substr(0,4),e.substr(5,2)-1,e.substr(8,2),0,0,0,0):t}},d.guid=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})},d.escapeAttribute=function(e){return e.replace(/'/g,"&#39;").replace(/"/g,"&#34;")},d.escapeHTML=function(e){return d.escapeAttribute(e.replace(/&/g,"&#38;").replace(/</g,"&#60;").replace(/>/g,"&#62;"))},d.decodeUTF8Base64=function(e){return decodeURIComponent(escape(atob(e)))},d.findPotentialPII=function(e,t,a){if(e&&"string"!=typeof e)return!1;var r,n,i={"digits":/\d(?:[\s\-\\.\\/]?\d){8,}(?!\d)/gi,"phone":/\+?(\d{1,3})?[-._\s]?(\(?\d{3}\)?)[-._\s]?(\d{3})[-._\s]?(\d{4})/gi,"passport":/\b[A-Za-z]{2}[\s\\.-]*?\d{6}\b/gi,"email":/\b(?:[a-zA-Z0-9_\-\\.]+)(?:@|%40|%2540)(?:[a-zA-Z0-9_\-\\.]+)\.(?:[a-zA-Z]{2,5})\b/gi,"looseEmail":/([a-zA-Z0-9_\-.]+)\s*@([\sa-zA-Z0-9_\-.]+)[.,]([a-zA-Z]{1,5})/g,"looseEmail2":/([a-zA-Z0-9._%+-]+)\s?@\s?(gmail|outlook|icloud|hotmail|yahoo)(\s?\.?\s?(com|ca))?/gi,"postalCode":/\b[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d\b/gi,"username":/(?:(username|user)[%20]?([:=]|(%EF%BC%9A))[^\s&]*)/gi,"password":/(?:(password|pass)[%20]?([:=]|(%EF%BC%9A))[^\s&]*)/gi},o=!1,s=a&&a.replaceWith?a.replaceWith:"",l="object"==typeof t||t,c=(a=a||{}).useFullBlock||!1,d="object"==typeof t?{}:i,a=$.extend({},{"isCustomExclusive":!1,"useFullBlock":!1,"replaceWith":""},a);if(0===Object.keys(d).length)if(a.isCustomExclusive)for(var u in t)t[u]instanceof RegExp&&(d[u]=t[u]);else if(1===Object.keys(t).length&&Object.values(t)[0]instanceof RegExp)(d=i)[Object.keys(t)[0]]=Object.values(t)[0];else for(var p in t)Object.prototype.hasOwnProperty.call(i,p)?d[p]=i[p]:t[p]instanceof RegExp&&(d[p]=t[p]);for(n in d)(r=e.match(d[n]))&&(o=!0,l)&&(s=c?"█".repeat(r[0].length):s,e=e.replaceAll(d[n],s));return l&&o?e:o}}(wb,window),function(a){"use strict";var t={"default":"wet-boew"},r={"init":function(e){return a.extend(t,e||{})},"show":function(t){a(this).each(function(){var e=a(this);e.attr("aria-hidden","false"),void 0===t&&e.removeClass("wb-inv")})},"hide":function(t){a(this).each(function(){var e=a(this);if(e.attr("aria-hidden","true"),void 0===t)return e.addClass("wb-inv")})},"toggle":function(e,t){a(this).addClass(e).removeClass(t)}};a.fn.wb=function(e){r[e]?r[e].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof e&&e?a.error("Method "+e+" does not exist on jquery.wb"):r.init.apply(this,arguments)}}(jQuery),function(s){"use strict";function r(e,t,a){var r,n,i,o=e.nodeName.toLowerCase();return"area"===o?(n=(r=e.parentNode).name,!(!e.href||!n||"map"!==r.nodeName.toLowerCase()||!(i=s("img[usemap=#"+n+"]")[0]))&&l(i)):a?(/input|select|textarea|button|object|summary/.test(o)?!e.disabled:"a"===o&&e.href||t)&&l(e):/input|select|textarea|button|object|summary/.test(o)?!e.disabled:"a"===o&&e.href||t}function l(e){return s.expr.filters.visible(e)&&!s(e).parents().addBack().filter(function(){return"hidden"===s.css(this,"visibility")}).length}s.extend(s.expr.pseudos,{"data":function(e,t,a){return!!s.data(e,a[3])},"focusable":function(e){return r(e,!isNaN(s.attr(e,"tabindex")),!0)},"discoverable":function(e){return r(e,!isNaN(s.attr(e,"tabindex")))},"tabbable":function(e){var t=s.attr(e,"tabindex"),a=isNaN(t);return(a||0<=t)&&r(e,!a)}})}(jQuery),function(d,u){"use strict";function p(e){return e=e.is("[datetime]")?e.attr("datetime"):e.text(),new Date(e).toISOString().replace(/\..*[0-9]/g,"").replace(/-|:|\./g,"")}var f="wb-addcal",g=".provisional."+f,e=u.doc;e.on("click",".download-ics",function(e){e=d(e.currentTarget).parentsUntil("."+f).parent()[0],e=d(e).attr("data-ics-file");u.download(new Blob([e],{"type":"text/calendar;charset=utf-8"}),"evenement-gc-event.ics")}),e.on("timerpoke.wb wb-init.wb-addcal",g,function(e){var e=u.init(e,f,g),t=d(e);if(e){u.ready(d(e),f);var a,r,n,i,o,s=e.querySelectorAll("[property]"),l=new Object,c=[];for(o={"addto":(o={"en":{"addcal-addto":"Add to","addcal-calendar":"calendar","addcal-other":"Other (Outlook, Apple, etc.)"},"fr":{"addcal-addto":"Ajouter au","addcal-calendar":"calendrier","addcal-other":"Autre (Outlook, Apple, etc.)"}}[d("html").attr("lang")||"en"])["addcal-addto"],"calendar":o["addcal-calendar"],"ical":o["addcal-other"]},l.dtStamp=p(d("time[property='dateModified']")),r=s.length,a=0;a<r;a++)switch((n=s[a]).getAttribute("property")){case"name":d(n).parentsUntil("."+f,"[typeof=Place]").length?l.placeName=n.textContent:l.name=n.textContent;break;case"description":l.description=n.textContent.replace(/(\r\n|\n|\r)/gm," ");break;case"startDate":l.sDate=p(d("time[property='startDate']",t));break;case"endDate":l.eDate=p(d("time[property='endDate']",t));break;case"location":n.getAttribute("typeof")&&("VirtualLocation"!==n.getAttribute("typeof")||d(n).find("[property=url]").length)||(l.placeName=n.textContent);break;case"streetAddress":l.placeAddress=n.textContent;break;case"addressLocality":l.placeLocality=n.textContent;break;case"addressRegion":l.placeRegion=n.textContent;break;case"postalCode":l.placePostalCode=n.textContent;break;case"url":d(n).parentsUntil("."+f,"[property=location]").length&&(l.placeName=n.textContent)}if(c.push(l.placeName||"",l.placeAddress||"",l.placeLocality||"",l.placeRegion||"",l.placePostalCode||""),!l.name)throw f+": Event title is missing.";if(!l.sDate)throw f+": Start date is missing.";if(!l.eDate)throw f+": End date is missing.";l.uid=window.location.href.replace(/\.|-|\/|:|[G-Zg-z]/g,"").toUpperCase().substr(-10)+"-"+l.sDate+"-"+l.dtStamp,i=encodeURI("https://www.google.com/calendar/render?action=TEMPLATE&text="+l.name+"&details="+l.description+"&dates="+l.sDate+"/"+l.eDate+"&location="+c.join(" ")),c="BEGIN:VCALENDAR\nVERSION:2.0\nPRODID:-//WET-BOEW//Add to Calendar v4.0//\nBEGIN:VEVENT\nDTSTAMP:"+l.dtStamp+"\nSUMMARY:"+l.name+"\nDESCRIPTION:"+l.description+"\nUID:"+l.uid+"\nDTSTART:"+l.sDate+"\nDTEND:"+l.eDate+"\nLOCATION:"+c.join(" ")+"\nEND:VEVENT\nEND:VCALENDAR",e.dataset.icsFile=c,t.append("<details class='max-content "+f+"-buttons'><summary>"+o.addto+" "+o.calendar+"</summary><ul class='list-unstyled mrgn-bttm-0'><li><a class='btn btn-link' href='"+i.replace(/'/g,"%27")+"' target='_blank' rel='noreferrer noopener'>Google<span class='wb-inv'>"+o.calendar+"</span></a></li><li><button class='btn btn-link download-ics'>"+o.ical+"<span class='wb-inv'>Calendar</span></button></li></ul></details>")}u.ready(d(e),f)}),u.add(g)}(jQuery,wb),function(p,f,g){"use strict";f.doc.on("ajax-fetch.wb",function(e){var n,t=e.element||e.target,a=e.fetch,r=a.url.split(" "),i=r[0],o=i.split("#"),s=o[1],l=r[1]||!!s&&"#"+s,c={},d=a.nocache,u=a.nocachekey||f.cacheBustKey||"wbCacheBust";l&&(a.url=r[0],r[1])&&(l=r.slice(1).join(" ")),d&&(r=u+"="+(r="nocache"===d?f.guid():f.sessionGUID()),i=-1!==(u=o[0]).indexOf("?")?u+"&"+r+(s?"#"+s:""):u+"?"+r+(s?"#"+s:""),a.url=i),t!==e.target&&e.currentTarget!==e.target||(t.id||(t.id=f.getId()),n=t.id,a.dataType&&"jsonp"===a.dataType&&(a.dataType="json"),a.jsonp&&(a.jsonp=!1),p.ajax(a).done(function(t,e,a){var r=typeof t;if(l&&(t=p("<div>"+t+"</div>").find(l)),c.pointer=p("<div id='"+f.getId()+"' data-type='"+r+"'></div>").append("string"==r?t:""),a.responseJSON)t=a.responseText;else try{t=p(t)}catch(e){t=g.sanitize(a.responseText)}c.response=t,c.hasSelector=!!l,c.status=e,c.xhr=a,p("#"+n).trigger({"type":"ajax-fetched.wb","fetch":c},this)}).fail(function(e,t,a){p("#"+n).trigger({"type":"ajax-failed.wb","fetch":{"xhr":e,"status":t,"error":a}},this)},this))})}(jQuery,wb,DOMPurify),function(l,c){"use strict";function d(){for(var e,t,a,r,n,i,o=window.innerWidth,s={},l=h.length,c=0;c<l;c++){for(t=1/0,r=(a=g[h[c]]).length,e=0;e<r;e++)(n=a[e])[1]>=o&&t>n[1]&&(t=n[1],s[h[c]]=n[0]);t===1/0&&(s[h[c]]=a[r-1][0])}for(i in s)document.getElementById(i).style.backgroundImage="https://wet-boew.github.io/vocab/wb/utilities#no-image"===s[i]?"none":"url("+s[i]+")"}var e=c.doc,u=c.win,p="wb-bgimg",f="[data-bgimg-srcset], [data-bgimg]",g={},h=[];e.on("timerpoke.wb wb-init."+p,f,function(e){var t,a,r,n,i,o,s,e=c.init(e,p,f);if(e){if(e.id||(e.id=c.getId()),t=e.id,(a=e.dataset.bgimg)&&(e.style.backgroundImage="url("+a+")"),e.dataset.bgimgSrcset){for(h.push(e.id),i=(r=e.dataset.bgimgSrcset.split(",")).length,g[t]=[],n=0;n<i;n++)o=(s=r[n].trim().split(" "))[0],s=s[s.length-1],s=parseInt(s.substring(0,s.length-1)),g[t].push([o,s]);g[t].sort(function(e,t){return e[1]>t[1]?1:-1}),d(),u.on("resize",d)}c.ready(l(e),p)}}),c.add(f)}(jQuery,wb),function(w,u,v){"use strict";function o(e){for(var t=w(e),a=t.data("calevt").split(/\s+/),r=w.Deferred(),n=a.length,i=[],o=function(e){t.append(w.trim(e))},s=0;s<n;s+=1)i.push(w.get(a[s],o,"html"));return w.when.apply(w,i).always(function(){r.resolve()}),r.promise()}function s(e){var t,a,r,n,i=w.extend({},u[p],e.data(g)),o=new Date,s=o.getTime(),l=h(e),c=e.data("calevtSrc"),c=w("#"+c).addClass(f),d=i.year,i=i.month;e.data("calevtMinDate")&&(t=b(e.data("calevtMinDate"))),e.data("calevtMaxDate")&&(a=b(e.data("calevtMaxDate"))),(!t||l.minDate<t)&&(t=l.minDate),(!a||l.maxDate>a)&&(a=l.maxDate),r=t.getTime(),n=a.getTime(),!d&&r<s&&s<n?d=o.getFullYear():!d&&s<r?d=t.getFullYear():!d&&n<s&&(d=a.getFullYear()),!i&&r<s&&o.getTime()<n?i=o.getMonth():!i&&s<r?i=t.getMonth():!i&&n<s&&(i=a.getMonth()),v.calendar.create(c,{"year":d,"month":i,"minDate":t,"maxDate":a,"daysCallback":m,"events":l.list,"$events":e})}function a(){var e=w(this).closest("td");setTimeout(function(){0===e.find("a:focus").length&&e.find("ul").removeClass(r).find("a").attr("tabindex","-1")},5)}var p="wb-calevt",l="."+p,f=p+"-cal",e="."+f,r="ev-details",n="focus",g=p,t=v.doc,h=function(e){for(var t,a,r,n,i,o,s,l,c,d,u,p,f=!w(e).hasClass("evt-anchor"),g={"minDate":null,"maxDate":null,"iCount":0,"list":[{"a":1}]},h=e.find("ul, ol").first().find("> li:not(.wb-fltr-out)"),m=h.length,b=/datetime\s+\{date:\s*(\d+-\d+-\d+)\}/,y=0;y!==m;y+=1)if(d=(p=h.eq(y))[0],l=(t=p.find("*:header:first")).attr("class"),a=t.text(),r=(p=p.find("a")[0]).getAttribute("href"),n=p.getAttribute("target"),s=1,f||(p=d.id||v.getId(),r="#"+(d.id=p)),(i=new Date).setHours(0,0,0,0),0!==(p=d.getElementsByTagName("time")).length){for((u=("time"===(d=p[0]).nodeName.toLowerCase()?d.getAttribute("datetime"):d.className.match(b)[1]).substr(0,10).split("-"))[1]=u[1]-1,i.setFullYear(u[0],u[1],u[2]),1!==p.length&&((p=("time"===(d=p[1]).nodeName.toLowerCase()?d.getAttribute("datetime"):d.className.match(b)[1]).substr(0,10).split("-"))[1]=p[1]-1,s+=(d=u,u=p,p=void 0,d=v.date.convert(d),u=v.date.convert(u),p=0,d.setHours(0),d.setMinutes(0),d.setSeconds(0),u.setHours(0),u.setMinutes(0),u.setSeconds(0),p=d<u?6e4*(u.getTimezoneOffset()-d.getTimezoneOffset()):6e4*(d.getTimezoneOffset()-u.getTimezoneOffset()),u=Math.abs(u.getTime()-d.getTime())-p,Math.ceil(u/864e5))),o=0;o!==s;o+=1)0!==o&&(i=new Date(i.setDate(i.getDate()+1))),(null===g.minDate||i<g.minDate)&&(g.minDate=i),(null===g.maxDate||g.maxDate<i)&&(g.maxDate=i),g.list[g.iCount]={"title":a,"date":new Date(i.getTime()),"href":r,"target":n},c="filter-"+i.getFullYear()+"-"+v.string.pad(i.getMonth()+1,2),l?-1===l.indexOf(c)&&(l+=" "+c):l=c,g.iCount+=1;t.attr("class",l)}return g.list.sort(function(e,t){return e.date-t.date}),g},m=function(e,t,a){for(var r,n,i,o,s=this.events,l=0,c=s.length;l!==c;l+=1)if((r=(o=s[l]).date).getFullYear()===e){if(t<r.getMonth())break;r.getMonth()===t&&(r=r.getDate()-1,1!==(i=("A"!==(n=w(a[r])).parent().get(0).nodeName?n:n.parent()).next()).length&&(i=w("<ul></ul>").insertAfter(n),r&&"A"===a[r-1].parentNode.nodeName?n.wrap("<a class='cal-evt' tabindex='-1'></a>"):n.wrap("<a class='cal-evt'></a>"),n.parent().attr("href","javascript:;")),i.append("<li><a tabindex='-1' class='cal-evt-lnk' href='"+o.href+"'>"+o.title+"</a></li>"))}},b=function(e){var t=new Date,e=e.split("-");return e[1]=e[1]-1,t.setFullYear(e[0],e[1],e[2]),t};t.on("timerpoke.wb "+("wb-init"+l)+" wb-redraw"+l,l,function(e){var t,a,r=e.type,n=w("#"+e.target.id),i=e.currentTarget.dataset.calevtSrc;switch(r){case"timerpoke":case"wb-init":t=e,(t=v.init(t,p,l))&&(a=w(t),w.when.apply(w,w.map(a.find("[data-calevt]"),o)).always(function(){s(a),v.ready(a,p)}));break;case"wb-redraw":w("#"+i+" .wb-clndr").remove(),s(n),n.trigger("wb-updated"+l)}}),t.on("wb-navigate.wb-clndr",e,function(e){var t=e.target.lib;t&&(t=t.$events)&&(!function(e,t){this.find("li.cal-disp-onshow").addClass("hidden").has(":header[class*=filter-"+e+"-"+v.string.pad(parseInt(t,10)+1,2)+"]").removeClass("hidden")}.call(t,e.year,e.month),t.trigger("wb-updated"+l))}),t.on("focusin focusout keydown",e+" .cal-evt",function(e){var t;switch(e.type){case"focusin":!function(){w(this).next().addClass(r)}.call(e.target);break;case"focusout":a.call(e.target);break;case"keydown":t=w(e.target),13!==e.which&&32!==e.which||!t.hasClass("cal-evt")||w(e.target).next().find("a:first").trigger(n)}}),t.on("keydown",e+" td > ul li",function(e){var t,a=w(e.currentTarget);switch(e.which){case 38:(t=0===(t=a.prev().find("a")).length?a.siblings(":last").find("a"):t).trigger(n);break;case 40:(t=0===(t=a.next().find("a")).length?a.siblings(":first").find("a"):t).trigger(n);break;case 27:a.closest("td").children("a").trigger(n)}}),t.on("focusout",e+" td > ul",function(e){a.call(e.target)}),v.add(l)}(jQuery,window,wb),function(C,E,s,f){function l(e){var t,a,r,n="",i=this.$o;for((e=e||{}).year!==f&&e.month!==f?(r={"minDate":new Date(e.year,0,1),"maxDate":new Date(e.year,11,31)},C.extend(this,r,e)):C.extend(this,o),t=this.maxDate.getFullYear(),r=i.find(".cal-year").empty(),a=this.minDate.getFullYear();a<=t;a+=1)n+="<option value='"+a+"'>"+a+"</option>";r.append(n),i.trigger({"type":h,"year":this.year,"month":this.month,"initEvent":!0})}function g(e,t,a){var r,n,i,o,s,l,c,d,u,p,f,g,h,m=C(e).find(".cal-days"),b=m.get(0),y=1,w=j.currDay,v=(T=e.lib).minDate,x=T.maxDate,T=T.daysCallback,A=new Date(t,a,1),k=A.getDay();for(A.setMonth(a+1,0),i=A.getDate(),o=(A=new Date).getFullYear(),s=A.getMonth(),l=A.getDate(),m.empty(),c=1;c<7;c+=1){for(r=b.insertRow(),d=0;d<7;d+=1)1===c&&d<k||i<y?((n=r.insertCell()).classList.add("cal-empty"),n.textContent=" "):(u="cal-index-"+y+((f=y===l&&a===s&&t===o)?" cal-curr-day ":""),A.setFullYear(t,a,y),p=A.toLocalISOString().substr(0,10),f=function(t){var a=j.dayNames,r=j.monthNames;return j.format.replace(/\{ddd\}|\{d\}|\{M\}|\{Y\}/g,function(e){switch(e){case"{ddd}":return a[parseInt(t.getDay(),10)];case"{d}":return parseInt(t.getDate(),10);case"{M}":return r[parseInt(t.getMonth(),10)];case"{Y}":return t.getFullYear()}})}(A)+(f?"<span class='wb-inv'>"+w+"</span>":""),(n=r.insertCell()).setAttribute("class",u),n.innerHTML=E.sanitize("<time datetime='"+p+"'>"+f+"</time>"),i<=y&&(g=!0),y+=1);if(g)break}T&&(m=m.find("time"),h={},t===v.getFullYear()&&a===v.getMonth()&&(h.min=v.getDate()-1),t===x.getFullYear()&&a===x.getMonth()&&(h.max=x.getDate()-1),T.call(e.lib,t,a,m,h))}var j,c,e=s.doc,r=".wb-clndr",h="wb-navigate"+r,d=!1,t=new Date,a=t.getFullYear(),o={"year":a,"month":t.getMonth(),"minDate":new Date(a,0,1),"maxDate":new Date(a,11,31)};function n(e){return e<10?"0"+e:e}s.calendar={"create":function(e,t){var a,r,n,i,o={"reInit":l};return d||(i=s.i18n,j={"monthNames":i("mnths"),"prevMonth":i("prvMnth"),"nextMonth":i("nxtMnth"),"goToYear":i("cal-goToYr"),"goToMonth":i("cal-goToMnth"),"dayNames":i("days"),"currDay":i("currDay"),"format":i("cal-format"),"calendar":i("cal")},r=j.dayNames,n=j.monthNames,c=C("<div class='wb-clndr' role='application' aria-label='"+j.calendar+"'><div class='cal-nav'><span class='wb-inv current-month' aria-live='polite'></span><button type='button' class='btn pull-left cal-month-prev'><span class='glyphicon glyphicon-arrow-left' aria-hidden='true'></span><span class='wb-inv'>"+j.prevMonth+"<span></span></span></button><button type='button' class='btn pull-right cal-month-next'><span class='glyphicon glyphicon-arrow-right' aria-hidden='true'></span><span class='wb-inv'>"+j.nextMonth+"<span></span></span></button><div class='form-group'><label><span class='wb-inv'>"+j.goToYear+"</span><select class='cal-year'></select></label>\n<label><span class='wb-inv'>"+j.goToMonth+"</span><select class='cal-month'>"+function(){for(var e="",t=0;t<12;t+=1)e+="<option value='"+t+"'>"+n[t]+"</option>";return e}()+"</select></label></div></div><table><thead><tr>"+function(){for(var e="",t=0;t<7;t+=1)e+="<th><abbr title='"+r[t]+"'>"+r[t].substr(0,1)+"</abbr></th>";return e}()+"</tr></thead><tbody class='cal-days'></tbody></table></div>"),d=!0),a=(i=c.clone()).get(0),o.$o=i,(o.o=a).lib=o,i.appendTo(e),o.reInit(t),o}},e.on(h,r,function(e){var t=e.currentTarget,a=C(t),r=e.year,n=e.month,t=t.lib,i=t.maxDate.getFullYear(),o=t.maxDate.getMonth(),s=t.minDate.getFullYear(),l=t.minDate.getMonth(),c=a.find(".cal-month-prev"),d=a.find(".cal-month-next"),u=a.find(".cal-month"),p="disabled";r!==f&&(t.year=r),n!==f&&(t.month=n),a.find(".cal-year").val(r),u.val(n),u.children(":"+p).removeAttr(p),r<s||r===s&&n<=l?c.attr(p,p):c.removeAttr(p),i<r||r===i&&o<=n?d.attr(p,p):d.removeAttr(p),r===s&&u.children(":lt("+l+")").attr(p,p),r===i&&u.children(":gt("+o+")").attr(p,p),g(e.currentTarget,e.year,e.month),e.initEvent||a.find(".current-month").text(j.monthNames[n]+" "+r)}),e.on("change",r,function(e){var t,a,r=e.target,n=e.currentTarget;switch(r.className){case"cal-year":t=parseInt(r.value,10),a=n.lib.month;break;case"cal-month":t=n.lib.year,a=parseInt(r.value,10)}C(n).trigger({"type":h,"year":t,"month":a})}),e.on("click",".cal-month-prev, .cal-month-next",function(e){var t=C(e.currentTarget).closest(r),a=t.get(0),e=-1!==e.currentTarget.className.indexOf("cal-month-prev")?-1:1,a=new Date(a.lib.year,a.lib.month+e,1);t.trigger({"type":h,"year":a.getFullYear(),"month":a.getMonth()}),s.ie11&&t.trigger("focusin")}),e.on("keydown",r,function(e){var t,a,r,n,i=e.currentTarget,o=C(e.currentTarget).find(".cal-days"),s=e.target,l=e.which,c=i.lib,d=new Date(c.year,c.month,1),u=c.minDate,c=c.maxDate,p=new Date(d),f=!0;if(!e.altKey&&!e.metaKey&&32<l&&l<41){switch(t=null!==(s=s.parentNode.className.match(/cal-index-(\d{1,2})/)),l){case 33:d.setDate(u.getDate());case 34:r=33===l?-1:1,e.ctrlKey||e.shiftKey||e.altKey?d.setYear(d.getFullYear()+r):d.setMonth(d.getMonth()+r)}if(t)switch(n=parseInt(s[1],10),d.setMonth(d.getMonth()+1,0),a=d.getDate(),d.setDate(a<n?a:n),l){case 35:d.setDate(a);break;case 36:d.setDate(1);break;case 37:d.setDate(n-1);break;case 38:d.setDate(n-7);break;case 39:d.setDate(n+1);break;case 40:d.setDate(n+7)}return(d<u||c<d)&&(35===l?d.setDate(c.getDate()):36===l?d=u:f=!1),!f||d.getMonth()===p.getMonth()&&d.getFullYear()===p.getFullYear()||C(i).trigger({"type":h,"year":d.getFullYear(),"month":d.getMonth()}),t&&o.find(".cal-index-"+d.getDate()+" a:first").focus(),e.preventDefault(),!1}}),Date.prototype.toLocalISOString=function(){var e=this.getTimezoneOffset();return 0===e?this.toISOString():this.getFullYear()+"-"+n(this.getMonth()+1)+"-"+n(this.getDate())+"T"+n(this.getHours())+":"+n(this.getMinutes())+":"+n(this.getSeconds())+"."+(this.getMilliseconds()/1e3).toFixed(3).slice(2,5)+(e<0?"+":"-")+n(Math.floor(Math.abs(e/60)))+":"+n(e%60)},Date.prototype.toISOString||(Date.prototype.toISOString=function(){return this.getUTCFullYear()+"-"+n(this.getUTCMonth()+1)+"-"+n(this.getUTCDate())+"T"+n(this.getUTCHours())+":"+n(this.getUTCMinutes())+":"+n(this.getUTCSeconds())+"."+(this.getUTCMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"})}(jQuery,DOMPurify,(window,document,wb)),function($,G,J){"use strict";function o(t){var d,e,a,r,R,n,i,o,s,l,c,u,p,f,g,h,m,b,y,w,v,x,T,A,k,C,E=[],_=[],j=[],S=0,N=$("caption",t),O=N.html()||"",z=N.text()||"",D=0,B=/[^+\-., 0-9]+[^\-+0-9]*/,M={"flot":{"prefix":"wb-charts-","defaults":{"colors":J.drawColours,"canvas":!0,"xaxis":{"ticks":{}}},"line":{},"area":{"lines":{"show":!0,"fill":!0}},"bar":{"bars":{"show":!0,"barWidth":1,"align":"center"}},"pie":{"series":{"pie":{"show":!0}},"fn":{"/series/pie/label/formatter":function(e,t){var a;return C.decimal?(a=Math.round(t.percent*Math.pow(10,C.decimal)),a/=Math.pow(10,C.decimal)):a=Math.round(t.percent),(a=C.nolegend?e+"<br />"+a:a)+"%"}}},"donut":{"base":"pie","series":{"pie":{"radius":1,"label":{"show":!0,"radius":1,"threshold":.08},"tilt":.5,"innerRadius":.45,"startAngle":1}},"grid":{"hoverable":!0}},"slicelegend":{"base":"pie","series":{"pie":{"radius":1,"label":{"radius":1,"show":!0,"threshold":.05},"combine":{"threshold":.05,"color":"#555","label":K.slicelegend}}},"fn":{"/series/pie/label/formatter":function(e){return e}}}},"series":{"prefix":"wb-charts-","defaults":{},"line":{},"area":{"lines":{"show":!0,"fill":!0}},"bar":{"bars":{"show":!0,"barWidth":1,"align":"center"}},"stacked":{"base":"bar"}},"charts":{"prefix":"wb-charts-","defaults":{"graphclass":"wb-graph","noencapsulation":!1,"labelposition":!1,"referencevalue":!1,"legendinline":!1,"nolegend":!1,"decimal":0,"width":t.width(),"height":t.height(),"reversettblparsing":!1,"fn":{"/getcellvalue":function(e){e=$.trim(e.dataset.wbChartsValue||$(e).text());return[parseFloat(e.replace(/(\d{1,3}(?:(?: |,)\d{3})*)(?:(?:.|,)(\d{1,2}))?$/,function(e,t,a){return t.replace(/ |,/g,"")+"."+a||"0"}),10),e.match(B)]}}},"donut":{"decimal":1}}};function I(e,t,a){var r,n=a[e];if(n)for(r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[e][r]=n[r])}function L(e,t,a){var r,n,i,o,s,l,c,d,u,p,f,g=$.extend(!0,{},e.defaults||e),h=$.extend(!0,{},e.defaults&&e.defaults.fn||{}),m=t.attr("class")||"";if(m.length)for(l=(s=e.prefix||"").length,n=0,i=(r=m.split(" ")).length;n!==i;n+=1)(c=(o=r[n]).length)<=l||o.slice(0,l)!==s||(c=e[o=o.slice(l,c)])&&(c.base&&(g=$.extend(!0,g,e[c.base]),h=$.extend(!0,h,e[c.base].fn||{})),g=$.extend(!0,g,c),h=$.extend(!0,h,c.fn||{}));for(d in g=$.extend(!0,g,J.getData(t,a)),h)if(Object.prototype.hasOwnProperty.call(h,d)){for(f=g,n=0,i=(u=d.split("/")).length-1;n!==i;n+=1)""!==(p=u.shift())&&(f[p]||(f[p]={}),f=f[p]);f[p=u.shift()]=h[d]}return g}function P(e,t){var a,r,n,i=e.child.length,o=1;if(0!==i)for(o*=r=i*t,a=0;a!==i;a+=1)0!==(n=e.child[a]).child.length&&(o*=P(n,r));return o}function U(e,t,a,r,n){for(var i,o=0,s=0,l=e.cell.length;s!==l;s+=1)if(i=e.cell[s],!(0!==s&&i.uid===e.cell[s-1].uid||n&&i.colpos<n)){if(b){if(i.rowgroup&&3===i.rowgroup.type)break}else if(i.colgroup&&3===i.colgroup.type)break;0<i.child&&t<r?i.flotDelta=a*i.child.length:i.flotDelta=a,1!==i.type&&7!==i.type||((!d||i.flotDelta<d)&&(d=i.flotDelta),i.flotValue=o,o+=a,0<i.child.length&&function e(t,a,r,n){var i,o,s,l,c=0;a+=1;c=t.flotValue;i=r/t.child.length;(!d||i<d)&&(d=i);for(o=0,s=t.child.length;o!==s;o+=1)(l=t.child[o]).flotDelta=a<n?i*l.child.length:i,l.flotValue=c,0<l.child.length&&e(l,a,i,n),c+=i}(i,t,a,r))}}function F(e,t){for(var a,r,n,i,o,s,l,c,d=t-1;-1!==d;--d)for(a=0,n=(c=e[d]).cell.length;a!==n;a+=1)if(!((s=c.cell[a]).flotDelta||0<a&&s.uid===c.cell[a-1].uid||1!==s.type&&7!==s.type)){for(r=o=0,i=s.child.length;r!==i;r+=1)o=(l=s.child[r]).flotDelta,s.flotValue||(s.flotValue=l.flotValue);s.flotDelta=o}}function H(e,t){for(var a,r=[],n=0,i=e.cell.length;n!==i;n+=1)a=e.cell[n],0!==n&&a.uid===e.cell[n-1].uid||1!==a.type&&7!==a.type||t&&a.colpos<t||r.push([a.flotValue,$(a.elem).text()]);return r}function q(e){var t=C.labelposition;return(!t||t>e.length?f.theadRowStack.length:t)-1}function Y(e){var t=b&&!1!==C.referencevalue?C.referencevalue:e.colgrouphead.col.length,a=function(e,t){var a,r,n,i,o,s=1;if(e){for(i=e.col[t],o=e.col[0],r=0,n=i.cell.length;r!==n;r+=1)if(a=i.cell[r],0===r||0<r&&o.cell[r-1].uid!==a.uid){if(a.rowgroup&&3===a.rowgroup.type)break;1!==a.type&&7!==a.type||0!==a.child.length&&(s*=P(a,1))}return s}}(e.colgrouphead,t-=1),r=b?q(e.colgrouphead.col):e.colgrouphead.col.length-1,n=t;return U(e.colgrouphead.col[t],n,a,t),F(e.colgrouphead.col,t),H(e.colgrouphead.col[r])}function W(e){var t,a,r,n,i,o,s=-1,l=e.theadRowStack;if(l){for(a=0,r=e.colgroup.length;a!==r;a+=1)if(2===e.colgroup[a].type){s=e.colgroup[a].start;break}return o=!b&&!1===C.referencevalue||b?l.length:C.referencevalue,i=function(e,t,a){for(var r,n=1,i=e[t].elem.cells,o=0,s=i.length;o!==s&&(!(r=$(i[o]).data().tblparser).colgroup||3!==r.colgroup.type);o+=1)r.colpos>=a&&(1===r.type||7===r.type)&&0!==r.child.length&&(n=n*r.child.length*P(r,1));return n}(l,o-=1,s),n=b?l.length-1:q(l),U(l[t=o],t,i,o,s),F(l,o),H(l[n],s)}}function V(){var e;O.length&&(e=$("<summary>"+O+K.tableMention+"</summary>"),t.wrap("<details></details>").before(e),e.trigger("wb-init.wb-details"))}function Q(e){return t.wrap("<figure class='"+C.graphclass+"'/>").before((O.length?"<figcaption>"+O+"</figcaption>":"")+"<div role='img' aria-label='"+z+K.tableFollowing+"'"+(e?"style='height:"+C.height+"px; width:"+C.width+"px'":"")+"></div>"),$("div:eq(0)",t.parent())}if(G.chartsGraphOpts||((N=G[Z])&&(I("flot",M,N),I("series",M,N),I("charts",M,N)),G.chartsGraphOpts=M),k=L((M=G.chartsGraphOpts).flot,t,"flot"),(C=L(M.charts,t,Z)).width=C.width&&250<C.width?C.width:250,C.height=C.height&&250<C.height?C.height:250,f=t.data().tblparser,b=C.reversettblparsing,m=f.lstrowgroup[0],k.series&&k.series.pie){for(a=Q(!1),r="<div style='height:"+C.height+"px; width:"+C.width+"px'></div>",g=b?(o=m,(1===f.colgroup[0].type?f.colgroup[1]:f.colgroup[0]).col.length-1):(o=1===f.colgroup[0].type?f.colgroup[1]:f.colgroup[0],m.row.length-1);0<=g;--g){for(l=0,c=(y=b?o.row:o.col).length;l!==c;l+=1){for(j=[],u=D=0,p=(w=y[l]).cell.length;u!==p;u+=1)if(v=w.cell[u],!b||1!==v.col.type){if(x=void 0,0!==u&&(x=w.cell[u-1]),!b&&(2!==v.row.type||x&&x.rowgroup.uid!==v.rowgroup.uid)||b&&2!==v.col.type||x&&1!==x.col.type&&x.col.groupstruct.uid!==v.col.groupstruct.uid)break;s=(b?v.col:v.row).header,n=C.getcellvalue((b?w.datacell:w.cell)[g].elem),j.push([D,"object"==typeof n?n[0]:n]),D+=s[s.length-1].flotDelta;break}A={},v=(b?w.datacell:w.cell)[g],s=(s=(b?v.row:v.col).header)[s.length-1],(A=L(M.series,$(s.elem),"flot")).data=j,A.label=$((b?w.header[w.header.length-1]:w.dataheader[w.dataheader.length-1]).elem).text(),E.push(A)}1!==m.row.length||m.row[0].header[0].elem.innerHTML!==O&&0!==m.row[0].header.length?(R="<figure><figcaption>"+(s=(b?v.col:v.row).header)[s.length-1].elem.innerHTML+"</figcaption>"+r+"</figure>",a.append($(R)),e=$("div:last()",a)):(e=a).css({"height":C.height,"width":C.width}),$.plot(e,E,k),C.legendinline||$(".legend",e).appendTo(a),E=[]}C.nolegend&&$(".legend",a).remove(),C.legendinline||($(".legend > div",a).remove(),$(".legend > table",a).removeAttr("style").addClass("font-small"),$(".legend",e).appendTo(void 0)),$(".pieLabel").removeAttr("id"),C.noencapsulation||V()}else{for(_=(b?(o=1===f.colgroup[0].type?f.colgroup[1]:f.colgroup[0],g=m.row.length-1,Y):(o=m,g=(1===f.colgroup[0].type?f.colgroup[1]:f.colgroup[0]).col.length-1,W))(f),k.xaxis.ticks=_,l=0,c=(y=b?o.col:o.row).length;l!==c;l+=1)T=(w=y[l]).header[w.header.length-1],((h=L(M.series,$(T.elem),"flot")).bars||k.bars&&!h.lines)&&(S+=1,h.bars||(h.bars={"show":!0,"barWidth":.9}),h.bars.order||(h.bars.order=S)),T.chartOption=h;for(l=0,c=y.length;l!==c;l+=1){for(j=[],h=(T=y[l]).header[T.header.length-1].chartOption,u=D=i=0,p=T.cell.length;u!==p&&(v=T.cell[u],!(1<i&&2!==v.col.groupstruct.type));u+=1)(!b&&2===v.col.groupstruct.type||b&&2===v.row.rowgroup.type)&&(s=(b?v.row:v.col).header,n=C.getcellvalue(v.elem),j.push([D,"object"==typeof n?n[0]:n]),D+=s[s.length-1].flotDelta,i+=1);h.data=j,h.label=$(T.header[T.header.length-1].elem).text(),h.bars&&(h.bars.barWidth=h.bars.barWidth*(1/S)),E.push(h)}k.bars&&(k.bars.barWidth=k.bars.barWidth*(1/S)),(e=Q(!0)).css("width","100%"),$.plot(e,E,k),C.legendinline||($(".legend > div",e).remove(),$(".legend > table",e).removeAttr("style").addClass("font-small"),e.css("height","auto")),C.nolegend&&$(".legend",e).remove(),C.noencapsulation||V(),$("canvas:eq(1)",e).css("position","static"),$("canvas:eq(0)",e).css("width","100%"),t.trigger("wb-updated"+X)}}var s,K,Z="wb-charts",X="."+Z,e=J.doc;e.on("timerpoke.wb wb-init.wb-charts parsecomplete.wb-tableparser",X,function(e){var t,a,r,n=e.type,i=e.target;switch(n){case"timerpoke":case"wb-init":t=e,t=J.init(t,Z,X),r=G[Z],t&&(a=t.id,t=["site!deps/jquery.flot"+(t=J.getMode()+".js"),"site!deps/jquery.flot.pie"+t,"site!deps/jquery.flot.canvas"+t,"site!deps/jquery.flot.orderBars"+t,"site!deps/tableparser"+t],r&&r.plugins&&(t=t.concat(r.plugins)),K||(s=J.i18n,K={"tableMention":s("hyphen")+s("tbl-txt"),"tableFollowing":s("hyphen")+s("tbl-dtls"),"slicelegend":s("chrt-cmbslc")}),Modernizr.load({"load":t,"complete":function(){var e=$("#"+a);e.trigger("passiveparse.wb-tableparser"),J.ready(e,Z)}}));break;case"parsecomplete":e.currentTarget===i&&o($(i))}return!0}),J.add(X)}(jQuery,window,(document,wb)),function(r,a){"use strict";var n,i="wb-collapsible",o="details.alert",e=a.doc;e.on("timerpoke.wb wb-init.wb-collapsible",o,function(e){var t,e=a.init(e,i,o);if(e){t=r(e),n="alert-collapsible-state-"+e.getAttribute("id");try{localStorage.getItem(n)?"open"===localStorage.getItem(n)?(e.setAttribute("open","open"),e.className+=" open"):"closed"===localStorage.getItem(n)&&(e.removeAttribute("open"),e.className=e.className.replace(" open","")):e.hasAttribute("open")?localStorage.setItem(n,"open"):localStorage.setItem(n,"closed")}catch(e){}a.ready(t,i)}}),Modernizr.details&&e.on("click keydown toggle."+i,o+" summary",function(e){var t=e.which,a=e.currentTarget;if(t&&1!==t||-1!==a.className.indexOf("wb-toggle")&&("toggle"!==e.type||e.namespace!==i))13!==t&&32!==t||(e.preventDefault(),r(a).trigger("click"));else if(e=null===(t=a.parentNode).getAttribute("open"),n="alert-collapsible-state-"+t.getAttribute("id"),e)try{localStorage.setItem(n,"open")}catch(e){}else try{localStorage.setItem(n,"closed")}catch(e){}return!0}),a.add(o)}(jQuery,(window,wb)),function(r,n){"use strict";var i="wb-ctrycnt",o="[data-ctrycnt]",e=n.doc;e.on("timerpoke.wb wb-init.wb-ctrycnt",o,function(e){var t,a,e=n.init(e,i,o);e&&(t=r(e),a=t.data("ctrycnt"),r.when(function(){var t=r.Deferred(),a=localStorage.getItem("countryCode");if(a===null)r.ajax({"url":"https://api.country.is/","dataType":"json","cache":true,"success":function(e){if(e){a=e.country;try{localStorage.setItem("countryCode",a)}catch(e){}}t.resolve(a)},"error":function(){t.reject("")}});else t.resolve(a);return t.promise()}()).then(function(e){""!==e&&(a=a.replace("{country}",e.toLowerCase()),t.load(a,function(){n.ready(t,i)}))}))}),n.add(o)}(jQuery,(window,wb)),function(c,o,d){"use strict";function u(e,t){var e=e.target,a=c(e),r=o[p],t={"url":e=(t=t||m(e)).url,"nocache":t.nocache,"nocachekey":t.nocachekey};!r||"http"!==e.substr(0,4)&&"//"!==e.substr(0,2)||(e=d.getUrlParts(e),d.pageUrlParts.protocol===e.protocol&&d.pageUrlParts.host===e.host)||Modernizr.cors&&!r.forceCorsFallback||"function"==typeof r.corsFallback&&(t.dataType="jsonp",t.jsonp="callback",t=r.corsFallback(t)),a.trigger({"type":"ajax-fetch.wb","fetch":t})}var e,p="wb-data-ajax",f="wb-ajax",t=["[data-ajax-after]","[data-ajax-append]","[data-ajax-before]","[data-ajax-prepend]","[data-ajax-replace]","[data-"+f+"]"],s=["before","replace","after","append","prepend"],a=t.length,g=t.join(","),h="wb-contentupdated",r=d.doc,m=function(e){for(var t,a,r,n,i=s.length,o=0;o!==i&&(t=s[o],!(a=e.getAttribute("data-ajax-"+t)));o+=1);if(!a){if(!(n=d.getData(c(e),f)))return{};if(t=n.type,-1===s.indexOf(t))throw"Invalid ajax type";if(!(a=l(n.url,n.httpref)))return{"type":t};r=n.nocache,n=n.nocachekey}return{"url":a,"type":t,"nocache":r,"nocachekey":n}},l=function(e,t){var a,r,n,i;if(!t)return e;for(Array.isArray(t)?a=t:(a=[]).push(t),r=o.document.referrer,i=a.length,n=0;n!==i;n+=1)if(new RegExp(a[n]).test(r))return Array.isArray(e)&&e.length===i?e[n]:e;return""};for(r.on("timerpoke.wb "+("wb-init."+p)+" "+("wb-update."+p)+" ajax-fetched.wb",g,function(e){var t,a,r,n,i,o,s,l=e.target;switch(e.type){case"timerpoke":case"wb-init":!function(e){var t=m(e.target),a=t.type,r=d.init(e,p+"-"+a,g);r&&t.url&&(u.call(this,e,t),d.ready(c(r),p,[a]))}(e);break;case"wb-update":u(e);break;default:e.currentTarget===l&&(t=l,a=e.fetch,r=c(t),n=m(t).type,t=(d.getData(c(t),f)||{}).encode,i=a.hasSelector,o="",s=a.response)&&0<s.length&&(t&&i?s=(s=1<s.length?(s.each(function(e,t){o+=t.outerHTML+"\n"}),o):s.html()).replaceAll("<","&lt;"):t&&!i&&(s=a.xhr.responseText.replaceAll("<","&lt;")),t=jQuery.ajaxSettings.cache,jQuery.ajaxSettings.cache=!0,"replace"===n?r.html(s):r[n](s),jQuery.ajaxSettings.cache=t,r.trigger(h,{"ajax-type":n,"content":s}))}return!0}),r.on(h,function(e){d.isDisabled||(e=e.target,c(e).find(d.allSelectors).addClass("wb-init").filter(":not(#"+e.id+" .wb-init .wb-init)").trigger("timerpoke.wb"))}),e=0;e!==a;e+=1)d.add(t[e])}(jQuery,window,wb),function(a,r){"use strict";var n="wb-data-fusion-query",i="[data-fusion-query][name]";r.doc.on("timerpoke.wb wb-init.wb-data-fusion-query",i,function(e){var t,e=r.init(e,n,i);e&&(t=(e=a(e)).attr("name"),(t=r.pageUrlParts.params[t])&&e.val(t.replace(/\+/g," ")),r.ready(e,n))}),r.add(i)}((document,jQuery),wb),function(u,p){"use strict";function n(t){var e=t.outerWidth(),a=t.outerHeight(),r=g.scrollTop(),n=r+g.height(),i=g.scrollLeft()+e,o=(d=t.offset().left)+e,s=t.offset().top,l=s+a,c=t.attr("data-inviewstate"),d=n<s||l<r||i<d||o<i,o=l<n&&r<s?"all":d?"none":"partial",i=u("#"+t.attr("data-inview"));0===e||0===a?(h=h.not(t),i.addClass("user-closed"),i.trigger({"type":"close","namespace":"wb-overlay","noFocus":!0})):(t.hasClass("wb-dismissable")&&i.hasClass("wb-overlay")&&i.children(".overlay-close").on("click",function(e){e=e.which;e&&1!==e||t.parent().siblings(".content-dismiss").trigger("click")}),o!==c&&(l=d||!t.hasClass("show-none")&&"partial"==o,t.attr("data-inviewstate",o),0===i.length||i.hasClass("user-closed")||(i.hasClass("wb-overlay")?(c||i.addClass("outside-off"),i.trigger({"type":l?"open":"close","namespace":"wb-overlay","noFocus":!0})):(i.attr("data-hasPlayed")||(i.toggleClass("in",!l),i.hasClass("in-only"))||i.toggleClass("out",l),p.isReady&&"all"==o&&i.hasClass("in-only")&&i.attr("data-hasPlayed","true"))),t.trigger(o+f)))}var i="wb-inview",f="."+i,e="wb-init"+f,t="scroll"+f,a=p.doc,g=p.win,h=u();a.on("timerpoke.wb "+e+" "+t,f,function(e){var t,a,r=e.target;switch(e.type){case"timerpoke":case"wb-init":t=e,(t=p.init(t,i,f))&&(a=u(t),h=h.add(a),setTimeout(function(){n(a),p.ready(a,i)},1));break;case"scroll":e.currentTarget===r&&n(u(r))}return!0}),g.on("scroll scrollstop",function(){h.trigger(t)}),a.on("txt-rsz.wb win-rsz-width.wb win-rsz-height.wb",function(){h.trigger(t)}),a.on("refresh.wb",function(){h.each(function(){n(u(this))})}),p.add(f)}(jQuery,(window,wb)),function(d,u,p){"use strict";var f,g="wb-pic",h="[data-pic]",m="picfill."+g,b=p.doc;b.on("timerpoke.wb wb-init.wb-pic "+m,h,function(e){var t=e.target;switch(e.type){case"timerpoke":case"wb-init":n=e,(n=p.init(n,g,h))&&(n=d(n),f=n.data("class")||"",n.trigger(m),p.ready(n,g));break;case"picfill":if(e.currentTarget===t){for(var a,r,n=t,i=[],o=n.getElementsByTagName("img")[0],s=n.getElementsByTagName("span"),l=0,c=s.length;l!==c;l+=1)(r=s[l].getAttribute("data-media"))&&!u.matchMedia(r).matches||i.push(s[l]);0!==i.length?(a=i.pop(),o||((o=b[0].createElement("img")).alt=n.getAttribute("data-alt"),o.className=f),o.src=a.getAttribute("data-src"),a.appendChild(o)):o&&o.parentNode.removeChild(o),d(n).trigger("wb-updated."+g)}}}),b.on("txt-rsz.wb win-rsz-width.wb win-rsz-height.wb",function(){d(h).trigger(m)}),p.add(h)}(jQuery,window,wb),function(r,n){"use strict";var i,o="wb-details-close",s=".provisional."+o,e="wb-init"+s,t=n.doc,l=["xxs","xs","sm","md","lg","xl"],c=["xxsmallview","xsmallview","smallview","mediumview","largeview","xlargeview"];t.on("timerpoke.wb "+e,s,function(e){var e=n.init(e,o,s);if(e){e=r(e),i=e.data("breakpoint")||"sm",l.length===c.length&&(t=l.indexOf(i),c=c.slice(0,t+1));var t=r(s),a="html."+c.join(", html.");if(r(a).length)t.removeAttr("open");else t.attr("open","");n.ready(e,o)}}),n.add(s)}(jQuery,wb),function(r,n){"use strict";var i,o,s="wb-dismissable",l="."+s,c="content-dismiss",d="dismissable-item-id",u=n.doc;u.on("timerpoke.wb wb-init.wb-dismissable",l,function(e){var t,a,e=n.init(e,s,l);e&&(o||(i=n.i18n,o={"dismiss":i("dismiss")}),void 0===(t=e.hasAttribute("id")&&0===(t=e.getAttribute("id")).indexOf("wb-auto-")?void 0:t)&&(t=n.hashString(n.stripWhitespace(e.innerHTML))),a=t,"true"===(null!==(a=localStorage.getItem(a))&&a)?e.parentNode&&e.parentNode.removeChild(e):(r(e).wrap("<div class='wb-dismissable-wrapper'>"),a=e.parentNode,r(a).wrap("<div class='wb-dismissable-container'>"),e=a.parentNode,a="<button type='button' class='mfp-close "+c+"' title='"+o.dismiss+"'>&#xd7;<span class='wb-inv'> "+o.dismiss+"</span></button>",r(e).append(a),e.setAttribute("data-"+d,t)),n.ready(u,s))}),u.on("click","."+c,function(e){var t=e.currentTarget,e=e.which;e&&1!==e||(e=t.parentNode,localStorage.setItem(e.getAttribute("data-"+d),!0),e.parentNode.removeChild(e),u.trigger("refresh.wb"))}),n.add(l)}(jQuery,(window,wb)),function(t,a){"use strict";var r="wb-eqht-grd",n="."+r+" .eqht-trgt";a.doc.on("timerpoke.wb wb-init.wb-eqht-grd .eqht-trgt",n,function(e){var e=a.init(e,r,n);e&&((e=t(e)).addClass("hght-inhrt"),e.parentsUntil("[class*='"+r+"']").addClass("hght-inhrt"),a.ready(e,r))}),a.add(n)}(jQuery,(window,wb)),function(f,g,t){"use strict";function a(){for(var e,t,a,r,n,i,o,s,l=f(h),c=[],d=-1,u=-1,p=l.length-1;-1!==p;--p){for((t=(e=l.eq(p)).find(".eqht-trgt")).length||(t=e.children()),c=[],a=E(e),i=t.length-1;-1!==i;--i)0<(n=(r=t[i]).style.cssText.toLowerCase()).length&&n.substr(n.length-1)!==T&&(n+=T),-1!==n.indexOf(b)?n=n.replace(A,b+x+y+T):n+=" "+b+x+y+T,-1!==n.indexOf(w)?n=n.replace(k,w+x+v+T):n+=" "+w+x+v+T,r.style.cssText=n,t.eq(i).data(w,v);for(e=j(a),d=t[0]?t[0].getBoundingClientRect().top+g.pageYOffset:0,u=t[0]?t[0].offsetHeight:0,i=0;i<t.length;i++)o=(r=t[i]).getBoundingClientRect().top+g.pageYOffset,(s=r.offsetHeight)&&(o!==d&&(C(c,u),o=r.getBoundingClientRect().top+g.pageYOffset,c.length=0,d=o,u=s),u=Math.max(s,u),c.push(t.eq(i)));C(c,u),m.trigger("wb-updated"+h)}}var r="wb-eqht",h="."+r,m=t.doc,n="timerpoke.wb",e="wb-init"+h,b="vertical-align",y="top",w="min-height",v="0",x=":",T=";",i=" ?[^;]+",A=new RegExp(b+x+" ?"+i+T+"?","i"),k=new RegExp(w+x+" ?"+i+T+"?","i"),C=function(e,t){for(var a=0;a<e.length;a++)e[a][0].style.minHeight=t+1+"px"},E=function(e){var t=e.prev(),a=e.next(),r=e.parent();return t.length?e.data({"anchor":t,"anchorRel":"prev"}):a.length?e.data({"anchor":a,"anchorRel":"next"}):r.length&&e.data({"anchor":r,"anchorRel":"parent"}),e.detach()},j=function(e){var t=e.data("anchor");switch(e.data("anchorRel")){case"prev":t.after(e);break;case"next":t.before(e);break;case"parent":t.append(e)}return e};m.on(n+" "+e,h,function(e){t.init(e,r,h)&&(m.off(n,h),a(),t.ready(m,r))}),m.on("txt-rsz.wb win-rsz-width.wb win-rsz-height.wb wb-contentupdated wb-updated.wb-tables wb-update"+h,a),t.add(h)}(jQuery,window,wb),function(d,o,u,p){"use strict";var f,g="wb-exitscript",s="."+g,e=u.doc,h=g+"key",m=g+"-modal",l={"en":{"msgboxHeader":"Warning","exitMsg":"You are about to leave a secure site, do you wish to continue?","targetWarning":"The link will open in a new browser window.","yesBtn":"Yes","cancelBtn":"Cancel"},"fr":{"msgboxHeader":"Avertissement","exitMsg":"Vous êtes sur le point de quitter un site sécurisé. Voulez-vous continuer?","targetWarning":"Le lien s'ouvrira dans une nouvelle fenêtre de navigateur.","yesBtn":"Oui","cancelBtn":"Annuler"}};e.on("click",s,function(e){var t=e.currentTarget,t=d(t),a="",r=document.createDocumentFragment(),n=document.createElement("div"),t=t.data(g),i=f.msgboxHeader,o=f.yesBtn,s=f.cancelBtn,l=f.exitMsg,c=f.targetWarning;t.i18n&&(i=t.i18n.msgboxHeader||f.msgboxHeader,o=t.i18n.yesBtn||f.yesBtn,s=t.i18n.cancelBtn||f.cancelBtn,l=t.i18n.exitMsg||f.exitMsg,c=t.i18n.targetWarning||f.targetWarning),t.url||e.preventDefault(),a=this.hasAttribute("target")?"target='"+this.getAttribute("target")+"'":"target='"+a+"'","_blank"===this.getAttribute("target")&&(l=l+" "+c),document.getElementById(m)&&document.getElementById(m).remove(),t.url?p&&this[g]&&(localStorage.setItem(g,u.string.arrayBufferToBase64(this[g])),localStorage.setItem(h,JSON.stringify(this[h]))):(n.innerHTML="<section id='"+m+"' class='mfp-hide modal-dialog modal-content overlay-def'><header class='modal-header'><h2 class='modal-title'>"+i+"</h2></header><div class='modal-body'><p>"+l+"</p></div><div class='modal-footer'><ul class='list-inline text-center'><li><a class='btn btn-default pull-right popup-modal-dismiss'"+a+" href='"+this.getAttribute("href")+"'>"+o+"</a></li><li><button class='btn btn-primary popup-modal-dismiss pull-left'>"+s+"</button></li></ul></div></section>",r.appendChild(n),e=r.firstChild.firstChild,document.body.appendChild(e),d(e).trigger("open.wb-lbx",[[{"src":"#"+m,"type":"inline"}],!0]))}),e.on("timerpoke.wb wb-init.wb-exitscript",s,function(e){var r,n,i=u.init(e,g,s),e=o.location.search,e=new URLSearchParams(e),t=u.string.fromHexString(e.get("exturl")),a=localStorage.getItem(g),e=JSON.parse(localStorage.getItem(h));i&&(n=d(i),r=d.extend(!0,o[g],u.getData(n,g)),n.data(g,r),r.url&&p&&p.subtle.generateKey({"name":"AES-CTR","length":256},!0,["encrypt","decrypt"]).then(function(e){var t,a;p.subtle.exportKey("jwk",e).then(function(e){i[h]=e}),t=(new TextEncoder).encode(i.href),a=p.getRandomValues(new Uint8Array(16)),p.subtle.encrypt({"name":"AES-CTR","counter":a,"length":64},e,t).then(function(e){i[g]=e}),n.attr("href",r.url+"?exturl="+u.string.toHexString(a))}),f=l[u.lang||"en"],n.hasClass("wb-exitscript-urlparam")&&null!==a&&null!==e&&p.subtle.importKey("jwk",e,{"name":"AES-CTR","length":256},!0,["decrypt"]).then(function(e){p.subtle.decrypt({"name":"AES-CTR","counter":t,"length":64},e,u.string.base64ToArrayBuffer(a)).then(function(e){e=(new TextDecoder).decode(e);e.match(/^(http|https):\/\//g)&&(i.outerHTML="<a href='"+e+"'>"+e+"</a>")})}),localStorage.removeItem(g),localStorage.removeItem(h),u.ready(n,g))}),u.add(s)}(jQuery,window,wb,crypto),function(a,r,n){"use strict";var i="wb-facebook",o="."+i,e=n.doc,s=!1;e.on("timerpoke.wb wb-init.wb-facebook",o,function(e){var t=n.init(e,i,o),e=n.pageUrlParts.protocol;t&&Modernizr.load({"load":[(-1===e.indexOf("http")?"http:":e)+"//connect.facebook.net/"+n.lang+"_US/sdk.js"],"complete":function(){s||(r.FB.init({"version":"v2.4"}),s=!0),r.FB.XFBML.parse(t[0]),n.ready(a(t),i)}})}),n.add(o)}(jQuery,window,wb),function(l,c,a){"use strict";function d(e){return e.substring(0,e.lastIndexOf("/")+1)}var u="wb-favicon",r="link[rel='icon']",p="wb-updated."+u,n="mobile."+u,f=a.doc,i={"filename":"favicon-mobile.png","path":null,"rel":"apple-touch-icon","sizes":"57x57 72x72 114x114 144x144 150x150"};f.on("timerpoke.wb wb-init.wb-favicon",r,function(e){var t,e=a.init(e,u,r,!0);e&&(e=l(e),t=l.extend({},i,e.data()),e.trigger(n,t),a.ready(f,u))}),f.on(n+" icon.wb-favicon",r,function(e,t){var a,r,n,i,o,s=e.target;if(e.currentTarget===s)switch(e.type){case"mobile":a=s,r=t,i=l("link[rel^='apple']"),(o=0!==i.length)||((n=c.createElement("link")).setAttribute("rel",r.rel),n.setAttribute("sizes",r.sizes),n.setAttribute("class",u),c.head.appendChild(n),i=l(n)),i.hasClass(u)&&(n=null!==r.path?r.path:d(a.getAttribute("href")),i.attr("href",n+r.filename),o||a.parentNode.insertBefore(i[0],a)),f.trigger(p,["mobile"]);break;case"icon":n=s,o=null!==(r=t).path?r.path:d(n.getAttribute("href")),n.setAttribute("href",o+r.filename),f.trigger(p,["icon"])}return!0}),a.add(r)}(jQuery,document,wb),function(w,v){"use strict";function t(e,t){return String.fromCharCode(parseInt(t,16))}function u(e){return(e=e.className.match(/\blimit-\d+/))?Number(e[0].replace(/limit-/i,"")):0}function o(e){for(var t=e,a=[],r=this.fIcon,n=(e=this._content).data("toProcess"),i=t.length,o=0;o!==i;o+=1){t[o].fIcon=r,void 0===t[o].publishedDate&&(t[o].publishedDate=t[o].published||t[o].pubDate||t[o].updated||"");var s=t[o].link;s&&s.href&&(t[o].link=s.href),a.push(t[o])}if(a=w.merge(a,e.data("entries")),1!==n)return e.data({"toProcess":--n,"entries":a}),n;var l,c,d,n=a,u=e.data("feedLimit"),p=this.feedType,f=0<u&&u<n.length?u:n.length,g="",h=v.date.compare,u=e.closest("details"),m=!0,b=".feeds-cont",y="vis-handler";for(c="youtube"!==p?n.sort(function(e,t){return h(t.publishedDate,e.publishedDate)}):n,l=0;l!==f;l+=1)d=c[l],g+=T[p](d);return e.data(x+"-result",g),0!==u.length&&("tabpanel"===u.attr("role")?"true"===u.attr("aria-hidden")&&(m=!1,e.empty().addClass("waiting"),(n=u.closest(".wb-tabs")).hasClass(y)||n.on("wb-updated.wb-tabs",function(e,t){t=t.find(b);t.hasClass("feed-active")||A(t)}).addClass(y)):u.attr("open")||(m=!1,e.empty().addClass("waiting"),u.children("summary").on("click.wb-feeds",function(e){e=w(e.currentTarget).off("click.wb-feeds");A(e.parent().find(b))}))),m&&A(e),0}var x="wb-feeds",p="."+x,e="wb-init"+p,a=v.doc,r=/\\u([\d\w]{4})/g,T={"flickr":function(e){var t=e.media.m,t={"title":e.title,"thumbnail":t.replace("_m.","_s."),"image":t.replace("_m",""),"description":e.description.replace(/^\s*<p>(.*?)<\/p>\s*<p>(.*?)<\/p>/i,"")};return"<li><a class='feed-flickr' href='#' data-flickr='"+v.escapeAttribute(JSON.stringify(t))+"'><img src='"+t.thumbnail+"' alt='"+v.escapeAttribute(t.title)+"' title='"+v.escapeAttribute(t.title)+"' class='img-responsive'/></a></li>"},"youtube":function(e){e={"title":e.title,"videoId":e.id};return"<li class='col-md-4 col-sm-6'><button class='btn btn-lnk feed-youtube' data-youtube='"+v.escapeAttribute(JSON.stringify(e))+"'><img src='"+v.pageUrlParts.protocol+"//img.youtube.com/vi/"+e.videoId+"/mqdefault.jpg' alt='"+v.escapeAttribute(e.title)+"' title='"+v.escapeAttribute(e.title)+"' class='img-responsive' /></button></li>"},"pinterest":function(e){return"<li class='media'>"+n(e.description).replace(/<a href="\/pin[^"]*"><img ([^>]*)><\/a>([^<]*)(<a .*)?/,"<a href='"+e.link+"'><img alt='' class='center-block' $1><br/>$2</a>$3")+(""!==e.publishedDate?" <small class='small feeds-date'><time>"+v.date.toDateISO(e.publishedDate,!0)+"</time></small>":"")+"</li>"},"generic":function(e){var t=e.title;return"object"==typeof t&&(t.content?t=t.content:"xhtml"===t.type&&t.div&&(t=t.div.content)),"<li><a href='"+e.link+"'>"+t+"</a><br />"+(""!==e.publishedDate?" <small class='feeds-date'><time>"+v.date.toDateISO(e.publishedDate,!0)+"</time></small>":"")+"</li>"}},n=function(e){return e.replace(r,t)},c=function(e){var t,a,r,n,i,o,s={},l=e.nodeType;if(1===l){if((i=e.attributes).length)for(s["@attributes"]={},t=0;t<i.length;t++)a=i.item(t),s["@attributes"][a.nodeName]=a.nodeValue}else 3===l&&(s=e.nodeValue);if(e.hasChildNodes())for(o=e.childNodes,t=0;t<o.length;t++)void 0===s[r=(a=o.item(t)).nodeName]?s[r]=c(a):(void 0===s[r].push&&(n=s[r],s[r]=[],s[r].push(n)),s[r].push(c(a)));return s},A=function(e){var t,a,r=e.data(x+"-result"),n=e.data(x+"-postProcess");if(r){if(e.empty().removeClass("waiting").addClass("feed-active").append(r),n)for(t=n.length-1;-1!==t;--t)a=n[t],e.find(a).trigger("wb-init"+a);e.trigger("wb-feed-ready"+p)}};a.on("ajax-fetched.wb data-ready.wb-feeds",p+" li > a",function(e,t){var a,r,n,i=e.target;e.currentTarget===i&&(n=w(i).parentsUntil(p).parent(),"ajax-fetched"===e.type?(r="string"==typeof(r=e.fetch.response)?JSON.parse(r):r.get(0)).documentElement?a=function(e,t){var a,r,n=e.getElementsByTagName("entry").length,i=[],o={},e=JSON.stringify(c(e)),s=JSON.parse(e);if((n<t||0===t||null===t)&&(t=n),1===n)o={"title":(r=s.feed.entry).title["#text"],"link":r.link?r.link["@attributes"].href:r.id["#text"],"updated":r.updated["#text"]},i.push(o);else if(n)for(a=0;a<t;a++)o={"title":(r=s.feed.entry[a]).title["#text"],"link":r.link?r.link["@attributes"].href:r.id["#text"],"updated":r.updated["#text"]},i.push(o);return i}(r,u(n[Object.keys(n)[0]])):r.query?(n=r.query.results)?a=[]:(a=n.item,Array.isArray(a)||(a=[a])):a=r.responseData?r.responseData.feed.entries:r.items||r.feed.entry:a=e.feedsData,0===o.apply(t,[a]))&&v.ready(w(i).closest(p),x)}),a.on("click",p+" .feed-youtube",function(e){var t="#wb-feeds-youtube-lbx",a=w(t),e=v.getData(e.currentTarget,"youtube"),r=v.pageUrlParts.protocol+"//www.youtube.com/watch?v="+e.videoId,r="<figure class='wb-mltmd'><video title='"+e.title+"'><source type='video/youtube' src='"+r+"'></source></video><figcaption><p>"+e.title+"</p></figcaption></figure>";0===a.length?a=w("<section id='wb-feeds-youtube-lbx' class='mfp-hide modal-dialog modal-content overlay-def'><header class='modal-header'><h2 class='modal-title'>"+e.title+"</h2></header><div class='modal-body'>"+r+"</div></section>").insertAfter("main"):(a.find(".modal-title").text(e.title),a.find(".modal-body").empty().append(r)),a.find(".wb-mltmd").trigger("wb-init.wb-mltmd"),w(document).trigger("open.wb-lbx",[{"src":t,"type":"inline"}])}),a.on("click",p+" .feed-flickr",function(e){var t="#wb-feeds-flick-lbx",a=w(t),e=v.getData(e.currentTarget,"flickr"),r="<img src='"+e.image+"' class='thumbnail center-block' alt='"+e.title+"' /><span>"+e.description+"</span>";0===a.length?a=w("<section id='wb-feeds-flick-lbx' class='mfp-hide modal-dialog modal-content overlay-def'><header class='modal-header'><h2 class='modal-title'>"+e.title+"</h2></header><div class='modal-body'>"+r+"</div></section>").insertAfter("main"):(a.find(".modal-title").text(e.title),a.find(".modal-body").empty().append(r)),w(document).trigger("open.wb-lbx",[{"src":t,"type":"inline"}])}),a.on("timerpoke.wb "+e,p,function(e){var t,a,r,n,i,o,s,l,c,d,e=v.init(e,x,p);if(e)for(a=w(e).find(".feeds-cont"),e=u(e),i=(r=a.find("li > a")).length-1,a.data("toProcess",r.length).data("feedLimit",e).data("entries",[]),o=i;-1!==o;--o)c=(l=r.eq(o)).find("> img"),t={"dataType":"jsonp","timeout":1e4},l.attr("data-ajax")?(-1!==l.attr("href").indexOf("flickr")?(n="flickr",s="jsoncallback",a.data(x+"-postProcess",[".wb-lbx"])):(s=!(n="generic"),t.dataType="json"),t.url=l.attr("data-ajax"),t.jsonp=s):l.attr("data-youtube")?(d=v.getData(l,"youtube"),a.data(x+"-postProcess",[".wb-lbx",".wb-mltmd"]),d.playlist&&l.trigger({"type":"data-ready.wb-feeds","feedsData":d.playlist},{"feedType":"youtube","_content":a})):(d=l.attr("href"),t.dataType="xml",n=-1<(t.url=d).indexOf("pinterest.com")?"pinterest":"generic"),t.jsonp=s,t.context={"fIcon":0!==c.length?c.attr("src"):"","feedType":n,"_content":a},l.trigger({"type":"ajax-fetch.wb","fetch":t})}),v.add(p)}(jQuery,(window,wb)),function(c,d,u){"use strict";function a(e,t,a){function r(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}var n,i,o,s,e=r(e.val().trim()),l=a.filterCallback,c=(a.section||"")+" ",d=a.hdnparentuntil,u=t.find(c+a.selector),p=u.length;for(t.find("."+b).removeClass(b),s=function(e,t){var a,r,n,i=t;switch(e){case"and":if(a=T(t))for(i=".*",n=a.length,r=0;r<n;r++)i=i+("(?=.*"+a[r])+")";break;case"or":(a=T(t))&&(i=a.join("|"))}return new RegExp(i,"i")}(a.filterType,e),n=0;n<p;n+=1)o=r((i=u.eq(n)).text()),s.test(o)||(i=d?i.parentsUntil(d):i).addClass(b);(l=l&&"function"==typeof l?l:f).apply(this,arguments),t.trigger("wb-contentupdated")}function f(e,t,a){for(var r,n=t.find(a.section),i=n.length,o=y+a.selector,s=0;s<i;s+=1)0===(r=n.eq(s)).find(o).length&&r.addClass(b)}var p,g,r,h="wb-filter",m="."+h,e=u.doc,b="wb-fltr-out",y=":not(."+b+"):not(.wb-tgfltr-out)",w="wb-fltr-inpt",v="wbfltrid",x={"std":{"selector":"li"},"grp":{"selector":"li","section":">section"},"tbl":{"selector":"tr","section":">tbody"},"tblgrp":{"selector":" th:not([scope])"+y,"hdnparentuntil":"tbody","section":">tbody"}},T=function(e){return(e=e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")).match(/[^\s"]+|"([^"]*)"/gi)};e.on("keyup",".wb-fltr-inpt",function(e){var e=e.target,e=c(e),t=c("#"+e.data(v));r&&clearTimeout(r),r=setTimeout(a.bind(this,e,t,t.data()),250)}),e.on("timerpoke.wb wb-init.wb-filter",m,function(e){var t,a,r,n,i,o,s,l,e=u.init(e,h,m);e&&(t=c(e),i=e.nodeName,l=e.id+"-info",0<=["DIV","SECTION","ARTICLE"].indexOf(i)?(n=x.grp,s=!0):n="TABLE"===i?1<t.find("tbody").length?x.tblgrp:x.tbl:x.std,a=c.extend(!0,{},n,d[h],u.getData(t,h)),t.data(a),g||(p=u.i18n,g={"filter_label":p("fltr-lbl"),"fltr_info":p("fltr-info")}),Modernizr.addTest("stringnormalize","normalize"in String),Modernizr.load({"test":Modernizr.stringnormalize,"nope":["site!deps/unorm"+u.getMode()+".js"]}),e.id||(e.id=u.getId()),a.uiTemplate?((n=(i=document.querySelector(a.uiTemplate)).querySelector("input[type=search]"))?(i=i.querySelector(".wb-fltr-info"),n.classList.add(w),n.setAttribute("data-"+v,e.id),n.setAttribute("aria-controls",e.id),i&&(l=i.id||l,i.id=l,i.setAttribute("role","status"))):console.error(h+': an <input type="search"> is required in your UI template.'),a.source&&console.warn(h+": the 'source' option is not compatible with the 'uiTemplate' option. If both options are defined, only 'uiTemplate' will be registered.")):(n=e.id+"-inpt",i=c('<div class="input-group"><label for="'+n+'" class="input-group-addon"><span class="glyphicon glyphicon-filter" aria-hidden="true"></span> '+g.filter_label+'</label><input id="'+n+'" class="form-control '+w+'" data-'+v+'="'+e.id+'" aria-controls="'+e.id+'" type="search"></div><p role="status" id="'+l+'">'+g.fltr_info+"</p>"),a.source?c(a.source).prepend(i):s?t.prepend(i):t.before(i)),r=(a.section||"")+" ",n=t.find(r+a.selector).length,o=document.querySelector("#"+l+" [data-nbitem]"),s=document.querySelector("#"+l+" [data-total]"),o&&(o.textContent=n,new MutationObserver(function(){o.textContent=t.find(r+a.selector+y).length}).observe(e,{"attributes":!0,"subtree":!0})),s&&(s.textContent=n),u.ready(t,h))}),u.add(m)}(jQuery,window,wb),function(l,c){"use strict";var d="wb-fnote",u="."+d,p="data-"+d,f="setfocus.wb",g=c.doc;g.on("timerpoke.wb wb-init.wb-fnote",u,function(e){var t,a,r,n,i,o,s,e=c.init(e,d,u);if(e){for(t=l(e),a=e.getElementsByTagName("dd"),r=e.getElementsByTagName("dt"),i=a.length,n=0;n!==i;n+=1)o=a[n],s=r[n],o.setAttribute("tabindex","-1"),s.id=o.id+"-dt";t.find("dd p.fn-rtn a span span").remove(),g.on("click","main :not("+u+") sup a.fn-lnk",function(e){var t=e.target,e=e.which;if(!e||1===e)return e="#"+c.jqEscape(t.getAttribute("href").substring(1)),(e=g.find(e)).find("p.fn-rtn a").attr("href","#"+t.parentNode.id).attr(p,!0),e.trigger(f),!1}),g.on("click",u+" dd p.fn-rtn a",function(e){var t,a,r=e.which,e=l(e.target);if((!r||1===r)&&"#"===(r=e.attr("href")).charAt(0))return-1===(a=(t=c.jqEscape(r.substring(1))).indexOf("-"))||e.attr(p)||(a=t.substring(0,a+1),(a=c.jqEscape(l("sup[id^='"+a+"']:first()").attr("id")))&&t===a)||(console.warn(d+" - Relink first reference of "+r+" for #"+a),e.attr("href","#"+(t=a)).attr(p,!0)),g.find("#"+t+" a").trigger(f),!1}),c.ready(t,d)}}),c.add(u)}(jQuery,(window,wb)),function(T,c,A,d){"use strict";var a,k,u="wb-frmvld",p="."+u,f=d.doc,g=0,h={"hdLvl":"h2","ignore":":hidden"};f.on("timerpoke.wb wb-init.wb-frmvld",p,function(e){var s,t,l=d.init(e,u,p);l&&((s=l.id)||(s=u+"-id-"+g,g+=1,l.id=s),t=d.getMode()+".js",k||(a=d.i18n,k={"colon":a("colon"),"hyphen":a("hyphen"),"error":a("err"),"errorFound":a("err-fnd"),"errorsFound":a("errs-fnd"),"formNotSubmitted":a("frm-nosubmit"),"errorCorrect":a("err-correct")}),Modernizr.load({"load":["site!deps/jquery.validate"+t],"testReady":function(){return T.validator},"complete":function(){Modernizr.load({"load":["site!deps/additional-methods"+t],"testReady":function(){return T.validator.methods.bic},"complete":function(){var e,t,a,r=T("#"+s),b=r.find("form"),n=b.get(0),i=b.attr("id"),o=n.getElementsByTagName("label"),y=!1,w=!1,v="errors-"+(i||"default"),n=T.extend(!0,{},h,c[u],d.getData(r,u)),x=n.hdLvl;for("fr"===d.lang&&(T.validator.addMethod("alphanumeric",function(e,t){return this.optional(t)||/^[a-zàâçéèêëîïôûùüÿæœ0-9_]+$/i.test(e)},"Letters, numbers, and underscores only please."),T.extend(T.validator.messages,{"alphanumeric":"Veuillez fournir seulement des lettres, nombres et soulignages."})),r.append("<div class='arialive wb-inv' aria-live='polite' aria-relevant='all'></div>"),t=o.length,e=0;e!==t;e+=1)o[e].insertAdjacentHTML("beforeend"," ");b.find("strong.required:not([aria-hidden='true'])").each(function(){var e=T(this),t=e.closest("label"),a=t.attr("for");let r=a?T("#"+a):t.find(":input").first();r.length||(a=t.attr("id")||e.closest("id"),r=b.find("[aria-labelledby~='"+a+"']:input").first()),r.is("[required], [aria-required='true']")&&e.attr("aria-hidden","true")}),a=b.validate({"meta":"validate","focusInvalid":!1,"ignore":n.ignore,"errorElement":"strong","errorPlacement":function(e,t){var a=t.attr("type"),r=t.attr("data-rule-require_from_group");if(e.data("element-id",t.attr("id")),!a||"radio"!==(a=a.toLowerCase())&&"checkbox"!==a||0===(i=t.closest("fieldset")).length||0===(n=i.find("legend").first()).length||1===i.find("input[name='"+t.attr("name")+"']")){if(r){var n,i=t.closest("fieldset");if(0!==i.length)if(0!==(n=i.find("legend").first()).length&&1!==i.find("input[name='"+t.attr("name")+"']"))return a=n.find("strong.error"),r=n.attr("id"),0<a.length&&a.remove(),r||(r="required-group-"+g,g+=1,n.attr("id",r)),e.data("element-id",r),e.attr("id",r),void e.appendTo(n)}0<b.find("label").find(".wb-server-error + input.css-implicite-input[name='"+t.attr("name")+"']").length?e.insertBefore(b.find(".wb-server-error + input.css-implicite-input[name='"+t.attr("name")+"']")):e.appendTo(b.find("label[for='"+t.attr("id")+"']"))}else e.appendTo(n)},"showErrors":function(e){this.defaultShowErrors();var t,a,r,n,i,o,s=b.find(".wb-server-error, strong.error").filter(":not(:hidden)"),l=b.find("input.error, select.error, textarea.error"),c="<span class='prefix'>"+k.error+"&#160;",d=k.colon+" </span>",u=k.hyphen,p=b.closest(".wb-frmvld").find(".arialive")[0];if(b.find(".has-error [aria-invalid=false]").closest(".has-error").removeClass("has-error"),0!==s.length){for(a="<"+x+">"+k.formNotSubmitted+s.length+(1!==s.length?k.errorsFound:k.errorFound)+"</"+x+"><ul>",l.closest(".form-group").addClass("has-error"),i=s.length,n=0;n!==i;n+=1){var f,g,h,m=c+(n+1)+d;0===(h=(g=s.eq(n)).closest("label").find(".field-name")).length&&0!==(f=g.closest("fieldset")).length&&(h=f.find("legend .field-name")),g.find("span.prefix").detach(),s[n].classList.contains("wb-server-error")?(s[n].id&&(null===(f=A.getElementById(s[n].id).parentElement)?a+="<li><a>"+m+(0!==h.length?h.html()+u:"")+g.text()+u+k.errorCorrect+"</a></li>":f.hasAttribute("for")&&0<f.getAttribute("for").length?a+="<li><a href='#"+f.getAttribute("for")+"'>"+m+(0!==h.length?h.html()+u:"")+g.text()+u+k.errorCorrect+"</a></li>":f.getElementsByTagName("input")[0]&&0<f.getElementsByTagName("input")[0].name.length?a+="<li><a href='#"+f.getElementsByTagName("input")[0].id+"'>"+m+(0!==h.length?h.html()+u:"")+g.text()+u+k.errorCorrect+"</a></li>":"LEGEND"===f.tagName&&("checkbox"===f.parentElement.getElementsByTagName("input")[0].type||"radio"===f.parentElement.getElementsByTagName("input")[0].type&&0<f.parentElement.getElementsByTagName("input")[0].name.length)?a+="<li><a href='#"+f.parentElement.getElementsByTagName("input")[0].id+"'>"+m+(0!==h.length?h.html()+u:"")+g.text()+u+k.errorCorrect+"</a></li>":a+="<li><a>"+m+(0!==h.length?h.html()+u:"")+g.text()+u+k.errorCorrect+"</a></li>",g.html("<strong>"+m+g.text()+"</strong>")),w=!0):(a+="<li><a href='#"+g.data("element-id")+"'>"+m+(0!==h.length?h.html()+u:"")+g.text()+"</a></li>",g.html("<span class='label label-danger'>"+m+g.text()+"</span>"))}if(a+="</ul>",!y){for(r in n=0,e)if(Object.prototype.hasOwnProperty.call(e,r)){n+=1;break}if(0!==n){for(i=s.length,n=0;n!==i;n+=1)if((o=s[n].parentNode).getAttribute("for")===r){A.getElementById(r).matches(":focus")?p.innerHTML="":(o=o.innerHTML)!==p.innerHTML&&(p.innerHTML=o);break}}else 0!==p.innerHTML.length&&(p.innerHTML="")}w&&setTimeout(function(){var e;0===(t=b.find("#"+v)).length?t=T("<section id='"+v+"' class='alert alert-danger' tabindex='-1'>"+a+"</section>").prependTo(b):t.html()!==a.replace(/'/g,'"').replace(/&#160;/g,"&nbsp;")&&(e=t.find("a").is(":focus"),t.empty().append(a),e)&&t.find("a").last().trigger("focus"),y&&(t.trigger("setfocus.wb"),y=!1)},100)}else 0!==p.innerHTML.length&&(p.innerHTML=""),b.find("#"+v).detach(),w=!1},"invalidHandler":function(){w=y=!0}}),b.on("change","input[type=date], input[type=file], select",function(){b.validate().element(this)}),f.on("click",p+" input[type=reset]",function(e){var e=e.which;e&&1!==e||(a.resetForm(),T("#"+v).detach(),0!==(e=b.closest(".wb-frmvld").find(".arialive")[0]).innerHTML.length&&(e.innerHTML=""),b.find(".has-error").removeClass("has-error"))}),b.find(".wb-server-error").filter(":not( :hidden )").parent().each(function(){this.attributes.for&&0<this.attributes.for.value.length?b.validate().element(T("[id ="+this.attributes.for.value+"]")):T(this).find("input")[0]?b.validate().element(T(this).find("input")[0]):T(this).next(".radio, .checkbox").children("label").children("input")[0]?T(this).find(T(this).next(".radio, .checkbox").children("label").children("input")[0].id)&&b.validate().element(T(this).next(".radio, .checkbox").children("label").children("input")[0]):T(this).next(".radio-inline, .checkbox-inline, .label-inline").children("input")[0]&&T(this).find(T(this).next(".radio-inline, .checkbox-inline, .label-inline").children("input")[0].id)&&b.validate().element(T(this).next(".radio-inline, .checkbox-inline, .label-inline").children("input")[0])}),b.trigger("formLanguages.wb"),d.ready(T(l),u)}})}}))}),d.add(p)}(jQuery,window,document,wb),function(r,n){"use strict";var i="wb-geomap",o="."+i;n.doc.on("timerpoke.wb wb-init.wb-geomap",o,function(e){var t,a,e=n.init(e,i,o);e&&(e=(t=r(e)).find(".wb-geomap-map"),a=t.find(".wb-geomap-legend"),e.height(.8*e.width()),e.append("<div class='geomap-progress'><span class='wb-inv'>"+n.i18n("load")+"</span></div>"),a.append("<div class='skeleton-lgnd-1'><span class='skeleton-lgnd-3'></span></div><div class='skeleton-lgnd-2'><span class='skeleton-lgnd-3'></span></div><div class='skeleton-lgnd-2'><span class='skeleton-lgnd-3'></span></div><div class='skeleton-lgnd-2'><span class='skeleton-lgnd-3'></span></div><div  class='skeleton-lgnd-1'><span class='skeleton-lgnd-3'></span></div>"),e=n.getMode()+".js",Modernizr.load([{"both":["site!deps/proj4"+e,"site!deps/ol"+e,"site!deps/geomap-lib"+e],"complete":function(){t.trigger("geomap.wb")}}]))}),n.add(o)}(jQuery,wb),function(p,o,f){"use strict";function g(t,e,a,r,n,i,o){if(!window.jsonpointer)return setTimeout(function(){g(t,e,a,r,n,i,o)},100);if(i)try{a=jsonpointer.get(a,i)}catch(e){console.error("JSON fetch - Bad JSON selector: "+i),console.error(a),console.error(p("#"+t).get(0))}p("#"+t).trigger({"type":"json-fetched.wb","fetch":{"response":a,"status":r,"xhr":n,"refId":e,"fetchedOpts":o}},this)}var e=o.doc,h={},m={};e.on("json-fetch.wb",function(e){var s,t,a=e.element||e.target,l=e.fetch||{"url":""},r=l.url.split("#"),c=r[0],n=l.nocache,i=l.nocachekey||o.cacheBustKey||"wbCacheBust",d=r[1]||!1,u=l.refId;if(a===e.target||e.currentTarget===e.target){if(a.id||(a.id=o.getId()),s=a.id,d){if(91===(r=d.split("/")[0]).charCodeAt(0))return void p("#"+s).trigger({"type":"postpone.wb-jsonmanager","postpone":{"callerId":s,"refId":u,"dsname":r,"selector":d.substring(r.length)}});l.url=c}n&&(e=i+"="+("nocache"===n?o.guid():o.sessionGUID()),c=-1!==c.indexOf("?")?c+"&"+e:c+"?"+e,l.url=c),Modernizr.load({"load":"site!deps/jsonpointer"+o.getMode()+".js","complete":function(){if(c){if(!l.nocache){if(t=h[c])return void g(s,u,t,"success",void 0,d,l);if(m[c])return void m[c].push({"callerId":s,"refId":u,"selector":d});m[c]=[]}if(l.dataType="json",l.jsonp&&(l.jsonp=!1),l.data){try{l.data="string"==typeof l.data?l.data:JSON.stringify(l.data)}catch(e){throw"JSON fetch - Data being sent to server - "+e}l.method=l.method||"POST",l.contentType=l.contentType||"application/json"}p.ajax(l).done(function(e,t,a){var r,n,i,o;if(!l.nocache)try{h[c]=e}catch(e){return}if(g(s,u,e,t,a,d,l),m[c])for(n=(o=m[c]).length,r=0;r!==n;r+=1)i=o[r],g(i.callerId,i.refId,e,t,a,i.selector,l)}).fail(function(e,t,a){e.responseText=f.sanitize(e.responseText),p("#"+s).trigger({"type":"json-failed.wb","fetch":{"xhr":e,"status":t,"error":a,"refId":u,"fetchOpts":l}},this)},this)}}})}})}(jQuery,wb,DOMPurify),function(l,r,i,d,c,u){"use strict";function p(){o||(e=c.i18n,o={"close":e("close"),"oClose":e("overlay-close"),"tClose":e("close")+e("space")+e("overlay-close")+e("space")+e("esc-key"),"tLoading":e("load"),"gallery":{"tPrev":e("prv-l"),"tNext":e("nxt-r"),"tCounter":e("lb-curr")},"image":{"tError":e("lb-img-err")+' (<a href="url%">)'},"ajax":{"tError":e("lb-xhr-err")+' (<a href="url%">)'}},f={"open":function(){var e,t,a=this.currItem,r=this.contentContainer,n=this.wrap,i=n.find(".mfp-container"),o=i.parent(),s=n.find(".modal-dialog"),l=n.find(".mfp-arrow"),c=l.length;for(y(s),b.find("body").addClass("wb-modal"),b.find(m).attr("aria-hidden","true"),e=0;e!==c;e+=1)(t=l[e]).innerHTML+="<span class='wb-inv'> "+t.title+"</span>";"image"===a.type?r.find(".mfp-bottom-bar").attr("id","lbx-title"):r.attr("role","document"),this.contentContainer.attr("data-pgtitle",d.getElementsByTagName("H1")[0].textContent),w(n),o.is("dialog")?o.attr("open","open"):i.wrap("<dialog class='mfp-container' open='open'></dialog>")},"close":function(){b.find("body").removeClass("wb-modal"),b.find(m).removeAttr("aria-hidden"),this.wrap.find("dialog").removeAttr("open")},"change":function(){var e,t,a,r,n=this.currItem,i=this.contentContainer;"image"===n.type?(e=n.el,n=n.img,t=i.find(".mfp-bottom-bar"),e?(a=e.find("img"),n.attr("alt",a.attr("alt")),(r=a.attr("aria-describedby"))&&n.attr("aria-describedby",r),(r=a.attr("longdesc"))&&n.attr("longdesc",r),(a=e.attr("data-title"))&&null!==(r=d.getElementById(a))&&t.find(".mfp-title").html(r.innerHTML)):n.attr("alt",t.find(".mfp-title").html())):i.find(".modal-title, h1").first().attr("id","lbx-title"),i.attr("aria-labelledby","lbx-title")},"parseAjax":function(e){var t=this.currItem,a=t.el,t=t.src.split("#")[1],a=(a?a.data("wbLbxFilter"):u)||!!t&&"#"+t;e.data=r.sanitize(e.data),t=a?l("<div>"+e.data+"</div>").find(a):l(e.data),y(t),t.find(".modal-title, h1").first().attr("id","lbx-title"),e.data=t},"ajaxContentAdded":function(){w(this.wrap)}}),Modernizr.load({"load":"site!deps/jquery.magnific-popup"+c.getMode()+".js","testReady":function(){return l.magnificPopup},"complete":function(){l.extend(!0,l.magnificPopup.defaults,o,t),b.trigger(h)}})}var f,e,o,g="wb-lbx",s="."+g,h="deps-loaded"+s,m="#wb-tphp, body > header, body > main, body > footer",b=c.doc,t={"closeMarkup":"<button type='button' class='mfp-close'><span class='mfp-close' aria-hidden='true'>&times;</span><span class='wb-inv'>%title%</span></button>"},y=function(e){var t,a,r,n,i;null!==e&&e.hasClass("modal-dialog")&&(i=(a=(t=e.find(".modal-footer").first()).length)&&0!==l(t).find(".popup-modal-dismiss").length,r=o.close,n=o.oClose,i||(a||(t=d.createElement("div")).setAttribute("class","modal-footer"),i="<button type='button' class='btn btn-sm btn-primary pull-left popup-modal-dismiss'>"+r+"<span class='wb-inv'>"+n+"</span></button>",l(t).append(i),a)||l(t).insertAfter(e.find(".modal-body")))},w=function(n){n.on("keydown",function(e){var t,a,r;9===e.which&&(t=(a=n.find(".mfp-container :tabbable")).first()[0],a=a.last()[0],r=l(d.activeElement)[0],e.shiftKey||r!==a?!e.shiftKey||r!==t&&r!==n[0]||(e.preventDefault(),a.focus()):(e.preventDefault(),t.focus()))})};b.on("timerpoke.wb wb-init.wb-lbx",s,function(e){var n,e=c.init(e,g,s);e&&(n=e.id,b.one(h,function(){var e,t=d.getElementById(n),a=l(t),r={};t&&(r.callbacks=f,"a"!==t.nodeName.toLowerCase()?(r.delegate="a:not("+s+"-skip)",e=t.getElementsByTagName("a")[0],-1!==t.className.indexOf("-gal")&&(r.gallery={"enabled":!0})):e=t,e&&("#"===e.getAttribute("href").charAt(0)?r.type="inline":-1!==e.className.indexOf("lbx-iframe")?r.type="iframe":0===e.getElementsByTagName("img").length?r.type="ajax":r.type="image",-1!==t.className.indexOf("lbx-modal")&&(r.modal=!0),-1!==t.className.indexOf("lbx-ajax")&&(r.type="ajax"),-1!==t.className.indexOf("lbx-image")&&(r.type="image"),-1!==t.className.indexOf("lbx-inline")&&(r.type="inline"),r=l.extend(!0,r,i[g],c.getData(a,g)),a.magnificPopup(r).data("wbLbxFilter",r.filter)),c.ready(a,g))}),p())}),b.on("click",".mfp-wrap a[href^='#']",function(e){var t=e.which,a=e.currentTarget;t&&1!==t||(t=l(a).closest(".mfp-wrap"),(a=d.getElementById(a.getAttribute("href").substring(1)))&&!l.contains(t[0],a)&&(e.stopPropagation?e.stopImmediatePropagation():e.cancelBubble=!0,l.magnificPopup.close(),l(a).trigger("setfocus.wb")))}),l(d).on("click",".popup-modal-dismiss",function(e){this.hasAttribute("target")||e.preventDefault(),l.magnificPopup.close()}),l(d).on("open"+s,function(e,t,a,r,n){var i,o,s;e.namespace===g&&(i=1<t.length,o=!(!a||i)&&a,s=r?function(){return r[l.magnificPopup.instance.index]}:"title",e.preventDefault(),b.one(h,function(){l.magnificPopup.open({"items":t,"modal":o,"gallery":{"enabled":i},"image":{"titleSrc":s},"callbacks":f,"ajax":n}),c.ready(u,g)}),p())}),c.add(s)}(jQuery,DOMPurify,window,document,wb),function(y,w,v){"use strict";function n(h,m){function e(){var e,t,a,r,n,i=m&&"string"===m.attr("data-type")?m:h,o=i.find(".menu"),s=o.find("> li > a"),l=h.data("trgt"),c=y("#wb-sec"),d=y("#wb-lng"),u=w.getElementById("wb-srch"),p="",l=w.getElementById(l),f=y(l),g=[];if(null!==u&&(p+="<section class='srch-pnl'>"+u.innerHTML.replace(/h2>/i,"h3>").replace(/(for|id)="([^"]+)"/gi,"$1='$2-imprt'")+"</section>"),0!==d.length){for(a=d.find("li:not(.curr)"),r=a.length,p+="<section class='lng-ofr'><h3>"+d.children("h2").html()+"</h3><ul class='list-inline'>",n=0;n!==r;n+=1)p+=a[n].innerHTML.replace(/(<a\s.*<\/a>?)/,"<li>$1</li>");p+="</ul></section>"}0===c.length&&0===o.length&&0===b.length||(0!==c.length&&(g.push([c.find("ul").filter(":not(li > ul)").find(" > li > *:first-child").get(),"sec-pnl",c.find("h2").html()]),0===c.find(".wb-navcurr").length)&&c.trigger(k,A),0!==o.length&&(o.attr("role")||o.attr("role","menubar"),g.push([s.get(),"sm-pnl",i.find("h2").html()])),0!==b.length&&(g.push([b.find("h3, a").not("section a"),"info-pnl",b.find("h2").html()]),0===b.find(".wb-navcurr").length)&&b.trigger(k,A),p+=function(e){for(var t,a,r,n,i,o,s,l,c="",d=e.length,u=0;u!==d;u+=1){for(t="",s=(r=(a=e[u])[0]).length,o=0;o!==s;o+=1)n=r[o],t+=0!==(l=(i=y(n.parentNode).find("> ul > li")).length)?E(n,o,s,i,l):"<li class='no-sect'>"+("li"===(i=n.parentNode).nodeName.toLowerCase()?i.innerHTML:i.getElementsByTagName("a")[0]===n.getElementsByTagName("a")[0]?n.innerHTML:"<a href='"+i.getElementsByTagName("a")[0].href+"'>"+n.innerHTML+"</a>").replace(/(<a\s)/,"$1 class='mb-item' role='menuitem' aria-setsize='"+s+"' aria-posinset='"+(o+1)+"' tabindex='-1' ")+"</li>";c+="<nav role='navigation' typeof='SiteNavigationElement' id='"+a[1]+"' class='"+a[1]+" wb-menu wb-menu-inited'><h3>"+a[2]+"</h3><ul class='list-unstyled mb-menu' role='menu'>"+t+"</ul></nav>"}return c.replace(/['"]?list-group-item['"]?/gi,'""')}(g)),l.innerHTML="<header class='modal-header'><div class='modal-title'>"+w.getElementById("wb-glb-mn").getElementsByTagName("h2")[0].innerHTML+"</div></header><div class='modal-body'>"+p+"</div>",l.className+=" wb-overlay modal-content overlay-def wb-panel-r",0<y.active?y(w).ajaxStop(function(){x(f)}):x(f),i.find(":discoverable").attr("tabindex","-1"),0!==s.length&&(s[0].setAttribute("tabindex","0"),C(s),s.filter("[aria-haspopup=true]").append("<span class='expicon glyphicon glyphicon-chevron-down'></span>")),h.html(i.html()),setTimeout(function(){for(h.trigger(k,A),f.find("#sm-pnl").trigger(k,A),e=f.find(".wb-navcurr"),r=e.length,n=0;n!==r;n+=1)(t=e.eq(n)).hasClass(".mb-item")||(t=t.closest("details").children("summary").addClass("wb-navcurr"));"true"===(t=f.find("#sec-pnl .wb-navcurr.mb-item")).attr("aria-haspopup")&&t.trigger("click").parent().prop("open","open"),v.ready(h,T)},1)}var b=y("#wb-info"),t=b.find("[data-ajax-replace],[data-ajax-append],[data-ajax-prepend]").length,a=0;0===t?e():b.on("wb-contentupdated ajax-failed.wb",function(){(a+=1)===t&&e()})}function x(e){e.trigger("wb-init.wb-overlay").find("summary").attr("tabindex","-1").trigger(t),e.find(".mb-menu > li:first-child").find(".mb-item").attr("tabindex","0")}function d(e,t,a){var r=e.length,t=e.index(t)+a;e.eq(t===r?0:-1===t?r-1:t).trigger(h)}function u(e,t){e.removeClass("sm-open").children(".open").removeClass("open").attr({"aria-hidden":"true","aria-expanded":"false"}).find("details").removeAttr("open").children("ul").attr({"aria-hidden":"true","aria-expanded":"false"}),t&&e.removeClass("active")}function p(e,t){var a=t.children("a");u(e.find(".active"),!0),t.addClass("active"),"true"===a.attr("aria-haspopup")&&t.addClass("sm-open").children(".sm").addClass("open").attr({"aria-hidden":"false","aria-expanded":"true"})}function f(e,t){for(var a,r=t.length,n=String.fromCharCode(e),i=0;i!==r;i+=1)if((a=t[i]).innerHTML.charAt(0)===n)return y(a).trigger(h),!0;return!1}var r,T="wb-menu",g="."+T,A=w.getElementById("wb-bc"),k="navcurr.wb",h="setfocus.wb",t="wb-init.wb-details",m="> a, > details > summary",e=v.doc,i=0,C=function(e){for(var t,a,r=e.length,n=0;n!==r;n+=1)a=(t=e.eq(n)).siblings("ul"),t.attr({"aria-posinset":n+1,"aria-setsize":r,"role":"menuitem"}),0!==a.length&&(t.attr("aria-haspopup","true"),a.attr({"aria-expanded":"false","aria-hidden":"true"}),C(a.children("li").find(m)))},E=function(e,t,a,r,n){for(var i,o,s,l,e=y(e),c="' aria-posinset='",d=" role='menuitem' aria-setsize='",u="<li><details><summary class='mb-item"+(e.hasClass("wb-navcurr")||0!==e.children(".wb-navcurr").length?" wb-navcurr'":"'")+d+a+c+(t+1)+"' aria-haspopup='true'>"+e.text()+"</summary><ul class='list-unstyled mb-sm' role='menu' aria-expanded='false' aria-hidden='true'>",p=0;p!==n;p+=1)i=(s=(o=r.eq(p)).find(m))[0],l=(s=s.parent().find("> ul > li")).length,i&&0===l&&"a"===i.nodeName.toLowerCase()?u+="<li>"+o[0].innerHTML.replace(/(<a\s)/,"$1"+d+n+c+(p+1)+"' tabindex='-1' ")+"</li>":u+=E(i,p,n,s,s.length);return u+"</ul></details></li>"};e.on("timerpoke.wb wb-init.wb-menu ajax-fetched.wb ajax-failed.wb",g,function(e){var t,a,r=e.type;switch(r){case"ajax-fetched":case"ajax-failed":return t=e.target,e.currentTarget===t&&(t=y(t),n(t,"ajax-fetched"===r?e.fetch.pointer:t)),!1;case"timerpoke":case"wb-init":t=e,(t=v.init(t,T,g))&&((t=y(t)).attr("id")||t.attr("id",T+"-"+i),i+=1,(a=t.data("ajax-fetch"))?t.trigger({"type":"ajax-fetch.wb","fetch":{"url":a}}):t.data("ajax-replace")||t.data("ajax-append")||t.data("ajax-prepend")||n(t,t))}return!0}),e.on("mouseleave",g+" .menu",function(e){var t=y(e.currentTarget);clearTimeout(r),r=setTimeout(function(){u(t.find(".active"),!0)},500)}),e.on("mouseenter",g+" .sm",function(){"true"===y(this).attr("aria-expanded")&&clearTimeout(r)}),e.on("click",g+" .item[aria-haspopup=true]",function(e){var t=e.which;t&&1!==t||(e.preventDefault(),(t=y(this)).parent().hasClass("sm-open"))||t.trigger("focusin")}),e.on("click",g+" [role=menu] [aria-haspopup=true]",function(e){var t,e=e.currentTarget,a=e.parentNode,r=a.getElementsByTagName("ul")[0],n="false"===r.getAttribute("aria-hidden");n||(y(a).closest("[role^='menu']").find("[aria-hidden=false]").parent().find("[aria-haspopup=true]").not(e).trigger("click"),a=w.getElementById("mb-pnl"),t=e.offsetTop,y.contains(a,e)&&t<a.scrollTop&&(a.scrollTop=t)),r.setAttribute("aria-expanded",!n),r.setAttribute("aria-hidden",n)}),e.on("click",function(e){var t=e.which;""!==e.type&&t&&1!==t||0!==(t=y(g+" .sm-open")).length&&0===y(e.target).closest(g).length&&u(t,!0)}),e.on("mouseover focusin",g+" .item",function(e){var t=y(e.currentTarget).parent(),a=t.closest(g);clearTimeout(r),"focusin"===e.type?p(a,t):r=setTimeout(function(){p(a,t)},500)}),e.on("keydown",g+" [role=menuitem]",function(e){var t,a,r,n=e.currentTarget,i=e.which,o=y(n),s="true"===o.attr("aria-haspopup"),l=o.parent().closest("[role^='menu']"),c="menubar"===l.attr("role");e.ctrlKey||e.altKey||e.metaKey||(9===i?u(y(g+" .active"),!0):"A"!==n.nodeName||!n.hasAttribute("href")||13!==i&&32!==i?c?37===i||39===i?(e.preventDefault(),d(l.find("> li > a"),o,37===i?-1:1)):!s||13!==i&&32!==i&&38!==i&&40!==i?27===i?(e.preventDefault(),u(l.closest(g).find(".active"),!1)):64<i&&i<91&&(e.preventDefault(),f(i,o.parent().find("> ul > li > a, > ul > li > details > summary").get())):(e.preventDefault(),(r=(a=o.parent()).find(".sm")).hasClass("open")||p(l.closest(g),a),r.children("li").eq(0).find(m).trigger(h)):(c=m,38===i||40===i?(e.preventDefault(),d(l.children("li").find(c),o,38===i?-1:1)):!s||13!==i&&32!==i&&39!==i?27===i||37===i||39===i?(s=(a=l.parent()).closest("[role^='menu']"),37!==i&&39!==i||e.preventDefault(),"menubar"===s.attr("role")?(t=l.siblings("a"),27===i?(e.preventDefault(),t.trigger(h),setTimeout(function(){u(t.parent(),!1)},100)):"menubar"===s.attr("role")&&d(s.find("> li > a"),t,37===i?-1:1)):39!==i&&(r=0!==s.length?l:o,0!==s.length?(e.preventDefault(),l.closest("li").find(c).trigger("click").trigger("setfocus.wb")):"false"===o.parent().children("ul").attr("aria-hidden")&&(e.preventDefault(),o.trigger("click").trigger("setfocus.wb")))):64<i&&i<91&&(e.preventDefault(),a=o.closest("li"),f(i,a.nextAll().find(c).get())||f(i,a.prevAll().find(c).get())):(a=o.parent(),e.stopImmediatePropagation(),e.preventDefault(),n.nodeName.toLowerCase("summary")&&(a.attr("open")||(y(parent).closest("[role^='menu']").find("[aria-hidden=false]").parent().find("[aria-haspopup=true]").not(n).trigger("click"),r=w.getElementById("mb-pnl"),s=n.offsetTop,y.contains(r,n)&&s<r.scrollTop&&(r.scrollTop=s),o.trigger("click")),a.children("ul").attr({"aria-expanded":"true","aria-hidden":"false"}).find("[role=menuitem]:first").trigger("setfocus.wb")))):(e.preventDefault(),n.click(),u(y(g+" .active"),!0)))}),e.on("keyup",g+" [role=menuitem]",function(e){return e.preventDefault(),!1}),e.on("mediumview.wb largeview.wb xlargeview.wb",function(){var e=w.getElementById("mb-pnl");e&&"false"===e.getAttribute("aria-hidden")&&y(e).trigger("close.wb-overlay")}),v.add(g)}(jQuery,(window,document),wb),function(w,l,p,v,x){"use strict";function T(e){var t,a,r=2,n="",i=function(e,t){return new Array(Math.max(t-String(e).length+1,0)).join(0)+e};for(e=Math.floor(e);0<=r;)t=Math.pow(60,r),""!==n&&(n+=":"),n+=i(a=Math.floor(e/t),2),e-=t*a,--r;return n}function s(e){for(var t,a,r,n,i=[],o=".wb-tmtxt",s=e.find(o),l=s.length,c=0;c!==l;c+=1)n=r=-1,(t=w(s[c])).attr("data-begin")!==x?(r=I(t.attr("data-begin")),n=t.attr("data-end")!==x?I(t.attr("data-end")):I(t.attr("data-dur"))+r):t.attr("data")!==x&&(a=t.attr("data").replace(/(begin|dur|end)/g,'"$1"').replace(/'/g,'"'),a=w.parseJSON(a),r=I(a.begin),n=a.end!==x?I(a.end):I(a.dur)+r),(t=t.clone()).find(o).detach(),i[i.length]={"text":t.html(),"begin":r,"end":n};return i}function c(r,e){w.ajax({"url":e,"dataType":"html","dataFilter":function(e){return e.replace(/<img|object [^>]*>/g,"")},"success":function(e){e=-1!==e.indexOf("<html")?s(w(l.sanitize(e,{"WHOLE_DOCUMENT":!0}))):function(e){for(var t,a,r,n=[],i=(new DOMParser).parseFromString(e,"application/xml").querySelectorAll("[begin]"),o=i.length,s=0;s!==o;s+=1)t=i[s],a=I(t.getAttribute("begin")+""),r=t.hasAttribute("end")?I(t.getAttribute("end")+""):I(t.getAttribute("dur")+"")+a,n[n.length]={"text":l.sanitize(t.textContent),"begin":a,"end":r};return n}(e);e.length?r.trigger({"type":g,"captions":e}):r.trigger({"type":h})},"error":function(e,t,a){r.trigger({"type":h,"error":a})}})}function d(e,t){var a,r;switch(e){case"play":try{this.object.play()}catch(e){this.object.doPlay()}break;case"pause":try{this.object.pause()}catch(e){this.object.doPause()}break;case"getCaptionsVisible":return w(this).hasClass(N);case"setCaptionsVisible":a=w(this),t?a.addClass(N):a.removeClass(N),a.trigger(m);break;case"fullscreen":this.object.requestFullscreen?this.object.requestFullscreen():this.object.webkitRequestFullscreen?this.object.webkitRequestFullscreen():this.object.msRequestFullscreen&&this.object.msRequestFullscreen();break;case"getBuffering":return this.object.buffering||!1;case"setBuffering":this.object.buffering=t;break;case"getPreviousTime":return this.object.previousTime;case"setPreviousTime":this.object.previousTime=t;break;default:switch(r=e.charAt(3).toLowerCase()+e.substr(4),e.substr(0,3)){case"get":return"function"!=typeof this.object[r]?this.object[r]:this.object[r]();case"set":"function"!=typeof this.object[r]?this.object[r]=t:this.object[e](t)}}}function o(e){var t,a,r,n=e.target.getIframe(),i=w(n);switch(e.data){case null:i.trigger("canplay").trigger("durationchange"),(t=i.parentsUntil(C).parent()).data("putMutedOnInit")&&(P.call(t.get(0),"setMuted",!0),t.data("putMutedOnInit",!1));break;case-1:e.target.unMute(),i.trigger("durationchange");break;case 0:i.trigger("ended"),n.timeline=clearInterval(n.timeline);break;case 1:r=(a=(t=i.parentsUntil(C).parent()).get(0)).player("getMuted"),n.dataset.L2&&P.call(a,"setCaptionsVisible",t.hasClass(N)),i.trigger("canplay").trigger("play").trigger("playing"),r&&P.call(a,"setMuted",!0),n.timeline=setInterval(function(){i.trigger("timeupdate")},250);break;case 2:i.trigger("pause"),n.timeline=clearInterval(n.timeline);break;case 3:n.timeline=clearInterval(n.timeline)}}function n(){w(C+" object, "+C+" iframe, "+C+" video").trigger(j)}var u,t,A,a,k="wb-mltmd",C="."+k,e="wb-init"+C,r=C+" .wb-mm-ctrls",i=C+" .display,"+r,f="ready.youtube",g="ccloaded"+C,h="ccloadfail"+C,m="ccvischange"+C,b="renderui"+C,y="inited"+C,E="youtube"+C,j="resize"+C,S="templateloaded"+C,N="cc_on",O=["durationchange","playing","pause","ended","volumechange","timeupdate","waiting","canplay","seeked","progress",g,h,m,"cuepoint"+C].join(" "),D=v.doc,M=v.win,I=function(e){var t,a,r,n;if(e===x)return-1;if("s"===e.charAt(e.length-1))return parseFloat(e.substring(0,e.length-1));for(t=n=0,r=(a=e.split(":").reverse()).length;t<r;t+=1)n+=(0===t?parseFloat(a[t]):parseInt(a[t],10))*Math.pow(60,t);return n},L=(a=new RegExp("{{\\s*([a-z0-9_$][\\.a-z0-9_]*)\\s*}}","gi"),function(e,o){return e.replace(a,function(e,t){for(var a=t.split("."),r=a.length,n=o,i=0;i<r;i+=1){if((n=n[a[i]])===x)throw"tim: '"+a[i]+"' not found in "+e;if(i===r-1)return n}})}),P=function(e,t){var a,r=w(this.object.getIframe());switch(e){case"play":return this.object.wasMutedPlay=this.object.isMuted(),this.object.playVideo();case"pause":return this.object.pauseVideo();case"getPaused":return-1===(a=this.object.getPlayerState())||0===a||2===a||5===a;case"getPlayed":return-1<this.object.getPlayerState();case"getEnded":return 0===this.object.getPlayerState();case"getDuration":return this.object.getDuration();case"getCurrentTime":return this.object.getCurrentTime();case"setCurrentTime":return this.object.seekTo(t,!0);case"fullscreen":return this.object.getIframe().requestFullscreen();case"getMuted":return!this.object.playedOnce&&this.object.wasMutedPlay?(a=this.object.wasMutedPlay,this.object.playedOnce=!0,a):this.object.isMuted();case"setMuted":t?this.object.mute():this.object.unMute(),setTimeout(function(){r.trigger("volumechange")},v.isReady?50:500);break;case"getVolume":return this.object.getVolume()/100;case"setVolume":this.object.setVolume(100*t),setTimeout(function(){r.trigger("volumechange")},50);break;case"getCaptionsVisible":return w(this).hasClass(N);case"setCaptionsVisible":if(t){w(this).addClass(N);try{this.object.loadModule("cc"),this.object.setOption("cc","track",{"languageCode":this.object.getOption("cc","tracklist")[0].languageCode})}catch(e){this.object.loadModule("captions"),this.object.setOption("captions","track",{"languageCode":this.object.getOption("captions","tracklist")[0].languageCode})}}else w(this).removeClass(N),this.object.unloadModule("cc"),this.object.unloadModule("captions");r.trigger("ccvischange")}};D.on("timerpoke.wb "+e,C,function(e){e=v.init(e,k,C);e&&(A||(p.addEventListener("message",function(e){var t,a,r,n,i;try{if((t=JSON.parse(e.data)).event&&"infoDelivery"===t.event&&t.info&&t.info.playerState)for(n=(a=document.getElementsByTagName("iframe")).length,r=0;r<n;r++)if((i=a[r]).dataset.L2&&i.contentWindow===e.source){o.call(i,{"target":i.parentElement.parentElement.object,"data":t.info.playerState});break}}catch(e){}}),t=v.i18n,A={"play":t("mmp-play"),"pause":t("pause"),"volume":t("volume"),"cc_on":t("cc","on"),"cc_off":t("cc","off"),"cc_error":t("cc-err"),"fs":t("fs"),"mute_on":t("mute","on"),"mute_off":t("mute","off"),"duration":t("dur"),"position":t("pos")}),u===x?(u="",w(e).trigger({"type":"ajax-fetch.wb","fetch":{"url":v.getPath("/assets")+"/mediacontrols.html"}})):""!==u&&w(e).trigger(S))}),M.on("resize",n),D.on("ready",n),D.on("ajax-fetched.wb "+S,C,function(e){var t=w(this);"ajax-fetched"===e.type&&(u=e.fetch.pointer.html(),t=w(C)),t.trigger({"type":y})}),D.on(y,C,function(e){if(e.namespace===k){var t=w(this),e=t.children("audio, video").eq(0),a=e.children("track[kind='captions']").attr("src")||x,r=t.attr("id"),n=e.attr("id")||r+"-md",i=e.is("audio")?"audio":"video",o=e.attr("title")||"",s="video"==i?e.attr("width")||e.width():0,l="video"==i?e.attr("height")||e.height():0,c=v.getData(t,k),d=w.extend({"media":e,"captions":a,"id":r,"mId":n,"type":i,"title":o,"height":l,"width":s},A),a=e.get(0),r=p.youTube,u=v.i18n;if(e.attr("id")===x&&e.attr("id",n),c!==x&&(d.shareUrl=c.shareUrl,d.fullscreen=c.fullscreenBtn||!1),t.addClass(i),0<e.find("[type='video/youtube']").length)return o=v.getUrlParts(t.find("[type='video/youtube']").attr("src")),d.youTubeId=o.params.v||o.pathname.substr(1),d.isInitMuted=e.get(0).muted,!1===r.ready?D.one(f,function(){t.trigger(E,d)}):t.trigger(E,d),Modernizr.load({"load":"https://www.youtube.com/iframe_api","complete":function(){setTimeout(function(){var e;p.performance.getEntriesByType("resource").filter(function(e){return"iframe"===e.initiatorType&&e.name.includes(d.youTubeId)}).length<1&&(v.isReady||(e=w("<div aria-live='polite' class='pstn-lft-xs bg-dark text-white'><p class='mrgn-tp-md mrgn-rght-md mrgn-bttm-md mrgn-lft-md'>"+u("msgYoutubeNotLoad")+"</p></div>"),t.prepend(e),d.notifyText=e,v.ready(t,k)))},1e3)}});null===a.error&&""!==a.currentSrc&&a.currentSrc!==x&&(t.trigger(b,[i,d]),v.ready(t,k))}}),D.on(E,C,function(e,t){var a,r;e.namespace===k&&(r=t.mId,a=w(e.currentTarget),e=new YT.Player(r,{"videoId":t.youTubeId,"playerVars":{"autoplay":0,"controls":0,"origin":v.pageUrlParts.host,"modestbranding":1,"rel":0,"showinfo":0,"html5":1,"cc_load_policy":1},"events":{"onReady":function(e){t.notifyText&&t.notifyText.hide(),n(),o(e),v.isReady||v.ready(a,k)},"onStateChange":o,"onApiChange":function(){var e=a.get(0);e.player("setCaptionsVisible",e.player("getCaptionsVisible"))},"onError":function(){console.warn("There is an issue loading the Youtube player")}}}),a.addClass("youtube"),r=a.find("#"+r).attr("tabindex",-1),t.media=r,t.ytPlayer=e,t.fullscreen&&a.attr("data-fullscreen-btn",!0),r.on("load",function(e){e=e.currentTarget.dataset;e.L1?e.L2=!0:e.L1=!0}),a.trigger(b,["youtube",t]))}),D.on(b,C,function(e,t,a){if(e.namespace===k){var r,n=w(e.currentTarget),e=v.getUrlParts(a.captions),i=v.getUrlParts(p.location.href),o=a.media;if(o.after(L(u,a)).wrap('<div class="display"></div>'),(r=o.is("object")?o.children(":first-child"):o).on(O,function(e){n.trigger(e)}),this.object=a.ytPlayer||o.get(0),this.player=a.ytPlayer?P:d,"youtube"===t||isNaN(this.player("getDuration"))||r.trigger("durationchange"),n.find("progress").trigger("wb-init.wb-progress"),n.find("input[type='range']").trigger("wb-init.wb-slider"),a.shareUrl!==x&&w("<div class='wb-share' data-wb-share='{\"type\": \""+("audio"===t?t:"video")+'", "title": "'+a.title.replace(/'/g,"&apos;")+'", "url": "'+a.shareUrl+'", "pnlId": "'+a.id+"-shr\"}'></div>").insertBefore(o.parent()).trigger("wb-init.wb-share"),a.isInitMuted?n.data("putMutedOnInit",!0):!a.ytPlayer&&this.object.muted&&o.trigger("volumechange"),a.captions===x)return 1;i.absolute.replace(i.hash||"#","")!==e.absolute.replace(e.hash||"#","")?c(o,e.absolute):(r=o,t=w("#"+v.jqEscape(e.hash.substring(1))),(t=s(t)).length?r.trigger({"type":g,"captions":t}):r.trigger({"type":h})),a.fullscreen&&n.attr("data-fullscreen-btn",!0)}}),D.on("click",C,function(e){var t=w(e.target),a=t.attr("class")||"";if(2===e.which||3===e.which)return!0;/playpause|-play|-pause|display/.test(a)||t.is("object")||t.is("video")?this.player("getPaused")||this.player("getEnded")?this.player("play"):this.player("pause"):!/(^|\s)cc\b|-subtitles/.test(a)||t.attr("disabled")||t.parent().attr("disabled")?/\bmute\b|-volume-(up|off)/.test(a)?this.player("setMuted",!this.player("getMuted")):t.is("progress")||t.hasClass("progress")||t.hasClass("progress-bar")?this.player("setCurrentTime",this.player("getDuration")*((e.pageX-t.offset().left)/t.width())):/\brewind\b|-backward/.test(a)?this.player("setCurrentTime",this.player("getCurrentTime")-.05*this.player("getDuration")):/\bfastforward\b|-forward/.test(a)?this.player("setCurrentTime",this.player("getCurrentTime")+.05*this.player("getDuration")):a.includes("cuepoint")?w(this).trigger({"type":"cuepoint","cuepoint":t.data("cuepoint")}):/fullscreen|fs/.test(a)&&this.player("fullscreen"):this.player("setCaptionsVisible",!this.player("getCaptionsVisible"))}),D.on("input change",C,function(e){var t=e.target;w(t).hasClass("volume")&&(e.currentTarget.player("setMuted",!1),e.currentTarget.player("setVolume",t.value/100))}),D.on("keydown",i,function(e){var t=e.currentTarget.parentNode,a=e.which,r=0,n=w(t);if(!(e.ctrlKey||e.altKey||e.metaKey)){switch(a){case 32:(w(e.target).hasClass("mute")||"INPUT"===e.target.nodeName?n.find(".mute"):w(e.target).hasClass("fs")?n.find(".fs"):w(e.target).hasClass("cc")?n.find(".cc"):n.find(".playpause")).trigger("click");break;case 37:t.player("setCurrentTime",this.parentNode.player("getCurrentTime")-.05*this.parentNode.player("getDuration"));break;case 39:t.player("setCurrentTime",this.parentNode.player("getCurrentTime")+.05*this.parentNode.player("getDuration"));break;case 38:r=Math.round(100*t.player("getVolume"))/100+.05,t.player("setVolume",r<1?r:1);break;case 40:r=Math.round(100*t.player("getVolume"))/100-.05,t.player("setVolume",0<r?r:0);break;default:return!0}return!1}}),D.on("keyup",r,function(e){if(32===e.which&&!(e.ctrlKey||e.altKey||e.metaKey))return!1}),D.on("wb-activate",C,function(){this.player("play")}),D.on("closed.wb-overlay",".wb-overlay",function(e){e=e.currentTarget.querySelector(C);e&&e.player("pause")}),D.on(O,C,function(e,t){var a,r,n,i,o,s=e.currentTarget,l=e.type,c=e.namespace,d=w(s),u="<span class='wb-inv'>",p="</span>";switch(l){case"playing":case"pause":case"ended":n="playing"===l,r=(a=d.find(".playpause")).data("state-"+(n?"off":"on")),n?(d.addClass("playing"),d.find(".progress").addClass("active")):("ended"===l&&(this.loading=clearTimeout(this.loading)),d.removeClass("playing")),a.attr("title",r).children("span").toggleClass("glyphicon-play",!n).toggleClass("glyphicon-pause",n).html(u+r+p);break;case"volumechange":n=s.player("getMuted"),r=(a=d.find(".mute")).data("state-"+(n?"off":"on")),o=100*s.player("getVolume"),a.attr({"title":r,"aria-pressed":n}).children("span").toggleClass("glyphicon-volume-up",!n).toggleClass("glyphicon-volume-off",n).html(u+r+p),(i=d.find("input[type='range']"))[0].value=n?0:o,i.trigger("wb-update.wb-slider");break;case"timeupdate":if(n=s.player("getCurrentTime"),d.find("progress").attr("value",Math.round(n/s.player("getDuration")*1e3)/10).trigger("wb-update.wb-progress"),d.find(".wb-mm-tmln-crrnt span:nth-child(2)").text(T(n)),d.hasClass(N)&&w.data(s,"captions")!==x){var f,g,h=d.find(".wb-mm-cc"),m=n,b=w.data(s,"captions"),y=b.length;for(h.html("&#160;"),g=0;g<y;g+=1)m>=(f=b[g]).begin&&m<=f.end&&h.html(w("<div>"+f.text+"</div>"))}break;case"durationchange":d.find(".wb-mm-tmln-ttl span:nth-child(2)").text(T(s.player("getDuration"))),(o=v.pageUrlParts.params[e.target.id])&&(o=I(o),s.player("setCurrentTime",o));break;case"ccloaded":c===k&&w.data(s,"captions",e.captions);break;case"ccloadfail":c!==k||d.hasClass("errmsg")||d.addClass("cc_on errmsg").find(".wb-mm-cc").append("<div>"+A.cc_error+"</div>").end().find(".cc").attr("disabled","").removeAttr("aria-pressed");break;case"ccvischange":c===k&&(i=s.player("getCaptionsVisible"),r=(a=d.find(".cc")).data("state-"+(i?"off":"on")),a.attr({"title":r,"aria-pressed":i}).children("span").html(u+r+p));break;case"waiting":t||D.off("progress",C),this.loading=setTimeout(function(){d.addClass("waiting")},500);break;case"canplay":case"seeked":this.loading=clearTimeout(this.loading),d.removeClass("waiting");break;case"cuepoint":s.player("setCurrentTime",I(e.cuepoint))}}),D.on("progress",C,function(e){var e=e.currentTarget,t=w(e);!1===this.player("getPaused")&&this.player("getCurrentTime")===this.player("getPreviousTime")?!1===e.player("getBuffering")&&(e.player("setBuffering",!0),t.trigger("waiting",!0)):!0===e.player("getBuffering")&&(e.player("setBuffering",!1),t.trigger("canplay",!0)),e.player("setPreviousTime",e.player("getCurrentTime"))}),D.on(j,C,function(e){var t,a;e.namespace===k&&(a=e.target,t=w(a),w(e.currentTarget).hasClass("video"))&&(0===a.videoWidth||a.videoWidth===x?(e=t.attr("height")/t.attr("width"),a=Math.round(t.width()*(isNaN(e)?.5625:e)),t.css("height",a+"px")):t.css("height",""))}),p.onYouTubeIframeAPIReady=function(){p.youTube.ready=!0,D.trigger(f)},p.youTube={"ready":!1},v.add(C)}(jQuery,DOMPurify,window,wb),function(T,A,k){"use strict";var C,E,j="wb-navcurr";k.doc.on("navcurr.wb",function(e,t,a){if("wb"===e.namespace){var r,n,i,o,s,l,c,d,u,p,f,g,e=k.init(e.target,j,".wb-navcurr"),h=e.getElementsByTagName("a"),m=[],b=[],y=A.location,w=y.hostname+y.pathname.replace(/^([^/])/,"/$1"),v=y.search,x=!1,y=a||j;if(e){for(n=h.length-1;-1!==n;--n)if(null!==(s=(o=h[n]).getAttribute("href"))&&0!==s.length&&"#"!==s.charAt(0)){if(l=o.hostname+o.pathname.replace(/^([^/])/,"/$1"),d=(c=o.search).length,w.slice(-l.length)===l&&(0===d||v.slice(-d)===c)){x=!0;break}m.push(o),b.push(l)}if(!x&&t)for(C?(u=C,p=E):(u=[],p=[],(r=(a=(t.jquery?t[0]:t).getElementsByTagName("li")).length)&&(s=(t=(o=a[r-1]).firstChild)&&"A"===t.nodeName?t.getAttribute("href"):"")&&"#"!==s.charAt(0)&&(u.push(t),p.push(t.hostname+t.pathname.replace(/^([^/])/,"/$1"))),C=u,E=p),r=m.length,i=u.length-1;-1!==i;--i){for(g=p[i],f=u[i].search,n=0;n!==r;n+=1)if(d=(c=(o=m[n]).search).length,g.slice(-(l=b[n]).length)===l&&(0===d||f.slice(-d)===c)){x=!0;break}if(x)break}x&&(o.className+=" "+y,-1!==e.className.indexOf("wb-menu"))&&-1===o.className.indexOf("item")&&T(o).closest(".sm").parent().children("a").addClass(y),k.ready(T(e),j)}}})}(jQuery,window,wb),function(w,s,v,x){"use strict";function T(e,t){var a=w("#"+x.jqEscape(e));a.addClass("open").attr("role","dialog").attr("aria-hidden","false"),(a.hasClass("wb-popup-full")||a.hasClass("wb-popup-mid"))&&(a.attr("data-pgtitle",v.getElementsByTagName("H1")[0].textContent),o.find("body").addClass(i)),t||a.scrollTop(0).trigger(D),l[e]||setTimeout(function(){l[e]=null},1),a.trigger("opened"+j)}function A(e,t,a){var r=w("#"+e),n=l[e];r.removeClass("open").removeAttr("role").attr("aria-hidden","true"),(r.hasClass("wb-popup-full")||r.hasClass("wb-popup-mid"))&&o.find("body").removeClass(i),a&&r.addClass("user-closed"),!t&&n&&w(n).trigger(D),delete l[e],r.trigger("closed"+j)}var k,C,E="wb-overlay",j="."+E,S="overlay-close",N="outside-off",i="wb-overlay-dlg",O=!1,l={},D="setfocus.wb",o=x.doc;o.on("timerpoke.wb wb-init.wb-overlay keydown open"+j+" close"+j,j,function(e){var t,a,r,n,i,o,s,l,c,d,u,p,f,g=e.type,h=e.which,m=e.target,b=e.currentTarget,y=b.id;switch(g){case"timerpoke":case"wb-init":i=e,(i=x.init(i,E,j))&&(o=w(i),C||(k=x.i18n,C={"close":k("close"),"colon":k("colon"),"space":k("space"),"esc":k("esc-key"),"closeOverlay":k(S)}),l=-1<o.attr("class").indexOf("wb-panel"),s=-1<o.attr("class").indexOf("wb-popup"),(l||s)&&(f=(c=!(!(l=o.find(".modal-footer")[0])||0===l.length))&&0!==w(l).find("."+S).length,d=(o.hasClass("wb-panel-l")?"pull-right ":"pull-left ")+S,u=C.close,p=C.closeOverlay,f||(c||(l=v.createElement("div")).setAttribute("class","modal-footer"),s&&(l.style.border="0"),f="<button type='button' class='btn btn-sm btn-primary "+d+"' title='"+p+"'>"+u+"<span class='wb-inv'>"+p+"</span></button>",w(l).append(f),c)||o.append(l)),d=(d=0!==(s=o.find(".modal-title")).length?C.close+C.colon+C.space+s.text()+C.space+C.esc:C.closeOverlay).replace("'","&#39;"),o.append("<button type='button' class='mfp-close "+S+"' title='"+d+"'>&#xd7;<span class='wb-inv'> "+d+"</span></button>"),i.setAttribute("aria-hidden","true"),O=!0,x.ready(o,E));break;case"open":b===m&&T(y,e.noFocus);break;case"close":b===m&&A(y,e.noFocus);break;default:switch(t=v.getElementById(y),h){case 9:-1===t.className.indexOf(N)&&(n=(a=w(t).find(":tabbable")).length,-1!==(r=a.index(e.target)+(e.shiftKey?-1:1))&&r!==n||(e.preventDefault(),a.eq(-1===r?n-1:0).trigger(D)));break;case 27:e.isDefaultPrevented()||A(y,!1,!0)}}}),o.on("click","."+S,function(e){var t=e.which;!O||t&&1!==t||A(w(e.currentTarget).closest(j).attr("id"),!1,!0)}),o.on("click keydown",".overlay-lnk",function(e){var t=e.which,a=e.currentTarget,r=a.hash.substring(1);!O||t&&1!==t&&32!==t||(e.preventDefault(),setTimeout(function(){l[r]=a,T(r)},1))}),o.on("click",j+" a[href^='#']",function(e){var t,a=e.which,r=e.target;!O||a&&1!==a||(a=w(r).closest(j)[0],r=r.getAttribute("href"),t=v.getElementById(r.substring(1)),1<r.length&&!w.contains(a,t)&&(e.stopPropagation?e.stopImmediatePropagation():e.cancelBubble=!0,A(a.id,!0),w(t).trigger(D)))}),o.on("click focusin","body",function(e){var t,a,r=e.target,e=e.which;if(O&&(!e||1===e))for(t in l)(a=v.getElementById(t))&&"false"===a.getAttribute("aria-hidden")&&r.id!==t&&-1===a.className.indexOf(N)&&!w.contains(a,r)&&A(t)}),o.on("keyup",function(){var e,t,a,r,n,i,o;if(O&&(t=(e=v.activeElement).getBoundingClientRect(),a=0,r=s.innerHeight,!w.isEmptyObject(l))&&-1===e.className.indexOf(E)&&0===w(e).parents(j).length&&e!==v.body){for(n in l)(i=v.getElementById(n))&&"false"===i.getAttribute("aria-hidden")&&(o=i.getBoundingClientRect(),-1!==i.className.indexOf("wb-bar-t")?a=Math.max(o.bottom,a):-1!==i.className.indexOf("wb-bar-b")&&(r=Math.min(o.top,r)));t.top<a?s.scrollBy(0,a-t.top):t.bottom>r&&s.scrollBy(0,t.bottom-r)}}),x.add(j)}(jQuery,window,document,wb),function(n,i,l,o){"use strict";let s,c;function d(e){var t="",a=e.pgSettings.currPage,r=e.pgSettings.pagesCount,n=l.querySelector("#"+f+"-"+e.id),i=1;if(r<a&&(a=r),1<r){var t='<ol class="pagination">',o="";for(t+=(o+="<li"+(i===a?' class="disabled"':"")+">")+('<button type="button" class="paginate-prev" aria-controls="'+e.id+'"><span class="wb-inv">Page </span>'+c.prv+"</button>")+"</li>";i<=r;i++){var s="";t+=(s+='<li class="'+p(a,r,i)+'">')+('<button type="button" '+b+'="'+i+'" aria-controls="'+e.id+'"'+(i===a?' aria-current="true"':"")+'><span class="wb-inv">Page </span>'+i+"</button>")+"</li>"}o="";t=t+((o+="<li"+(i===a?' class="disabled"':"")+">")+('<button type="button" class="paginate-next" aria-controls="'+e.id+'"><span class="wb-inv">Page </span>'+c.nxt+"</button>")+"</li>")+"</ol>"}n.innerHTML=t}function u(e){let a=e.pgSettings.currPage,t=e.pgSettings.items,r=e.pgSettings.itemsPerPage;t.forEach(function(e,t){t<r*a&&t>=r*a-r?e.classList.remove(h):e.classList.add(h)}),e.pgSettings.pagesCount=Math.ceil(t.length/r)}function p(e,t,a){let r="";return 1<e&&e<t?1<Math.abs(e-a)&&(r+="hidden-xs hidden-sm",2<Math.abs(e-a))&&(r+=" hidden-md"):2<Math.abs(e-a)&&(r+="hidden-xs hidden-sm",4<Math.abs(e-a))&&(r+=" hidden-md"),10<t&&(e<=5?10<a&&(r+=" hidden"):5<e&&e<t-5?(a<e-4||e+5<a)&&(r+=" hidden"):a<=t-10&&(r+=" hidden")),a===e&&(r+=" active"),r}const f="wb-paginate",g=".provisional."+f,e=(g,o.doc),h="wb-pgfltr-out",m="wb-paginate-pager",b="data-pagination-idx",y=":not(.wb-fltr-out):not(.wb-tgfltr-out)",w={"lst":{"selector":"li"},"grp":{"selector":"> *"},"tbl":{"selector":"tr","section":":scope > tbody"},"itemsPerPage":10};e.on("click","."+m+" button",function(){let o=l.querySelector("#"+this.getAttribute("aria-controls")),e=+this.getAttribute(b)||o.pgSettings.currPage;if(this.classList.contains("paginate-next")?e++:this.classList.contains("paginate-prev")&&e--,e!==o.pgSettings.currPage){o.pgSettings.currPage=e,u(o);{var s=o;let e=l.querySelector("#"+f+"-"+s.id),t=e.querySelectorAll("li"),a,r,n=s.pgSettings.currPage,i=s.pgSettings.pagesCount;t.forEach(function(e,t){(r=e.querySelector("button")).classList.contains("paginate-prev")?1<n?e.classList.remove("disabled"):e.classList.add("disabled"):r.classList.contains("paginate-next")?n<i?e.classList.remove("disabled"):e.classList.add("disabled"):(e.className="",e.children[0].removeAttribute("aria-current"),a=p(n,i,t),t===n&&e.children[0].setAttribute("aria-current","true"),e.className=a)})}n(o).trigger("setfocus.wb"),o.getBoundingClientRect().top<0&&o.scrollIntoView({"behavior":"smooth"},!0)}}),e.on("wb-contentupdated",g,function(){this.pgSettings.currPage=1,this.pgSettings.items=this.pgSettings.items=this.querySelectorAll((this.pgSettings.section||":scope")+" "+this.pgSettings.selector+y),u(this),d(this)}),e.on("timerpoke.wb wb-init.provisional.wb-paginate",g,function(e){e=o.init(e,f,g);if(e){var t,a=n(e),r=e.nodeName;switch(c||(s=o.i18n,c={"prv":s("prv"),"nxt":s("nxt")}),r){case"UL":t=w.lst;break;case"TABLE":t=w.tbl;break;default:t=w.grp}e.id=e.id||o.getId(),e.pgSettings=n.extend(!0,{},t,i[f],o.getData(a,f)),e.pgSettings.currPage=1,e.pgSettings.itemsPerPage=e.pgSettings.itemsPerPage||w.itemsPerPage,e.pgSettings.items=e.querySelectorAll((e.pgSettings.section||":scope")+" "+e.pgSettings.selector+y),(a=l.createElement("div")).id=f+"-"+e.id,a.classList.add(m),e.pgSettings.uiTarget?l.querySelector(e.pgSettings.uiTarget).appendChild(a):(!e.pgSettings.section||"UL"===r||"TABLE"===r?e:e.querySelector(e.pgSettings.section)).after(a),u(e),d(e),o.ready(n(e),f)}}),o.add(g)}(jQuery,window,document,wb),function(o,s){"use strict";var a,l,r,c,e=s.doc,n="wb-pii-scrub",d="."+n,u="data-wb-pii-blocked",p="data-scrub-field",f="data-scrub-submit",g=n+"-modal",h={"scrubChar":"********"};e.on("timerpoke.wb wb-init.wb-pii-scrub",d,function(e){var t,i=s.init(e,n,d),e=o(i);i&&(t=i.getAttribute("data-"+n),l||(a=s.i18n,l={"header":a("pii-header"),"intro":a("pii-intro"),"viewMore":a("pii-view-more"),"viewMoreInfo":a("pii-view-more-info"),"confirmBtn":a("pii-yes-btn"),"cancelBtn":a("pii-cancel-btn"),"redacted":a("redacted")}),t=t&&JSON.parse(t),i.settings={...h,...t},i.id=i.id||s.getId(),i.setAttribute(u,"true"),i.addEventListener("submit",function(e){e.preventDefault();{var n=i,t=n.querySelectorAll("["+p+"]");n.PIIFields=[],t.forEach(e=>{var t,a,r;s.findPotentialPII(e.value,!1)&&(t=(n.querySelector("[for="+e.id+"] > span.field-name")||n.querySelector("[for="+e.id+"]")).innerText,a=s.findPotentialPII(e.value,!0,{"replaceWith":n.settings.scrubChar}),r=s.findPotentialPII(e.value,!0,{"replaceWith":"<span role='img' aria-label='"+l.redacted+"'>"+n.settings.scrubChar+"</span>"}),n.PIIFields.push({"elm":e,"scrubVal":a,"scrubValHTML":r,"label":t}))}),1===n.PIIFields.length&&document.getElementById(n.PIIFields[0].elm.id).focus(),0<n.PIIFields.length?n.setAttribute(u,"true"):n.setAttribute(u,"false")}setTimeout(function(){if(!i.querySelector(".error .label.label-danger"))if((r=e.submitter).name&&!i.classList.contains("wb-postback")&&((c=document.createElement("input")).type="hidden",c.name=r.name,c.value=r.value,i.appendChild(c)),0<i.PIIFields.length){{var n=i;let t="",e=document.createElement("section"),a=n.settings.moreInfo?n.settings.moreInfo:l.viewMoreInfo,r=n.querySelector("template"+n.settings.modalTemplate);if(document.getElementById(g))document.getElementById(g).remove();if(n.PIIFields.length>1){t+="<dl>";n.PIIFields.forEach(e=>{t+="<dt>"+e.label+'</dt><dd class="well well-sm">'+e.scrubValHTML.replace(/\n/g,"<br>")+"</dd>"});t+="</dl>"}else t+='<div class="well well-sm">'+n.PIIFields[0].scrubValHTML.replace(/\n/g,"<br>")+"</div>";if(e.id=g,e.className="modal-dialog modal-content overlay-def",e.setAttribute("data-form",n.id),r)e.appendChild(r.content.cloneNode(true));else e.innerHTML=`<header class="modal-header">
					<h2 class="modal-title">${l.header}</h2>
				</header>
				<div class="modal-body">
					<p>${l.intro}</p>
					${t}
					<details class="mrgn-tp-md">
						<summary>${l.viewMore}</summary>
						${a}
					</details>
				</div>
				<div class="modal-footer">
					<div class="row">
						<div class="col-xs-12 col-sm-5 mrgn-tp-sm"><button type="button" class="btn btn-link btn-block popup-modal-dismiss">${l.cancelBtn}</button></div>
						<div class="col-xs-12 col-sm-7 mrgn-tp-sm"><button type="button" class="btn btn-primary btn-block popup-modal-dismiss" ${f}>${l.confirmBtn}</button></div>
					</div>
				</div>`;if(o("body").append(e),r)o("#"+g+" [data-scrub-modal-fields]").html(t)}o("#"+g).trigger("open.wb-lbx",[[{"src":"#"+g,"type":"inline"}],!0])}else i.classList.contains("wb-postback")?o(i).trigger("wb-postback.submit",r):i.submit()},50)}),s.ready(e,n))}),e.on("click","#"+g+" ["+f+"]",function(){var e,t=document.getElementById(g),t=document.getElementById(t.dataset.form);(e=t).PIIFields.forEach(e=>{e.elm.value=e.scrubVal}),e.PIIFields=[],t.classList.contains("wb-postback")?o(t).trigger("wb-postback.submit",r):t.submit()}),s.add(d)}(jQuery,wb),function(s,t,l){"use strict";function a(){l.ready(p,c)}var c="wb-prettify",d="."+c,u="prettyprint"+d,p=l.doc,f={"linenums":!1,"allpre":!1};p.on("timerpoke.wb "+("wb-init"+d),d,function(e){var t,a,r,n,e=l.init(e,c,d),i=l.getMode()+".js",o=["site!deps/prettify"+i];if(e){for(n=s(e),t=e.className.split(" "),e=s.extend({},f,n.data()),a=0,r=t.length;a!==r;a+=1)0===t[a].indexOf("lang-")&&o.push("site!deps/"+t[a]+i);e.allpre=e.allpre||n.hasClass("all-pre"),e.linenums=e.linenums||n.hasClass("linenums"),(e.allpre||e.linenums)&&(n=p.find("pre"),e.allpre&&n.addClass("prettyprint"),e.linenums)&&n.filter(".prettyprint").addClass("linenums"),Modernizr.load({"load":o,"complete":function(){p.trigger(u)}})}}).on(u,function(e){e.namespace===c&&"function"==typeof t.prettyPrint&&t.prettyPrint(a)}),l.add(d)}(jQuery,window,wb),function(r,t,n){"use strict";function i(e){var t,a;for(t in p){if(e<p[t])break;a=t}a!==s&&(n.html.removeClass(s||"").addClass(a),s=a,c.trigger(a+".wb"))}var o,s,a="wb-rsz",l="#"+a,e="wb-init"+l,c=n.doc,d=[],u=["txt-rsz.wb","win-rsz-width.wb","win-rsz-height.wb"],p={"xxsmallview":0,"xsmallview":480,"smallview":768,"mediumview":992,"largeview":1200,"xlargeview":1600};c.on(e,function(e){n.init(e,a,l)&&((e=t.createElement("span")).innerHTML="&#160;",e.setAttribute("id",a),t.body.appendChild(e),d=[(o=e).offsetHeight,r.innerWidth||c.width(),r.innerHeight||c.height()],i(d[1]),n.ready(c,a))}),c.on("timerpoke.wb",l,function(){for(var e=[o.offsetHeight,r.innerWidth||c.width(),r.innerHeight||c.height()],t=e.length,a=0;a!==t;a+=1)e[a]!==d[a]&&(c.trigger(u[a],e),i(e[1]));d=e}),c.trigger(e),n.add(l)}((jQuery,window),document,wb),function(c,s,l,d,u){"use strict";function o(e){var t;if(e=u.init(e,x,T)){(n=c(e)).attr("data-"+x)||(j="wet-boew"),t=c.extend({},S,l[x],n.data(j)),n.data(j,t),w||(y=u.i18n,e=t.textOverrides,w=e?{"buttonContinue":Object.hasOwn(e,"buttonContinue")?s.sanitize(e.buttonContinue):y("st-btn-cont"),"buttonEnd":Object.hasOwn(e,"buttonEnd")?s.sanitize(e.buttonEnd):y("st-btn-end"),"buttonSignin":Object.hasOwn(e,"buttonSignin")?s.sanitize(e.buttonSignin):y("tmpl-signin"),"timeoutBegin":y("st-to-msg-bgn"),"timeoutEnd":Object.hasOwn(e,"timeoutEnd")?s.sanitize(e.timeoutEnd):y("st-to-msg-end"),"timeoutTitle":y("st-msgbx-ttl"),"timeoutAlready":Object.hasOwn(e,"timeoutAlready")?s.sanitize(e.timeoutAlready):y("st-alrdy-to-msg")}:{"buttonContinue":y("st-btn-cont"),"buttonEnd":y("st-btn-end"),"buttonSignin":y("tmpl-signin"),"timeoutBegin":y("st-to-msg-bgn"),"timeoutEnd":y("st-to-msg-end"),"timeoutTitle":y("st-msgbx-ttl"),"timeoutAlready":y("st-alrdy-to-msg")});var n,a,r,i,e=function(){var a=n,r=t;r.refreshOnClick&&v.on("click",function(e){var t,e=e.target.className;e&&-1!==e.indexOf(A)||0!==c(".mfp-ready ."+A).length||(e=a.data("lastActivity"),t=N(),(!e||t-e>r.refreshLimit)&&(a.trigger(k,r).trigger(C,r),a.data("lastActivity",t)))});n.trigger(k,t),u.ready(n,x)},o="#"+x+"-modal";if(v.find(o).length===0){r=d.createDocumentFragment();i=d.createElement("div");i.innerHTML="<a class='wb-lbx lbx-modal mfp-hide' href='#"+x+"-modal'>"+w.timeoutTitle+"</a>"+"<section id='"+x+"-modal' class='mfp-hide modal-dialog modal-content overlay-def'>"+"<header class='modal-header'><h2 class='modal-title'>"+w.timeoutTitle+"</h2></header>"+"<div class='modal-body'></div>"+"<div class='modal-footer'></div>"+"</section>";while((a=i.firstChild)!==null)r.appendChild(a);d.body.appendChild(r);h=v.find(o);m=h.prev().one("wb-ready.wb-lbx",e).trigger("wb-init.wb-lbx")}else e()}}function p(e,t,a,r){a=n(a),clearTimeout(e.data(t)),e.data(t,setTimeout(function(){e.trigger(t,r)},a))}function f(e,t){var o,s,a=O(t.reactionTime),l=N(),a=w.timeoutBegin.replace("#min#","<span class='min'>"+a.minutes+"</span>").replace("#sec#","<span class='sec'>"+a.seconds+"</span>"),r="<button type='button' class='",n="</button>";clearInterval(c(e.target).data(C)),o=c(r+A+" btn btn-primary popup-modal-dismiss'>"+w.buttonContinue+n).data(t).data("start",l),s=c(r+A+" btn btn-default'>"+w.buttonEnd+n).data("logouturl",t.logouturl),g({"body":"<p>"+a+"<br />"+w.timeoutEnd+"</p>","buttons":[o,s],"open":function(){var r=h.find(".min"),n=h.find(".sec"),i=t.reactionTime;b=setInterval(function(){var e,t,a;e=r,t=n,a=l,a=O(i-(N()-a)),e.text(a.minutes),t.text(a.seconds),a.minutes<=0&&a.seconds<=0&&(clearInterval(b),h.find("p").text(w.timeoutAlready),o.text(w.buttonSignin),s.hide())},500)}})}function g(e){(h=h.detach()).find(".modal-body").html(e.body),h.find(".modal-footer").empty().append(e.buttons),h=h.insertAfter(m),m.magnificPopup("open"),e.open&&e.open()}var h,m,b,y,w,v=u.doc,x="wb-sessto",T="."+x,A=x+"-confirm",k="reset"+T,C="keepalive"+T,E="inactivity"+T,j=x,S={"inactivity":12e5,"reactionTime":18e4,"sessionalive":12e5,"refreshCallbackUrl":null,"logouturl":"./","signInUrl":null,"refreshOnClick":!0,"refreshLimit":12e4,"method":"POST","textOverrides":null,"additionalData":null,"refreshCallback":function(e){return"true"===e.replace(/\s/g,"")}},N=function(){return(new Date).getTime()},n=function(e){var t;return null==e?null:(t=/^([0-9]+(?:\.[0-9]*)?)\s*(.*s)?$/.exec(c.trim(e.toString())))[2]?parseFloat(t[1])*({"ms":1,"cs":10,"ds":100,"s":1e3,"das":1e4,"hs":1e5,"ks":1e6}[t[2]]||1):e},O=function(e){var t={"minutes":"","seconds":""};return null!=e&&(t.minutes=parseInt(e/6e4%60,10),t.seconds=parseInt(e/1e3%60,10)),t};v.on("timerpoke.wb "+("wb-init"+T)+" "+C+" "+E+" "+k,T,function(e,t){var a,r;switch(e.type){case"timerpoke":case"wb-init":o(e);break;case"keepalive":a=t,r=c(e.target),null!==a.refreshCallbackUrl&&c.ajax({"url":a.refreshCallbackUrl,"data":a.additionalData,"dataType":"text","method":a.method,"success":function(e){(e=s.sanitize(e))&&a.refreshCallback(e)?r.trigger(k,a):(clearTimeout(r.data(E)),clearTimeout(r.data(C)),g({"body":"<p>"+w.timeoutAlready+"</p>","buttons":c("<button type='button' class='"+A+" btn btn-primary popup-modal-dismiss'>"+w.buttonSignin+"</button>").data("logouturl",a.logouturl)}))}});break;case"inactivity":f(e,t);break;case"reset":var n=e,i=t;n=c(n.target),p(n,E,i.inactivity,i),null!==i.refreshCallbackUrl&&p(n,C,i.sessionalive,i)}}),v.on("click","."+A,function(e){var t=e.target,t=c(t).data();e.preventDefault(),c.magnificPopup.close(),clearInterval(b),void 0!==t.start&&N()-t.start<=t.reactionTime?c(T).trigger(k,t).trigger(C,t):l.location.href=t.signInUrl||t.logouturl}),u.add(T)}(jQuery,DOMPurify,window,document,wb),function(w,v,e,x){"use strict";var T,A,k="wb-share",C="."+k,E=0,t=x.doc,j={"hdLvl":"h2","type":"page","custType":"","url":x.pageUrlParts.href,"title":e.title||t.find("h1:first").text(),"pnlId":"","lnkClass":"","img":"","desc":"","filter":[],"sites":{"blogger":{"name":"Blogger","url":"https://www.blogger.com/blog_this.pyra?t=&amp;u={u}&amp;n={t}"},"bluesky":{"name":"Bluesky","url":"https://bsky.app/intent/compose?text={t}+{u}"},"diigo":{"name":"Diigo","url":"https://www.diigo.com/post?url={u}&amp;title={t}"},"facebook":{"name":"Facebook","url":"https://www.facebook.com/sharer.php?u={u}&amp;t={t}"},"gmail":{"name":"Gmail","url":"https://mail.google.com/mail/?view=cm&fs=1&tf=1&to=&su={t}&body={u}%0A{d}"},"linkedin":{"name":"LinkedIn®","url":"https://www.linkedin.com/shareArticle?mini=true&amp;url={u}&amp;title={t}&amp;ro=false&amp;summary={d}&amp;source="},"myspace":{"name":"MySpace","url":"https://www.myspace.com/Modules/PostTo/Pages/?u={u}&amp;t={t}"},"pinterest":{"name":"Pinterest","url":"https://www.pinterest.com/pin/create/button/?url={u}&amp;media={i}&amp;description={t}"},"reddit":{"name":"reddit","url":"https://reddit.com/submit?url={u}&amp;title={t}"},"tinyurl":{"name":"TinyURL","url":"https://tinyurl.com/create.php?url={u}"},"tumblr":{"name":"tumblr","url":"https://www.tumblr.com/share/link?url={u}&amp;name={t}&amp;description={d}"},"twitter":{"name":"X","url":"https://twitter.com/intent/tweet?text={t}&url={u}"},"x":{"name":"X","url":"https://twitter.com/intent/tweet?text={t}&url={u}"},"yahoomail":{"name":"Yahoo! Mail","url":"https://compose.mail.yahoo.com/?to=&subject={t}&body={u}%0A{d}"},"whatsapp":{"name":"Whatsapp","url":"https://api.whatsapp.com/send?text={t}%0A{d}%0A{u}"}}};t.on("timerpoke.wb wb-init.wb-share",C,function(e){var t,a,r,n,i,o,s,l,c,d,u,p,f,g,h,m,b,y,e=x.init(e,k,C);if(e){if(A||(T=x.i18n,A={"shareText":T("shr-txt"),"page":T("shr-pg"),"video":T("shr-vid"),"audio":T("shr-aud"),"disclaimer":T("shr-disc"),"email":T("email")},j.sites.email={"name":A.email,"url":"mailto:?subject={t}&body={u}%0A{d}","isMailto":!0}),i=w(e),t=(r=w.extend(!0,{},j,v[k],x.getData(i,k))).sites,g=r.filter,a=r.hdLvl,u=A.shareText+(0!==r.custType.length?r.custType:A[r.type]),p="shr-pg"+(0!==(p=r.pnlId).length?"-"+p:E),o=encodeURIComponent(r.url),f=/'|&#39;|&apos;/g,s=encodeURIComponent(r.title).replace(f,"%27"),l=encodeURIComponent(r.img),c=encodeURIComponent(r.desc).replace(f,"%27"),-1===e.className.indexOf("link-only")){if(n="<section id='"+p+"' class='shr-pg mfp-hide modal-dialog modal-content overlay-def'><header class='modal-header'><"+a+" class='modal-title'>"+u+"</"+a+"></header><div class='modal-body'><ul class='list-unstyled colcount-xs-2'>",g&&0!==g.length)b=g;else for(y in b=[],t)Object.prototype.hasOwnProperty.call(t,y)&&b.push(y);for(b.sort(function(e,t){return x.normalizeDiacritics(e).localeCompare(x.normalizeDiacritics(t))}),m=(b=b.includes("twitter")&&b.includes("x")?b.filter(function(e){return"twitter"!==e}):b).length,h=0;h!==m;h+=1)n+="<li><a href='"+(d=t[y=b[h]]).url.replace(/\{u\}/,o).replace(/\{t\}/,s).replace(/\{i\}/,l).replace(/\{d\}/,c)+"' class='shr-lnk "+(d.isMailto?"email":y)+" btn btn-default' target='_blank' rel='noreferrer noopener'>"+d.name+"</a></li>";n+="</ul><p class='col-sm-12 shr-dscl'>"+A.disclaimer+"</p><div class='clearfix'></div></div></section>",E+=1}f="<a href='#"+p+"' aria-controls='"+p+"' class='shr-opn wb-lbx "+r.lnkClass+"'><span class='glyphicon glyphicon-share'></span>"+u+"</a>",e=w((n||"")+f),i.append(e),e.trigger("wb-init.wb-lbx"),x.ready(i,k)}}),x.add(C)}(jQuery,window,document,wb),function(m,b,y){"use strict";function w(e,t,a){var r=b.createElement("BUTTON");return r.className=("prev"===e?"btn btn-md btn-default":"btn btn-md btn-primary")+" "+t,r.setAttribute("type","button"),r.innerHTML=a,r}function v(e){e.addEventListener("click",function(e){e.preventDefault();var e=this.className||!1,e=e&&-1<e.indexOf("btn-primary"),t=!0,a=this.parentElement,r=a.parentElement,a=a.previousElementSibling.classList;(t=e&&jQuery.validator&&"undefined"!==jQuery.validator?m("#"+r.parentElement.id).valid():t)?(n(r,e),e&&a.remove("wb-steps-error")):e&&!t&&a.add("wb-steps-error")})}var x,T,A,k,C,E="wb-steps",j=".provisional."+E,e=y.doc,n=function(e,t){var a=e.getElementsByTagName("FIELDSET")[0],r=a.getElementsByTagName("div")[0],a=a.getElementsByTagName("legend")[0],n=e.querySelector("div.buttons");e&&(r.classList.add("hidden"),n.classList.add("hidden"),a&&a.classList.remove("wb-steps-active"),r=t?e.nextElementSibling:e.previousElementSibling)&&(a=r.getElementsByTagName("LEGEND")[0],e=r.getElementsByTagName("DIV")[0],n=r.querySelector("div.buttons"),a&&(a.classList.add("wb-steps-active"),a.tabIndex=0,a.focus(),a.tabIndex=-1),e&&e.classList.remove("hidden"),n)&&n.classList.remove("hidden")};e.on("timerpoke.wb wb-init.provisional.wb-steps",j,function(e){e=y.init(e,E,j);if(e){e.id||(e.id=y.getId()),T||(x=y.i18n,T={"prv":x("prv"),"nxt":x("nxt")});var t=e.getElementsByTagName("FORM")[0],a=t?m(t).children("fieldset"):0;A=w("prev","mrgn-rght-sm mrgn-bttm-md",T.prv),k=w("next","mrgn-bttm-md",T.nxt),(C=t.querySelector("input[type=submit], button[type=submit]")).classList.add("mrgn-bttm-md");for(var r=0,n=a.length;r<n;r++){var i,o,s=a[r],l=0===r,c=r===n-1,d=s.firstElementChild,u=!(!d||"LEGEND"!==d.tagName)&&d.nextElementSibling,p=b.createElement("div"),f=b.createElement("div"),g=p.classList,h=u.classList;g.add("buttons"),s.parentNode.insertBefore(f,s),f.appendChild(s),f.classList.add("steps-wrapper"),u&&"DIV"===u.tagName&&(o=!0,l||(i=A.cloneNode(!0),v(i),p.appendChild(i),f.appendChild(p)),c?p.appendChild(C):(i=k.cloneNode(!0),v(i),p.appendChild(i)),f.appendChild(p),s.classList.add("wb-tggle-fildst"),h.add("hidden"),g.add("hidden"),l)&&(d.classList.add("wb-steps-active"),i.classList.remove("hidden"),h.remove("hidden"),g.remove("hidden"))}t&&o&&(m(t).children("input").hide(),y.ready(m(e),E))}}),y.add(j)}(jQuery,(window,document),wb),function(m,s,n){"use strict";function l(e,t){var a=document.createElement("OL"),r=document.createElement("LI"),n=e.find(".paginate_button");if(0!==e.length){for(var t=t||e.get(0).id,i=0;i<n.length;i++){var o=r.cloneNode(!0);o.appendChild(n[i]),a.appendChild(o)}a.className="pagination mrgn-tp-0 mrgn-bttm-0",e.empty(),e.append(a),e.find(".paginate_button").attr({"href":"#"+t}).on("keypress",function(e){13===e.keyCode&&(s.location=e.target.href)}).not(".previous, .next").attr("aria-pressed","false").html(function(e,t){return"<span class='wb-inv'>"+c.paginate.page+" </span>"+t}).filter(".current").attr("aria-pressed","true")}}var t,c,i,o="wb-tables",d="."+o,e=n.doc,a=0;e.on("timerpoke.wb wb-init.wb-tables",d,function(e){var r,e=n.init(e,o,d);e&&((r=e.id)||(r=o+"-id-"+a,a+=1,e.id=r),c||(t=n.i18n,c={"aria":{"sortAscending":t("sortAsc"),"sortDescending":t("sortDesc")},"emptyTable":t("emptyTbl"),"info":t("infoEntr"),"infoEmpty":t("infoEmpty"),"infoFiltered":t("infoFilt"),"lengthMenu":t("lenMenu"),"loadingRecords":t("load"),"paginate":{"first":t("first"),"last":t("last"),"next":t("nxt"),"previous":t("prv"),"page":t("page")},"processing":t("process"),"search":t("filter"),"thousands":t("info1000"),"zeroRecords":t("infoEmpty"),"tblFilterInstruction":t("tbFilterInst")}),i={"asStripeClasses":[],"language":c,"dom":"<'top'fil>rt<'bottom'p><'clear'>"},Modernizr.load({"load":["site!deps/jquery.dataTables"+n.getMode()+".js"],"testReady":function(){return m.fn.dataTable&&m.fn.dataTable.version},"complete":function(){var e=m("#"+r),t=m.fn.dataTableExt,a=n.getData(e,o)||{};e.hasClass("provisional")&&e.hasClass("filterEmphasis")&&(a.paging=a.paging||!1),m.extend(t.type.order,{"html-pre":function(e){return n.normalizeDiacritics(e?e.replace?e.replace(/<.*?>/g,"").toLowerCase():e+"":"")},"string-case-pre":function(e){return n.normalizeDiacritics(e)},"string-pre":function(e){return n.normalizeDiacritics(e)},"formatted-num-asc":function(e,t){return n.formattedNumCompare(e,t)},"formatted-num-desc":function(e,t){return n.formattedNumCompare(t,e)}}),e.dataTable(m.extend(!0,{},i,s[o],a))}}))}),e.on("draw.dt",d,function(e,t){var e=m(e.target),a=e.next(".bottom").find("div:first-child"),r=e.prevAll(".top").find("div.dataTables_paginate"),n=e.next(".bottom").find(".paginate_button").length,i=2===a.find(".last, .first").length,o=2===a.find(".previous, .next").length,s=e.dataTable({"retrieve":!0}).api().order();e.find("th").each(function(e){var t=m(this),a=t.find("button");s&&s.length&&s[0][0]===e&&(e="desc"===s[0][1]?c.aria.sortAscending:c.aria.sortDescending,e=a.text()+e,a.attr("title",e)),t.removeAttr("aria-label")}),1===n||3===n&&(i||o)||5===n&&i&&o?(a.addClass("hidden"),r.addClass("hidden")):(a.removeClass("hidden"),r.removeClass("hidden"),l(a,e.get(0).id),l(r)),e.trigger("wb-updated"+d,[t])}),e.on("init.dt",d,function(e){var t=m(e.target),a=m.extend(!0,{},i,s[o],n.getData(t,o));a&&!1===a.ordering||(t.find("thead th").each(function(){var e=m(this),t="ascending"===e.attr("aria-sort")?c.aria.sortDescending:c.aria.sortAscending;"false"===e.attr("data-orderable")||e.hasClass("sorting_disabled")||(e.html("<button type='button' aria-controls='"+e.attr("aria-controls")+"' title='"+e.text().replace(/'/g,"&#39;")+t+"'>"+e.html()+"<span class='sorting-cnt'><span class='sorting-icons' aria-hidden='true'></span></span></button>"),e.removeAttr("aria-label tabindex aria-controls"))}),t.attr("aria-label",c.tblFilterInstruction)),t.hasClass("provisional")&&t.hasClass("filterEmphasis")&&t.parent().addClass("provisional filterEmphasis"),n.ready(m(e.target),o)}),e.on("submit",".wb-tables-filter",function(e){e.preventDefault();function p(e){return e instanceof Date&&!isNaN(e)}var e=m(this),f=m("#"+e.data("bind-to")).dataTable({"retrieve":!0}).api(),g=(f.search("").columns().search(""),-1),h="";return e.find("[name]").each(function(){var t,a,e=m(this),r=e.val(),n="",i="",o=parseInt(e.attr("data-column"),10),s=e.data("aopts"),l=m('[data-aopts*=\'"column": "'+o+"\"']:checked"),c=l&&l.data("aopts")?l.data("aopts").type.toLowerCase():"";if(!s){if(o===g&&-1!==g||(h=""),g=o,e.is("select"))n=e.find("option:selected").val();else if(e.is("input[type='number']")){var d,u=null;""===h?h=(h=parseFloat(r))||"-0":u=(u=parseFloat(r))||"-0",d=h,isNaN(d)||isNaN(u)||(i="("+(n=f.column(o).data().filter(function(e){e=(e=e.toString()).replace(/[^0-9\-,.]+/g,""),e=(e=/[,]\d{1,2}$/.test(e)?e.replace(/(\d{2})$/,".$1"):e).replace(/,/g,"");e=parseFloat(e);if(!isNaN(e))if("and"===c){if(h!==u&&"-0"!==h&&"0"!==u&&d<=e&&e<=u)return!0}else{if(null===u)return"-0"===d||d===e;if(u===h&&"-0"===h)return!0;if("-0"!==u&&d===u&&e===u)return!0;if("-0"===u&&d<=e)return!0;if("-0"===h&&e<=u)return!0;if("-0"!==h&&d<=e&&e<=u)return!0}return!1}).join("|")||"-0").replace(/&nbsp;|\s/g,"\\s").replace(/\$/g,"\\$")+")")}else if(e.is("input[type='date']")&&r)""===h&&((h=new Date(r)).setDate(h.getDate()+1),h.setHours(0,0,0,0)),t=h,(a=new Date(r)).setDate(a.getDate()+1),a.setHours(23,59,59,999),n=f.column(o).data().filter(function(e){e=e.replace(/[0-9]{2}\s[0-9]{2}:/g,function(e){return e.replace(/\s/g,"T")});if(e.includes("T")||(e+="T00:00:00"),(e=new Date(e)).setHours(0,0,0,0),p(t)&&p(a)&&p(e))return t<=e&&e<=a}).join("|")||r;else if(e.is(":checkbox")){if(e.is(":checked"))switch("both"===c?h+="(?=.*\\b"+r+"\\b)":h=h+(0<h.length?"|":"")+r,n=(n=h).replace(/\s/g,"\\s*"),c){case"both":i="("+n+").*";break;case"either":i="^("+n+")$";break;case"and":i=-1<n.indexOf("|")?"^("+n+"|[,\\s])("+n+"|[,\\s])+$":"("+n+")";break;default:i="("+n+")"}}else n=r;n&&(i=i||(e[0].getAttribute("data-exact")?"^"+n+"$":"("+(n=n.replace(/\s/g,"\\s*"))+")"),f.column(o).search(i,!0))}}),f.draw(),!1}),e.on("click",".wb-tables-filter [type='reset']",function(e){e.preventDefault();e=m(this).closest(".wb-tables-filter");return m("#"+e.data("bind-to")).dataTable({"retrieve":!0}).api().search("").columns().search("").draw(),e.find("select").prop("selectedIndex",0),e.find("input:checkbox, input:radio").prop("checked",!1),e.find(":input").not(":button, :submit, :reset, :hidden, :checkbox, :radio").val(""),!1}),n.add(d)}(jQuery,window,wb),function(M,I,L){"use strict";function s(e){var t,a,r,n,i,o,s,l,c,d,u,p,f,g,h,m,b,e=L.init(e,B,U),y="open",w=0;if(e){0===(i=M(e)).children(".tabpanels").length&&i.children("[role=tabpanel], details").wrapAll("<div class='tabpanels'></div>"),a=i.find("> .tabpanels > [role=tabpanel], > .tabpanels > details"),(t=0!==(r=i.children("[role=tablist]")).length)&&1===a.length&&(i.removeClass("show-thumbs playing"),i.addClass("exclude-controls")),n=(f=0!==(C=L.jqEscape(L.pageUrlParts.hash.substring(1))).length)?a.filter("#"+C):void 0,b=n&&0!==n.length,e=e.id,s=M.extend(!0,{},V,{"interval":i.hasClass("slow")?9:i.hasClass("fast")?3:V.interval,"excludeControls":i.hasClass("exclude-controls"),"excludePlay":i.hasClass("exclude-play"),"updateHash":i.hasClass("update-hash"),"playing":i.hasClass("playing"),"ignoreSession":i.hasClass("ignore-session")},I[B],L.getData(i,B));try{if(b){if(!s.ignoreSession)try{sessionStorage.setItem(Y+e+q,C)}catch(e){}}else f&&(o=a.find("#"+C),w=o.length),0!==w?C=o.closest("[role=tabpanel]").attr("id"):s.ignoreSession||(C=sessionStorage.getItem(Y+e+q)),C&&(n=a.filter("#"+C))}catch(e){}if(z=-1!==document.documentElement.className.indexOf(W),_||(R=L.i18n,_={"prev":R("prv"),"next":R("nxt"),"play":R("tab-play"),"rotStart":R("tab-rot").on,"rotStop":R("tab-rot").off,"space":R("space"),"hyphen":R("hyphen"),"pause":R("pause"),"tabCount":R("lb-curr")}),t)n&&0!==n.length&&(a.filter(".in").addClass("out noheight").removeClass("in"),n.addClass("in").removeClass("out noheight"),r.find(".active").removeClass("active"),r.find("a").filter("[href$='"+C+"']").parent().addClass("active"));else{for(i.addClass(H),m=e+"-grp",d=(a=(C=i.children(".tabpanels")).children("details")).length,C.detach(),n&&0!==n.length||0===(n=a.filter("[open]").first()).length&&(n=a.eq(0)),a.removeAttr(y),n.attr(y,y),u="<ul role='tablist' aria-live='off' class='generated'>",c=0;c!==d;c+=1)(l=a.eq(c)).addClass(m).html(l.html().replace(/(<\/summary>)/i,"$1<div class='tgl-panel'>")+"</div>"),(g=l.attr("id"))||(g=L.getId(),l.attr("id",g)),p=!!l.attr(y),z?L.supportsDetails||l.toggleClass("open",p):(l.attr({"role":"tabpanel","open":y}),l.addClass((L.supportsDetails?"":y+" ")+"fade "+(p?"in":"noheight out wb-inv"))),u+="<li"+(p?" class='active'":"")+"><a id='"+g+"-lnk' href='#"+g+"'>"+l.children("summary").html()+"</a></li>";r=M(u+"</ul>"),C.find("> details > summary").addClass("wb-toggle tgl-tab").attr("data-toggle",'{"parent": "#'+e+'", "group": ".'+m+'"}'),i.prepend(r).append(C).trigger("wb-init.wb-toggle")}for(var v,x,T,A,k,C=a,E=r,j=C.get(),S=j.length-1,N=E.children().get(),O=N.length-1,D=C[0].nodeName.toLowerCase()==="details";S!==-1;S-=1){x=j[S];v=x.className.indexOf("out")===-1;if(!D||!z){x.setAttribute("aria-hidden",v?"false":"true");x.setAttribute("aria-expanded",v?"true":"false")}x.setAttribute("aria-labelledby",x.id+"-lnk")}for(k=false;O!==-1;O-=1){x=N[O];x.setAttribute("role","presentation");v=x.className.indexOf("active")!==-1;if(v)k=true;else if(O===0&&!k){v=true;x.className+=" active"}T=x.getElementsByTagName("a")[0];A=T.getAttribute("href").substring(1);T.tabIndex=v?"0":"-1";T.setAttribute("role","tab");T.setAttribute("aria-selected",v?"true":"false");T.setAttribute("aria-controls",A);T.id=A+"-lnk"}E.attr("aria-live","off"),!t||!function(e,t){var a=_.prev,r=_.next,n=_.space,i=t.excludeControls,o=t.excludePlay,s=!i&&!o&&t.playing,l=s?_.pause:_.play,c=s?_.rotStop:_.rotStart,d="<span class='glyphicon glyphicon-",u="<span class='wb-inv'>",p="<li class='control ",f="' href='javascript:;' role='button' title='",g="</span></a></li> ",h=d+(s?"pause":"play")+"'></span>",m=e.find("[role=tab]"),b=m.index(m.filter("[aria-selected=true]"))+1,y=_.tabCount,w=y.indexOf("%"),v=y.lastIndexOf("%")+1,x=p+"prv'><a class='prv"+f+a+"'>"+d+"chevron-left'></span>"+u+a+g,T=p+" tab-count' tabindex='0'><div>"+y.substring(0,w)+"<div class='curr-count'>"+y.substring(w,v).replace("%curr%","<span class='curr-index'>"+b+"</span>").replace("%total%",m.length)+"</div>"+y.substring(v)+"</div></li>",A,k=p+"plypause'><a class='plypause"+f+l+"'>"+h+" <span>"+l+"</span>"+u+n+_.hyphen+n+c+g;if(!i)e.prepend(x+T+(p+"nxt'><a class='nxt"+f+r+"'>"+d+"chevron-right'></span>"+u+r+g));if(!i&&!o)e.append(k);return e.find("a[role=button]").attr("href","javascript:;"),s}(r,s)||L.add("#"+e+U),f&&setTimeout(function(){-1!==(h=b?r.offset().top:0!==w?o.offset().top:-1)&&h<document.body.scrollTop&&(document.body.scrollTop=h)},1),i.data({"wb-tabs":{"panels":a,"tablist":r,"settings":s,"ctime":0}}),F=!0,P(i),s.updateHash&&Q(n[0]),L.ready(i,B)}}function d(e,t){var a,r,n,i,o=(e=e.data(B)).panels,e=e.tablist,s=o.filter("#"+t.attr("aria-controls")),l=e.find("[role=tab]").index(t)+1,c=o.filter(".in"),d=s.closest(U),u=d.data(B).settings,p=c.find(".wb-mltmd-inited").get(),f=p.length;for(-1!==c[0].className.indexOf("slide")&&(r=o.index(c),n=o.index(s),i=o.length-1,o.toggleClass("reverse",n<r&&(r!==i||0!==n)||0===r&&n===i)),c.removeClass("in").addClass("out noheight").attr({"aria-hidden":"true","aria-expanded":"false"}),r=0;r!==f;r+=1)(a=p[r]).player&&a.player("pause");if(s.removeClass("out noheight").addClass("in").attr({"aria-hidden":"false","aria-expanded":"true"}),e.find(".active").removeClass("active").children("a").attr({"aria-selected":"false","tabindex":"-1"}),e.find(".curr-index").html(l),t.attr({"aria-selected":"true","tabindex":"0"}).parent().addClass("active"),!u.ignoreSession)try{sessionStorage.setItem(Y+d.attr("id")+q,s.attr("id"))}catch(e){}u.updateHash&&Q(s[0]),d.trigger(g,[s])}function l(e,t){var a=M(e="#"+e);z&&"details"===a[0].nodeName.toLowerCase()?a.children("summary").trigger(a.attr("open")?p:"click"):((a=M(e+"-lnk")).trigger({"type":"click","which":t?void 0:1}),t||a.trigger(p))}function u(e,t){e.trigger({"type":c,"shiftto":t})}function P(e){var t,a,r,n,i,o,s,l,c,d,u,p,f,g,h,m,b,y;if(F){if(m=(z=-1!==document.documentElement.className.indexOf(W))!==w,b=!!e.length,m||b){for(f=(t=b?e:M(U)).length,p=0;p!==f;p+=1)if(h=(n=(r=(a=t.eq(p)).children(".tabpanels")).children("details")).length,0!==n.length){if(r.detach(),d=n.children("summary"),o=a.children("ul"),z)for(c=o.find(".active a").attr("href").substring(1),g=0;g!==h;g+=1)u=(i=n.eq(g)).children(".tgl-panel"),y=i.attr("id")===c,i.removeAttr("role aria-expanded aria-hidden").removeClass("fade out noheight in").toggleClass("open",y),u.attr("role","tabpanel").removeAttr("aria-expanded").removeAttr("aria-hidden"),y?i.attr("open","open"):i.removeAttr("open"),b||i.children("summary").attr({"aria-expanded":y,"aria-selected":y});else w&&(l=(s=n.filter("[open]")).attr("id"),s=(0===s.length?n:s).eq(0),n.attr({"role":"tabpanel","open":"open"}).not(s).addClass("fade out noheight wb-inv").attr({"aria-hidden":"true","aria-expanded":"false"}),n.children(".tgl-panel").removeAttr("role"),s.addClass("fade in").attr({"aria-hidden":"false","aria-expanded":"true"}));d.attr("aria-hidden",!z),o.attr("aria-hidden",z),a.append(r),z&&!b?a.attr("role","tablist"):w&&(a.removeAttr("role").find(v).removeAttr("role"),a.find("> ul [href$='"+l+"']").trigger("click"))}b&&!z&&t.hasClass(H)&&setTimeout(function(){t.removeAttr("role").find(v).removeAttr("role")},1)}w=z}(m||b)&&setTimeout(function(){M(U+" .tabpanels > details.wb-inv").removeClass("wb-inv")},300)}var R,_,z,w,B="wb-tabs",U="."+B,e="wb-init"+U,c="wb-shift"+U,t="wb-select"+U,g="wb-updated"+U,p="setfocus.wb",a=U+" ul[role=tablist] a, "+U+" ul[role=tablist] .tab-count",F=!1,H="tabs-acc",v="> .tabpanels > details > .tgl-panel",q="-activePanel",r="click keydown",Y=L.pageUrlParts.pathname+"#",n=L.doc,W="smallview",V={"excludePlay":!1,"interval":6,"updateHash":!1,"ignoreSession":!1},Q=function(e){var t=e.id;L.ignoreHashChange=!0,e.id+="-off",I.location.hash=t,e.id=t,L.ignoreHashChange=!1};n.on("timerpoke.wb "+e+" "+c+" "+t,U,function(e){var t,a,r,n,i,o=e.target;if(e.currentTarget===o)switch(e.type){case"timerpoke":(r=M(o)).hasClass(B+"-inited")?r.hasClass("playing")&&(n=(r=r).data(B),i=parseFloat(n.ctime)+.5,parseFloat(n.settings.interval)<=i&&(r.trigger(c),i=0),n.ctime=i,r.data(B,n)):s(e);break;case"wb-init":s(e);break;case"wb-shift":i=e,r=M(o),n=r.data(B).panels,t=n.length,r=r.find("> .tabpanels > .in").prevAll("[role=tabpanel]").length,a=!i.shiftto,r=t<r?0:r+(a?1:i.shiftto),l(n[t-1<r?0:r<0?t-1:r].id,a);break;case"wb-select":l(e.id)}return!0}),n.on(r,a,function(e){var t,a,r,n,i,o=e.which,s=e.currentTarget,l=s.className,c=_.space;return e.ctrlKey||e.altKey||e.metaKey||o&&1!==o&&13!==o&&27!==o&&32!==o&&!(36<o&&o<41)||(e.preventDefault(),e.stopPropagation?e.stopImmediatePropagation():e.cancelBubble=!0,a=(t=(e=M(s)).closest(U))[0].id,n=t.hasClass("playing"),i=-1!==l.indexOf("plypause"),(r=t.data(B)).ctime=0,t.data(B,r),(n&&o||i&&!(36<o&&o<41))&&(n?L.remove("#"+a+U):L.add("#"+a+U),t.toggleClass("playing"),r=(n=!n)?_.pause:_.play,(a=t.find("a.plypause")[0]).setAttribute("title",r),a.innerHTML="<span class='glyphicon glyphicon-"+(n?"pause":"play")+"'></span> <span>"+r+"</span><span class='wb-inv'>"+c+_.hyphen+c+(n?_.rotStop:_.rotStart)+"</span>"),36<o?(u(t,o<39?-1:1),t.find("> [role=tablist] .active a").trigger(p)):27!==o&&("tab"===s.getAttribute("role")?("true"!==s.getAttribute("aria-selected")&&d(t,e),13!==o&&32!==o||t.find(s.getAttribute("href")).trigger(p)):i||u(t,-1!==l.indexOf("prv")?-1:1))),!0}),n.on(r,U+" [role=tabpanel]",function(e){var t=e.currentTarget,a=e.which;e.stopPropagation?e.stopImmediatePropagation():e.cancelBubble=!0,e.ctrlKey&&38===e.which?(z?M(t).prev():M(t).closest(U).find("[href$='#"+t.id+"']")).trigger(p):a&&1!==a&&27!==a||(t=M(e.currentTarget).closest(U)).hasClass("playing")&&t.find(".plypause").trigger("click")}),n.on("click",U+" [role=tabpanel] a",function(e){var t=e.currentTarget,a=t.getAttribute("href"),r=e.which;r&&1!==r||"#"!==a.charAt(0)||0!==(t=(r=M(t).closest(".tabpanels")).children("#"+L.jqEscape(a.substring(1)))).length&&(e.preventDefault(),(0!==(e=t.children("summary")).length&&"true"!==e.attr("aria-hidden")?e:r.parent().find(a+"-lnk")).trigger("click"))}),n.on(L.resizeEvents,P),n.on(r,U+" > .tabpanels > details > summary",function(e){var t,a=e.which,r=e.currentTarget.parentNode;if(!(e.ctrlKey||e.altKey||e.metaKey||a&&1!==a&&13!==a&&32!==a)){if(e=M(r.parentNode.parentNode),a=M(r),!(t=e.data(B).settings).ignoreSession)try{sessionStorage.setItem(Y+e.attr("id")+q,r.id)}catch(e){}t.updateHash&&Q(r),a.attr("open")||e.trigger(g,[a])}}),n.on("click",".wb-tabs-ext",function(e){var t=e.which;t&&1!==t||(e.preventDefault(),l(e.currentTarget.getAttribute("href").substring(1)))}),L.add(U)}(jQuery,window,wb),function(s,n,i,o){"use strict";let l;function c(t){var r,a,n=t;for(r in n.activeFilters=[],n.filters){let e=n.filters[r],t=e.filter(function(e){return!0===e.isChecked}).length,a=[];switch(e[0].type){case"checkbox":0<t&&e.forEach(function(e){e.isChecked&&a.push(e.value)});break;case"radio":if(0<t){for(var i of e)if(!0===i.isChecked){""!==i.value&&a.push(i.value);break}}else console.warn(d+': Radio button groups must have a default selected value. If you want to display all items, add an option called "All" with an empty value.');break;case"select-one":""!==e[0].value&&a.push(e[0].value)}n.activeFilters.push(a)}{var o=t;let e=o.activeFilters.length;o.items.forEach(function(t){let a=0;o.activeFilters.forEach(function(e){0!==e.length&&!e.filter(function(e){return t.tags.includes(e)}).length||a++}),a===e?t.isMatched=!0:t.isMatched=!1})}(a=t).items.forEach(function(e){var t=a.querySelector("#"+e.id);e.isMatched?t.classList.contains(f)&&t.classList.remove(f):t.classList.contains(f)||t.classList.add(f)}),s(t).trigger("wb-contentupdated",[{"source":d}])}const d="wb-tagfilter",u=".provisional."+d,p="."+d+"-ctrl",e="wb-init"+u,t=o.doc,f="wb-tgfltr-out",g="wb-tagfilter-items",h="wb-tagfilter-noresult";t.on("change",p,function(e){let t=e.currentTarget,a=t.type,r=t.name,n=t.value,i=t.closest(u),o=i.filters[r];switch(a){case"checkbox":o.find(function(e){return e.value===n}).isChecked=!!t.checked;break;case"radio":o.forEach(function(e){e.isChecked=!1}),o.find(function(e){return e.value===n}).isChecked=!0;break;case"select-one":o[0].value=n}c(i)}),t.on("wb-contentupdated",u,function(e,t){let a=this,r=n.getComputedStyle(i.documentElement).getPropertyValue("--supports-has");t&&t.source!==d&&(l&&clearTimeout(l),l=setTimeout(function(){a.classList.remove("wb-init",d+"-inited"),s(a).trigger("wb-init."+d)},100)),"false"===r&&(t=this.querySelector("."+h))&&0<this.items.length&&(this.querySelectorAll("."+g+" [data-wb-tags]:not(."+f+", .wb-fltr-out)").length<1?t.style.display="block":t.style.display="none")}),t.on("timerpoke.wb "+e,u,function(e){e=o.init(e,d,u);if(e){const t=e.querySelectorAll(p),a=e.querySelectorAll("[data-wb-tags]"),r=e.querySelector("."+g),n=e.querySelector("."+h);e.items=[],e.filters={},e.activeFilters=[],r&&(r.id=r.id||o.getId(),r.setAttribute("aria-live","polite")),t.length&&(e.filters=function(e){let t={};return e.forEach(function(e){if(!e.name)console.error(d+": Filter controls require an attribute 'name'.");switch(e.type){case"checkbox":case"radio":if(!(e.name in t))t[e.name]=[];t[e.name].push({"isChecked":e.checked,"type":e.type,"value":e.value});break;case"select-one":t[e.name]=[{"type":e.type,"value":e.value}];break}}),t}(t),t.forEach(function(e){e.setAttribute("aria-controls",r.id)})),a.length&&(e.items=function(e){let a=[];return e.forEach(function(e){let t=e.dataset.wbTags.split(" ");if(!e.id)e.setAttribute("id",o.getId());a.push({"id":e.id,"tags":t,"isMatched":true,"itemText":e.innerText.toLowerCase()})}),a}(a)),n&&n.setAttribute("role","status"),c(e),o.ready(s(e),d)}}),o.add(u)}(jQuery,window,document,wb),function(i,o){"use strict";var s="wb-txthl",l="."+s;o.doc.on("timerpoke.wb wb-init.wb-txthl",l,function(e){var t,a,r=o.init(e,s,l),n=o.pageUrlParts.params;r&&(e.txthl?a=Array.isArray(e.txthl)?e.txthl.join("|"):e.txthl:n&&n.txthl&&(a=decodeURIComponent(o.pageUrlParts.params.txthl.replace(/^\s+|\s+$|\|+|"|\(|\)/g,"").replace(/\++/g,"|"))),a&&(t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#039;"},a="(?=([^>]*<))([\\s'])?("+(a=a.replace(/[&<>"']/g,function(e){return t[e]}))+")(?!>)",e=r.innerHTML.replace(new RegExp(a,"gi"),function(e,t,a,r){return(a||"")+"<mark>"+r+"</mark>"}),r.innerHTML=e),o.ready(i(r),s))}),o.add(l)}(jQuery,(window,document,wb)),function(v,x,T){"use strict";function n(e){var t,a,r;if(e=T.init(e,k,C,!0)){t=v(e),a=v.extend({},O,t.data("toggle")),t.data("toggle",a);var n,i,o,s,l,c,d,u,p,f,g=a,h="",m=false;if(g.group!=null&&g.parent!=null){l=document.querySelector(g.parent);if(l.getAttribute("role")!=="tablist"){if(l.className.indexOf("wb-tabs")===-1||document.documentElement.className.indexOf("smallview")!==-1)l.setAttribute("role","tablist");s=l.querySelectorAll(g.group);c=l.querySelectorAll(g.group+" "+j);for(n=0,i=s.length;n!==i;n+=1){o=s[n];d=c[n];u=o.querySelector(E);p=o.nodeName.toLowerCase()==="details"?!!o.hasAttribute("open"):(" "+d.className+" ").indexOf(" "+g.stateOn+" ");if(p)m=true;if(!d.getAttribute("id"))d.setAttribute("id",T.getId());if(o.nodeName.toLowerCase()==="details"&&o.parentNode.className.toLowerCase().indexOf("accordion")>-1){f=document.createElement("div");f.classList.add("tgl-tab");f.setAttribute("role","tab");f.setAttribute("aria-selected",p);f.setAttribute("tabindex",p?"0":"-1");f.setAttribute("aria-posinset",n+1);f.setAttribute("aria-setsize",i);l.replaceChild(f,o);f.appendChild(o)}else{d.setAttribute("role","tab");d.setAttribute("aria-selected",p);d.setAttribute("tabindex",p?"0":"-1");d.setAttribute("aria-posinset",n+1);d.setAttribute("aria-setsize",i)}u.setAttribute("role","tabpanel");u.setAttribute("aria-labelledby",d.getAttribute("id"));u.setAttribute("aria-expanded",p);u.setAttribute("aria-hidden",!p)}if(!m)c[0].setAttribute("tabindex","0")}}else{s=D(e,g);for(n=0,i=s.length;n!==i;n+=1){o=s[n];if(!o.id)o.id=T.getId();h+=o.id+" "}e.setAttribute("aria-controls",h.slice(0,-1))}if(a.persist&&(r=function(e,t){var a,r=e[0];if(t.persist=t.persist==="session"?sessionStorage:localStorage,t.persistKey=k+(t.group?t.group:"")+r.id,a=t.persist.getItem(t.persistKey))e.trigger(S,v.extend({},t,{"type":a}));return a}(t,a)),a.print){var b=t,y=a,w="beforeprint";if(N.on(w,function(){b.trigger(S,v.extend({},y,{"type":y.print}))}),x.matchMedia){e=x.matchMedia("print");if(e.addListener)e.addListener(function(e){if(e.matches)N.trigger(w)})}}!r&&a.state&&A(t,a,a.state),T.ready(t,k)}}function i(e,t){if(e.namespace===k){var a,r,n=!!t.group,i=!!t.persist,o=n&&!!t.parent,e=e.currentTarget,s=v(e),l=function(e,t){var a=t.parent,r=t.selector,n=t.type;if(n)return n==="on"?t.stateOff:t.stateOn;else if(e[0].nodeName.toLowerCase()==="summary")return e.parent().attr("open")?t.stateOn:t.stateOff;else if(!r)return e.data(k+"-state")||t.stateOff;else if(r==="details"&&!n){var i=false;D(e,t).each(function(){if(!v(this).attr("open"))i=true});return i?t.stateOff:t.stateOn}else if(Object.prototype.hasOwnProperty.call(f,r))return Object.prototype.hasOwnProperty.call(f[r],a)?f[r][a]:f[r].all;return t.stateOff}(s,t),c=l===t.stateOff,d=c?t.stateOn:t.stateOff,u=o?s.parent(t.group):D(e,t);if(n&&(a=v.extend({},t,{"selector":t.group}),e=D(e,a),A(o?v(t.parent).find(j):e,a,t.stateOff),e.wb("toggle",t.stateOff,t.stateOn),e.trigger(p,{"isOn":!1,"isTablist":o,"elms":e}),i))for(r in t.persist)0===r.indexOf(k+t.group)&&t.persist.removeItem(r);if(A(o?s:u,t,d),u.wb("toggle",d,l),u.trigger(p,{"isOn":c,"isTablist":o,"elms":u}),n&&u.find("summary").attr({"tabindex":"0"}),i)try{t.persist.setItem(t.persistKey,d)}catch(e){}}}function A(e,t,a){var r,n=t.parent,i=t.selector,o=f[i];if(i)if(o||(o={"all":t.stateOff},f[i]=o),n)o[n]=a;else for(r in o)Object.prototype.hasOwnProperty.call(o,r)&&(o[r]=a);e.data(k+"-state",a)}var k="wb-toggle",C="."+k,E=".tgl-panel",j=".tgl-tab",S="toggle"+C,p="toggled"+C,o="setfocus.wb",f={},s=T.doc,N=T.win,O={"stateOn":"on","stateOff":"off"},D=function(e,t){e=t.selector||e,t=t.parent||null;return null!==t?v(t).find(e):v(e)};s.on("timerpoke.wb "+("wb-init"+C)+" "+S+" click",C,function(e,t){var a,r;switch(e.type){case"click":(r=v((a=e).currentTarget)).trigger(S,r.data("toggle")),a.preventDefault(),r.trigger(o);break;case"toggle":i(e,t);break;case"timerpoke":case"wb-init":n(e)}}),s.on(p,"summary, details",function(e,t){var a,r,n;e.namespace===k&&e.target===e.currentTarget&&(a=t.isOn,r=t.elms,n=(n=v(this)).is("summary")?n.parent():n,e.stopPropagation?e.stopImmediatePropagation():e.cancelBubble=!0,n.prop("open",a),t.isTablist)&&(r.find(j).parents(j).attr({"aria-selected":a,"tabindex":a?"0":"-1"}),r.find(E).attr({"aria-hidden":!a,"aria-expanded":a}),a)&&1===r.length&&(e=r.offset().top)<N.scrollTop()&&N.scrollTop(e)}),s.on("keydown",j,function(e){var t,a,r,n,i=e.which;if(!e.ctrlKey&&34<i&&i<41){switch(e.preventDefault(),t=(e=v(e.currentTarget)).data("toggle"),n=(a=s.find(t.parent).find(t.group)).index(e.parent()),i){case 35:r=a.last();break;case 36:r=a.first();break;case 37:case 38:r=0===n?a.last():a.eq(n-1);break;case 39:case 40:r=n===a.length-1?a.first():a.eq(n+1)}r.children("summary").trigger(o)}}),s.on("keydown",E,function(e){e.ctrlKey&&38===e.which&&v(e.currentTarget).prev().trigger(o)}),T.add(C)}(jQuery,window,wb),function(l,u){"use strict";var t,p,f="wb-twitter",a="."+f,c=u.doc,g=function(e){e=e.match(/\/screen-name\/([^?]+)/);return e?e[1]:null},h=function(e,t,a,r,n){var i=document.createElement("span"),o=document.createElement("p");return i.innerHTML=e.replace("%username%",t),o.id=a+"-"+n,o.className=r+"-"+n,o.prepend(i),o.addEventListener("blur",function(e){e.target.removeAttribute("tabindex")}),o},m=function(e,t,a,r,n){var i=document.createElement("span"),o=document.createElement("a"),s=document.createElement("p");return i.innerHTML=e.replace("%username%",t),o.href="#"+a,o.prepend(i),l(o).on("click",function(e){e=e.currentTarget,e="#"+u.jqEscape(e.getAttribute("href").substring(1));return c.find(e).trigger("setfocus.wb"),!1}),s.className=r+" "+r+"-"+n,s.prepend(o),s};c.on("timerpoke.wb "+("wb-init"+a),a,function(e){var r=u.init(e,f,a),e=u.pageUrlParts.protocol;if(r){const c=r.querySelector("a.twitter-timeline");if(u.ie11)u.ready(l(r),f);else{if(c){const d=document.createElement("div");let a;p||(t=u.i18n,p={"startNotice":t("twitter-start-notice"),"endNotice":t("twitter-end-notice"),"skipEnd":t("twitter-skip-end"),"skipStart":t("twitter-skip-start"),"timelineTitle":t("twitter-timeline-title")}),"string"!=typeof p.startNotice&&console.warn(f+": i18n text is missing. Iframe title override and skip links will be disabled."),!c.dataset.lang&&c.closest("[lang='zh-Hans']")&&(c.dataset.lang="zh-cn"),("fb-page"===c.dataset.height||c.dataset.tweetLimit&&!c.dataset.height)&&(c.dataset.height="500"),c.dataset.dnt||(c.dataset.dnt="true"),d.className="twitter-timeline-loading",c.after(d),(a=new MutationObserver(function(e){e.forEach(function(e){switch(e.type){case"attributes":var t=e.target;"IFRAME"===t.nodeName&&t.title!==p.timelineTitle&&"string"==typeof p.timelineTitle&&(t.title=p.timelineTitle);break;case"childList":e.removedNodes.forEach(function(n){if(n===c&&e.nextSibling===d){n=d.previousElementSibling;d.remove();{const i=n.getElementsByTagName("iframe")[0],o=g(i.src),s=f+"-"+"notice",l=f+"-"+"skip";let e,t,a,r;o&&"string"==typeof p.timelineTitle&&(e=h(p.startNotice,o,i.id,s,"start"),n.before(e),t=h(p.endNotice,o,i.id,s,"end"),n.after(t),a=m(p.skipEnd,o,t.id,l,"end"),e.after(a),r=m(p.skipStart,o,e.id,l,"start"),t.before(r))}r.style.opacity=1,r.style.opacity="",a.disconnect()}})}})})).observe(r,{"attributeFilter":["title"],"childList":!0,"subtree":!0})}Modernizr.load({"load":(-1===e.indexOf("http")?"http:":e)+"//platform.twitter.com/widgets.js","complete":function(){u.ready(l(r),f)}})}}}),u.add(a)}(jQuery,(window,wb)),function(x,T,A){"use strict";function k(e,t,a,r,n,i,o,s){x(e).trigger({"type":"json-fetch.wb","fetch":{"url":t,"refId":a,"nocache":r,"nocachekey":n,"data":i,"contentType":o,"method":s}})}function C(e,t,a){var r,n,i=e.className,o=t.source?document.querySelector(t.source):e.querySelector("template");if("TABLE"===e.tagName&&-1!==i.indexOf("wb-tables")){if(-1===i.indexOf("wb-tables-inited"))return x(e).one("wb-ready.wb-tables,init.dt",function(){C(e,t,a)});if(!x.fn.dataTable.isDataTable(e)&&-1===i.indexOf(j+"-dtwait"))return e.classList.add(j+"-dtwait"),setTimeout(function(){C(e,t,a)},50);n=(r=x(e).dataTable({"retrieve":!0}).api()).row.add,t.tobeclone="tr"}o&&(o.content||A.tmplPolyfill(o),t.streamline?v(e,e,a,t):b(e,a,t),n)&&r.draw()}function b(e,t,a,r){var n,i,o,s,l,c,d=e;for(a.appendto&&(d=x(a.appendto).get(0)),"TABLE"===e.tagName&&-1!==e.className.indexOf("wb-tables")&&(c=(l=x(e).dataTable({"retrieve":!0}).api()).row.add,a.tobeclone="tr"),i=(t=Array.isArray(t)?t:"object"!=typeof t?[t]:x.map(t,function(e,t){return e&&"object"==typeof e&&!Array.isArray(e)?e["@id"]||(e["@id"]=t):e={"@id":t,"@value":e},[e]})).length,!r&&a.source?s=document.querySelector(a.source):!r&&a.template?s=e.querySelector(a.template):r||(s=(1===(s=e.querySelectorAll(":scope > template")).length||0===s[0].attributes.length)&&s[0]),n=0;n<i;n+=1){var u=t[n];p(u,a.filter,a.filternot)&&(!o&&r&&(o=r),r||!s||a.tobeclone?!r&&s&&(o=s.content.querySelector(a.tobeclone).cloneNode(!0)):o=s.content.cloneNode(!0),(u=v(e,o,u,a))&&(delete a.template,o=u),c?c(x(o)):!r&&s&&d.appendChild(o))}c&&l.draw()}function y(e,t){var a,r,n=t.operand||"softEq";if(t.test){try{a=_(e,t.assess||t.value),r=R(a)}catch(e){r=a=void 0}return L[t.test]?(e=L[t.test].call(t,r,a),P[n]||(console.error("The operand '"+n+"' don't exist"),console.error(t),n="softEq"),!!P[n].call(t,e,t.expect)):(console.error("The test function '"+t.test+"' don't exist. Default to false test result"),console.error(t),!1)}}var E,e,j="wb-data-json",S="wb-json",t=["[data-json-after]","[data-json-append]","[data-json-before]","[data-json-prepend]","[data-json-replace]","[data-json-replacewith]","[data-"+S+"]"],N=["after","append","before","prepend","val"],O=/(href|src|data-*|aria-*|role|pattern|min|max|step|low|high|lang|hreflang|action)/,D=/(checked|selected|disabled|required|readonly|multiple|hidden)/,a=t.length,M=t.join(","),I=j+"-queue",r=A.doc,w={"rdf:Alt":function(e,t,a,r){for(var n,i=r.mapping,o=i.length,s=a,l=0;l<o||0===l;l+=1)if(n=i[l],y(a,n))return delete(n=x.extend(!0,{},n)).test,n.value&&(s=R(a,n.value)),void v(e,t,s,n)}},L={"fn:isArray":function(e){return Array.isArray(e)},"fn:isLiteral":function(e){return!(!(e=e&&e["@value"]?e["@value"]:e)||"object"==typeof e)},"fn:getType":function(e,t){t=e["@type"]||t["@type"];return"@json"===t?"rdf:JSON":(Array.isArray(t)&&-1!==t.indexOf("@json")&&(t[t.indexOf("@json")]="rdf:JSON"),t||typeof e)},"fn:getValue":function(e){return e},"fn:guessType":function(e,t){var a;return e?e["@type"]?a=e["@type"]:t["@type"]?a=t["@type"]:e["@value"]&&(e=e["@value"]):a="undefined",a&&"undefined"!==a&&("@json"===a?a="rdf:JSON":Array.isArray(a)&&-1!==a.indexOf("@json")&&(a[a.indexOf("@json")]="rdf:JSON")),a=a||("string"==typeof e&&e.match(/^([a-z][a-z0-9+\-.]*):/i)?["xsd:anyURI","rdfs:Literal"]:"string"==typeof e?["xsd:string","rdfs:Literal"]:"boolean"==typeof e?["xsd:boolean","rdfs:Literal"]:"number"==typeof e?["xsd:double","rdfs:Literal"]:void 0===e?"undefined":Array.isArray(e)?"rdfs:Container":"rdfs:Resource")}},P={"softEq":function(e,t){var a,r;if(Array.isArray(e)&&!Array.isArray(t)&&-1!==e.indexOf(t))return!0;if(Array.isArray(e)&&Array.isArray(t)){for(r=t.length,a=0;a!==r;a++)if(e.indexOf(t[a]))return!0}else{if(t&&e===t)return!0;if(!t&&e)return!0}return!1},"eq":function(e,t){return!!c(e,t)},"neq":function(e,t){return!c(e,t)},"in":function(e,t){var a;if(t){if(Array.isArray(e)&&!Array.isArray(t)&&-1!==e.indexOf(t))return!0;if(Array.isArray(e)&&Array.isArray(t)){for(a=0;a!==t.length;a++)if(e.indexOf(t[a]))return!0}else{if(!Array.isArray(e)&&Array.isArray(t)&&-1!==t.indexOf(e))return!0;if(e===t)return!0}}else console.error("Expected value is missing. Defaulting to false."),console.error(this);return!1},"nin":function(e,t){return!P.in.call(this,e,t)}},v=function(e,t,a,r){var n,i,o,s,l,c,d,u,p,f,g,h=r.queryall,m=r.mapping;if(r["@type"])w[r["@type"]].call(a,e,t,a,r);else if((!r.test||y(a,r))&&(m||h||r.template||"object"==typeof m)){if(t=t||e,(r=x.extend(!0,{},r)).template&&(g=t.querySelector(r.template),f=t,t=g.content.cloneNode(!0),delete r.template),Array.isArray(a))b(t,a,r,t);else{for(m=m||[{}],p=(m=Array.isArray(m)?m:[m]).length,n=0;n<p||0===n;n+=1)"string"==typeof m[n]&&(m[n]={"value":m[n]});if(h)for(u=t.querySelectorAll(h),n=0;n<u.length||0===n;n+=1)m[n].selector||-1!==h.indexOf("nth-child")?m[n].selector||(m[n].selector=h):m[n].selector=h+":nth-child("+(n+1)+")";for(n=0;n<p||0===n;n+=1){c=l=d=!1,o=(i=m[n]).selector?t.querySelector(i.selector):t;try{s=_(a,i)}catch(e){console.info("JSON selector path for mapping don't exist in content"),console.info(i),console.info(a);continue}if(void 0!==s)if(s&&s["@value"]&&s["@type"]&&(Array.isArray(s["@type"])||(s["@type"]=[s["@type"]]),d=-1!==s["@type"].indexOf("@id"),l=-1!==s["@type"].indexOf("rdf:HTML"),c=-1!==s["@type"].indexOf("rdf:JSON")||-1!==s["@type"].indexOf("@json")),Array.isArray(s)&&(i.mapping||i.queryall))b(o,s,i);else if(i.mapping||i.queryall||!i.mapping&&"object"==typeof i.mapping)try{v(g||e,o,s,i)}catch(e){if("cached_node: null"!==e||"object"!=typeof s)throw e;b(o,s,i)}else if(d&&l)o.dataset.wbAjax=JSON.stringify({"url":s["@value"],"type":"replace","dataType":c?"json":null,"encode":i.encode});else if(l&&c&&!d)s=JSON.stringify(s["@value"]),z(o,s,i);else{if(!o&&"object"==typeof s)throw"cached_node: null";null!==r.mapping&&("object"==typeof(s=R(s))&&null!==s&&(s=JSON.stringify(s)),z(o,s,i))}}}return g?(g.parentNode?r.append?g.parentNode.appendChild(t):g.parentNode.insertBefore(t,g):f.appendChild(t),e):void 0}},R=function(e,t){e=_(e,t);return e="object"==typeof e&&null!==e&&Object.prototype.hasOwnProperty.call(e,"@value")?e["@value"]:e},_=function(e,t){return t=t||!1,"string"==typeof e||"/"===t||"/@value"===t||"/"===t.value||"/@value"===t.value?e:"string"==typeof t?jsonpointer.get(e,t):t.value?jsonpointer.get(e,t.value):e},z=function(e,t,a){var r=a.attr;r&&(e.hasAttribute(r)||e.setAttribute(r,""),e=e.getAttributeNode(r)),null!==(t=a.placeholder?(e.textContent||"").replace(a.placeholder,t):t)&&(a.isHTML?e.innerHTML=t:e.textContent=t)},p=function(e,t,a){var r,n,i,o=t?t.length:0,s=a?a.length:0,l=!1;if(o||s){for(r=0;r<o;r+=1)if(n=t[r],i=c(jsonpointer.get(e,n.path),n.value),n.optional)l=l||i;else{if(!i)return!1;l=!0}if(o&&!l)return!1;for(r=0;r<s;r+=1)if(n=a[r],(i=c(jsonpointer.get(e,n.path),n.value))&&!n.optional||i&&n.optional)return!1}return!0},c=function(e,t){switch(typeof e){case"undefined":return!1;case"boolean":case"string":case"number":return e===t;case"object":if(null===e)return null===t;var a,r;if(Array.isArray(e)){if(!Array.isArray(t)||e.length!==t.length)return!1;for(a=0,r=e.length;a<r;a++)if(!c(e[a],t[a]))return!1}else{var n=i(t).length;if(i(e).length!==n)return!1;for(a in e)if(!c(e[a],t[a]))return!1}return!0;default:return!1}},i=function(e){if(Array.isArray(e))for(var t=new Array(e.length),a=0;a<t.length;a++)t[a]=""+a;else{if(Object.keys)return Object.keys(e);for(var r in t=[],e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r)}return t};r.on("json-failed.wb",M,function(e){var t=e.currentTarget,a=x(t).data(I),r=e.fetch,n=r.xhr,a=a[r.refId].fail;a&&(a.streamline=!0,C(t,a,{"error":r.error.message||n.statusText,"status":r.status,"url":r.fetchOpts.url,"response":{"text":n.responseText||"","status":n.status,"statusText":n.statusText}})),console.info(e.currentTarget),console.error("Error or bad JSON Fetched from url in "+j)}),Modernizr.load({"test":"content"in document.createElement("template"),"nope":"site!deps/template"+A.getMode()+".js"}),r.on("timerpoke.wb wb-init.wb-data-json wb-update.wb-data-json json-fetched.wb",M,function(e){if(e.currentTarget===e.target)switch(e.type){case"timerpoke":case"wb-init":var t,a=e,r=A.init(a,j,M),n=T[j]||{};if(r){for(var i,o,s,l,c=["before","replace","replacewith","after","append","prepend"],d=c.length,u=[],a=x(r),p=0;p!==d;p+=1)null!==(l=r.getAttribute("data-json-"+(i=c[p])))&&u.push({"type":i,"url":l});if(!E){if(n.functionForTest)for(t in n.functionForTest)L[t]||(L[t]=n.functionForTest[t]);if(n.functionForOperand)for(t in n.functionForOperand)P[t]||(P[t]=n.functionForOperand[t]);E=!0}if(A.ready(a,j),(o=A.getData(a,S))&&o.url)u.push(o);else if(o&&Array.isArray(o))for(d=o.length,p=0;p!==d;p+=1)u.push(o[p]);for(a.data(I,u),d=u.length,p=0;p!==d;p+=1)k(r,(s=u[p]).url,p,s.nocache,s.nocachekey,s.data,s.contenttype,s.method)}break;case"wb-update":var a=e,f=a.target,g=x(f),h=g.data(I),m=h.length;if(!(a=a["wb-json"]).url||!a.type&&!a.source)throw"Data JSON update not configured properly";h.push(a),g.data(I,h),k(f,a.url,m);break;default:var h=(g=e).target,f=x(h),m=f.data(I),g=g.fetch,b=(m=m[g.refId]).type,y=m.prop||m.attr,w=m.showempty,v=typeof(g=g.response);if(w||"undefined"!=v){if(w&&"undefined"==v&&(g=""),w=jQuery.ajaxSettings.cache,jQuery.ajaxSettings.cache=!0,b)if("replace"===b)f.html(g);else if("replacewith"===b)f.replaceWith(g);else if("addclass"===b)f.addClass(g);else if("removeclass"===b)f.removeClass(g);else if("prop"===b&&y&&D.test(y))f.prop(y,g);else if("attr"===b&&y&&O.test(y))f.attr(y,g);else{if("function"!=typeof f[b]||-1===N.indexOf(b))throw j+" do not support type: "+b;f[b](g)}else b="template",C(h,m,g),m.trigger&&!A.isDisabled&&f.find(A.allSelectors).addClass("wb-init").filter(":not(#"+h.id+" .wb-init .wb-init)").trigger("timerpoke.wb");jQuery.ajaxSettings.cache=w,f.trigger("wb-contentupdated",{"json-type":b,"content":g})}}return!0});for(e=0;e!==a;e+=1)A.add(t[e])}(jQuery,window,wb),function(f,g,h){"use strict";function m(){e.forEach(e=>{"object"==typeof e.selectors&&(e.selectors=e.selectors.join("."+t+",")),e.selectors=e.selectors+"."+t,f(e.selectors).trigger("wb-init."+e.initEvent)})}var b="wb-disable",y="#wb-tphp",w=h.doc,t="wb-disable-allow",e=[{"selectors":"[data-wb-jsonmanager]","initEvent":"wb-jsonmanager"},{"selectors":"[data-wb-postback]","initEvent":"wb-postback"},{"selectors":["[data-ajax-after]","[data-ajax-append]","[data-ajax-before]","[data-ajax-prepend]","[data-ajax-replace]","[data-wb-ajax]"],"initEvent":"wb-data-ajax"},{"selectors":["[data-json-after]","[data-json-append]","[data-json-before]","[data-json-prepend]","[data-json-replace]","[data-json-replacewith]","[data-wb-json]"],"initEvent":"wb-data-json"}];w.on("timerpoke.wb",y,function(e){var t,a,r,e=h.init(e,b,y,!0),n="?",i=h.html,o=h.i18n,s=h.pageUrlParts,l=o("disable-notice-h"),c=o("disable-notice");if(e){for(t in s.params)t&&Object.prototype.hasOwnProperty.call(s.params,t)&&"wbdisable"!==t&&(n+=t+"="+s.params[t]+"&");try{if(h.isDisabled||h.ie){i.addClass("wb-disable");try{localStorage.setItem("wbdisable","true")}catch(e){}m(),document.querySelector("link[rel=canonical]")||((a=g.location.href.replace(/&?wbdisable=true/gi,"").replace("?&","?").replace("?#","#")).indexOf("?")===a.length-1&&(a=a.replace("?","")),(r=document.createElement("link")).rel="canonical",r.href=a,document.head.appendChild(r));var d=h.getId(),u="<section class='container-fluid bg-warning text-center mrgn-tp-sm py-4'><h2 class='mrgn-tp-0'>"+l+"</h2><p>"+c+"</p><p><a id='"+d+"' rel='alternate' href='"+n+"wbdisable=false'>"+o("wb-enable")+"</a>.</p></section>";return f(e).after(u),document.querySelector("#"+d).setAttribute("property","significantLink"),!0}i.addClass("wb-enable"),localStorage&&localStorage.setItem("wbdisable","false");var p=g.location.href.replace(/&?wbdisable=false/gi,"").replace("?&","?").replace("?#","#");p.indexOf("?")===p.length-1&&(p=p.replace("?","")),g.history.replaceState("","",p)}catch(e){}h.addSkipLink(o("wb-disable"),{"href":n+"wbdisable=true","rel":"alternate"},!1,!0),h.ready(w,b)}}),w.on("wb-contentupdated",function(){m()}),h.add(y)}(jQuery,window,wb),function(s,a){"use strict";function e(){var e=a.pageUrlParts.hash;e&&0!==(r=s("#"+a.jqEscape(e.substring(1)))).length&&r.trigger(l)}var r,t=a.doc,n=a.win,l="setfocus.wb";t.on(l,function(e){if("wb"===e.namespace){var t,a,r,n,i=e.target,o=s(i),e=o.not("summary").parents("details, [role='tabpanel']");if(0!==e.length)for(e.not("[open]").children("summary").trigger("click"),r=(t=e.filter("[aria-hidden='true']")).length,n=0;n!==r;n+=1)(a=t.eq(n)).closest(".wb-tabs").find("#"+a.attr("aria-labelledby")).trigger("click");setTimeout(function(){let e=!1;var t;return o.trigger("focus"),i!==document.activeElement&&null===i.getAttribute("tabindex")&&(i.setAttribute("tabindex","-1"),e=!0,o.trigger("focus")),i===document.activeElement?0!==(t=s(".wb-bar-t[aria-hidden=false]")).length&&(document.documentElement.scrollTop-=t.outerHeight()):(e&&i.removeAttribute("tabindex"),console.error(l+": Unable to focus onto the destination element... maybe it's hidden?"),console.error(i)),o},100)}}),t.on("wb-ready.wb",e),n.on("hashchange",function(){a.pageUrlParts.hash=window.location.hash,a.ignoreHashChange||e()}),t.on("click","a[href]",function(e){var t=e.currentTarget.getAttribute("href");1<t.length&&"#"===t.charAt(0)&&!e.isDefaultPrevented()&&0!==(r=s("#"+a.jqEscape(t.substring(1)))).length&&(a.ignoreHashChange=!0,r.trigger(l))})}(jQuery,wb),function(y,f,w){"use strict";function v(e,t,a,r){e.after('<p lang="en"><strong>JSON Manager Debug</strong> ('+t+')</p><ul lang="en"><li>JSON: <pre><code>'+JSON.stringify(a)+"</code></pre></li><li>Patches: <pre><code>"+JSON.stringify(r)+"</code></pre>"),console.log(a)}function x(e,t,a,r){var n,i;if(Array.isArray(a)||(a=[a]),Array.isArray(r)||(r=[r]),n=jsonpointer.get(e,t),Array.isArray(n))for(i=n.length-1;-1!==i;--i)c(n[i],a,r)||jsonpatch.apply(e,[{"op":"remove","path":t+"/"+i}]);return e}var T="wb-jsonmanager",g="[data-"+T+"]",A="data-"+T+"-reload",h=[],k={},C={},E={},j={},S={},N={},O={},e=w.doc,D={"ops":[{"name":"patches","fn":function(e,t,a){var r=this.path,n=this.patches,i=jsonpointer.get(a,r);n.forEach(e=>{e.mainTree=a,e.pathParent=r,jsonpatch.apply(i,[e])})}},{"name":"wb-count","fn":function(e,t,a){var r,n,i=e[t],o=0,s=this.filter||[],l=this.filternot||[];if(Array.isArray(s)||(s=[s]),Array.isArray(l)||(l=[l]),(s.length||l.length)&&Array.isArray(i))for(r=i.length,n=0;n!==r;n+=1)c(i[n],s,l)&&(o+=1);else Array.isArray(i)&&(o=i.length);b(a,"add",this.set,o)}},{"name":"wb-first","fn":function(e,t,a){e=e[t];Array.isArray(e)&&0!==e.length&&b(a,"add",this.set,e[0])}},{"name":"wb-last","fn":function(e,t,a){e=e[t];Array.isArray(e)&&0!==e.length&&b(a,"add",this.set,e[e.length-1])}},{"name":"wb-nbtolocal","fn":function(e,t,a){var e=e[t],t=this.locale||f.wb.lang,r=this.suffix||"",n=this.prefix||"";"string"==typeof e&&(e=parseFloat(e),isNaN(e))||b(a,"replace",this.path,n+e.toLocaleString(t)+r)}},{"name":"wb-decodeUTF8Base64","fn":function(e,t,a){e=e[t];this.set?b(a,"add",this.set,w.decodeUTF8Base64(e)):b(a,"replace",this.path,w.decodeUTF8Base64(e))}},{"name":"wb-escapeHTML","fn":function(e,t,a){e=e[t];this.set?b(a,"add",this.set,w.escapeHTML(e)):b(a,"replace",this.path,w.escapeHTML(e))}},{"name":"wb-toDateISO","fn":function(e,t,a){this.set?b(a,"add",this.set,w.date.toDateISO(e[t])):b(a,"replace",this.path,w.date.toDateISO(e[t]))}},{"name":"wb-toDateTimeISO","fn":function(e,t,a){this.set?b(a,"add",this.set,w.date.toDateISO(e[t],!0)):b(a,"replace",this.path,w.date.toDateISO(e[t],!0))}},{"name":"wb-swap","fn":function(e,t,a){var r,n,i,o,s,l,c,d,u,p,f=e[t],g=this.ref,h=this.mainTree||e,m=this.path;if(f)for(n=jsonpointer.get(h,g),i=Array.isArray(n),l=(f=(o=Array.isArray(f))?f:[f]).length,s=0;s!==l;s++){if(c=f[s],r=void 0,i){for(u=n.length,d=0;d!==u;d++)if((p=n[d])["@id"]&&p["@id"]===c){r=p;break}if(!r){console.error("wb-swap: Unable to find a corresponding value for: "+f+" in reference "+g);break}}else c=c.replaceAll("~","~0").replaceAll("/","~1"),r=h?jsonpointer.get(h,g+"/"+c):jsonpointer.get(a,g+"/"+c);r&&!o?b(a,"replace",m,r):r&&b(a,"replace",m+"/"+s,r)}}}],"opsArray":[{"name":"wb-toDateISO","fn":function(e){for(var t=this.set,a=this.path,r=e.length,n=0;n!==r;n+=1)t?jsonpatch.apply(e,[{"op":"wb-toDateISO","set":"/"+n+t,"path":"/"+n+a}]):jsonpatch.apply(e,[{"op":"wb-toDateISO","path":"/"+n+a}])}},{"name":"wb-toDateTimeISO","fn":function(e){for(var t=this.set,a=this.path,r=e.length,n=0;n!==r;n+=1)t?jsonpatch.apply(e,[{"op":"wb-toDateTimeISO","set":"/"+n+t,"path":"/"+n+a}]):jsonpatch.apply(e,[{"op":"wb-toDateTimeISO","path":"/"+n+a}])}},{"name":"wb-swap","fn":function(a){a.forEach((e,t)=>{jsonpatch.apply(a,[{"op":"wb-swap","path":"/"+t+this.path,"ref":this.ref,"mainTree":this.mainTree}])})}},{"name":"patches","fn":function(a){a.forEach((e,t)=>{jsonpatch.apply(this.mainTree||a,[{"op":"patches","path":(this.pathParent||"")+"/"+t+this.path,"patches":this.patches}])})}}],"opsRoot":[],"settings":{},"docMapKeys":{"referer":document.referrer,"locationHref":location.href}},c=function(e,t,a){var r,n,i,o=t.length,s=a.length,l=!1;if(o||s){for(r=0;r<o;r+=1)if(n=t[r],i=d(jsonpointer.get(e,n.path),n.value),n.optional)l=l||i;else{if(!i)return!1;l=!0}if(o&&!l)return!1;for(r=0;r<s;r+=1)if(n=a[r],(i=d(jsonpointer.get(e,n.path),n.value))&&!n.optional||i&&n.optional)return!1}return!0},d=function(e,t){switch(typeof e){case"undefined":return!1;case"boolean":case"string":case"number":return e===t;case"object":if(null===e)return null===t;var a,r;if(Array.isArray(e)){if(Array.isArray(t)||e.length!==t.length)return!1;for(a=0,r=e.length;a<r;a++)if(!d(e[a],t[a]))return!1}else{var n=i(t).length;if(i(e).length!==n)return!1;for(a=0;a<n;a++)if(!d(e[a],t[a]))return!1}return!0;default:return!1}},i=function(e){if(Array.isArray(e))for(var t=new Array(e.length),a=0;a<t.length;a++)t[a]=""+a;else{if(Object.keys)return Object.keys(e);for(var r in t=[],e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r)}return t},b=function(e,t,a,r){jsonpatch.apply(e,[{"op":t,"path":a,"value":r}])};e.on("json-failed.wb",g,function(e){var t=e.target;t===e.currentTarget&&((e=y(t)).addClass("jsonfail"),w.ready(e,T))}),e.on("json-fetched.wb",g,function(e){var t,a,r,n,i,o,s,l,c,d,u,p,f=e.target,g=y(f),h=e.fetch.fetchedOpts,m=f.hasAttribute(A),b=e.fetch.response;if(f===e.currentTarget){if(e=w.getData(g,T),h&&h.savingPath)for(r=(c=h.savingPath.split("/")).length-1;0<r;r--)c[r]&&((l={})[c[r]]=b,b=l);if(b=(h=Array.isArray(b))?y.extend(!0,[],b):y.extend(!0,{},b),t=e.name,S[t]--,N[t]=N[t]||h,N[t]!==h)throw"Can't merge, incompatible JSON type (array vs object)";if(O[t]=O[t]?h?O[t].concat(b):y.extend(O[t],b):b,m||!S[t]){b=O[t],(h=e.extractor)&&(Array.isArray(h)||(h=[h]),b=y.extend(b,function(e){for(var i,t,a,r=!1,n=[],o={},s=[],l=[],c=0,d=[],u=function(e){e.selector!==a||n.includes(a)||(o[e.path]=(e.attr&&l[c].getAttributeNode(e.attr)?l[c].getAttributeNode(e.attr):l[c]).textContent,n.push(a))},p=function(e,t,a){var r,n=e.path.split("/").filter(Boolean);t&&t instanceof NodeList&&0===t.length&&(t=null),1<n.length?(r="",r=n.pop(),n[0]&&""!==n[0]?(a[n[0]]||d.includes(n[0])||(d.push(n[0]),a[n[0]]={}),e.selectAll&&!a[n[0]][r]&&(a[n[0]][r]=[]),e.selectAll?a[n[0]][r].push(t):a[n[0]][r]=t):e.selectAll?a[n[0]].push(t):a[r]=t):e.selectAll?(a[i.path]||(a[i.path]=[]),a[i.path].push(t)):a[i.path]=t},f={},g=0;g<=e.length-1;g++){if((i=e[g]).interface)t=D.docMapKeys[i.interface],p(i,t,f);else{if(t=document.querySelectorAll(i.selector||""),r=!!(i.extractor&&1<=i.extractor.length),i.selectAll)for(var h=0;h<=t.length-1;h++){var m=(i.attr&&t[h].getAttributeNode(i.attr)?t[h].getAttributeNode(i.attr):t[h]).textContent;p(i,m,f)}if(r){f[i.path]=[];for(var l=t[0].children,b=Object.keys(i.extractor).length,c=0;c<=l.length-1;c++)a=l[c].tagName.toLowerCase(),i.extractor.find(u),Object.keys(o).length===b&&(s.push(o),o={},n=[]);y.extend(f[i.path],s)}t.length&&(t=(i.attr&&t[0].getAttributeNode(i.attr)?t[0].getAttributeNode(i.attr):t[0]).textContent)}i.selectAll||!1===r&&p(i,t,f)}return f}(h))),t="["+t+"]",h=e.patches||[],p=e.fpath,d=e.filter||[],u=e.filternot||[],Array.isArray(h)||(h=[h]),p&&(b=x(b,p,d,u)),e.wraproot&&((i={})[e.wraproot]=b,b=i),h.length&&jsonpatch.apply(b,h),e.debug&&v(g,"initEvent",b,h);try{k[t]=b}catch(e){return}if(C[t]=e,m&&(f.removeAttribute(A),i=j[t])&&g.trigger(i),!e.wait&&E[t])for(n=(o=E[t]).length,r=0;r!==n;r+=1){if((s=(i=o[r]).selector).length)try{a=jsonpointer.get(b,s)}catch(e){throw t+" - JSON selector not found: "+s}else a=b;y("#"+i.callerId).trigger({"type":"json-fetched.wb","fetch":{"response":a,"status":"200","refId":i.refId,"xhr":null}},this)}w.ready(g,T)}}}),e.on("patches.wb-jsonmanager",g,function(e){var t,a,r,n,i,o,s,l,c,d=e.target,u=y(d),p=e.patches,f=e.fpath,g=e.filter||[],h=e.filternot||[],m=!!e.cumulative;if(d===e.currentTarget&&Array.isArray(p)){if(!(t=w.getData(u,T)))return!0;if(a="["+t.name+"]",d.hasAttribute(A))return j[a]=e,!0;if(!E[a])throw"Applying patched on undefined dataset name: "+a;for(r=k[a],m||(r=y.extend(!0,Array.isArray(r)?[]:{},r)),f&&(r=x(r,f,g,h)),jsonpatch.apply(r,p),t.debug&&v(u,"patchesEvent",r,p),s=(i=E[a]).length,o=0;o!==s;o+=1){if((c=(l=i[o]).selector).length)try{n=jsonpointer.get(r,c)}catch(e){throw a+" - JSON selector not found: "+c}else n=r;y("#"+l.callerId).trigger({"type":"json-fetched.wb","fetch":{"response":n,"status":"200","refId":l.refId,"xhr":null}},this)}}}),e.on("postpone.wb-jsonmanager",function(t){var e,t=t.postpone,a=t.dsname,r=t.callerId,n=t.refId,t=t.selector;if(E[a]||(E[a]=[]),E[a].push({"callerId":r,"refId":n,"selector":t}),k[a]&&!C[a].wait){if(e=k[a],t.length)try{e=jsonpointer.get(e,t)}catch(e){throw a+" - JSON selector not found: "+t}y("#"+r).trigger({"type":"json-fetched.wb","fetch":{"response":e,"status":"200","refId":n,"xhr":null}},this)}}),e.on("op.action.wb-fieldflow",".wb-fieldflow",function(e,t){var a,r,n,i;t.op&&(t.preventSubmit=!0,a=y(t.provEvt),r="wb-fieldflow-submit",t=t,(i=(i=a.data(r))&&!n?i:[]).push(t),a.data(r,i))}),e.on("op.submit.wb-fieldflow",".wb-fieldflow",function(e,t){var a,r=t.op,t=t.source;if(!r)return!0;Array.isArray(r)?a=r:(a=[]).push(r),y(t).trigger({"type":"patches.wb-jsonmanager","patches":a})}),e.on("timerpoke.wb wb-init.wb-jsonmanager",g,function(e){var t,a,r,n,i,o,s,l,c,d,e=w.init(e,T,g),u=f[T]||{},p={};e&&(t=y(e),Modernizr.load({"load":["site!deps/json-patch"+w.getMode()+".js","site!deps/jsonpointer"+w.getMode()+".js"],"testReady":function(){return f.jsonpatch&&f.jsonpointer},"complete":function(){var e=w.getData(t,T);if(!D.registered){if(a=D.ops.concat(u.ops||[]),r=D.opsArray.concat(u.opsArray||[]),n=D.opsRoot.concat(u.opsRoot||[]),a.length)for(i=0,o=a.length;i!==o;i++)s=a[i],jsonpatch.registerOps(s.name,s.fn);if(r.length)for(i=0,o=r.length;i!==o;i++)s=r[i],jsonpatch.registerOpsArray(s.name,s.fn);if(n.length)for(i=0,o=n.length;i!==o;i++)s=n[i],jsonpatch.registerOpsRoot(s.name,s.fn);D.settings=y.extend({},D.settings,u.settings||{}),D.registered=!0}if(!(d=e.name)||d in h)throw"Dataset name must be unique";if(h.push(d),l=e.url)for(o=(l="string"==typeof l?[l]:l).length,S[d]=o,i=0;i!==o;i++)c=l[i],p={"nocache":e.nocache,"nocachekey":e.nocachekey,"data":e.data,"contentType":e.contenttype,"method":e.method},c.url?(p.savingPath=c.path||"",p.url=c.url):p.url=c,t.trigger({"type":"json-fetch.wb","fetch":p}),35===p.url.charCodeAt(0)&&91===p.url.charCodeAt(1)&&w.ready(t,T);else!l&&e.extractor?t.trigger({"type":"json-fetched.wb","fetch":{"response":{}}}):t.trigger({"type":"json-fetch.wb"}),w.ready(t,T)}}))}),w.add(g)}(jQuery,window,wb),function(u,r){"use strict";var e=r.doc,n="wb-postback",p="."+n,f={};e.on("timerpoke.wb wb-init.wb-postback",p,function(e){var t=r.init(e,n,p);if(t){var i=u(t),e=u.extend(!0,{},f,r.getData(i,n)),o=void 0!==i.data(n+"-multiple"),s=e.toggle||"hide",l=e.success,c=e.failure||l;const a="data-wb-blocked",d="data-wb-sending";t.addEventListener("submit",function(e){e.preventDefault(),t.parentElement.classList.contains("wb-frmvld")&&(i.valid()?u(this).removeAttr(a):u(this).attr(a,"true")),u(this).attr(a)||u(this).attr(d)||u(this).attr("data-wb-pii-blocked")||i.trigger(n+".submit",e.submitter)}),i.on(n+".submit",function(e,t){var a=i.serializeArray(),r=u(l),n=u(c);u(this).attr(d,!0),t&&t.name&&a.push({"name":t.name,"value":t.value}),n.addClass(s),r.addClass(s),u.ajax({"type":this.method,"url":this.action,"data":u.param(a)}).done(function(){i.trigger("success.wb-postback"),r.removeClass(s)}).fail(function(e){i.trigger("fail.wb-postback",e),n.removeClass(s)}).always(function(){o||i.addClass(s),i.removeAttr(d)})}),r.ready(u(t),n)}}),r.add(p)}(jQuery,wb),function(o,s,l){"use strict";var e=l.doc,c="wb-randomize",d="[data-wb-randomize]",u={};e.on("timerpoke.wb wb-init[data-wb-randomize]",d,function(e){var t,a,e=l.init(e,c,d);if(e){if(t=o(e),(a=o.extend(!0,{},u,s[c],l.getData(t,c))).attribute){if(!a.values||!Array.isArray(a.values))throw c+': You must define the property "values" to an array of strings when "attribute" property is defined.';var r,n=r=a.values;for(let e=n.length-1;0<e;e--){var i=Math.floor(Math.random()*(e+1));[n[e],n[i]]=[n[i],n[e]]}e.setAttribute(a.attribute,r[0])}else{if(!(e=a.selector?o(a.selector,t):t.children()).length)throw c+" selector setting is invalid or no children";a.shuffle&&(e=l.shuffleDOM(e)),a.toggle&&(e=l.pickElements(e,a.number)).toggleClass(a.toggle)}l.ready(t,c)}}),l.add(d)}(jQuery,window,wb),function(d,u){"use strict";var p,f,g="wb-zebra",h="."+g,e=g+"-col-hover",e="."+e+" td, "+e+" th",t=u.doc,m=0;t.on("timerpoke.wb wb-init.wb-zebra parsecomplete.wb-tableparser",h,function(e){var t,a,r=e.target;switch(e.type){case"timerpoke":case"wb-init":s=e,s=u.init(s,g,h),a=["site!deps/tableparser"+u.getMode()+".js"],s&&((t=s.id)||(t=g+"-id-"+m,m+=1,s.id=t),f||(p=u.i18n,f={"tableMention":p("hyphen")+p("tbl-txt"),"tableFollowing":p("hyphen")+p("tbl-dtls")}),Modernizr.load({"load":a,"complete":function(){d("#"+t).trigger("passiveparse.wb-tableparser")}}));break;case"parsecomplete":if(e.currentTarget===r){var n,i,o,s=d(r),l=s.data().tblparser;function c(e,t){for(var a=0,r=e.length;a!==r;a+=1)d(e[a].elem).addClass(t)}if(l.keycell&&c(l.keycell,"wb-cell-key"),l.desccell&&c(l.desccell,"wb-cell-desc"),l.layoutCell&&c(l.layoutCell,"wb-cell-layout"),l.lstrowgroup)for(n=0,i=l.lstrowgroup.length;n!==i;n+=1)3!==(o=l.lstrowgroup[n]).type&&3!==o.row[0].type||d(o.elem).addClass("wb-group-summary");if(l.colgroup)for(n=0,i=l.colgroup.length;n!==i;n+=1)3===(o=l.colgroup[n]).type&&d(o.elem).addClass("wb-group-summary");u.ready(s,g)}}return!0}),t.on("mouseenter focusin",e,function(e){e=d(e.currentTarget).data().tblparser;e.col&&e.col.elem&&d(e.col.elem).addClass("table-hover")}),t.on("mouseleave focusout",e,function(e){e=d(e.currentTarget).data().tblparser;e.col&&e.col.elem&&d(e.col.elem).removeClass("table-hover")}),u.add(h)}(jQuery,(window,document,wb));
//# sourceMappingURL=wet-boew.min.js.map