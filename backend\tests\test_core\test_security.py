"""
Unit tests for app/core/security.py

This file contains comprehensive unit tests for all security-related functions:

- authenticate_user:
    - test_authenticate_user_success
    - test_authenticate_user_wrong_password
    - test_authenticate_user_no_password_set
    - test_authenticate_user_not_found
    - test_authenticate_user_exception
- authenticate_msal_user:
    - test_authenticate_msal_user_success_with_user
    - test_authenticate_msal_user_success_no_user
    - test_authenticate_msal_user_missing_config
    - test_authenticate_msal_user_expired_token
    - test_authenticate_msal_user_invalid_token
    - test_authenticate_msal_user_unexpected_error
- create_access_token:
    - test_create_access_token_default_expiry
    - test_create_access_token_custom_expiry
    - test_create_access_token_no_email
- verify_token:
    - test_verify_token_success
    - test_verify_token_invalid
    - test_verify_token_with_email_extraction
    - test_verify_token_email_extraction_fails

Tests use mocking to isolate functions from external dependencies and database operations.
"""

import pytest
import uuid
import os
from datetime import datetime, timedelta, timezone
from unittest.mock import patch, MagicMock, Mock
from jose import jwt
from jose.exceptions import J<PERSON><PERSON>rror, ExpiredSignatureError

from app.core.security import (
    authenticate_user,
    authenticate_msal_user, 
    create_access_token,
    verify_token
)
from app.models.user import User as UserModel
from app.common.exceptions import NotFoundError


@pytest.fixture
def mock_db_session():
    """Fixture for a mocked database session."""
    db_session = MagicMock()
    return db_session


@pytest.fixture
def mock_user():
    """Fixture for a mock user object."""
    user = MagicMock(spec=UserModel)
    user.user_id = uuid.uuid4()
    user.email = "<EMAIL>"
    user.password = "hashed_password"
    user.azure_ad_id = "azure_ad_123"
    return user


@pytest.fixture
def mock_user_no_password():
    """Fixture for a mock user without password (Azure AD user)."""
    user = MagicMock(spec=UserModel)
    user.user_id = uuid.uuid4()
    user.email = "<EMAIL>"
    user.password = None
    user.azure_ad_id = "azure_ad_456"
    return user


class TestAuthenticateUser:
    """Test suite for authenticate_user function."""
    
    @patch('app.core.security.verify_password')
    @patch('app.core.security.get_user_by_email')
    def test_authenticate_user_success(self, mock_get_user, mock_verify_password, mock_db_session, mock_user):
        """Test successful user authentication."""
        mock_get_user.return_value = mock_user
        mock_verify_password.return_value = True
        
        result = authenticate_user(mock_db_session, "<EMAIL>", "password123")
        
        assert result == mock_user
        mock_get_user.assert_called_once_with(mock_db_session, "<EMAIL>")
        mock_verify_password.assert_called_once_with("password123", "hashed_password")
    
    @patch('app.core.security.verify_password')
    @patch('app.core.security.get_user_by_email')
    def test_authenticate_user_wrong_password(self, mock_get_user, mock_verify_password, mock_db_session, mock_user):
        """Test authentication with wrong password."""
        mock_get_user.return_value = mock_user
        mock_verify_password.return_value = False
        
        result = authenticate_user(mock_db_session, "<EMAIL>", "wrong_password")
        
        assert result is False
        mock_verify_password.assert_called_once_with("wrong_password", "hashed_password")
    
    @patch('app.core.security.log_warning')
    @patch('app.core.security.get_user_by_email')
    def test_authenticate_user_no_password_set(self, mock_get_user, mock_log_warning, mock_db_session, mock_user_no_password):
        """Test authentication for Azure AD user with no local password."""
        mock_get_user.return_value = mock_user_no_password
        
        result = authenticate_user(mock_db_session, "<EMAIL>", "any_password")
        
        assert result is False
        mock_log_warning.assert_called_once()
        assert "no password set" in mock_log_warning.call_args[0][0]
    
    @patch('app.core.security.get_user_by_email')
    def test_authenticate_user_not_found(self, mock_get_user, mock_db_session):
        """Test authentication when user is not found."""
        mock_get_user.side_effect = NotFoundError()
        
        result = authenticate_user(mock_db_session, "<EMAIL>", "password123")
        
        assert result is False
    
    @patch('app.core.security.log_error')
    @patch('app.core.security.get_user_by_email')
    def test_authenticate_user_exception(self, mock_get_user, mock_log_error, mock_db_session):
        """Test authentication when an unexpected exception occurs."""
        mock_get_user.side_effect = Exception("Database error")
        
        result = authenticate_user(mock_db_session, "<EMAIL>", "password123")
        
        assert result is False
        mock_log_error.assert_called_once()
        assert "Error during authentication" in mock_log_error.call_args[0][0]


class TestAuthenticateMsalUser:
    """Test suite for authenticate_msal_user function."""
    
    @patch.dict(os.environ, {'TENANT_ID': 'test_tenant', 'CLIENT_ID': 'test_client'})
    @patch('app.core.security.get_user_by_azure_ad_id')
    @patch('app.core.security.jwt.decode')
    @patch('app.core.security.PyJWKClient')
    def test_authenticate_msal_user_success_with_user(self, mock_jwks_client, mock_jwt_decode, mock_get_user, mock_db_session, mock_user):
        """Test successful MSAL authentication with existing user."""
        # Setup mocks
        mock_signing_key = MagicMock()
        mock_signing_key.key = "test_key"
        mock_jwks_client.return_value.get_signing_key_from_jwt.return_value = mock_signing_key
        
        token_claims = {'oid': 'azure_ad_123', 'email': '<EMAIL>'}
        mock_jwt_decode.return_value = token_claims
        mock_get_user.return_value = mock_user
        
        result_user, result_claims = authenticate_msal_user(mock_db_session, "valid_token")
        
        assert result_user == mock_user
        assert result_claims == token_claims
        mock_get_user.assert_called_once_with(mock_db_session, 'azure_ad_123')
    
    @patch.dict(os.environ, {'TENANT_ID': 'test_tenant', 'CLIENT_ID': 'test_client'})
    @patch('app.core.security.get_user_by_azure_ad_id')
    @patch('app.core.security.jwt.decode')
    @patch('app.core.security.PyJWKClient')
    def test_authenticate_msal_user_success_no_user(self, mock_jwks_client, mock_jwt_decode, mock_get_user, mock_db_session):
        """Test successful MSAL authentication with no existing user."""
        # Setup mocks
        mock_signing_key = MagicMock()
        mock_signing_key.key = "test_key"
        mock_jwks_client.return_value.get_signing_key_from_jwt.return_value = mock_signing_key
        
        token_claims = {'oid': 'new_azure_id', 'email': '<EMAIL>'}
        mock_jwt_decode.return_value = token_claims
        mock_get_user.side_effect = NotFoundError()
        
        result_user, result_claims = authenticate_msal_user(mock_db_session, "valid_token")
        
        assert result_user is None
        assert result_claims == token_claims
    
    @patch('app.core.security.log_error')
    def test_authenticate_msal_user_missing_config(self, mock_log_error, mock_db_session):
        """Test MSAL authentication with missing configuration."""
        with patch.dict(os.environ, {}, clear=True):
            result_user, result_claims = authenticate_msal_user(mock_db_session, "token")
            
            assert result_user is None
            assert result_claims is None
            mock_log_error.assert_called_once()
            assert "not configured" in mock_log_error.call_args[0][0]
    
    @patch.dict(os.environ, {'TENANT_ID': 'test_tenant', 'CLIENT_ID': 'test_client'})
    @patch('app.core.security.log_warning')
    @patch('app.core.security.PyJWKClient')
    def test_authenticate_msal_user_expired_token(self, mock_jwks_client, mock_log_warning, mock_db_session):
        """Test MSAL authentication with expired token."""
        mock_jwks_client.return_value.get_signing_key_from_jwt.side_effect = ExpiredSignatureError()
        
        result_user, result_claims = authenticate_msal_user(mock_db_session, "expired_token")
        
        assert result_user is None
        assert result_claims is None
        mock_log_warning.assert_called_once()
        assert "expired" in mock_log_warning.call_args[0][0]
    
    @patch.dict(os.environ, {'TENANT_ID': 'test_tenant', 'CLIENT_ID': 'test_client'})
    @patch('app.core.security.log_warning')
    @patch('app.core.security.PyJWKClient')
    def test_authenticate_msal_user_invalid_token(self, mock_jwks_client, mock_log_warning, mock_db_session):
        """Test MSAL authentication with invalid token."""
        mock_jwks_client.return_value.get_signing_key_from_jwt.side_effect = JWTError("Invalid token")
        
        result_user, result_claims = authenticate_msal_user(mock_db_session, "invalid_token")
        
        assert result_user is None
        assert result_claims is None
        mock_log_warning.assert_called_once()
        assert "Invalid Azure AD token" in mock_log_warning.call_args[0][0]
    
    @patch.dict(os.environ, {'TENANT_ID': 'test_tenant', 'CLIENT_ID': 'test_client'})
    @patch('app.core.security.log_error')
    @patch('app.core.security.PyJWKClient')
    def test_authenticate_msal_user_unexpected_error(self, mock_jwks_client, mock_log_error, mock_db_session):
        """Test MSAL authentication with unexpected error."""
        mock_jwks_client.return_value.get_signing_key_from_jwt.side_effect = Exception("Unexpected error")
        
        result_user, result_claims = authenticate_msal_user(mock_db_session, "token")
        
        assert result_user is None
        assert result_claims is None
        mock_log_error.assert_called_once()
        assert "Unexpected error" in mock_log_error.call_args[0][0]


class TestCreateAccessToken:
    """Test suite for create_access_token function."""
    
    @patch('app.core.security.logger')
    @patch('app.core.security.settings')
    @patch('app.core.security.jwt.encode')
    def test_create_access_token_default_expiry(self, mock_jwt_encode, mock_settings, mock_logger):
        """Test token creation with default expiry time."""
        mock_settings.SECRET_KEY = "test_secret"
        mock_jwt_encode.return_value = "encoded_token"
        
        data = {"sub": "<EMAIL>", "role": "admin"}
        result = create_access_token(data)
        
        assert result == "encoded_token"
        mock_jwt_encode.assert_called_once()
        
        # Verify the data passed to jwt.encode includes expiry
        call_args = mock_jwt_encode.call_args[0]
        token_data = call_args[0]
        assert "exp" in token_data
        assert token_data["sub"] == "<EMAIL>"
        assert token_data["role"] == "admin"
        
        # Verify logging
        mock_logger.info.assert_called_once()
        assert "Created <NAME_EMAIL>" in mock_logger.info.call_args[0][0]
    
    @patch('app.core.security.logger')
    @patch('app.core.security.settings')
    @patch('app.core.security.jwt.encode')
    def test_create_access_token_custom_expiry(self, mock_jwt_encode, mock_settings, mock_logger):
        """Test token creation with custom expiry time."""
        mock_settings.SECRET_KEY = "test_secret"
        mock_jwt_encode.return_value = "encoded_token"
        
        data = {"sub": "<EMAIL>"}
        custom_expiry = timedelta(hours=2)
        result = create_access_token(data, custom_expiry)
        
        assert result == "encoded_token"
        
        # Verify the expiry time is approximately correct (within 1 minute tolerance)
        call_args = mock_jwt_encode.call_args[0]
        token_data = call_args[0]
        expected_exp = datetime.now(timezone.utc) + custom_expiry
        actual_exp = token_data["exp"]
        
        # Allow 1 minute tolerance for test execution time
        assert abs((actual_exp - expected_exp).total_seconds()) < 60
    
    @patch('app.core.security.logger')
    @patch('app.core.security.settings')
    @patch('app.core.security.jwt.encode')
    def test_create_access_token_no_email(self, mock_jwt_encode, mock_settings, mock_logger):
        """Test token creation without email in data."""
        mock_settings.SECRET_KEY = "test_secret"
        mock_jwt_encode.return_value = "encoded_token"
        
        data = {"role": "admin"}
        result = create_access_token(data)
        
        assert result == "encoded_token"
        
        # Verify logging uses "unknown" when no email
        mock_logger.info.assert_called_once()
        assert "Created token for unknown" in mock_logger.info.call_args[0][0]


class TestVerifyToken:
    """Test suite for verify_token function."""
    
    @patch('app.core.security.settings')
    @patch('app.core.security.jwt.decode')
    def test_verify_token_success(self, mock_jwt_decode, mock_settings):
        """Test successful token verification."""
        mock_settings.SECRET_KEY = "test_secret"
        expected_payload = {"sub": "<EMAIL>", "role": "admin", "exp": 1234567890}
        mock_jwt_decode.return_value = expected_payload
        
        result = verify_token("valid_token")
        
        assert result == expected_payload
        mock_jwt_decode.assert_called_once_with("valid_token", "test_secret", algorithms=["HS256"])
    
    @patch('app.core.security.logger')
    @patch('app.core.security.settings')
    @patch('app.core.security.jwt.decode')
    def test_verify_token_invalid(self, mock_jwt_decode, mock_settings, mock_logger):
        """Test token verification with invalid token."""
        mock_settings.SECRET_KEY = "test_secret"
        mock_jwt_decode.side_effect = JWTError("Invalid token")
        
        result = verify_token("invalid_token")
        
        assert result is None
        mock_logger.warning.assert_called_once()
        assert "JWT verification error" in mock_logger.warning.call_args[0][0]
    
    @patch('app.core.security.logger')
    @patch('app.core.security.settings')
    @patch('app.core.security.jwt.decode')
    def test_verify_token_with_email_extraction(self, mock_jwt_decode, mock_settings, mock_logger):
        """Test token verification failure with email extraction for logging."""
        mock_settings.SECRET_KEY = "test_secret"
        
        # First call fails with JWTError, second call (for logging) succeeds
        mock_jwt_decode.side_effect = [
            JWTError("Invalid signature"),
            {"sub": "<EMAIL>"}  # Unverified payload for logging
        ]
        
        result = verify_token("invalid_token")
        
        assert result is None
        assert mock_jwt_decode.call_count == 2
        
        # Verify logging includes extracted email
        mock_logger.warning.assert_called_once()
        log_message = mock_logger.warning.call_args[0][0]
        assert "JWT verification error <NAME_EMAIL>" in log_message
    
    @patch('app.core.security.logger')
    @patch('app.core.security.settings')
    @patch('app.core.security.jwt.decode')
    def test_verify_token_email_extraction_fails(self, mock_jwt_decode, mock_settings, mock_logger):
        """Test token verification failure when email extraction also fails."""
        mock_settings.SECRET_KEY = "test_secret"
        
        # Both calls fail
        mock_jwt_decode.side_effect = [
            JWTError("Invalid signature"),
            Exception("Cannot decode")  # Email extraction fails
        ]
        
        result = verify_token("invalid_token")
        
        assert result is None
        
        # Verify logging uses "unknown" when email extraction fails
        mock_logger.warning.assert_called_once()
        log_message = mock_logger.warning.call_args[0][0]
        assert "JWT verification error for user unknown" in log_message
