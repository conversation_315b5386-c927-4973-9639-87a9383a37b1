from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, ForeignKey, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
import uuid
from .base import Base
from app.common.constants import RequisitionSampleStatus

class RequisitionSample(Base):
    __tablename__ = "requisition_samples"

    req_sample_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    requisition_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey('requisitions.req_id'), nullable=False)
    sample_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey('samples.sample_id'), nullable=False)
    survey_id: Mapped[Optional[str]] = mapped_column(String)
    status: Mapped[str] = mapped_column(
        Enum(RequisitionSampleStatus, name='requisition_sample_status', values_callable=lambda x: [e.value for e in x]),
        default=RequisitionSampleStatus.SUBMITTED.value
    )
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    
    tests = relationship("RequisitionSampleTest", back_populates="requisition_sample", cascade="all, delete-orphan")
    sample = relationship("Sample", backref="requisition_samples")