/*
 * Test Type Table Manager
 * Table for managing test types with bulk operations
 * Uses table architecture with BulkActions mixin
 */
import { Table } from '../../core/components/table.js';
import { withBulkActions } from '../../core/components/table-features.js';
import { createTableConfig } from '../../core/helpers/table-helpers.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { escapeHtml } from '../../core/helpers/format-helpers.js';
import { fetchTestTypesForLab } from './shared/lab-helpers.js';
import { TestApi } from '../../core/services/test-api.js';
import { TEST_CONFIGS } from '../../core/config/test-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

// Compose Table with BulkActions functionality
const BulkActionsTable = withBulkActions(Table);

class TestTypeTableManager extends BulkActionsTable {
    constructor(options = {}) {
        super(createTableConfig(TEST_CONFIGS, 'testTypeTable', 'test', {
            entityFormatters: {
                testStatusCheckbox: (_, row) => {
                    const testId = row.test_type_id || row.id;
                    const testName = escapeHtml(row.name || '');
                    const isActive = row.is_active;
                    const checkedAttr = isActive ? 'checked' : '';

                    return `<div class="checkbox gc-chckbxrdio">
                        <input type="checkbox"
                               id="test-active-${testId}"
                               class="test-status-checkbox"
                               data-test-id="${testId}"
                               data-original-state="${isActive}"
                               ${checkedAttr}>
                        <label for="test-active-${testId}">
                            <span class="wb-inv">Toggle active status for ${testName}</span>
                        </label>
                    </div>`;
                }
            },
            ...options
        }));

        // Store user info for conditional columns
        this.userInfo = options.userInfo || null;
    }

    // Fetch test type data from API
    async fetchData() {
        try {
            const testTypes = await fetchTestTypesForLab(this.userInfo);
            return testTypes || [];
        } catch (error) {
            console.error('Failed to fetch test types:', error);
            throw error;
        }
    }

    // Override create method to pass user info for conditional columns
    async create(config = {}) {
        // Update userInfo if provided in config
        if (config.userInfo) {
            this.userInfo = config.userInfo;
        }

        // Set current config for conditional column evaluation
        this.currentConfig = {
            role: this.userInfo?.role || 'user',
            ...config
        };

        return super.create(config);
    }

    // Build bulk actions HTML for test type management
    buildBulkActionsHtml() {
        const saveButtonText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.buttons.save }, 'message');
        const cancelButtonText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.buttons.cancel }, 'message');

        return `
            <!-- Bulk Action Buttons - Following GC Web Standards pattern -->
            <div class="form-group mrgn-tp-lg mrgn-bttm-xl" id="bulk-actions-container">
                <button type="button" class="btn btn-primary mrgn-rght-md" id="save-changes-btn">
                    <span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span>
                    ${saveButtonText}
                </button>
                <button type="button" class="btn btn-default" id="cancel-changes-btn">
                    <span class="glyphicon glyphicon-remove" aria-hidden="true"></span>
                    ${cancelButtonText}
                </button>
            </div>
        `;
    }

    // Initialize event handlers for bulk operations
    initializeEventHandlers() {
        const self = this;

        // Handle checkbox changes using document delegation
        $(document).off('change', '.test-status-checkbox').on('change', '.test-status-checkbox', function() {
            self.handleCheckboxChange($(this));
        });

        // Handle save changes
        $(document).off('click', '#save-changes-btn').on('click', '#save-changes-btn', function() {
            self.handleSaveChanges();
        });

        // Handle cancel changes
        $(document).off('click', '#cancel-changes-btn').on('click', '#cancel-changes-btn', function() {
            self.handleCancelChanges();
        });

        // Initialize button states
        this.trackChanges(false);
    }

    // Handle individual checkbox change
    handleCheckboxChange($checkbox) {
        const originalState = $checkbox.data('original-state');
        const currentState = $checkbox.is(':checked');

        // Update change tracking
        if (originalState !== currentState) {
            this.trackChanges(true);
        } else {
            // Check if any other checkboxes have changes
            this.trackChanges(this.hasAnyChanges());
        }
    }

    // Utility method to iterate over all checkboxes with callback
    forEachCheckbox(callback) {
        $('.test-status-checkbox').each(function() {
            const $checkbox = $(this);
            const testId = $checkbox.data('test-id');
            const originalState = $checkbox.data('original-state');
            const currentState = $checkbox.is(':checked');

            return callback($checkbox, {
                testId,
                originalState,
                currentState,
                hasChanged: originalState !== currentState
            });
        });
    }

    // Check if any checkboxes have changes
    hasAnyChanges() {
        let hasChanges = false;
        this.forEachCheckbox((_, data) => {
            if (data.hasChanged) {
                hasChanges = true;
                return false; // Break out of each loop
            }
        });
        return hasChanges;
    }

    // Override toggleBulkActions for button state management
    toggleBulkActions() {
        // Buttons are always enabled for simple user experience
        // This method is called by trackChanges() in the base class
    }

    // Handle save changes action
    async handleSaveChanges() {
        try {
            const updates = this.collectChanges();

            if (updates.length === 0) {
                showMessage('#page-success-container', 'global.messages.info.common', 'noChangesToSave', this.messageConfigMaps);
                return;
            }

            // Disable button to prevent double-clicks
            const $saveBtn = $('#save-changes-btn');
            $saveBtn.prop('disabled', true);

            // Perform batch update
            await TestApi.batchUpdateTestTypes(updates);

            // Handle success
            this.handleSaveSuccess();

        } catch (error) {
            console.error('Bulk update failed:', error);
            this.handleSaveError(error);
        }
    }

    // Handle successful save
    handleSaveSuccess() {
        // Update original states to current states
        this.forEachCheckbox(($checkbox, data) => {
            $checkbox.data('original-state', data.currentState);
        });

        // Reset change tracking
        this.trackChanges(false);
        this.resetSaveButton();

        // Show success message with entity term
        const entityTerm = getLocalizedText({ message: TEST_CONFIGS.ui.testTypeTable.entityTerms }, 'message');
        showMessage('#page-success-container', 'global.messages.success.templates', 'entityUpdated', this.messageConfigMaps, {
            entity: entityTerm
        });
    }

    // Handle save error
    handleSaveError() {
        this.resetSaveButton();

        // Show error message
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', this.messageConfigMaps);
    }

    // Reset save button to default state
    resetSaveButton() {
        const $saveBtn = $('#save-changes-btn');
        const saveText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.buttons.save }, 'message');
        $saveBtn.prop('disabled', false).html(`<span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span> ${saveText}`);
    }

    // Collect all changes for batch update
    collectChanges() {
        const updates = [];
        this.forEachCheckbox((_, data) => {
            if (data.hasChanged) {
                updates.push({
                    test_type_id: data.testId,
                    is_active: data.currentState
                });
            }
        });
        return updates;
    }

    // Handle cancel changes
    handleCancelChanges() {
        // Revert all checkboxes to original states
        this.forEachCheckbox(($checkbox, data) => {
            $checkbox.prop('checked', data.originalState);
        });

        // Show cancelled message
        showMessage('#page-success-container', 'global.messages.info.common', 'cancelled', this.messageConfigMaps);

        // Reset change tracking
        this.trackChanges(false);
    }

    // Handle errors with standard GC error display
    handleError($container, error) {
        $container.empty();
        console.error(`Failed to create test type manager "${this.tableId}":`, error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', this.messageConfigMaps);
    }
}

// Export class and factory function for flexible usage
export { TestTypeTableManager };

// Factory function and default instance
export const createTestTypeTableManager = (options = {}) => new TestTypeTableManager(options);
export const testTypeTableManager = createTestTypeTableManager();
