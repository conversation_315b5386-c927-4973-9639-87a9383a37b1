"""
Test helper functions organized by domain and functionality.
These helpers provide reusable utilities for test setup and data creation.
"""

from typing import Any, Dict, List, Callable
from app.models.requisition_sample import RequisitionSample
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
from datetime import datetime, timedelta
import uuid
from uuid import UUID
from io import BytesIO

from app.common.constants import RequisitionStatus, UserRole
from app.models.file import File
from app.models.labs import Lab
from app.models.user import User
from app.models.requisition import Requisition
from app.models.sample import Sample
from app.models.staff import Staff
from app.models.test_type import TestType
from app.database import Base
from app.schemas.file import FileCreate
from app.utils.hashing import get_password_hash
from tests.test_utils.constants import BASE_REQUISITION_DATA, FILE_TEST_CONSTANTS

class TestHelpers:
    """
    Collection of test helper methods organized by domain.
    Provides a clean interface for common test operations.
    """
    
    class Auth:
        """Authentication related test helpers"""
        @staticmethod
        def get_headers(token: str) -> dict[str, str]:
            """Create authorization headers for authenticated requests."""
            return {"Authorization": f"Bearer {token}"}
    
    class Files:
        """File operation test helpers"""
        @staticmethod
        def create_test_file(db: Session, user_id: UUID, file_data: Dict[str, Any] | None = None) -> File:
            """Create a test file with specified or default attributes."""
            if file_data is None:
                file_data = FILE_TEST_CONSTANTS["TEST_FILES"]["unpublished"].copy()
            
            if "storage_id" not in file_data:
                file_data["storage_id"] = "test-storage-id"
            
            file = File(
                file_id=uuid.uuid4(),
                uploaded_by=user_id,
                **file_data
            )
            db.add(file)
            db.commit()
            db.refresh(file)
            return file

        @staticmethod
        def create_upload_data(file_content: bytes, file_name: str, file_type: str) -> Dict:
            """Prepare multipart form data for file upload testing."""
            return {
                "files": {
                    "file": (file_name, BytesIO(file_content), file_type)
                },
                "data": {
                    "file_type": file_type,
                    "file_size": str(len(file_content))
                }
            }

        @staticmethod
        def create_file_data(**kwargs) -> FileCreate:
            """Helper to create FileCreate objects with default test data."""
            base_data = FILE_TEST_CONSTANTS["TEST_FILES"]["base"]
            return FileCreate(**{**base_data, **kwargs})
    
    class Labs:
        """Lab management test helpers"""
        @staticmethod
        def create_test_lab(db: Session, lab_data: dict) -> Lab:
            lab = Lab(
                lab_id=uuid.uuid4(),
                lab_name=lab_data["lab_name"]
            )
            db.add(lab)
            db.commit()
            db.refresh(lab)

            return lab
        
        @staticmethod
        def create_test_labs(db: Session, labs_data: dict) -> List[Lab]:
            labs = []
            for lab in labs_data:
                labs.append(TestHelpers.Labs.create_test_lab(db,labs_data[lab]))
            return labs
    
    class Users:
        """User management test helpers"""
        @staticmethod
        def create_test_user(db: Session, user_data: dict) -> User:
            """Create a test user with specified attributes."""
            user = User(
                user_id=uuid.uuid4(),
                email=user_data["email"],
                password=get_password_hash(user_data["password"]),
                azure_ad_id = user_data["azure_ad_id"],
                is_active=True
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            return user
        
    class Requisitions:
        """Requisition management test helpers"""
        @staticmethod
        def create_test_requisition(
            db: Session, 
            user_id: UUID, 
            lab_id: UUID,
            deadline_days: int = 7,
            completion_days: int = 5,
            **override_data
        ) -> Requisition:
            """Create a test requisition with specified attributes."""
            data = BASE_REQUISITION_DATA.copy()
            data["submitted_by"] = user_id
            data["lab_id"] = lab_id
            
            # Add default dates if not explicitly provided in override_data
            if "deadline" not in override_data:
                data["deadline"] = datetime.now() + timedelta(days=deadline_days)
            if "expected_completion_date" not in override_data:
                data["expected_completion_date"] = datetime.now() + timedelta(days=completion_days)
            
            # Update with any override data
            data.update(override_data)
            
            requisition = Requisition(req_id=uuid.uuid4(), **data)
            db.add(requisition)
            db.commit()
            db.refresh(requisition)
            return requisition

        @staticmethod
        def create_batch(db: Session, user_ids: List[UUID], lab_ids: List[UUID], count: int = 3) -> List[Requisition]:
            """Create a batch of test requisitions with different statuses and owners."""
            requisitions = []
            for i in range(count):
                status = (
                    RequisitionStatus.SUBMITTED.value if i == 0
                    else RequisitionStatus.IN_PROGRESS.value if i == 1
                    else RequisitionStatus.COMPLETE.value
                )
                owner_id = user_ids[0] if i == 0 else user_ids[1]
                
                for lab_id in lab_ids:
                    req = TestHelpers.Requisitions.create_test_requisition(
                        db,
                        owner_id,
                        lab_id = lab_id,
                        lsa_number=f"LSA{i} - {lab_id}",
                        project_number=f"PRJ-{i} - {lab_id}",
                        project_name=f"Test Project {i} - {lab_id}",
                        status=status
                    )
                    requisitions.append(req)
            return requisitions

        @staticmethod
        def add_sample_to_requisition(
            db: Session,
            req_id: UUID,
            sample_id: UUID
        ) -> None:
            """Add a sample to a requisition."""
            from app.models.requisition_sample import RequisitionSample
            req_sample = RequisitionSample(
                requisition_id=req_id,
                sample_id=sample_id
            )
            db.add(req_sample)
            db.commit()

        """@staticmethod
        def create_test_requisition_with_samples(db: Session, user_id: str, samples: List[Sample], requisition_data: dict) -> Requisition:
            ""Create a test requisition and associate it with samples.""
            # Create the requisition
            requisition = TestHelpers.Requisitions.create_test_requisition(
                db=db,
                user_id=user_id,
                **requisition_data
            )

            # Add each sample to the requisition
            for sample in samples:
                req_sample = RequisitionSample(
                    requisition_id=requisition.req_id,  # Changed from req_id to requisition_id
                    sample_id=sample.sample_id
                )
                db.add(req_sample)
            
            db.commit()
            db.refresh(requisition)
            return requisition"""

        @staticmethod
        def add_tests_to_requisition(db: Session, requisition: Requisition, test_type_id: str, user_id: str, role: UserRole) -> Requisition:
            """Add tests to the first sample of a requisition."""
            if not requisition.samples:
                raise ValueError("Requisition has no samples")

            sample = requisition.samples[0]
            test_type_ids = [str(test_type_id)]
            
            from app.crud import requisition as requisition_crud
            requisition_crud.add_tests_to_requisition_sample(  # Changed to use the crud function directly
                db,
                str(requisition.req_id),
                str(sample.sample_id),
                test_type_ids,
                str(user_id),
                role,
                str(requisition.lab_id)
            )
            
            db.refresh(requisition)
            return requisition
    
    class Samples:
        """Sample management test helpers"""
        @staticmethod
        def create_test_sample(db: Session, sample_data: dict) -> Sample:
            """Create a test sample with specified attributes."""
            sample = Sample(
                sample_id=uuid.uuid4(),
                created_at=datetime.now(),
                updated_at=datetime.now(),
                **sample_data
            )
            db.add(sample)
            db.commit()
            db.refresh(sample)
            return sample

        @staticmethod
        def create_batch(db: Session, sample_data_list: List[dict]) -> List[Sample]:
            """Create multiple test samples."""
            samples = []
            for data in sample_data_list:
                sample = TestHelpers.Samples.create_test_sample(db, data)
                samples.append(sample)
            return samples
    
    class Staff:
        """Staff management test helpers"""
        @staticmethod
        def create_test_staff(db: Session, staff_data: dict) -> Staff:
            """Create a test staff member with specified attributes."""
            if staff_data["role"] == UserRole.SCIENTIST:
                staff_data["lab_id"] = None
                
            
            staff = Staff(
                staff_id=uuid.uuid4(),
                user_id=staff_data["user_id"],
                lab_id=staff_data["lab_id"],
                role=staff_data["role"]
            )
            db.add(staff)
            db.commit()
            db.refresh(staff)
            return staff
            
        @staticmethod
        def create_test_staff_multiple(db: Session, staff_data_list: List[dict]) -> List[Staff]:
            """Create multiple staff entries for a user with different lab roles."""
            staff_list = []
            for staff_data in staff_data_list:
                staff = TestHelpers.Staff.create_test_staff(db, staff_data)
                staff_list.append(staff)
            return staff_list

    class TestTypes:
        """Helper methods for creating test types in tests."""

        @staticmethod
        def create_test_type(db: Session, test_type_data: dict, lab_id: str = None) -> TestType:
            """Create a single test type for testing."""
            test_type = TestType(
                test_type_id=uuid.uuid4(),
                name=test_type_data["name"],
                description=test_type_data["description"],
                is_active=test_type_data.get("is_active", True),
                lab_id=lab_id
            )
            db.add(test_type)
            db.commit()
            db.refresh(test_type)
            return test_type

        @staticmethod
        def create_test_types(db: Session, test_types_data: dict, lab_id: str = None) -> List[TestType]:
            """Create multiple test types for testing."""
            test_types = []
            for test_type_data in test_types_data.values():
                test_type = TestHelpers.TestTypes.create_test_type(db, test_type_data, lab_id)
                test_types.append(test_type)
            return test_types

        @staticmethod
        def create_test_types_for_multiple_labs(db: Session, test_types_data: dict, labs: List) -> List[TestType]:
            """Create test types distributed across multiple labs."""
            test_types = []
            lab_index = 0
            for test_type_data in test_types_data.values():
                lab_id = str(labs[lab_index % len(labs)].lab_id) if labs else None
                test_type = TestHelpers.TestTypes.create_test_type(db, test_type_data, lab_id)
                test_types.append(test_type)
                lab_index += 1
            return test_types

        @staticmethod
        def create_batch_update_data(test_types: List[TestType], update_data_list: List[dict]) -> List[dict]:
            """
            Create batch update data by combining test type IDs with update data.

            Args:
                test_types: List of test type objects to update
                update_data_list: List of update data dictionaries

            Returns:
                List of batch update items with test_type_id and update fields
            """
            batch_updates = []
            for i, update_data in enumerate(update_data_list):
                if i < len(test_types):
                    batch_item = {"test_type_id": str(test_types[i].test_type_id)}
                    batch_item.update(update_data)
                    batch_updates.append(batch_item)
            return batch_updates

        @staticmethod
        def verify_no_updates_applied(db: Session, test_types: List[TestType], original_values: List[dict]):
            """
            Verify that test types retain their original values (transaction rollback verification).

            Args:
                db: Database session
                test_types: List of test type objects that should retain original values
                original_values: List of original values that should be preserved
            """
            for i, test_type in enumerate(test_types):
                db.refresh(test_type)
                if i < len(original_values):
                    original_value = original_values[i]
                    # Verify that original values are preserved
                    if "name" in original_value:
                        assert test_type.name == original_value["name"], f"Name should have been preserved for test type {i}, expected {original_value['name']}, got {test_type.name}"
                    if "description" in original_value:
                        assert test_type.description == original_value["description"], f"Description should have been preserved for test type {i}"
                    if "is_active" in original_value:
                        assert test_type.is_active == original_value["is_active"], f"is_active should have been preserved for test type {i}"



    class Database:
        """Database utility helpers"""
        @staticmethod
        def clear_all_tables(session: Session) -> None:
            """Clear all data from database tables while preserving structure."""
            try:
                session.rollback()  # Ensure clean state
                session.execute(text("SET session_replication_role = 'replica';"))
                meta = Base.metadata
                for table in reversed(meta.sorted_tables):
                    session.execute(text(f"TRUNCATE TABLE {table.name} CASCADE"))               
                session.commit()
            except Exception as e:
                session.rollback()
                raise e
            finally:
                session.execute(text("SET session_replication_role = 'origin';"))
                session.commit()
