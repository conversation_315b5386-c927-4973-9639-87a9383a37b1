"""
Model for labs table in the DB

This script describes the DB model for the Lab table
"""
from sqlalchemy import String
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from .base import Base

class Lab(Base):
    __tablename__ = "labs"

    lab_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lab_name: Mapped[str] = mapped_column(String)

    # Relationship to TestType
    test_types = relationship("TestType", back_populates="lab")