var jsonpatch;!function(e){function i(e){if(m(e))for(var t=new Array(e.length),r=0;r<t.length;r++)t[r]=""+r;else{if(Object.keys)return Object.keys(e);var n,t=[];for(n in e)e.hasOwnProperty(n)&&t.push(n)}return t}function o(e,t){switch(typeof e){case"undefined":case"boolean":case"string":case"number":return e===t;case"object":if(null===e)return null===t;if(m(e)){if(!m(t)||e.length!==t.length)return!1;for(var r=0,n=e.length;r<n;r++)if(!o(e[r],t[r]))return!1}else{var a=i(t).length;if(i(e).length!==a)return!1;for(r=0;r<a;r++)if(!o(e[r],t[r]))return!1}return!0;default:return!1}}var g={add:function(e,t){e[t]=this.value},remove:function(e,t){var r=e[t];return delete e[t],r},replace:function(e,t){var r=e[t];return e[t]=this.value,r},move:function(e,t,r){var n={op:"_get",path:this.path},n=(u(r,[n]),void 0===n.value?void 0:JSON.parse(JSON.stringify(n.value))),a={op:"_get",path:this.from};return u(r,[a]),u(r,[{op:"remove",path:this.from}]),u(r,[{op:"add",path:this.path,value:a.value}]),n},copy:function(e,t,r){var n={op:"_get",path:this.from};u(r,[n]),u(r,[{op:"add",path:this.path,value:n.value}])},test:function(e,t){return o(e[t],this.value)},_get:function(e,t){this.value=e[t]}},d={add:function(e,t){return e.splice(t,0,this.value),t},remove:function(e,t){return e.splice(t,1)[0]},replace:function(e,t){var r=e[t];return e[t]=this.value,r},move:g.move,copy:g.copy,test:g.test,_get:g._get},y={add:function(e){for(var t in y.remove.call(this,e),this.value)this.value.hasOwnProperty(t)&&(e[t]=this.value[t])},remove:function(e){var t,r={};for(t in e)e.hasOwnProperty(t)&&(r[t]=e[t],g.remove.call(this,e,t));return r},replace:function(e){var t=u(e,[{op:"remove",path:this.path}]);return u(e,[{op:"add",path:this.path,value:this.value}]),t[0]},move:g.move,copy:g.copy,test:function(e){return JSON.stringify(e)===JSON.stringify(this.value)},_get:function(e){this.value=e}},u=function(e,t,r){r||t.sort(function(e,t){return"test"===e.op&&"test"!==t.op?-1:"test"===t.op?1:0});for(var n,a,i=new Array(t.length),o=0,u=t.length;o<u;){n=t[o],o+=1;for(var s=n.path||"",h=n.op,l=!r&&"test"===h,f=s.split("/"),p=e,c=1,v=f.length;p;){if(a=f[c],c+=1,void 0===a&&v<=c){if(i[o-1]=y[h].call(n,p,a,e),l&&!i[o-1])return e;break}if(m(p)){if(a="-"===a?p.length:parseInt(a,10),v<=c){if(i[o-1]=d[h].call(n,p,a,e),l&&!i[o-1])return e;break}}else if(a&&-1!==a.indexOf("~")&&(a=a.replace(/~1/g,"/").replace(/~0/g,"~")),v<=c){if(i[o-1]=g[h].call(n,p,a,e),l&&!i[o-1])return e;break}p=p[a]}}return i},m=Array.isArray||function(e){return e.push&&"number"==typeof e.length};e.apply=u,e.registerOps=function(e,t){if(g[e])throw"The operation "+e+" already registered.";g[e]=t},e.registerOpsArray=function(e,t){if(d[e])throw"The operation "+e+" already registered.";d[e]=t},e.registerOpsRoot=function(e,t){if(y[e])throw"The operation "+e+" already registered.";y[e]=t}}(jsonpatch=jsonpatch||{});