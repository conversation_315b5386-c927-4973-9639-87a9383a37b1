"""
CRUD functions for samples

All CRUD relations with our DB for the Samples Table

Functions:
    - get_sample_by_SMS - send in DB and an sms_id and it returns all info about the sample
    - get_samples - get all samples from the DB
    - post_sample - add a new sample to the DB

"""
from sqlalchemy.orm import Session
from sqlalchemy import select, func
from typing import List, Optional

from ..models.sample import Sample
from ..schemas.sample import SampleBase
from ..common.constants import ERROR_MESSAGES
from ..common.exceptions import NotFoundError, ValidationError, StateError
from ..core.logging import log_info, log_error, log_warning

def get_samples(db: Session) -> List[Sample]:
    """
    Retrieve all samples

    - **db**: The DB to query

    Response Model:
    - Returns a list of samples
    """
    log_info("Executing query to retrieve all samples from database")
    try:
        query = select(Sample)

        # Sort by timestamp (newest first)
        # Use COALESCE to prioritize updated_at, fall back to created_at if updated_at is NULL
        query = query.order_by(func.coalesce(Sample.updated_at, Sample.created_at).desc())
        log_info("Applied default sorting by timestamp (newest first)")

        samples = db.execute(query).scalars().all()
        log_info(f"Successfully retrieved {len(samples)} samples")
        return list(samples)
    except Exception as e:
        log_error(f"Error listing samples: {str(e)}")
        raise StateError(ERROR_MESSAGES["sample"]["list_failed"].format(error=str(e)))

def get_sample_by_SMS(db: Session, sms_id: str) -> Optional[Sample]:
    """
    Retrieve sample by SMS ID

    - **db**: The DB the program is connected to
    - **SMS_id**: The ID of a potential sample

    ### Response Model:
    - Returns a sample (sample base is how a row in the DB is set up) Or nothing if SMS ID doesn't exist in the db
    """
    log_info(f"Executing query to find sample with SMS ID: {sms_id}")
    try:
        sample = db.execute(
            select(Sample).where(Sample.sms_number == sms_id)
        ).scalar_one_or_none()
        if not sample:
            log_warning(f"Sample not found with SMS ID: {sms_id}")
            raise NotFoundError(ERROR_MESSAGES["sample"]["not_found"])
        log_info(f"Successfully retrieved sample with SMS ID: {sms_id}")
        return sample
    except NotFoundError:
        raise
    except Exception as e:
        log_error(f"Error retrieving sample with SMS ID {sms_id}: {str(e)}")
        raise StateError(ERROR_MESSAGES["sample"]["get_failed"].format(error=str(e)))

def post_sample(db: Session, sample: SampleBase) -> Sample:
    """
    Make a new sample in the DB

    - **db**: A DB session to post to
    - **sample**: A SampleBase object with all the sample info with it

    ### Response Model:
    - Returns the sample made with the DB items (uuid, created_at and updated_at dates)

    Raises:
        ValueError: If the sample already exists or validation fails
        Exception: For unexpected errors
    """
    try:
        #Let's take this sample and make it into a Sample DB object
        if not isinstance(sample, SampleBase):
            log_error("Invalid sample format")
            raise ValidationError(ERROR_MESSAGES["sample"]["invalid_format"])
        
        log_info(f"Creating new sample with SMS number: {sample.sms_number}")
        
        # Check if sample already exists
        try:
            existing_sample = get_sample_by_SMS(db, sample.sms_number)
            if existing_sample:
                log_warning(f"Sample with SMS number {sample.sms_number} already exists")
                raise ValidationError(ERROR_MESSAGES["sample"]["already_exists"].format(
                    sms_number=sample.sms_number
                ))
        except NotFoundError:
            # This is expected for new samples - continue with creation
            log_info(f"No existing sample found with SMS number {sample.sms_number}, proceeding with creation")
            pass

        # Create and commit the new sample
        sample_commit = Sample(**sample.model_dump())
        #Now add it to the DB
        db.add(sample_commit)
        db.commit()
        db.refresh(sample_commit)

        log_info(f"Successfully created sample with SMS number: {sample.sms_number}")
        #Now return sample_commit
        return sample_commit

    except ValidationError as e:
        db.rollback()
        log_error(f"Validation error in sample data: {str(e)}")
        raise
    except Exception as e:
        db.rollback()
        log_error(f"Error creating sample: {str(e)}")
        raise StateError(ERROR_MESSAGES["sample"]["create_failed"].format(error=str(e)))