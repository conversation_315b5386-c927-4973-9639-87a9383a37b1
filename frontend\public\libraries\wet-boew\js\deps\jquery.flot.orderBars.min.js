jQuery.plot.plugins.push({init:function(r){var o,h,l,d,u=new Array,f=new Array,b=1,y=!1;function p(r,e){for(var a=new Array,n=0;n<r.length;n++)a[0]=r[n].data[0][e],a[1]=r[n].data[r[n].data.length-1][e];return"string"==typeof a[0]&&(a[0]=0,a[1]=r[0].data.length-1),a}function g(r,e){r=r.bars.barWidth||1,e=e.bars.barWidth||1;return r<e?-1:e<r?1:0}function A(r,e,a){a=r.slice((a||e)+1||r.length);r.length=e<0?r.length+e:e,r.push.apply(r,a)}function c(){var r=0;return r=h%2!=0?o[Math.floor(h/2)].bars.barWidth/2:r}function m(r,e,a){for(var n=0,s=e;s<=a;s++)n+=r[s].bars.barWidth+2*d;return n}function v(r,e,a){for(var n=r.pointsize,s=r.points,t=0,i=y?1:0;i<s.length;i+=n)s[i]+=a,e.data[t][3]=s[i],t++;return s}r.hooks.processDatapoints.push(function(r,e,a){var n,s,t,i=null;return null!=(t=e).bars&&t.bars.show&&null!=t.bars.order&&(e.bars.horizontal&&(y=!0),t=r,s=y?t.getPlaceholder().innerHeight():t.getPlaceholder().innerWidth(),t=(t=p(t.getData(),y?1:0))[1]-t[0],b=t/s,o=function(r){for(var e=new Array,a=0;a<r.length;a++)null!=r[a].bars.order&&r[a].bars.show&&e.push(r[a]);return function(r){var e,a=r.length;do{for(var n,s=0;s<a-1;s++)r[s].bars.order>r[s+1].bars.order?(n=r[s],r[s]=r[s+1],r[s+1]=n):r[s].bars.order==r[s+1].bars.order&&(r[s].sameSeriesArrayIndex?void 0!==r[s+1].sameSeriesArrayIndex&&(e=r[s].sameSeriesArrayIndex,r[s+1].sameSeriesArrayIndex=e,f[e].push(r[s+1]),f[e].sort(g),r[s]=f[e][0],A(r,s+1)):r[s+1].sameSeriesArrayIndex?void 0!==r[s].sameSeriesArrayIndex&&(e=r[s+1].sameSeriesArrayIndex,r[s].sameSeriesArrayIndex=e,f[e].push(r[s]),f[e].sort(g),r[s]=f[e][0],A(r,s+1)):(e=f.length,f[e]=new Array,r[s].sameSeriesArrayIndex=e,r[s+1].sameSeriesArrayIndex=e,f[e].push(r[s]),f[e].push(r[s+1]),f[e].sort(g),r[s]=f[e][0],A(r,s+1)),s--,a--)}while(a-=1,1<a);for(s=0;s<r.length;s++)r[s].sameSeriesArrayIndex&&(u[r[s].sameSeriesArrayIndex]=s);return r}(e)}(r.getData()),h=o.length,l="number"==typeof(t=e).bars.lineWidth?t.bars.lineWidth:2,d=l*b,2<=h?(s=function(r){var e=0;if(r.sameSeriesArrayIndex)e=u[r.sameSeriesArrayIndex];else for(var a=0;a<o.length;++a)if(r==o[a]){e=a;break}return e+1}(e),n=c(),i=v(a,e,s<=Math.ceil(h/2)?-1*m(o,s-1,Math.floor(h/2)-1)-n:m(o,Math.ceil(h/2),s-2)+n+2*d),a.points=i):1==h&&(i=v(a,e,n=-1*c()),a.points=i)),i})},options:{series:{bars:{order:null}}},name:"orderBars",version:"0.2"});