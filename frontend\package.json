{"name": "sed-lims-frontend", "version": "1.0.0", "description": "- /libraries # Contains WET assets -> Are we hosting locally or using a CDN\r - components # Contains components for pages\r - pages # pages that the user sees\r - javascript # For handling jQuery functions\r - css # Custom styles", "main": "server.js", "dependencies": {"accepts": "^1.3.8", "array-flatten": "^1.1.1", "body-parser": "^1.20.3", "bytes": "^3.1.2", "call-bind-apply-helpers": "^1.0.2", "call-bound": "^1.0.4", "content-disposition": "^0.5.4", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.0.6", "cors": "^2.8.5", "debug": "^2.6.9", "depd": "^2.0.0", "destroy": "^1.2.0", "dotenv": "^16.4.7", "dunder-proto": "^1.0.1", "ee-first": "^1.1.1", "encodeurl": "^2.0.0", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "escape-html": "^1.0.3", "etag": "^1.8.1", "eventemitter3": "^4.0.7", "express": "^4.21.2", "finalhandler": "^1.3.1", "follow-redirects": "^1.15.9", "forwarded": "^0.2.0", "fresh": "^0.5.2", "function-bind": "^1.1.2", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "http-errors": "^2.0.0", "http-proxy": "^1.18.1", "iconv-lite": "^0.4.24", "inherits": "^2.0.4", "ipaddr.js": "^1.9.1", "math-intrinsics": "^1.1.0", "media-typer": "^0.3.0", "merge-descriptors": "^1.0.3", "methods": "^1.1.2", "mime": "^1.6.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "ms": "^2.0.0", "negotiator": "^0.6.3", "object-assign": "^4.1.1", "object-inspect": "^1.13.4", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "path-to-regexp": "^0.1.12", "proxy-addr": "^2.0.7", "qs": "^6.13.0", "range-parser": "^1.2.1", "raw-body": "^2.5.2", "requires-port": "^1.0.0", "safe-buffer": "^5.2.1", "safer-buffer": "^2.1.2", "send": "^0.19.0", "serve-static": "^1.16.2", "setprototypeof": "^1.2.0", "side-channel": "^1.1.0", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2", "statuses": "^2.0.1", "toidentifier": "^1.0.1", "type-is": "^1.6.18", "unpipe": "^1.0.0", "utils-merge": "^1.0.1", "vary": "^1.1.2"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC"}