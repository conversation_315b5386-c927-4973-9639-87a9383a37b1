/*
 * Requisition Table
 * Table for displaying requisition information with queue management
 * Uses Table with Queue mixin to prevent WET-BOEW conflicts on multi-table pages
 */
import { Table } from '../../core/components/table.js';
import { withQueue } from '../../core/components/table-features.js';
import { createTableConfig } from '../../core/helpers/table-helpers.js';
import { RequisitionApi } from '../../core/services/requisition-api.js';
import { REQUISITION_CONFIGS } from '../../core/config/requisition-configs.js';

class RequisitionTable extends withQueue(Table) {
    constructor(options = {}) {
        super(createTableConfig(REQUISITION_CONFIGS, 'requisitionTable', 'requisition', {
            // Note: containerId and tableId will be provided by page initialization
            containerId: options.containerId || 'requisition-table-container',
            tableId: options.tableId || 'requisition-table',
            ...options
        }));
    }

    // Fetch requisitions data from API
    async fetchData() {
        try {
            const config = this.currentConfig;
            const requisitions = await RequisitionApi.getRequisitions(config.apiStatusQuery, config.labId, config.role);

            // Backend provides data sorted by timestamp (newest first)
            return requisitions || [];
        } catch (error) {
            console.error('Failed to fetch requisitions data:', error);
            throw error;
        }
    }

}

// Factory function and default instance
export const createRequisitionTable = (options = {}) => new RequisitionTable(options);
export const requisitionTable = createRequisitionTable();
