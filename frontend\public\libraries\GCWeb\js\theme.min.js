/*!
 * @title Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * @license wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v16.3.0 - 2025-02-20
 *
 */
((t,i,a)=>{function l(e){if(!e.content){for(var t=e.childNodes,a=i.createDocumentFragment();t[0];)a.appendChild(t[0]);e.content=a}}var r="wb-template",o="template",e=a.doc;a.tmplPolyfill=l,e.on("timerpoke.wb wb-init.wb-template",o,function(e){e=a.init(e,r,o);e&&(l(e),a.ready(t(e),r))}),a.add(o)})(jQuery,document,wb),((l,r)=>{function o(e,t){function a(e){var t=[e.substr(1,2),e.substr(3,2),e.substr(5,2)].map(e=>parseInt(e,16)/255);for(let e=0;e<t.length;e++)t[e]<=.03928?t[e]=t[e]/12.92:t[e]=Math.pow((t[e]+.055)/1.055,2.4);return.2126*t[0]+.7152*t[1]+.0722*t[2]}return i=a(e),n=a(t),((Math.max(i,n)+.05)/(Math.min(i,n)+.05)).toFixed(2)}var i,n,e=r.doc,s="gc-featured-link",p="."+s,c="#FFFFFF",d="#333333";e.on("timerpoke.wb wb-init .gc-featured-link",p,function(e){var t,a,i,e=r.init(e,s,p);e&&(t=e.dataset.bgColor,i=l(e),t&&(a=4.5<=o(c,t)?c:4.5<=o(d,t)?d:"#000000",e.style.backgroundColor=t,e.style.color=a,e.querySelectorAll("p, a").forEach(e=>{e.style.color=a})),r.ready(i,s))}),r.add(p)})(jQuery,(window,wb)),((a,i)=>{var e=i.doc,l="followus",r="."+l;e.on("timerpoke.wb wb-init .followus",r,function(e){var t=i.init(e,l,r);t&&e.currentTarget===e.target&&((e=t.querySelector(".twitter .wb-inv"))&&(e.innerHTML=e.innerHTML.replace("Twitter","X")),i.ready(a(t),l))}),i.add(r)})(jQuery,(window,wb)),((r,a)=>{var i,l,o,e=a.doc,n="gc-subway",s=".provisional."+n,p=n+"-section",c=n+"-index";e.on("timerpoke.wb wb-init .provisional.gc-subway",s,function(e){var t=a.init(e,n,s);t&&e.currentTarget===e.target&&(e=r(t),l=t.hasAttribute("data-sections-title")?t.getAttribute("data-sections-title"):"Sections",t.querySelector("h1")?((i=r("h1",e)).get(0).id=i.get(0).id||a.getId(),i&&a.addSkipLink(a.i18n("skip-prefix")+" "+i.text(),{href:"#"+i.get(0).id}),e.nextUntil(".pagedetails, .gc-subway-support").wrapAll("<section class='"+p+"'>"),e.wrap("<div class='gc-subway-wrapper'></div>"),(o=r("."+p+" h1")).wrap("<hgroup></hgroup>"),i.wrap("<hgroup></hgroup>"),r("<p>"+i.text()+"</p>").insertBefore(o),r("<p class='h3 hidden-xs visible-md visible-lg mrgn-tp-0'>"+l+"</p>").insertAfter(i),e.find("a.active").attr({tabindex:"0","aria-current":"page"}),o=r(s+" a, ."+p+" .gc-subway-pagination a, main .pager a"),(l=r(".gc-subway-support"))&&(l.clone().addClass("hidden-xs hidden-sm").insertAfter("."+n),l.addClass("hidden-md hidden-lg")),o.each(function(e,t){let a=r(t),i=a.attr("href"),l;i&&(l=i.includes("#")?i:i+="#wb-cont"),a.clone().addClass("hidden-md hidden-lg").attr("href",l).insertAfter(t),a.addClass("hidden-xs hidden-sm")}),t.classList.add("no-blink")):t.classList.contains(c)||t.classList.add(c),a.ready(e,n))}),a.add(s)})(jQuery,(window,wb)),((G,B,L)=>{function c(e,t,a){for(var i,l,r=a[t];i=r.shift();)(l=i.action)&&(e.trigger(l+"."+H,i),delete i.action)}function U(e,t){var a,i,l,r,o,n,s,p,c=t.cValue||G(t.srcInput).val()||"",d=e.target.id,u=c,m=t.actions,g=t.dntwb;for(m=G.isArray(m)?G.extend([],m):[m],i=m.length,a=0;a!==i;a+=1)if(l=m[a],r=l.action){if(p=l.default,s=!1,(o=l.match)&&!p)throw"'match' and 'default' property need to be set";if(p&&u.length&&"string"==typeof o)try{n=new RegExp(o),s=(s=n.exec(u))||p}catch(e){}else s||!p||u||(s=p);switch(!l.qval&&s&&(l.qval=s),r){case"patch":var h=l.patches,A=l.base||"/";h||(h=[f],l.cumulative=!0),G.isArray(h)||(h=[h]),h=v(h,l.qval,A),l.patches=h;break;case"ajax":l.trigger&&g&&(l.trigger=!1),l.url=w(l.url,l.qval);break;case"tblfilter":l.value=w(l.value,l.qval)}y(d,b,l)}}var e=B.doc,d="wb-actionmng",o="."+d,t="[data-"+d+"]",J=d+"Rn",H=d+o,u={},b={},M={},a=["mapfilter","tocsv","loadJSON","patch","ajax","addClass","removeClass","tblfilter","withInput","selectInput","run"].join("."+H+" ")+"."+H,f={op:"move",path:"{base}",from:"{base}/{qval}"},y=function(e,t,a){t[e]||(t[e]=[]),t[e].push(a)},v=function(e,t,a){var i,l,r,o=e.length,n=[];for(a=a||"/",i=0;i!==o;i+=1)l=e[i],r=G.extend({},l),l.path&&(r.path=w(l.path,t,a)),l.from&&(r.from=w(l.from,t,a)),l.value&&(r.value=w(l.value,t,a)),n.push(r);return n},w=function(e,t,a){return t?a?e.replace(/\{qval\}/,t).replace(/\{base\}/,a):e.replace(/\{qval\}/,t):e};e.on("do."+H,function(e){var t,a,i,l,r,o,n=e.element||e.target,s=n.id,p=e.actions||[];if((n===e.target||e.currentTarget===e.target)&&-1===n.className.indexOf(d)){for((i=(p=G.isArray(p)?p:[p]).length)&&(t=G(n)).addClass(d),s&&u[s]&&c(t,s,u),a=0;a!==i;a+=1)(r=(l=p[a]).action)&&((o=l.target)?(l.trgbefore?y(o,u,l):y(o,b,l),(o=l.trggroup)&&y(o,M,l)):t.trigger(r+"."+H,l));s&&b[s]&&c(t,s,b),G(e.target).removeClass(d)}}),e.on("clean."+H,function(e){var t,a,i=e.element||e.target,l=e.trggroup;if((i===e.target||e.currentTarget===e.target)&&l&&M[l])for(t=M[l];a=t.shift();)delete a.action}),e.on(a,o,function(e,t){var a,i,l,r,o,n,s,p=e.type;if(H===e.namespace)switch(p){case"run":var c,d,u,m,g=t,h=(h=e).target,A=G(h),b=M[g.trggroup];if(b&&!A.hasClass(J)){for(A.addClass(J),d=b.length,c=0;c!==d;c+=1)(m=(u=b[c]).action)&&A.trigger(m+"."+H,u);A.removeClass(J)}break;case"tblfilter":var h=t,g=(g=e).target,g=G(h.source||g),f=h.column,y=parseInt(f,10),v=!!h.regex,w=!h.smart||!!h.smart,x=!h.caseinsen||!!h.caseinsen;if("TABLE"!==g.get(0).nodeName)throw"Table filtering can only applied on table";g=g.dataTable({retrieve:!0}).api(),(f?g.column(!0===y?y:f):g).search(h.value,v,w,x).draw();break;case"addClass":y=e,f=t;y=G(f.source||y.target),f.class&&y.addClass(f.class);break;case"removeClass":v=e,w=t;v=G(w.source||v.target),w.class&&v.removeClass(w.class);break;case"ajax":x=e,(o=t).container?n=G(o.container):(s=B.getId(),n=G("<div id='"+s+"'></div>"),G(x.target).after(n)),o.trigger&&n.attr("data-trigger-wet","true"),n.attr("data-ajax-"+(o.type||"replace"),o.url),n.one("wb-contentupdated",function(e,t){var e=e.currentTarget,a=e.getAttribute("data-trigger-wet");e.removeAttribute("data-ajax-"+t["ajax-type"]),a&&(G(e).find(B.allSelectors).addClass("wb-init").filter(":not(#"+e.id+" .wb-init .wb-init)").trigger("timerpoke.wb"),e.removeAttribute("data-trigger-wet"))}),n.trigger("wb-update.wb-data-ajax");break;case"patch":o=(s=t).source,n=!!s.cumulative,(r=s.patches)&&(G.isArray(r)||(r=[r]),G(o).trigger({type:"patches.wb-jsonmanager",patches:r,fpath:s.fpath,filter:s.filter||[],filternot:s.filternot||[],cumulative:n}));break;case"selectInput":r=e,l=t,(L.querySelector(l.source)||r.currentTarget).querySelectorAll('[value="'+l.value+'"]').forEach(e=>{"OPTION"===e.nodeName?e.setAttribute("selected",!0):"INPUT"===e.nodeName&&e.setAttribute("checked",!0)});break;case"mapfilter":l=e,l=G((F=t).source||l.target).get(0).geomap,O=F.filter,F=F.value,"aoi"===O&&l.zoomAOI(F),"layer"===O&&l.showLayer(F,!0);break;case"tocsv":var k,E,S,C,N,O=t.source,F=t.filename,T=(O=G(O)).get(0),R=T.classList.contains("wb-tables"),W="",F=F||(T.caption||"table")+".csv",I=T.rows,Y=I.length,z=I[0].cells.length;if(R){for(Y=(S=O.dataTable({retrieve:!0}).api()).rows()[0].length,E=0;E<z;E+=1)N=(N=I[0].cells[E].textContent).replace(/"/g,'""'),W=E?W+',"'+N+'"':W+'"'+N+'"';W+="\n"}for(k=0;k<Y;k+=1){for(E=0;E<z;E+=1)R?-1!==(N=S.cell(k,E,{page:"all"}).data()).indexOf("<")&&((C=L.createElement("div")).innerHTML=N,N=C.textContent):N=I[k].cells[E].textContent,N=N.replace(/"/g,'""'),N+='"',W=E?W+',"'+N:W+'"'+N;W+="\n"}B.download(new Blob(["\ufeff"+W],{type:"text/plain;charset=utf-8"}),F);break;case"loadJSON":a=(T=t).source,i=T.url,G(a).attr("data-wb-jsonmanager-reload",""),G(a).trigger({type:"json-fetch.wb",fetch:{url:i,nocache:T.nocache,nocachekey:T.nocachekey,data:T.data,contentType:T.contenttype,method:T.method}});break;case"withInput":U(e,t)}}),e.on("timerpoke.wb wb-init.wb-actionmng",t,function(e){var t,a,i,l,r,e=B.init(e,d,o);if(e){if(e=G(e),t=B.getData(e,d))for(i=(t=G.isArray(t)?t:[t]).length,a=0;a!==i;a+=1)(r=(l=t[a]).trggroup)&&l.action&&y(r,M,l);B.ready(e,d)}}),B.add(t)})(jQuery,wb,document),((u,l,o)=>{var m,g,h,A,b,f,y,v,c,a,i,w="wb-chtwzrd",x="."+w,n=w+"-replace",e="wb-init"+x,k=o.doc,E={},S={},C={en:{"chtwzrd-send":"Send<span class='wb-inv'> reply and continue</span>","chtwzrd-reset":"Restart from the beginning","chtwzrd-toggle":"Switch to wizard","chtwzrd-notification":"Close chat notification","chtwzrd-open":"Open chat wizard","chtwzrd-minimize":"Minimize chat wizard","chtwzrd-history":"Conversation history","chtwzrd-reply":"Reply","chtwzrd-controls":"Controls","chtwzrd-toggle-basic":"Switch to basic form","chtwzrd-waiting":"Waiting for message","chtwzrd-answer":"You have answered:"},fr:{"chtwzrd-send":"Envoyer<span class='wb-inv'> la réponse et continuer</span>","chtwzrd-reset":"Recommencer depuis le début","chtwzrd-toggle":"Basculer vers l&apos;assistant","chtwzrd-notification":"Fermer la notification de discussion","chtwzrd-open":"Ouvrir l&apos;assistant de discussion","chtwzrd-minimize":"Réduire l&apos;assistant de discussion","chtwzrd-history":"Historique de discussion","chtwzrd-reply":"Répondre","chtwzrd-controls":"Contrôles","chtwzrd-toggle-basic":"Basculer vers le formulaire","chtwzrd-waiting":"En attente d&apos;un message","chtwzrd-answer":"Vous avez répondu&nbsp;:"}},r=function(e){A=localStorage.getItem("wb-chtwzrd-notif"),e.removeClass("hidden wb-inv").addClass(w+"-basic"),m=!(S={shortDelay:500,mediumDelay:750,longDelay:1250,xLongDelay:2e3,xxLongDelay:2500}),f=E.header.first,y=E.header.instructions||"",b=E.header.defaultDestination,v=E.questions[f],g=E.header.formType||"dynamic",h=!!E.header.inline,C={send:(C=C[u("html").attr("lang")||"en"])["chtwzrd-send"],reset:C["chtwzrd-reset"],toggle:C["chtwzrd-toggle"],notification:C["chtwzrd-notification"],trigger:C["chtwzrd-open"],minimize:C["chtwzrd-minimize"],conversation:C["chtwzrd-history"],reply:C["chtwzrd-reply"],controls:C["chtwzrd-controls"],toggleBasic:C["chtwzrd-toggle-basic"],waiting:C["chtwzrd-waiting"],answer:C["chtwzrd-answer"]},T(e,E.header.title);var t,a=u(x+"-basic"),i=u(x+"-bubble-wrap"),l=u(x+"-btn"),r=u(x+"-container"),o=u(".body",r),n=u(".history",r),s=u(".minimize",r),e=u(".reset",r),p=u(".basic-link",r),c=e,d=p;N(a),O(i),F(l),p.on("click",function(e){e.preventDefault();var t=u("legend:first",a);t.attr("tabindex","0"),n.attr("aria-live",""),R(a,"form"),r.stop().hide(),a.stop().show(function(){t.focus(),t.removeAttr("tabindex")}),u("body").removeClass(w+"-noscroll")}),u(x+"-link").on("click",function(e){e.preventDefault(),a.stop().hide(),t=u(":focus"),u(this).hasClass(w+"-bubble")||R(r,"wizard"),u(".bubble",i).removeClass("trans-pulse"),u("p",i).hide().removeClass("trans-left"),r.stop().show(),i.stop().hide(),l.prop("disabled",!0),h||u("body").addClass(w+"-noscroll"),n.length&&u(".conversation",r).scrollTop(n[0].scrollHeight),m||W(o),localStorage.setItem("wb-chtwzrd-notif",1)}),u(x+"-btn").on("click",function(e){e.preventDefault(),l.prop("disabled",!0),a.stop().hide(),t=u(":focus"),R(r,"wizard"),r.stop().show(),i.stop().hide(),r.find(":focusable").first().focus(),h||u("body").addClass(w+"-noscroll"),n.length&&u(".conversation",r).scrollTop(n[0].scrollHeight),m||W(o)}),h?u(x+"-link").click():r.on("keydown",function(e){9===e.keyCode&&(e.shiftKey?c.is(":focus")&&(e.preventDefault(),d.focus()):d.is(":focus")&&(e.preventDefault(),c.focus())),27===e.keyCode&&s.click()}),k.on("click",x+"-container .btn-send",function(e){"submit"!==u(this).attr("type")&&(e.preventDefault(),(e=u("input:checked",o)).length||(e=u("input:first",o)).attr("checked",!0),I(o,Y(e),!1))}),e.on("click",function(e){e.preventDefault(),R(u(x+"-container"),"wizard")}),s.on("click",function(e){e.preventDefault(),r.stop().hide(),l.prop("disabled",!1),i.stop().show(),u("body").removeClass(w+"-noscroll"),t.focus()})},N=function(e){var i=u("form",e),t=u("fieldset",e),a=t.first();"dynamic"===g&&(a.addClass(w+"-first-q"),t.not(x+"-first-q").hide()),e.hide(),u("input",i).prop("checked",!1),i.append("<button class='btn btn-sm btn-link "+w+"-link mrgn-rght-sm'>"+C.toggle+"</button>"),u("input",i).on("change",function(){var e,t=Y(u(this)),a=u("#"+t.qNext,i);"dynamic"===g&&(e=u(this).closest("fieldset"),!a.is(":hidden")&&e.next().attr("id")===a.attr("id")&&"none"!==t.qNext||e.nextAll("fieldset").hide().find("input").prop("checked",!1),"none"!==t.qNext&&u("#"+t.qNext).show(),""!==t.url)&&i.attr("action",t.url)})},O=function(t){var e,a=u("#wb-info"),i=u(x+"-link",t);E.header.avatar&&i.css("background-image","url("+E.header.avatar+")"),t.fadeIn("slow"),a.addClass(w+"-mrgn"),a.length&&((e=function(e){u(l).scrollTop()>=u(document).outerHeight()-u(l).outerHeight()-a.outerHeight()?e.css({bottom:a.outerHeight()-(u(document).outerHeight()-u(l).outerHeight()-u(l).scrollTop())+30}):e.css({bottom:30})})(t),u(l).on("resize scroll",function(){e(t)})),u(".notif",t).on("click",function(){i.click()}),u(".notif-close",t).on("click",function(e){e.preventDefault(),u(this).parent().hide(),t.focus(),localStorage.setItem("wb-chtwzrd-notif",1)})},F=function(e){e.attr("aria-controls",w+"-container")},s=function(e){var t=u("form",e),a=u("h2",e).first(),i=u("p:not("+x+"-greetings):not("+x+"-farewell)",t).first(),l="btn-former-send",r={},s={},o=u("fieldset",e);return e.data(w),(r=e.data(w)?e.data(w):{}).inline=e.hasClass("wb-chtwzrd-inline"),r.avatar=e.data(w+"-avatar"),r.defaultDestination=t.attr("action"),r.name=t.attr("name"),r.method=t.attr("method"),r.form={},r.form.title=a.html(),r.title=d(a,r.form.title),r.greetings=u("p"+x+"-greetings",t).html(),r.farewell=u("p"+x+"-farewell",t).html(),r.form.sendButton=u("input[type=submit]",t).length?u("input[type=submit]",t).addClass(l).val():u("button[type=submit]",t).addClass(l).html(),r.sendButton=d(u("."+l,t),r.form.sendButton),i.length&&(r.form.instructions=i.html(),r.instructions=d(i,r.form.instructions)),r.first=r.first||o.first().attr("id"),o.each(function(){var e=u(this),t=u("legend",e),a=u("label",e),i=this.id,e=u("input[type=radio]",e).length?"radio":"checkbox",o=[],n="",l={};a.each(function(e){var t=u("input",u(this)),a={},i=t.attr("name"),l=t.data(w+"-url"),r=t.siblings("span:not(.no-"+w+")").html();e||(n=i),a.content=r,a.value=t.val(),a.next=t.data(w+"-next"),l&&(a.url=l),o.push(a)}),l.name=n,l.input=e,l.formLabel=t.html(),l.label=d(t,l.formLabel),l.choices=o,s[i]=l}),{header:r,questions:s}},T=function(e,t){e.after("<div class='"+w+"-bubble-wrap'><a href='#"+w+"-container' aria-controls='"+w+"-container' class='"+w+"-link bubble trans-pulse' role='button'>"+C.trigger+"</a>"+(A?"":"<p class='trans-left'><span class='notif'>"+t+"</span> <a href='#' class='notif-close' title='"+C.notification+"' aria-label='"+C.notification+"' role='button'>&times;</a></p>")+"</div>"),e.next(x+"-bubble-wrap").after("<aside id='"+w+"-container' class='modal-content overlay-def "+w+"-container "+(h?" wb-chtwzrd-contained":"")+"'></aside>");e=u(x+"-container"),e.append("<header class='modal-header header'><h2 class='modal-title title'>"+t+"</h2><button type='button' class='reset' title='"+C.reset+"'> <span class='glyphicon glyphicon-refresh'></span><span class='wb-inv'>"+C.reset+"</span></button><button type='button' class='minimize' title='"+C.minimize+"'><span class='glyphicon glyphicon-chevron-down'></span><span class='wb-inv'>"+C.minimize+"</span></button></header>"),e.append("<form class='modal-body body' method='GET'></form>"),t=u(".body",e);t.append("<div class='conversation'><section class='history' aria-live='assertive'><h3 class='wb-inv'>"+C.conversation+"</h3></section><section class='reply'><h3 class='wb-inv'>"+C.reply+"</h3><div class='inputs-zone'></div></section><div class='form-params'></div></div>"),t.append("<section class='controls'><h3 class='wb-inv'>"+C.controls+"</h3><div class='row'><div class='col-xs-12'><button class='btn btn-primary btn-block btn-send' type='button'>"+C.send+"</button></div></div><div class='row'><div class='col-xs-12 text-center mrgn-tp-sm'><a href='#"+w+"-basic' class='btn btn-sm btn-link basic-link' role='button'>"+C.toggleBasic+"</a></div></div></section>"),t.attr("name",E.header.name+"-chat"),t.attr("method",E.header.method),i=u(".btn-send ",t).html()},p=function(e,t){e.html("");var a="<h2>"+t.header.title+"</h2>",i="<p>"+t.header.instructions+"</p>",l=">"+t.header.sendButton+"</button>",r=(t.header.form.title,a="<h2 data-"+n+"='"+t.header.title+"'>"+t.header.form.title+"</h2>",e.append(a+"<form class='mrgn-bttm-xl' action='"+t.header.defaultDestination+"' name='"+t.header.name+"' method='"+(t.header.method||"GET")+"'></form>"),u("form",e));t.header.form.instructions,i="<p data-"+n+"='"+t.header.instructions+"'>"+t.header.form.instructions+"</p>",r.append("<p class='wb-chtwzrd-greetings wb-inv'>"+t.header.greetings+"</p>"+i),u.each(t.questions,function(e,a){var i=o.getId(),t="<legend>"+a.label+"</legend>",l=(a.formLabel&&(t="<legend data-"+n+"='"+a.label+"'>"+a.formLabel+"</legend>"),r.append("<fieldset id='"+e+"' class='"+i+"'>"+t+"<ul class='list-unstyled mrgn-tp-md'></ul></fieldset>"),u("."+i,r));u.each(a.choices,function(e,t){i=o.getId(),u("ul",l).append("<li><label><input type='"+a.input+"' value='"+t.value+"' id ='"+i+"' name='"+a.name+"' data-value='"+t.content+"' /> <span>"+t.content+"</span>"),u("#"+i,l).attr("data-"+w+"-next",t.next),t.url,t.url&&u("#"+i,l).attr("data-"+w+"-url",t.url)})}),t.header.form.sendButton,l=" data-"+n+"='"+t.header.sendButton+"'>"+t.header.form.sendButton+"</button>",r.append("<p class='wb-chtwzrd-farewell wb-inv'>"+t.header.farewell+"</p><br/><button type='submit' class='btn btn-sm btn-primary'"+l),void 0!==E.header.first&&E.header.first||(E.header.first=u("fieldset",r).first().attr("id"))},R=function(e,t){"wizard"===t?(t=u(".conversation",e),l.clearTimeout(c),l.clearTimeout(a),m=!1,b=E.header.defaultDestination,f=E.header.first,y=E.header.instructions||"",v=E.questions[f],u(".history, .form-params",t).html(""),u(".btn-send",e).attr("type","button").html(i),u(".history",t).attr("aria-live","assertive"),W(u(".body",e))):(t=u("fieldset",e),"dynamic"===g&&(t.not(":first").hide(),u("input",t).prop("checked",!1)))},W=function(l){var r=u(".history",l),o=u(".inputs-zone",l),n=u(".conversation",l),s=u(".btn-send",l),e=""!==f||""!==y||"last"===v?"p":"h4",p=(m=!0,s.prop("disabled",!0),o.html(""),r.append("<div class='row mrgn-bttm-md'><div class='col-xs-9'><"+e+" class='mrgn-tp-0 mrgn-bttm-0'><span class='avatar'></span><span class='question'></span></"+e+"></div></div>"),u(".question:last",r)),e=(t(p),S.longDelay);h&&""!==f?e=S.xLongDelay:""===f&&""!==y&&(e=S.xxLongDelay),c=setTimeout(function(){""!==f?(p.html(E.header.greetings),f="",W(l)):""!==y?(p.html(y),y="",W(l)):"last"===v?(p.html(E.header.farewell),s.attr("type","submit").prop("disabled",!1).html(E.header.sendButton+"&nbsp;<span class='glyphicon glyphicon-chevron-right small'></span>"),l.attr("action",b)):(p.html(v.label),v.input="radio",a=setTimeout(function(){o.append("<fieldset><legend class='wb-inv'>"+v.label+"</legend><div class='row'><div class='col-xs-12'><ul class='list-unstyled mrgn-tp-sm choices'></ul></div></div></fieldset>");for(var e=0;e<v.choices.length;e++){var t=v.choices[e];u(".choices",o).append("<li><label><input type='"+v.input+"' value='"+t.value+"' name='"+v.name+"' data-"+w+"-next='"+t.next+"'"+(void 0===t.url?"":" data-"+w+"-url='"+t.url+"'")+(e?"":"checked ")+"/> <span>"+t.content+"</span></label></li>")}s.prop("disabled",!1);var a=n[0].scrollHeight,i=u(".reply",l);i.length&&i.outerHeight()+p.outerHeight()>n.innerHeight()&&(a=r[0].scrollHeight-p.outerHeight()-42),n.scrollTop(a)},S.mediumDelay)),n.scrollTop(n[0].scrollHeight)},e)},I=function(e,t){var a=o.getId(),i=u(".history",e),t=(i.append("<div class='row mrgn-bttm-md'><div class='col-xs-9 col-xs-offset-3'><div class='message text-right pull-right' id='"+a+"'><p class='mrgn-bttm-0'><span class='wb-inv'>"+C.answer+" </span>"+t.value+"</p></div></div></div>"),u(".form-params",e).append("<input type='hidden' name='"+t.name+"' value='"+t.val+"' data-value='"+t.value+"' />"),m=!1,""!==t.url&&(b=t.url),t.qNext),l=u("#"+a,i);v="none"===t?"last":E.questions[t],u(".btn-send",e).prop("disabled",!0),l.attr("tabindex","0"),c=setTimeout(function(){u(".inputs-zone",e).remove("fieldset"),l.focus(),l.removeAttr("tabindex"),W(e)},S.shortDelay)},t=function(e){e.html("<span class='loader-typing' aria-label='"+C.waiting+"'><span class='loader-dot dot1'></span><span class='loader-dot dot2'></span><span class='loader-dot dot3'></span></span>")},d=function(e,t){e=e.data(n);return e||t},Y=function(e){var t=e.data(w+"-next"),a=e.data(w+"-url");return{qNext:t,name:e.attr("name"),val:e.val(),url:a||"",value:e.next().html()}};k.on("timerpoke.wb "+e,x+".provisional",function(i){setTimeout(function(){var t,e,a=o.init(i,w,x);a&&((t=a=u(a)).data(w+"-src")?(e=t.data(w+"-src"),u.getJSON(e,function(e){p(t,E=e),r(t)})):(E=s(t),r(t)),o.ready(a,w))},500)}),o.add(x)})(jQuery,window,wb),((i,l)=>{var r="wb-doaction",o="a[data-"+r+"],button[data-"+r+"]",n="do.wb-actionmng",s=l.doc;s.on("click",o,function(e){var t=e.target,a=i(t);if("BUTTON"===(t=e.currentTarget!==e.target?(a=a.parentsUntil("main",o))[0]:t).nodeName||"A"===t.nodeName)return t.id||(t.id=l.getId()),l.isReady?a.trigger({type:n,actions:l.getData(a,r)}):s.one("wb-ready.wb",function(){a.trigger({type:n,actions:l.getData(a,r)})}),!1})})(jQuery,(window,wb)),((_,O,$)=>{function ee(e,t,a,i){var l=e.data(t);(l=l&&!i?l:[]).push(a),e.data(t,l)}function te(e,t){var a=t.$selElm,i=t.name,l=t.value;i&&t.provEvt.setAttribute("name",i),"string"==typeof l&&a.val(l),a.attr("data-"+r,r)}function ae(e,t){var a=_(t.origin),e=_(e.target).data(be),i=t.toggle;i&&"string"==typeof i&&(i={selector:i}),i=_.extend({},i,e.toggle),a.addClass("wb-toggle"),a.trigger("toggle.wb-toggle",i),i.type="off",a.one(ge,function(){a.addClass("wb-toggle"),a.trigger("toggle.wb-toggle",i),a.removeClass("wb-toggle")})}function ie(e,t){var a,i,l,r,o,n,s,p=t.outputctnrid,c=t.actions,d=t.lblselector,u=!!t.required,m=!t.noreqlabel,g=t.items,e=e.target,h=_(e),A=t.source,b=h.data(be).i18n,f=t.attributes,y=$.getId(),v="<legend",w="</span>",x="<fieldset id='"+y+"' data-"+ve+"='"+e.id+"' "+we+"='"+A.id+"' class='"+ne+" chkbxrdio-grp mrgn-bttm-md'",k="",E=t.typeRadCheck,S=t.inline,C=t.gcChckbxrdio,N=se+y;if(f&&"object"==typeof f)for(i in f)Object.prototype.hasOwnProperty.call(f,i)&&(x+=" "+i+"='"+f[i]+"'");for(e=_(x+"></fieldset>"),S&&e.addClass("form-inline"),C&&e.addClass("gc-chckbxrdio"),u&&m&&(v+=" class='required'",w+=" <strong class='required'>("+b.required+")</strong>"),v+=">",w+="</legend>",d?(A=_("<div>"+t.label+"</div>").find(d),e.append(v+A.html()+w).append(A.nextAll()),a=A.prevAll()):e.append(_(v+t.label+w)),C&&!S&&(s="closed"),i=0,l=g.length;i!==l;i+=1)if((n=g[i]).group)for("open"===s&&(k+="</ul>",s="closed"),k+="<p>"+n.label+"</p>","closed"===s&&(k+="<ul class='list-unstyled lst-spcd-2'>",s="open"),o=n.group.length,r=0;r!==o;r+=1)k+=F(n.group[r],N,E,S,C,u);else 0===i&&"closed"===s&&(k+="<ul class='list-unstyled lst-spcd-2'>",s="open"),k+=F(n,N,E,S,C,u);"open"===s&&(k+="</ul>"),e.append(k),_("#"+p).append(e),a&&e.before(a),c&&0<c.length&&e.data(fe,c),ee(h,Ae,y)}function le(e){var t=xe(e.label),a="<option value='"+$.escapeAttribute(t)+"'";return a+=p(e),e.isSelected&&(a+=" selected=selected"),a+=">"+t+"</option>"}function F(e,t,a,i,l,r){var o=$.getId(),n=e.label,s="<label for='"+o+"'>",o="<input id='"+o+"' type='"+a+"' name='"+t+"' value='"+$.escapeAttribute(xe(n))+"'"+p(e),e="<"+(t=!i&&l?"li":"div")+" class='"+a;return r&&(o+=" required='required'"),o+=" />",i&&(e+=" label-inline"),e=(e+="'>")+(i||l?o+s+n:s+o+" "+n)+("</label></"+t+">")}var N,re="wb-fieldflow",d="."+re,u=re+"-form",oe=re+"-sub",ne=re+"-init",a="."+ne,se=re+$.getId(),W="[name^="+se+"]",pe=re+"-label",ce=re+"-header",e="."+u,i="wb-init"+d,de="draw"+d,ue="action"+d,me="submit"+d,I="submited"+d,g="ready"+d,ge="clean"+d,T="reset"+d,he="createctrl"+d,Ae=re+"-register",R=re+"-hdnfld",be=re+"-config",fe=re+"-push",ye=re+"-submit",ve=re+"-origin",we="data-"+re+"-source",r=re+"-flagoptvalue",t=$.doc,m={toggle:{stateOn:"visible",stateOff:"hidden"},i18n:{en:{btn:"Continue",defaultsel:"Make your selection...",required:"required"},fr:{btn:"Allez",defaultsel:"Sélectionnez dans la liste...",required:"obligatoire"}},action:"ajax",prop:"url"},l=[["redir","query","ajax","addClass","removeClass","removeClass","append","tblfilter","toggle"].join("."+ue+" ")+"."+ue,["ajax","toggle","redir","addClass","removeClass"].join("."+me+" ")+"."+me,["tblfilter",re].join("."+de+" ")+"."+de,["select","checkbox","radio"].join("."+he+" ")+"."+he].join(" "),ke=function(e,t){for(var a,i,l,r,o,n,s,p,c,d,u,m,g=e.get(),h=g.length,A=[],b=0;b!==h;b+=1){if(r=null,i=l="",m=(a=g[b]).firstChild,n=(s=a.childNodes).length,!m)throw"You have a markup error, There may be an empyt <li> elements in your list.";for(u=[],(m=a.firstElementChild)&&"A"===m.nodeName&&(l=m.getAttribute("href"),i=_(m).html().trim(),n=1,u.push({action:"redir",url:l})),o=1;o!==n;o+=1){if(p=s[o],(c=_(p)).hasClass(oe)){d=p.id||$.getId(),p.id=d,l=re+"-"+d,u.push({action:"append",srctype:re,source:"#"+d});break}if("UL"===p.nodeName){if(t)throw"Recursive error, please check your code";r=ke(c.children(),!0)}c.hasClass(pe)&&(i=c.html())}i||((m=_(a).clone()).children("ul, .wb-fieldflow-sub").remove(),i=m.html().trim()),a.id||(a.id=$.getId()),A.push({bind:a.id,label:i,actions:u,group:r})}return A},p=function(e){var t="",a={};return a.bind=e.bind||"",a.actions=e.actions||[],t+=" data-"+re+"='"+JSON.stringify(a)+"'"},xe=function(e){return e.replace(/(<([^>]+)>)/gi,"")};t.on(T,d+", ."+oe,function(e){var t,a,i,l,r,o=e.target,n=[];if(o===e.currentTarget&&(e=(t=_(o)).data(be))&&e.reset)for(o=e.reset,_.isArray(o)?n=o:n.push(o),i=n.length,a=0;a!==i;a+=1)(r=(l=n[a]).action)&&(!1!==l.live&&(l.live=!0),t.trigger(r+"."+ue,l))}),t.on("change",e+" "+a,function(e){var t,a,i,l,r,o,n=e.currentTarget,e=_(n),s=e.nextAll(),p=_("#"+n.getAttribute("data-"+ve)),c=_("#"+n.getAttribute(we)),d=p.data(Ae),u=e.find(":checked",e),m=e.get(0).form,g=s.length;if(g){for(a=g;0!==a;--a)(r=s[a])&&(-1<(o=d.indexOf(r.id))&&d.splice(o,1),_("#"+r.getAttribute(we)).trigger(T).trigger(ge),_(r).trigger(ge));p.data(Ae,d),s.remove()}c.trigger(T).trigger(ge),e.trigger(ge),e.data(ye,[]);var h,A,b,f,y,v,w,x=[],k=[],E=[],S=p.data(be),C=c.data(be);for(C&&(S=S&&_.extend({},S,C)),u.length&&u.val()&&S&&S.default&&(t=S.default,_.isArray(t)?x=t:x.push(t)),b=S.action,f=S.prop,N=S.actionData||{},(t=e.data(fe))&&(x=x.concat(t)),a=0,g=u.length;a!==g;a+=1)if(h=u.get(a),(h=$.getData(h,re))&&(w=h.bind,x=x.concat(h.actions),w)&&"string"==typeof(A=O.getElementById(w).getAttribute("data-"+re))){if(A.startsWith("{")||A.startsWith("[")){try{t=JSON.parse(A)}catch(e){_.error("Bad JSON object "+A)}_.isArray(t)||(t=[t])}else(t={}).action=b,t[f]=A,t=[t=_.extend(!0,{},N,t)];x=x.concat(t)}if(x.length){for(a=0,g=x.length;a!==g;a+=1)((l=(i=x[a]).target)&&l!==w?E:k).push(i);for(y=S.base||{},v=E.length,a=0,g=k.length;a!==g;a+=1)(i=_.extend({},y,k[a])).origin=c.get(0),i.provEvt=n,i.$selElm=u,i.form=m,v&&(i.actions=E),p.trigger(i.action+"."+ue,i)}return!0}),t.on("submit",e+" form",function(e){var t,a,i,l,r,o,n,s,p,c,d,u,m,g=e.currentTarget,h=_(g),A=h.data(Ae),b=h.data(R)||[],f=A?A.length:0,y=[],v=[],w=!1;for(f&&(N=(a=_("#"+A[f-1])).data(Ae),_("#"+N[N.length-1]).trigger(ge),a.trigger(ge)),t=0;t!==f;t+=1)for(s=(i=(a=_("#"+A[t])).data(Ae)).length,n=0;n!==s;n+=1){if(l=_("#"+i[n]),r=_("#"+l.data(ve)),v.push(r),o=r.data(be),!(u=l.data(ye))&&o.defaultIfNone){for(p=0,c=(u=o.defaultIfNone).length;p!==c;p+=1)(d=u[p]).origin=r.get(0),d.$selElm=r.prev().find("input, select").eq(0),d.provEvt=d.$selElm.get(0),d.form=g,r.trigger(d.action+"."+ue,d);u=l.data(ye)}if(u)for(p=0,c=u.length;p!==c;p+=1)(d=u[p]).form=g,a.trigger(d.action+"."+me,d),y.push({$elm:a,data:d}),w=w||d.preventSubmit,m=d.provEvt}if(!w){for(h.find(W).removeAttr("name"),f=b.length,t=0;t!==f;t+=1)_(b[t]).remove();var x,k,E,S,C,b=[],N=h.attr("action");if(N&&0<(x=N.indexOf("?"))){for(f=(C=N.substring(x+1).split("&")).length,t=0;t!==f;t+=1)0<(E=k=C[t]).indexOf("=")&&(E=(S=k.split("=",2))[0],k=S[1]),(S=O.createElement("input")).type="hidden",S.name=E,S.value=$.escapeAttribute(k),h.append(S),b.push(S);h.data(R,b)}}for(f=v.length,t=0;t!==f;t+=1)(o=(r=v[t]).data(be)).action&&y.push({$elm:r,data:o});for(f=y.length,t=0;t!==f;t+=1)(d=y[t]).data.lastProvEvt=m,d.$elm.trigger(d.data.action+"."+I,d.data);if(w)return e.preventDefault(),e.stopPropagation?e.stopImmediatePropagation():e.cancelBubble=!0,!1}),t.on("keyup",e+" select",function(e){if(-1!==navigator.userAgent.indexOf("Gecko"))return e.keyCode&&(1===e.keyCode||9===e.keyCode||16===e.keyCode||e.altKey||e.ctrlKey)||_(e.target).trigger("change"),!0}),t.on(l,d,function(e,t){var a,i,l,r,o,n,G,B,L,s,U,p,c,d,u,m,g,h,A,b,f,J,y=e.type;switch(e.namespace){case de:switch(y){case re:d=t,(c=e).namespace===de&&(c=c.target,c=_(c),m=(u=_(d.source)).get(0),g=d.lblselector||"."+pe,A=d.itmselector||"ul:first() > li",u.hasClass(oe)&&(f=$.getData(u,re),u.data(be,f),d=_.extend({},d,f)),f=d.actions||[],J=d.renderas||"select",m.id||(m.id=$.getId()),(b=u.children().first()).hasClass(ce)?(h=b.html(),A="."+ce+" + "+A):(h=((b=b.find(g)).length?b:u.find("> p")).html(),g=null),b=ke(u.find(A)),d.outputctnrid||(d.outputctnrid=d.provEvt.parentElement.id),c.trigger(J+"."+he,{actions:f,source:m,attributes:d.attributes,outputctnrid:d.outputctnrid,label:h,lblselector:g,defaultselectedlabel:d.defaultselectedlabel,required:!d.isoptional,noreqlabel:d.noreqlabel,items:b,inline:d.inline,gcChckbxrdio:d.gcChckbxrdio,showLabel:d.showLabel}));break;case"tblfilter":((e,t)=>{if(e.namespace===de){var a,i,l,r,o,n,s,p,c,d,u,m=t.column,g=t.csvextract,h=t.source,A=_(h),b=[],f=t.label,y=t.defaultselectedlabel,v=t.lblselector,w=t.fltrseq||[],x=t.limit||10;if(!A.hasClass("wb-tables-inited"))return A.one("wb-ready.wb-tables",function(){_(e.target).trigger("tblfilter."+de,t)});if((a=A.dataTable({retrieve:!0}).api()).rows({search:"applied"}).data().length<=x)return;if(u=t.renderas||"select",!m&&w.length){if(!(p=w.shift()).column)throw"Column is undefined in the filter sequence";m=p.column,g=p.csvextract,y=p.defaultselectedlabel,f=p.label,v=p.lblselector}if(a=a.column(m,{search:"applied"}),g)for(r=0,o=(i=a.data()).length;r!==o;r+=1)b=b.concat(i[r].split(","));else for(r=0,o=(i=a.nodes()).length;r!==o;r+=1)for(n=0,s=(l=_(i[r]).find("li")).length;n!==s;n+=1)b.push(_(l[n]).text());var b=b.sort().filter(function(e,t,a){return!t||e!==a[t-1]}),g=e.target,g=_(g),k=[],E=t.actions||[];for(w.length&&(d={action:"append",srctype:"tblfilter",source:h,renderas:w[0].renderas||u,fltrseq:w,limit:x}),r=0,o=b.length;r!==o;r+=1)c={label:p=b[r],actions:[{action:"tblfilter",source:h,column:m,value:p}]},d&&c.actions.push(d),k.push(c);f=f||a.header().textContent,t.outputctnrid||(t.outputctnrid=t.provEvt.parentElement.id),g.trigger(u+"."+he,{actions:E,source:A.get(0),outputctnrid:t.outputctnrid,label:f,defaultselectedlabel:y,lblselector:v,items:k,inline:t.inline,gcChckbxrdio:t.gcChckbxrdio})}})(e,t)}break;case he:switch(y){case"select":var v,w,x,H,k,M,E,S=e,C=t,D=C.outputctnrid,D=_("#"+D),K=C.actions,j=C.lblselector,P=!!C.required,q=!C.noreqlabel,Q=C.items,S=S.target,X=_(S),V=C.source,N=C.attributes,Z=X.data(be).i18n,O=$.getId(),F="<label for='"+O+"'",T=C.inline&&!C.showLabel?" wb-inv":"",R="</span>",W=C.defaultselectedlabel||!1===C.defaultselectedlabel&&!C.live?C.defaultselectedlabel:Z.defaultsel;if(P&&q&&(F+=" class='required"+T+"'",R+=" <strong class='required'>("+Z.required+")</strong>"),F+="><span class='field-name'>",R+="</label>",j?(q=(v=_("<div>"+C.label+"</div>")).find(j)).html(F+q.html()+R):v=_(F+C.label+R),w="<select id='"+O+"' name='"+se+O+"' class='full-width form-control mrgn-bttm-md "+ne+"' data-"+ve+"='"+S.id+"' "+we+"='"+V.id+"'",P&&(w+=" required"),N&&"object"==typeof N)for(x in N)Object.prototype.hasOwnProperty.call(N,x)&&(w+=" "+x+"='"+N[x]+"'");for(w+=">",W&&(w+="<option value=''>"+W+"</option>"),x=0,H=Q.length;x!==H;x+=1)if(E=Q[x],x||W||(E.isSelected=!0),E.group){for(w+="<optgroup label='"+$.escapeAttribute(xe(E.label))+"'>",M=E.group.length,k=0;k!==M;k+=1)w+=le(E.group[k]);w+="</optgroup>"}else w+=le(E);T=_(w+="</select>"),D.append(v).append(T),K&&0<K.length&&T.data(fe,K),ee(X,Ae,O),W||T.trigger("change");break;case"checkbox":t.typeRadCheck="checkbox",ie(e,t);break;case"radio":t.typeRadCheck="radio",ie(e,t)}break;case ue:switch(y){case"append":var I=e,Y=t;if(I.namespace===ue){var z=Y.srctype||re;if(Y.container=Y.provEvt.parentNode.id,!Y.source)throw"A source is required to append a field flow control.";_(I.currentTarget).trigger(z+"."+de,Y)}break;case"redir":ee(_(t.provEvt),ye,t,!0);break;case"ajax":I=e,Y=(z=t).provEvt,z.live?(z.container||(p=_("<div></div>"),_(Y.parentNode).append(p),z.container=p.get(0)),_(I.target).trigger("ajax."+me,z)):(z.preventSubmit=!0,ee(_(Y),ye,z));break;case"tblfilter":p=t,(o=e).namespace===ue&&(o=p.source,o=_(o).dataTable({retrieve:!0}).api(),s=p.column,n=parseInt(s,10),G=!!p.regex,B=!p.smart||!!p.smart,L=!p.caseinsen||!!p.caseinsen,(U=o.column(s=!0===n?n:s)).search(p.value,G,B,L).draw(),_(p.provEvt).one(ge,function(){U.search("").draw()}));break;case"toggle":t.live?ae(e,t):(t.preventSubmit=!0,ee(_(t.provEvt),ye,t));break;case"addClass":if(!t.source||!t.class)return;t.live?_(t.source).addClass(t.class):(t.preventSubmit=!0,ee(_(t.provEvt),ye,t));break;case"removeClass":if(!t.source||!t.class)return;t.live?_(t.source).removeClass(t.class):(t.preventSubmit=!0,ee(_(t.provEvt),ye,t));break;case"query":te(0,t)}break;case me:switch(y){case"redir":r=(l=t).form,(l=l.url)&&r.setAttribute("action",l);break;case"ajax":i=(r=t).clean,r.container?a=_(r.container):(l=$.getId(),a=_("<div id='"+l+"'></div>"),_(r.form).append(a),i="#"+l),i&&_(r.origin).one(ge,function(){_(i).empty()}),r.trigger&&a.attr("data-trigger-wet","true"),a.attr("data-ajax-"+(r.type||"replace"),r.url),a.one("wb-contentupdated",function(e,t){var e=e.currentTarget,a=e.getAttribute("data-trigger-wet");e.removeAttribute("data-ajax-"+t["ajax-type"]),a&&(_(e).find($.allSelectors).addClass("wb-init").filter(":not(#"+e.id+" .wb-init .wb-init)").trigger("timerpoke.wb"),e.removeAttribute("data-trigger-wet"))}),a.trigger("wb-update.wb-data-ajax");break;case"toggle":ae(e,t);break;case"addClass":_(t.source).addClass(t.class);break;case"removeClass":_(t.source).removeClass(t.class);break;case"query":te(0,t)}}}),t.on("timerpoke.wb "+i,d,function(e){switch(e.type){case"timerpoke":case"wb-init":var t,a,i,l,r=e;if(r=$.init(r,re,d)){t=_(r),a=r.id,m.i18n[$.lang]&&(m.i18n=m.i18n[$.lang]),(i=$.getData(t,re))&&i.i18n&&(i.i18n=_.extend({},m.i18n,i.i18n)),(i=_.extend({},m,i)).defaultIfNone&&!_.isArray(i.defaultIfNone)&&(i.defaultIfNone=[i.defaultIfNone]),t.data(be,i),l=i.i18n,String.prototype.startsWith||(String.prototype.startsWith=function(e,t){return this.substr(t=t||0,e.length)===e});var o,n,s=$.getId(),p=i.btnStyle&&0<=["default","primary","success","info","warning","danger","link"].indexOf(i.btnStyle)?i.btnStyle:"default",c=!!i.showLabel;if(i.noForm){for(o="<div class='mrgn-tp-md'><div id='"+s+"'></div></div>",n=r.parentElement;"FORM"!==n.nodeName;)n=n.parentElement;_(n.parentElement).addClass(u)}else o=i.inline&&!i.renderas?(o="<div class='wb-frmvld mrgn-bttm-md "+u+"'><form><div class='input-group'><div id='"+s+"'>")+"</div><span class='input-group-btn"+(c?" align-bottom":"")+'\'><input type="submit" value="'+$.escapeAttribute(l.btn)+'" class="btn btn-'+p+'" /></span></div> </form></div>':(o="<div class='wb-frmvld "+u+"'><form><div id='"+s+"'>")+'</div><input type="submit" value="'+$.escapeAttribute(l.btn)+'" class="btn btn-primary mrgn-bttm-md" /> </form></div>';t.addClass("hidden"),o=_(o),t.after(o),i.noForm||(n=o.find("form"),o.trigger("wb-init.wb-frmvld")),c=_(n),ee(c,Ae,a),i.outputctnrid||(i.outputctnrid=s),i.source||(i.source=r),i.srctype||(i.srctype=re),i.inline=!!i.inline,i.gcChckbxrdio=!!i.gcChckbxrdio,t.trigger(i.srctype+"."+de,i),i.unhideelm&&_(i.unhideelm).removeClass("hidden"),i.hideelm&&_(i.hideelm).addClass("hidden"),$.ready(t,re),i.ext&&(i.form=c.get(0),t.trigger(i.ext+"."+g,i))}}return!0}),$.add(d)})(jQuery,document,wb),((w,x,k)=>{function l(e){e=e.target,e=x.getElementById(e.getAttribute("list")),Modernizr.load({test:Modernizr.stringnormalize,nope:["site!deps/unorm"+k.getMode()+".js"]}),w(e).trigger({type:"json-fetch.wb",fetch:{url:e.dataset.wbSuggest}})}function r(e){var t=e.target,a=x.getElementById(t.getAttribute("list")),i=e.target.value,l=e.which;switch(E&&clearTimeout(E),e.type){case"change":E=setTimeout(F.bind(a,i),S);break;case"keyup":e.ctrlKey||e.altKey||e.metaKey||(8===l||32===l||47<l&&l<91||95<l&&l<112||159<l&&l<177||187<l&&l<223)&&(E=setTimeout(F.bind(a,i),S))}}var E,o=k.doc,n="wb-suggest",s="[data-"+n+"]",S=250,C=5,N=function(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")},O=function(){for(var e,t=this.children,a=t.length-1;0<a;a-=1)1===(e=t[a]).nodeType&&"TEMPLATE"!==e.nodeName&&this.removeChild(e)},F=function(e,t,a){var i,l,r,o,n,s,p,c,d,u=a||JSON.parse(this.dataset.wbSuggestions||[]),m=this.dataset.wbFilterType||"any",g=u.length,h=[],A=this.childNodes,b=A.length-1,f=w("[list="+this.id+"]"),y=f.get(0);if(!u.length&&C&&(C-=1,E&&clearTimeout(E),E=setTimeout(F(e,t,a),S)),t=t||parseInt(this.dataset.wbLimit||g),e){switch(m){case"startWith":e="^"+e;break;case"word":e="^"+e+"|\\s"+e}i=new RegExp(e,"i")}if(!e||e.length<2)O.call(this),A=[];else for(p=b;0!==p;p-=1)1===(c=A[p]).nodeType&&"OPTION"===c.nodeName&&((d=c.getAttribute("value"))&&d.match(i)?h.push(N(d)):this.removeChild(c));var v=this.querySelector("template");for(v&&!v.content&&k.tmplPolyfill(v),l=0;l<g&&h.length<t;l+=1)r=u[l],o=N(r),-1!==h.indexOf(o)||e&&!r.match(i)||(h.push(o),v?s=(n=v.content.cloneNode(!0)).querySelector("option"):(n=x.createDocumentFragment(),s=x.createElement("OPTION"),n.appendChild(s)),s.setAttribute("label",r),s.setAttribute("value",r),this.appendChild(n));f.trigger("wb-update.wb-datalist"),y.value=y.value};o.on("timerpoke.wb wb-init.wb-suggest json-fetched.wb",s,function(e){var t,a,i=e.target;if(e.currentTarget===i)switch(e.type){case"timerpoke":case"wb-init":t=e,a=k.init(t,n,s),t="[list="+t.target.id+"]",a&&(Modernizr.addTest("stringnormalize","normalize"in String),o.one("focus",t,l),(a.dataset.wbLimit||a.dataset.wbFilterType)&&o.on("change keyup",t,r),k.ready(w(a),n));break;case"json-fetched":!function(e){this.dataset.wbSuggestions=JSON.stringify(e),delete this.dataset.wbSuggest,F.call(this,x.querySelector("[list="+this.id+"]").value)}.call(i,e.fetch.response)}return!0}),k.add(s)})(jQuery,document,wb),((n,s,p)=>{var c,d="wb-urlmapping",a="[data-"+d+"]",i="domapping."+d,l=p.doc;l.on(i,a,function(e){var t,a,i,l=n(e.target),r=(()=>{for(var e={},t=/\+/g,a=/([^&=]+)=?([^&]*)/g,i=function(e){return decodeURIComponent(e.replace(t," "))},l=s.location.search.substring(1),r=a.exec(l);r;)e[i(r[1])]=i(r[2]),r=a.exec(l);return e})(),o=n.extend({},s[d]||{},p.getData(l,d));for(t in r)if("object"==typeof(i=o[t+"="+(a=r[t])]||o[t])&&(l.trigger({type:"do.wb-actionmng",actions:{action:"withInput",actions:i,cValue:a,dntwb:l[0]!==c}}),!o.multiplequery))break}),l.on("timerpoke.wb wb-init.wb-urlmapping",a,function(e){var t,e=p.init(e,d,a);e&&(t=n(e),c=c||e,p.ready(t,d),p.isReady?t.trigger(i):l.one("wb-ready.wb",function(){t.trigger(i)}))}),p.add(a)})(jQuery,window,wb),((l,r)=>{var o="adapter-wb5-click",n="[data-wb5-click][data-toggle]",s="wb-postback";r.doc.on("timerpoke.wb wb-init.adapter-wb5-click",n,function(e){var t,a,i,e=r.init(e,o,n);e&&(t=(a=(e=l(e)).data("wb5-click").split("@"))[0],a=l(a[1]),i=r.getData(e,"toggle"),"postback"===t&&"FORM"===a[0].nodeName&&i.selector?(a.addClass(s),a.attr("data-"+s,JSON.stringify({success:i.selector,failure:i.selector})),a.parent().removeClass("gc-rprt-prblm-tggl"),e.removeAttr("data-wb5-click").removeAttr("toggle").removeAttr("data-toggle").removeAttr("aria-controls").removeClass("wb-toggle"),a.trigger("wb-init."+s)):console.log("Init failed for adapter wb5 click"),r.ready(e,o))}),r.add(n)})(jQuery,(window,wb)),((i,l)=>{function a(e){var t=i(e).parentsUntil(y).parents(),a=document.querySelector("html").className;b=-1!==a.indexOf("smallview"),n=-1!==a.indexOf("mediumview"),(b||n)&&d(!1,n),e.previousElementSibling.setAttribute("aria-label",c),l.ready(t,s)}var r,o,b,n,f,s="gcweb-menu",y="."+s,e=l.doc,t=y+" [data-ajax-replace],"+y+" [data-ajax-append],"+y+" [data-ajax-prepend],"+y+" [data-wb-ajax]",p=350,c={en:"Main Menu. Press the SPACEBAR to expand or the escape key to collapse this menu. Use the Up and Down arrow keys to choose a submenu item. Press the Enter or Right arrow key to expand it, or the Left arrow or Escape key to collapse it. Use the Up and Down arrow keys to choose an item on that level and the Enter key to access it.",fr:"Menu principal. Appuyez sur la barre d'espacement pour ouvrir ou sur la touche d'échappement pour fermer le menu. Utilisez les flèches haut et bas pour choisir un élément de sous-menu. Appuyez sur la touche Entrée ou sur la flèche vers la droite pour le développer, ou sur la flèche vers la gauche ou la touche Échap pour le réduire. Utilisez les flèches haut et bas pour choisir un élément de ce niveau et la touche Entrée pour y accéder."};function v(e){var t;"true"!==e.getAttribute("aria-expanded")&&((t=e.parentElement.parentElement.querySelector("[aria-haspopup][aria-expanded=true]:not([data-keep-expanded=md-min])"))&&!b&&w(t,!0),e.setAttribute("aria-expanded","true"),o=e,setTimeout(function(){o=!1},p))}function w(e,t){if(e){if(e.hasAttribute("aria-haspopup")||(e=e.previousElementSibling),!t){var t=e.nextElementSibling.querySelector("[role=menuitem]:focus"),a=e.parentElement.parentElement.querySelector("[role=menuitem]:focus");if(t||a===e)return}e.setAttribute("aria-expanded","false")}}function d(e,t){for(var a,i=document.querySelectorAll("[role=menu] [role=menu] [role=menuitem][aria-haspopup=true]"),l=i.length,r=t?"true":"false",o=e?"vertical":"horizontal",n=0;n<l;n++)a=i[n].nextElementSibling.querySelector("[role=menuitem]:focus")?"true":r,i[n].setAttribute("aria-expanded",a),i[n].parentElement.previousElementSibling.setAttribute("aria-orientation",o)}e.on("mouseenter",y+" ul [aria-haspopup]",function(e){var t;b||(clearTimeout(void 0),"md-min"!==(t=e.currentTarget).dataset.keepExpanded&&(clearTimeout(r),r=setTimeout(function(){v(t)},p)))}),e.on("focusin",y+" ul [aria-haspopup]",function(e){b?f=!1:v(e.currentTarget)}),e.on("mouseenter focusin",y+" [aria-haspopup] + [role=menu]",function(e){"md-min"===e.currentTarget.previousElementSibling.dataset.keepExpanded||b||o===e.currentTarget||clearTimeout(void 0)}),e.on("mouseleave",y+" [aria-haspopup]",function(){clearTimeout(r)}),e.on("click",y+" [aria-haspopup]",function(e){var t=e.currentTarget;f?f=!1:(!b&&"BUTTON"!==t.nodeName||("true"===t.getAttribute("aria-expanded")?o!==t&&w(t,!0):(v(t),(t=t.nextElementSibling.querySelector("[role=menuitem]")).focus(),t.setAttribute("tabindex","0"))),e.stopImmediatePropagation(),e.preventDefault())}),e.on(l.resizeEvents,function(e){switch(e.type){case"xxsmallview":case"xsmallview":case"smallview":d(!(b=!0),!1);break;case"mediumview":d(b=!1,!0);break;default:d(!(b=!1),!0)}}),e.on("keydown",function(e){27===e.keyCode&&w(document.querySelector(y+" button"))}),e.on("keydown",y+" button, "+y+" [role=menuitem]",function(e){var t=e.currentTarget,a=9===(a=e.charCode||e.keyCode)?"tab":13===a||32===a?"enter":27===a?"esc":39===a?"right":37===a?"left":40===a?"down":38===a&&"up",i=document.querySelector("[role=menuitem]:focus")||t,l=i.parentElement,r=l.parentElement,o="BUTTON"===i.nodeName;if("tab"===a)w(document.querySelector(y+" button"),!0);else if(o&&"enter"===a&&"true"===t.getAttribute("aria-expanded"))w(t,f=!0);else{i.nextElementSibling&&(n=i.nextElementSibling.querySelector("[role='menuitem']")),s=l.nextElementSibling?(s=l.nextElementSibling.querySelector("[role=menuitem]"))||l.nextElementSibling.nextElementSibling.querySelector("[role=menuitem]"):!b&&i.dataset.keepExpanded&&n?n:(!b&&r.previousElementSibling.dataset.keepExpanded?r.parentElement.parentElement:r).querySelector("[role=menuitem]");for(var n,s,p,c,d,u,m,g=r.previousElementSibling,h=l.previousElementSibling?(h=l.previousElementSibling.querySelector("[role=menuitem]"))||l.previousElementSibling.previousElementSibling.querySelector("[role=menuitem]"):!b&&r.lastElementChild.querySelector("[role=menuitem]").dataset.keepExpanded?r.lastElementChild.querySelector("[role=menuitem]").nextElementSibling.lastElementChild.querySelector("[role=menuitem]"):!b&&r.previousElementSibling.dataset.keepExpanded&&g?g:(o?i.nextElementSibling:r).lastElementChild.querySelector("[role=menuitem]"),A=l;A.nextElementSibling;)if("separator"===(A=A.nextElementSibling).getAttribute("role")){p=!(!A.hasAttribute("aria-orientation")||"vertical"!==A.getAttribute("aria-orientation")),c=A.nextElementSibling.querySelector("[role=menuitem]");break}for(A=l;A.previousElementSibling;){if("separator"===(A=A.previousElementSibling).getAttribute("role")){if(u)break;d=!(!A.hasAttribute("aria-orientation")||"vertical"!==A.getAttribute("aria-orientation")),u=A.previousElementSibling}u=u&&A}if(u=u&&u.querySelector("[role=menuitem]"),o||i.setAttribute("tabindex","-1"),"down"===a&&s)m=s;else if("up"===a&&h)m=h;else if(!o&&"right"===a&&n||"enter"===a&&n)m=n;else if(p&&"right"===a)m=c;else if(d&&"left"===a)m=u;else if(!o&&"left"===a||!o&&"esc"===a)m=g;else if("tab"===a)return;"left"!==a&&"esc"!==a||(!o&&b&&"true"===m.getAttribute("aria-expanded")?m.setAttribute("aria-expanded","false"):o&&t.setAttribute("aria-expanded","false")),m&&((b||o)&&"true"!==(r=m.parentElement.parentElement.previousElementSibling).getAttribute("aria-expanded")&&v(r),m.setAttribute("tabindex","0"),m.focus(),e.stopImmediatePropagation(),e.preventDefault())}}),e.on("ajax-fetched.wb ajax-failed.wb",t,function(e){var t=e.target;e.currentTarget===t&&a(t)}),e.on("timerpoke.wb wb-init.gcweb-menu",y,function(e){e=l.init(e,s,y);e&&(c[l.lang]?c=c[l.lang]:c.en&&(c=c.en),!e.querySelector(t))&&a(e.querySelector("[role=menu]"))}),l.add(y)})(jQuery,wb),document.querySelector(".gc-minister")&&!document.body.classList.contains("page-type-ilp")&&document.querySelector(".gc-srvinfo")&&document.querySelector(".list-unstyled.bold-content.mrgn-tp-lg.lst-spcd-2.colcount-md-2")&&(document.body.classList.add("page-type-ilp"),console.warn('It seems that this page is an institutional landing page. However, the <body> element is missing the "page-type-ilp" CSS class. It has been added for your convenience, but please make sure you follow the technical guidance: https://wet-boew.github.io/GCWeb/templates/institutional-landing/institutional-landing-doc-en.html')),(i=>{var l,e=wb.doc;e.on("wb-ready.wb-geomap","#sample_map",function(e,t){var t=i("#geomap-aoi-extent-"+(l=t).id),a=i("#geomap-aoi-extent-lonlat-"+l.id);t&&(t.on("change",function(){}),a.on("change",function(){}))}),e.on("wb-ready.wb-geomap","#location_map",function(e,t){t.getView().setCenter(ol.proj.transform([-75.70535,45.3995],"EPSG:4326","EPSG:3978")),t.getView().setZoom(5)})})(jQuery);var wet_boew_geomap={overlays:[{title:"WMS",caption:"This is a sample WMS service loaded by Geomap.",type:"wms",url:"//geo.weather.gc.ca/geomet?lang=en",visible:!1,version:"1.3.0",format:"image/png",layers:"GDPS.ETA_PR",transparent:!0,options:{opacity:.5,legendHTML:"<small>GeoMet Precipitation (mm)</small><ul class='list-unstyled'><li><span style='background-color:#800000;display:inline-block;height:20px;width:20px'/> <small>100.0</small></li><li><span style='background-color:#FF0000;display:inline-block;height:20px;width:20px'/> <small>50.0</small></li><li><span style='background-color:#FF4500;display:inline-block;height:20px;width:20px'/> <small>25.0</small></li><li><span style='background-color:#FFA500;display:inline-block;height:20px;width:20px'/> <small>20.0</small></li><li><span style='background-color:#FFD700;display:inline-block;height:20px;width:20px'/> <small>15.0</small></li><li><span style='background-color:#E5E500;display:inline-block;height:20px;width:20px'/> <small>10.0</small></li><li><span style='background-color:#7FFF00;display:inline-block;height:20px;width:20px'/> <small>7.5</small></li><li><span style='background-color:#7FFFD4;display:inline-block;height:20px;width:20px'/> <small>5.0</small></li><li><span style='background-color:#00FFFF;display:inline-block;height:20px;width:20px'/> <small>2.5</small></li><li><span style='background-color:#87CEFA;display:inline-block;height:20px;width:20px'/> <small>1.0</small></li><li><span style='background-color:#1E90FF;display:inline-block;height:20px;width:20px'/> <small>0.5</small></li><li><span style='background-color:#0000CD;display:inline-block;height:20px;width:20px'/> <small>0.25</small></li><li><span style='background-color:#000080;display:inline-block;height:20px;width:20px'/> <small>0.10</small></li></ul>"}},{title:"KML",caption:'This data is extracted from the <a href="http://geogratis.gc.ca/api/en/nrcan-rncan/ess-sst/457ede2f-fd65-5936-ab60-3fe71da0e98b">Principal mineral areas of Canada</a> publication.',type:"kml",url:"demo/producing-mines.kml",visible:!1,datatable:!0,tooltips:!0,tooltipText:"Operation",popups:!0,attributes:{Type_:"Type",OwnersE:"Owner",OperationE:"Operation",ComGroupE:"Commodity Group",CommodityE:"Commodity"},style:{type:"unique",field:"Type",init:{Coal:{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA+hJREFUSInN1n9MVWUYwPHvOfeC51zvubeQiwtDCS37sZxbP5YaKbVqaw1W/DKCQiaEBfYD19ZabrlaMp0tNREItJBoDabWrC2pK1qbxXRMmoMwDav1R8GFe4DzAvee0x/iTbpwuZRrPf++z/t8znPe57w7dv6jsP8vICH8i0GqxLIiP5AknVYUbcs/goQYvgHL9AJJIEV0sMgQhn6tomovzgoSYij5MnLU20bzkc+nNeKucfNSaQme+HkvCEOXFVV7PirIMIYWYlleYFHrsTaaDn8asRlPXBzbqqrZtKGEhPj4jcLQJUXVNkaEDENPkrC8QPKXbcf58NAlRJJlLNMMQ2LsdvJzMgkGgjR83ExBThYJ8fHlwvBLiuoqnxIaGfEvkMALpHx14gSNBz8BYMeW13BpGn19Pj472krbt+2hzeOBAFt27OSVslJKCwuo3t/Ak9mZzPd4yiawskmQZf0WMyokL7D42IlvONByOFTMpWnIkownfh4Fa7NJuj5x0vr42BgjQpAQm0B+Thbb99RQsaGY+R7Pc8LQ+xVV2xyCDMM9R5aCN17o7eWDloOTXs/vf/Qx3+MBQJZkfL7B0Fqs3U7FsyW4nE5efv1N0h96gBV3LGdbVQ3bN78KWMumPKOxsfGwczjyRSuFebnIkoxpmYyOjU5C3C4XW3dVMajrNLQcYvU9d6H79bA6M94MX7efIikxEb+uY1om2RnpBIImK+++E7fLReW7exkY/KvLtpPtxNjDy0Z1BV054rIkU5CbRX+/j7d2V9Hf74umxOzuuli7neW334ZvYBBVVViUeN3Vh2ImzsQ5dy5vvL2LZUuXsO6JHKzGj+g423V1IJvNRkXpelxOJ5W79zLg93P8u1OMBYKsy8ul7kATZ7p++HeQzWaj4pn1aJoWmq7LcfJ0B8FAgMK8texrbKKzuycyZJqmKdswFyYtkG9OSabr/E+hhFuXpKCqClt37kEfHg4r0H7me8YDAR59+MEQlJP+yKVFSQp9L3YAp9M5IoRepCpqfVlxkby7tj6EdXb30Nn9TsSuO852hc4pPzOD+1NTAesCyJsmQQCKor0vhC45VEddWXGRvKu2nu4rOosmnsp8jDWpqwDOW8hpquK8GAZNYPsvY+XFRdJssKezHmf1vSsBfrQgTVWdP1+5HjYMiqLtE4YuO1RHbbRYYXYm961aAXDuEqL98vecKadOUbU6Yfglh+qoKS8uknovhu0LxRwllpRFyQDnTMta43C4fp0qb9rxVlTXexNY9S1Lb5rhp4Ee07LSpkMiQhNYrRCDzZJks0XKEyI45Ha7RaScGT9YRXFHd5nNEH8C+eGD9m6tNTgAAAAASUVORK5CYII=",fillOpacity:"1",graphicWidth:"25",name:"Coal mines"},"Metal mines and mills":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA9FJREFUSInN1ltoHFUYwPH/mdndzKwzs6l7Iy1N25S+FCLaF69gg5cHq6S2FUSaS5OqD6YxASklgUpTCgqCIki1uTRF0QcDIlJpaWpES4UgrljBh7RRqsVmN5fNbpo52ezO+JBkcZtkk2gRv9fznfObc77vHMbDfxSe/wUkZWoriDdx3eIfJMQPmmZ2/CNIyltbcJ0BYCOIog4u1dJOr9N0s3VNkJRTmxeQcxPf8vFU/7JGSLE4Eq4n4gu2SDutaLr56qog254qx3UHgE3nJy5xJn2+6Gai6jpOJHpojzQQ8QabpZ0Wmm42F4VsO71R4A4Amy8kL9GbPgeAgoKDswjxCpUDwWqybo7TY59RH9xN1Bs6JO2U0HTr0JLQ9HRqg4ABoKI/eZme1Bxycv1hLI/JaGacLya/pt+O5SfPujnab77P65EGmiIv8F78E+ruribqCzXNY00FkOv+6Z2RYgDYejH5Hd2pL/OLWR4DBUHEF+RAeA+bkmUF4zNkmHYkURGmLljNG/FejoTrifpCr0g7Pa7p5tE8ZNuBEkXktg3L63SlzhYcTyIzTtQXmj9CwUR2Mj9WIry0hWqxVIPmG2+xz3iUR7RKTiR6eHfDYcC9Z8kaZZzZRXX4PPkVByPPoSBwcLDdmQKk1GPRMdJN0pmiK3WWJ/T7SDq3Fq2z4sswIH+ifKKMdG6KHA7Ph3bhjDo8bOyg1GNyPN7NmJPK51+wY3iFunYIKGhxBUFd+FnGZpN0xDtJ5FJFZq4RWogS4WWHsZ2J7CR+tYQtnrI7D3mFSluoFkO5i6M3T3Gvr4IXQ3sg0cdgZujOQB6h0h6qw1INjsd7GHfSXJQ/MjuWozG8FyfxKd9nrv07yCNU2oI1WKrBsfhcdy3EN/IK2dEsjeG95BJ9xDLDxSHHcRxFxSnX1iuV3nKuzF7PJ1R6N+FXdY6NdDPpLm7byzO/kE1k2R2oIpaYg2rNJ+cGhcjfFw+AYRjTUqYb/Ire0xLdr7wz8lEei2WGiY2cLLrrwcwQg4m5OjVaT/F46UOA+ysorxVAAJpmnpEyLfyKv7slul95e+RDfp79vShwexy0dvFY6YMAwy5Kla4Z+aMpqJGmmb0LWGu0RqwFe8l6mqrSBwCuuVCl60bBxEXNoGnmaWmnFb/i71wt9nLgGXYG7ge4OoeYf9yes2TXabrZLe2U8Cv+U63RGvGbvLEsoiklVGjlAFcd193p91tLJi/b3ppudc1jH2z3b1vhp4Ehx3WrlkOKQvNYp5STfUKoi1/Jv4WUualAICCL5ax4YTUtMLFSzmriL1z5gWQ67XYNAAAAAElFTkSuQmCC",fillOpacity:"1",graphicWidth:"25",name:"Metal mines"},"Industrial minerals":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA79JREFUSInN1ktMXFUYwPH/uVxmzoV5tBUGDe+SGmnwFWVBqQHUhYmJxo1xKZg2Pmh91IVJFWx140JXTapC62NtoombYmgHmoZQSUxUKDVWpuVheQ2tzAz3zMDc42KmIAwMUBvjtz3f/X7nnu87N9fkPwrzfwEpNV8F4iO0zr4hIX6S0nv8tiClYpVoJwiUgsjqoHlW2ZGd0vK+uS1IqWjFLWRy8Azhy19vaOS4A1Q0HEX6Ct9QdsSQlvf1LUG2HS1D6yBQPjnURXi4M+vLmHn3EOo5TmXje0hf4LCyI0Ja3sNZIduOlAp0EKiYvNRF+FJHakEYoJ1MRbgoqW1GJ5cYHzhNSW0L0hc4pOx5IS3foXWhhYX5YgFBYPfUcDfhoRSy5+lOci0f8UiYqcHviE50rTytE4ycfYeKhmNU7G/l2oUTFD/ajPQXtaax1lWQ1tdz40oEgaqp4bPMDn66XCvX8iGEgfQVUlb3EtOXK1et66QiuWhjmHdTXNtCqOdDKhuOIv1Fryk7Mictb9syZNt+tyGSe6LTI8wOnlx1OvH5GaS/KHVSwmApNrdycoakpL4N0+3lt+9b2XXv8/hKGgj1fkD1MycA/cC6PUomExltmPz1W8rrDyKEgdYOySW1jJTubyPX2kEo+D7OYpjZoZN4ip/CScxl1Nn0yxC73s3UUAVJNY/WSUpqX2DMWWJX1WMppOcYTmJ6OT86cQaEa/sQsGbEDcr3NROPzjFyrh0nPrmVEtv71glD4i9/hHjsBjkuidu3G3vmTkPCleqJ28NI97vkFTxEad0BRvsc1Gz/HYKESWl9O6bbm278LNE/f2DixyXK6g4y2qdR4Yv/EhImJfvayZVervakputWxCbPMX5xidK6A4z1Oai5geyQ4ziOkYOTf1eZ4drxIImbPy8nyJ0PY7osQr3t6MUbGQUWps8z1p8gUPMco+dTUMHeF9ObFIurII/Hs6BUpMV05Z2ubHjLCPV+soypuQGuBjfeKYCa7Wf0fKpPBTUvU1T9JKBDYLy9CgKQ0vuVUhFhuvJPpbCPSdz8JSuwNgpqXqGo+gmAEY3RZEnPaAaUxr5cwY6I7WCFNa8SqH4c4A8NTZblGfvnesYwSOn9QtkRw3Tld2wVK7y/lcB9jQBXUoh3fG3OulMnLe8pZc8L05X/eWXDERGbubYhkpPrxhOoArjiaN2Yl+ebWC9vw/GWlq8zjX3mL967yU8DvztaN22EZIXSWIdSf30jRE5OtjylklG/36+y5Wx6YaX0Z16e24i/AWg1hRJKTWppAAAAAElFTkSuQmCC",fillOpacity:"1",graphicWidth:"25",name:"Nonmetal Mines"},"Oil Sands":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA9VJREFUSInN1l9QVFUcwPHvucDuucveXZqCHXOkZakexFkcmHypRon+PFYPNT1UY0zAiEBKJmjiKDqF04zUWA+IWk0PvTiNNtWI5hDj+CCwFskb/8JyKvk37gL3uLD39sC2hguLmNN0Xn+/+/ucP79z5qbzH430/wWkVDgfxCFsO/WEhLgspdF0V5BS03nYVgewBkRKB5vnlRm5T+rG9hVBSk35/0b6v23natsXSxoZq7Ip2r8bd072NmVGNKkbb90RZJpTudh2B/BQ/3ftjHx8POVinA+u4vKeAxQd3IM7J6dWmREhdaM2JWSakTUCuwPwD5xpZ+RIHNE0sKwkRDgdFFRuJjY3R1/rCdZVlOH25dQoMyyk7qlZFJqZCa8W0AEEBs9+zy8fzSOPf3kU6fUwPTrOwFenGTt9NvGxfTNKV+0uij7YR1FdNT+2fMLa8s0YPl91HKteANn27xk3legA8ofOnWe45WiimPR6EELDnZNNYWUZQwH/gritbjJrKtKdTgrK3+By43sUHdiF2+fbqszIhNSNvQnINL1OTcQeGR8cYuhw64Ltmb4+itvnm98qoaHGJ25tnS4Jvr8H6THoLKsh99WXeKD0SULvHmTjsSOAHVz0jGLRaNI59J88xfqqcoTQsG2LmKluIc2N6FleeuqbiI1OMNzSSvYLzzE3NplUZ9mXYfyb8wz4/UTDN8CyKXjtFey5GKs3PYHMyiK0u4m5P64n8kdPtSOcjpVDwMIWFxCs2Iw5NkF3wz5mr/15JyVW9tYJXeJ7rBhzYpJ0l8T1aIAb9xoSTgfB5kachpuut/eStaGQYNWb9FoW4c5L9wjKSCfY3Ij0GITqm5i7PsbY1+foi85SWFXOT5ZF5EL3v4TiiNNj0LNzH7HRW+09eeYH+mIxglXl/GzZRC72pIYsy7K0NKwsf66WuaGQ6a7eRIKxYT0ZLp3Qzv3ExpPbdvLcBa5EowRefpHeOOTf8vp8UIjZBZDb7Z5RKlLm0F0niuu3aaFDHyawyMUeQilmChDuvERv/JzytleQ/+zTgD0M2o4FEICUxudKRYTDlXm8uH6bFmo+zHT3lZTA7SNQV0ngmVKAIRutRJfuq0lQHPssgTXUiZVggR1bCJSWAAzaUKLr7l//GU9qBimNT5UZ0RyuzLY7xfLf2UreUxsBBuYR47fbcxbtOqkbx5UZFg5X5tHihjoxOTSyJJIundz/cD7AgGXbm1wuz7VF85YqIHXPsTjW6lu3dpmfBvot2y5ZCkkJxbE2pW6cFCItLVWeUrEpr9erUuUse2Gl9CZfnrsYfwFqWYQCxROHDQAAAABJRU5ErkJggg==",fillOpacity:"1",graphicWidth:"25",name:"Oil sands mines"}},select:{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAACKElEQVR42uWWMU/bQBTHWVBR1aUSG7Sl4oMBoiwd+gEYGBgqWIoCLDDCBEUIqUJqB6TOEe1QCQGOEyeRHBTHl/OLfTkvcNwz2NDkzrGTpVItPeXknPy7/3t/vXdjY//V43nNWR/gyHGcEwzSbJ6QRuMp5DtXvuMcVkaAeO8BoM59X4QyuCZC/hDdrreRG0IpneEereKHXkyxgWH9KUcw33ULmSHtdvutTJeVFRLHr5+lGLY5EEIIecMpLeeBXBZNcfbNSNZRGhlsaSGu604Frmv2Qki1KjiAMH6bWtiPY0NAoxGtLxIY3e6DnJ/vjAcAhkoJQpLiy7UKtLFuCN9xxFXRfFLW9QVjsPoXSFr3FTrrYO9amZrnLlNBLov9akPmC7S/ErS2WlKeOFHVo6ggIVdFdUpxvxKEf3zWgJ6H32olkFalot0XpoEwDVmchspa0iRpexDk6EBfMoBQCToRbm7SDzOKot7Cx5bWgRzZB3PXqKBxF9Tr+RSFKYrQjd8P9WqJol48UIBs237JO3BLbFv5odPDwbXrPeRDR4evfd0h6HTmpJtuaUreswZCeNeryAb9TtnvOpTOI4yMAIsgAGWcAqkdHChdkBvvhoFxhs3UM3EKZJpJjNLFvDAsPg+gJCHTuaasTOMHhFFpELT9yrIZ/a49rj99LEfr/V1DhI8QHDVD3RsY85YQ1ndPiNcIwHEQgDE0JH5qtdpraf/JtLAsa+KfubrdA6qupf9mD9mBAAAAAElFTkSuQmCC",graphicOpacity:"1"}}},{title:"TopoJSON (World 110m)",caption:"This is a sample dataset loaded from a remote TopoJSON resource.",type:"topojson",url:"demo/topojson.json",accessible:!1,visible:!1,style:{strokeColor:"#3399ff",strokeWidth:2}},{title:"Esri REST Tile Layer",caption:"This is a sample dataset loaded from a remote Esri REST tile service.",type:"esritile",url:"//maps-cartes.services.geo.ca/server_serveur/rest/services/NRCan/Carte_climatique_HOT2000_Climate_Map_EN/MapServer/",params:{LAYERS:"show:0"},visible:!1,datatable:!1,options:{legendHTML:"<ul class='list-unstyled'><li><small>Weather Station</small><img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAFZJREFUOI3t0TEKwDAMQ1F9yIFzlN5YHUqgpOA2TZaANdnLQ9hFi1MSTPCKbbcZYAq0bWq3B2gI9pgkUWN086cAPG54xI95bdjQ+/674VdkGBxJgvM5AZAOH6jK5pnSAAAAAElFTkSuQmCC'></li></ul>"}},{title:"EsriJSON",caption:"This is a sample dataset loaded from a remote Esri JSON resource.",type:"esrijson",url:"https://geoappext.nrcan.gc.ca/arcgis/rest/services/FGP/TMX_EN/MapServer/2/query?where=OBJECTID>0&f=pjson",attributes:{Type:"Type"},visible:!1,zoom:!0,style:{strokeColor:"#FF0000",strokeWidth:2,strokeDash:[6,6]}},{title:"JSON (Earthquakes)",caption:"This is a sample dataset loaded from a remote JSON resource, in this case the USGS Earthquakes API.",type:"json",url:"https://earthquake.usgs.gov/earthquakes/feed/v1.0/summary/all_day.geojson",visible:!1,popups:!0,datatable:!0,zoom:!0,root:"features",attributes:{title:{path:"properties",alias:"Title"},mag:{path:"properties",alias:"Magnitude"},time:{path:"properties",alias:"Time"}},style:{type:"rule",rule:[{field:"Magnitude",value:[2],filter:"LESS_THAN",name:"M < 2",init:{strokeColor:"#333333",fillColor:"#000066",pointRadius:2.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[2,2.9],filter:"BETWEEN",name:"M-2",init:{strokeColor:"#333333",fillColor:"#6600cc",pointRadius:4.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[3,3.9],filter:"BETWEEN",name:"M-3",init:{strokeColor:"#333333",fillColor:"#990099",pointRadius:6.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[4,4.9],filter:"BETWEEN",name:"M-4",init:{strokeColor:"#333333",fillColor:"#ff0000",pointRadius:8,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[5,5.9],filter:"BETWEEN",name:"M-5",init:{graphicName:"star",strokeColor:"#333333",fillColor:"#ff6600",pointRadius:14,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[5.9],filter:"GREATER_THAN",name:"M-6+",init:{graphicName:"star",strokeColor:"#333333",fillColor:"#ff9933",pointRadius:18,fillOpacity:.8,strokeWidth:1}}]}},{title:"GeoJSON (CartoDB)",caption:"This is a sample dataset loaded from a remote GeoJSON resource, in this case traffic cameras in the city of Ottawa from the CartoDB API.",type:"geojson",url:"//stephenott.cartodb.com/api/v2/sql",params:{format:"GeoJSON",q:"SELECT * FROM traffic_cameras LIMIT 25"},attributes:{location_desc:"Location",longitude:"Latitude",latitude:"Longitude",updated_at:"Last updated"},visible:!1,zoom:!0,datatable:!0,style:{type:"symbol",init:{graphicWidth:32,graphicHeight:37,externalGraphic:"demo/trafficcamera.png",graphicOpacity:1},select:{graphicWidth:32,graphicHeight:37,externalGraphic:"demo/trafficcamera_active.png",graphicOpacity:1}}}]},wet_boew_geomap={basemap:{title:"Basic Map",type:"esri",url:"//geoappext.nrcan.gc.ca/arcgis/rest/services/BaseMaps/provinces1c/MapServer/export",options:{singleTile:!1,ratio:1,projection:"EPSG:3978",fractionalZoom:!0},mapOptions:{maxExtent:"-3000000.0, -800000.0, 4000000.0, 3900000.0",maxResolution:"auto",projection:"EPSG:3978",restrictedExtent:"-3000000.0, -800000.0, 4000000.0, 3900000.0",units:"m",displayProjection:"EPSG:4269",numZoomLevels:2}}},wet_boew_geomap={basemap:{title:"Basic Map",type:"esri",url:"//geoappext.nrcan.gc.ca/arcgis/rest/services/BaseMaps/provinces1c/MapServer/export",options:{singleTile:!1,ratio:1,projection:"EPSG:3978",fractionalZoom:!0},mapOptions:{maxExtent:"-3000000.0, -800000.0, 4000000.0, 3900000.0",maxResolution:"auto",projection:"EPSG:3978",restrictedExtent:"-3000000.0, -800000.0, 4000000.0, 3900000.0",units:"m",displayProjection:"EPSG:4269",numZoomLevels:2}}},wet_boew_geomap={overlays:[{title:"WMS",caption:"Ceci est un exemple de service WMS chargé par Geomap.",type:"wms",url:"//geo.weather.gc.ca/geomet?lang=en",visible:!1,version:"1.3.0",format:"image/png",layers:"GDPS.ETA_PR",transparent:!0,options:{opacity:.5,legendHTML:"<small>GeoMet Precipitation (mm)</small><ul class='list-unstyled'><li><span style='background-color:#800000;display:inline-block;height:20px;width:20px'/> <small>100.0</small></li><li><span style='background-color:#FF0000;display:inline-block;height:20px;width:20px'/> <small>50.0</small></li><li><span style='background-color:#FF4500;display:inline-block;height:20px;width:20px'/> <small>25.0</small></li><li><span style='background-color:#FFA500;display:inline-block;height:20px;width:20px'/> <small>20.0</small></li><li><span style='background-color:#FFD700;display:inline-block;height:20px;width:20px'/> <small>15.0</small></li><li><span style='background-color:#E5E500;display:inline-block;height:20px;width:20px'/> <small>10.0</small></li><li><span style='background-color:#7FFF00;display:inline-block;height:20px;width:20px'/> <small>7.5</small></li><li><span style='background-color:#7FFFD4;display:inline-block;height:20px;width:20px'/> <small>5.0</small></li><li><span style='background-color:#00FFFF;display:inline-block;height:20px;width:20px'/> <small>2.5</small></li><li><span style='background-color:#87CEFA;display:inline-block;height:20px;width:20px'/> <small>1.0</small></li><li><span style='background-color:#1E90FF;display:inline-block;height:20px;width:20px'/> <small>0.5</small></li><li><span style='background-color:#0000CD;display:inline-block;height:20px;width:20px'/> <small>0.25</small></li><li><span style='background-color:#000080;display:inline-block;height:20px;width:20px'/> <small>0.10</small></li></ul>"}},{title:"KML",caption:'Ces données sont extraites de la publication <a href="http://geogratis.gc.ca/api/en/nrcan-rncan/ess-sst/457ede2f-fd65-5936-ab60-3fe71da0e98b">Principales régions minérales du Canada</a>.',type:"kml",url:"demo/producing-mines.kml",visible:!1,datatable:!0,tooltips:!0,tooltipText:"Opération",popups:!0,attributes:{Type_:"Type",OwnersF:"Opérateur / propriétaire",OperationF:"Opération",ComGroupF:"Groupe de produits minéraux",CommodityF:"Produit minérale"},style:{type:"unique",field:"Type",init:{Coal:{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA+hJREFUSInN1n9MVWUYwPHvOfeC51zvubeQiwtDCS37sZxbP5YaKbVqaw1W/DKCQiaEBfYD19ZabrlaMp0tNREItJBoDabWrC2pK1qbxXRMmoMwDav1R8GFe4DzAvee0x/iTbpwuZRrPf++z/t8znPe57w7dv6jsP8vICH8i0GqxLIiP5AknVYUbcs/goQYvgHL9AJJIEV0sMgQhn6tomovzgoSYij5MnLU20bzkc+nNeKucfNSaQme+HkvCEOXFVV7PirIMIYWYlleYFHrsTaaDn8asRlPXBzbqqrZtKGEhPj4jcLQJUXVNkaEDENPkrC8QPKXbcf58NAlRJJlLNMMQ2LsdvJzMgkGgjR83ExBThYJ8fHlwvBLiuoqnxIaGfEvkMALpHx14gSNBz8BYMeW13BpGn19Pj472krbt+2hzeOBAFt27OSVslJKCwuo3t/Ak9mZzPd4yiawskmQZf0WMyokL7D42IlvONByOFTMpWnIkownfh4Fa7NJuj5x0vr42BgjQpAQm0B+Thbb99RQsaGY+R7Pc8LQ+xVV2xyCDMM9R5aCN17o7eWDloOTXs/vf/Qx3+MBQJZkfL7B0Fqs3U7FsyW4nE5efv1N0h96gBV3LGdbVQ3bN78KWMumPKOxsfGwczjyRSuFebnIkoxpmYyOjU5C3C4XW3dVMajrNLQcYvU9d6H79bA6M94MX7efIikxEb+uY1om2RnpBIImK+++E7fLReW7exkY/KvLtpPtxNjDy0Z1BV054rIkU5CbRX+/j7d2V9Hf74umxOzuuli7neW334ZvYBBVVViUeN3Vh2ImzsQ5dy5vvL2LZUuXsO6JHKzGj+g423V1IJvNRkXpelxOJ5W79zLg93P8u1OMBYKsy8ul7kATZ7p++HeQzWaj4pn1aJoWmq7LcfJ0B8FAgMK8texrbKKzuycyZJqmKdswFyYtkG9OSabr/E+hhFuXpKCqClt37kEfHg4r0H7me8YDAR59+MEQlJP+yKVFSQp9L3YAp9M5IoRepCpqfVlxkby7tj6EdXb30Nn9TsSuO852hc4pPzOD+1NTAesCyJsmQQCKor0vhC45VEddWXGRvKu2nu4rOosmnsp8jDWpqwDOW8hpquK8GAZNYPsvY+XFRdJssKezHmf1vSsBfrQgTVWdP1+5HjYMiqLtE4YuO1RHbbRYYXYm961aAXDuEqL98vecKadOUbU6Yfglh+qoKS8uknovhu0LxRwllpRFyQDnTMta43C4fp0qb9rxVlTXexNY9S1Lb5rhp4Ee07LSpkMiQhNYrRCDzZJks0XKEyI45Ha7RaScGT9YRXFHd5nNEH8C+eGD9m6tNTgAAAAASUVORK5CYII=",fillOpacity:"1",graphicWidth:"25",name:"Mines de charbon"},"Metal mines and mills":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA9FJREFUSInN1ltoHFUYwPH/mdndzKwzs6l7Iy1N25S+FCLaF69gg5cHq6S2FUSaS5OqD6YxASklgUpTCgqCIki1uTRF0QcDIlJpaWpES4UgrljBh7RRqsVmN5fNbpo52ezO+JBkcZtkk2gRv9fznfObc77vHMbDfxSe/wUkZWoriDdx3eIfJMQPmmZ2/CNIyltbcJ0BYCOIog4u1dJOr9N0s3VNkJRTmxeQcxPf8vFU/7JGSLE4Eq4n4gu2SDutaLr56qog254qx3UHgE3nJy5xJn2+6Gai6jpOJHpojzQQ8QabpZ0Wmm42F4VsO71R4A4Amy8kL9GbPgeAgoKDswjxCpUDwWqybo7TY59RH9xN1Bs6JO2U0HTr0JLQ9HRqg4ABoKI/eZme1Bxycv1hLI/JaGacLya/pt+O5SfPujnab77P65EGmiIv8F78E+ruribqCzXNY00FkOv+6Z2RYgDYejH5Hd2pL/OLWR4DBUHEF+RAeA+bkmUF4zNkmHYkURGmLljNG/FejoTrifpCr0g7Pa7p5tE8ZNuBEkXktg3L63SlzhYcTyIzTtQXmj9CwUR2Mj9WIry0hWqxVIPmG2+xz3iUR7RKTiR6eHfDYcC9Z8kaZZzZRXX4PPkVByPPoSBwcLDdmQKk1GPRMdJN0pmiK3WWJ/T7SDq3Fq2z4sswIH+ifKKMdG6KHA7Ph3bhjDo8bOyg1GNyPN7NmJPK51+wY3iFunYIKGhxBUFd+FnGZpN0xDtJ5FJFZq4RWogS4WWHsZ2J7CR+tYQtnrI7D3mFSluoFkO5i6M3T3Gvr4IXQ3sg0cdgZujOQB6h0h6qw1INjsd7GHfSXJQ/MjuWozG8FyfxKd9nrv07yCNU2oI1WKrBsfhcdy3EN/IK2dEsjeG95BJ9xDLDxSHHcRxFxSnX1iuV3nKuzF7PJ1R6N+FXdY6NdDPpLm7byzO/kE1k2R2oIpaYg2rNJ+cGhcjfFw+AYRjTUqYb/Ire0xLdr7wz8lEei2WGiY2cLLrrwcwQg4m5OjVaT/F46UOA+ysorxVAAJpmnpEyLfyKv7slul95e+RDfp79vShwexy0dvFY6YMAwy5Kla4Z+aMpqJGmmb0LWGu0RqwFe8l6mqrSBwCuuVCl60bBxEXNoGnmaWmnFb/i71wt9nLgGXYG7ge4OoeYf9yes2TXabrZLe2U8Cv+U63RGvGbvLEsoiklVGjlAFcd193p91tLJi/b3ppudc1jH2z3b1vhp4Ehx3WrlkOKQvNYp5STfUKoi1/Jv4WUualAICCL5ax4YTUtMLFSzmriL1z5gWQ67XYNAAAAAElFTkSuQmCC",fillOpacity:"1",graphicWidth:"25",name:"Mines de métaux"},"Industrial minerals":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA79JREFUSInN1ktMXFUYwPH/uVxmzoV5tBUGDe+SGmnwFWVBqQHUhYmJxo1xKZg2Pmh91IVJFWx140JXTapC62NtoombYmgHmoZQSUxUKDVWpuVheQ2tzAz3zMDc42KmIAwMUBvjtz3f/X7nnu87N9fkPwrzfwEpNV8F4iO0zr4hIX6S0nv8tiClYpVoJwiUgsjqoHlW2ZGd0vK+uS1IqWjFLWRy8Azhy19vaOS4A1Q0HEX6Ct9QdsSQlvf1LUG2HS1D6yBQPjnURXi4M+vLmHn3EOo5TmXje0hf4LCyI0Ja3sNZIduOlAp0EKiYvNRF+FJHakEYoJ1MRbgoqW1GJ5cYHzhNSW0L0hc4pOx5IS3foXWhhYX5YgFBYPfUcDfhoRSy5+lOci0f8UiYqcHviE50rTytE4ycfYeKhmNU7G/l2oUTFD/ajPQXtaax1lWQ1tdz40oEgaqp4bPMDn66XCvX8iGEgfQVUlb3EtOXK1et66QiuWhjmHdTXNtCqOdDKhuOIv1Fryk7Mictb9syZNt+tyGSe6LTI8wOnlx1OvH5GaS/KHVSwmApNrdycoakpL4N0+3lt+9b2XXv8/hKGgj1fkD1MycA/cC6PUomExltmPz1W8rrDyKEgdYOySW1jJTubyPX2kEo+D7OYpjZoZN4ip/CScxl1Nn0yxC73s3UUAVJNY/WSUpqX2DMWWJX1WMppOcYTmJ6OT86cQaEa/sQsGbEDcr3NROPzjFyrh0nPrmVEtv71glD4i9/hHjsBjkuidu3G3vmTkPCleqJ28NI97vkFTxEad0BRvsc1Gz/HYKESWl9O6bbm278LNE/f2DixyXK6g4y2qdR4Yv/EhImJfvayZVervakputWxCbPMX5xidK6A4z1Oai5geyQ4ziOkYOTf1eZ4drxIImbPy8nyJ0PY7osQr3t6MUbGQUWps8z1p8gUPMco+dTUMHeF9ObFIurII/Hs6BUpMV05Z2ubHjLCPV+soypuQGuBjfeKYCa7Wf0fKpPBTUvU1T9JKBDYLy9CgKQ0vuVUhFhuvJPpbCPSdz8JSuwNgpqXqGo+gmAEY3RZEnPaAaUxr5cwY6I7WCFNa8SqH4c4A8NTZblGfvnesYwSOn9QtkRw3Tld2wVK7y/lcB9jQBXUoh3fG3OulMnLe8pZc8L05X/eWXDERGbubYhkpPrxhOoArjiaN2Yl+ebWC9vw/GWlq8zjX3mL967yU8DvztaN22EZIXSWIdSf30jRE5OtjylklG/36+y5Wx6YaX0Z16e24i/AWg1hRJKTWppAAAAAElFTkSuQmCC",fillOpacity:"1",graphicWidth:"25",name:"Mines de non-métaux"},"Oil Sands":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA9VJREFUSInN1l9QVFUcwPHvucDuucveXZqCHXOkZakexFkcmHypRon+PFYPNT1UY0zAiEBKJmjiKDqF04zUWA+IWk0PvTiNNtWI5hDj+CCwFskb/8JyKvk37gL3uLD39sC2hguLmNN0Xn+/+/ucP79z5qbzH430/wWkVDgfxCFsO/WEhLgspdF0V5BS03nYVgewBkRKB5vnlRm5T+rG9hVBSk35/0b6v23natsXSxoZq7Ip2r8bd072NmVGNKkbb90RZJpTudh2B/BQ/3ftjHx8POVinA+u4vKeAxQd3IM7J6dWmREhdaM2JWSakTUCuwPwD5xpZ+RIHNE0sKwkRDgdFFRuJjY3R1/rCdZVlOH25dQoMyyk7qlZFJqZCa8W0AEEBs9+zy8fzSOPf3kU6fUwPTrOwFenGTt9NvGxfTNKV+0uij7YR1FdNT+2fMLa8s0YPl91HKteANn27xk3legA8ofOnWe45WiimPR6EELDnZNNYWUZQwH/gritbjJrKtKdTgrK3+By43sUHdiF2+fbqszIhNSNvQnINL1OTcQeGR8cYuhw64Ltmb4+itvnm98qoaHGJ25tnS4Jvr8H6THoLKsh99WXeKD0SULvHmTjsSOAHVz0jGLRaNI59J88xfqqcoTQsG2LmKluIc2N6FleeuqbiI1OMNzSSvYLzzE3NplUZ9mXYfyb8wz4/UTDN8CyKXjtFey5GKs3PYHMyiK0u4m5P64n8kdPtSOcjpVDwMIWFxCs2Iw5NkF3wz5mr/15JyVW9tYJXeJ7rBhzYpJ0l8T1aIAb9xoSTgfB5kachpuut/eStaGQYNWb9FoW4c5L9wjKSCfY3Ij0GITqm5i7PsbY1+foi85SWFXOT5ZF5EL3v4TiiNNj0LNzH7HRW+09eeYH+mIxglXl/GzZRC72pIYsy7K0NKwsf66WuaGQ6a7eRIKxYT0ZLp3Qzv3ExpPbdvLcBa5EowRefpHeOOTf8vp8UIjZBZDb7Z5RKlLm0F0niuu3aaFDHyawyMUeQilmChDuvERv/JzytleQ/+zTgD0M2o4FEICUxudKRYTDlXm8uH6bFmo+zHT3lZTA7SNQV0ngmVKAIRutRJfuq0lQHPssgTXUiZVggR1bCJSWAAzaUKLr7l//GU9qBimNT5UZ0RyuzLY7xfLf2UreUxsBBuYR47fbcxbtOqkbx5UZFg5X5tHihjoxOTSyJJIundz/cD7AgGXbm1wuz7VF85YqIHXPsTjW6lu3dpmfBvot2y5ZCkkJxbE2pW6cFCItLVWeUrEpr9erUuUse2Gl9CZfnrsYfwFqWYQCxROHDQAAAABJRU5ErkJggg==",fillOpacity:"1",graphicWidth:"25",name:"Mines de sables bitumineux"}},select:{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAACKElEQVR42uWWMU/bQBTHWVBR1aUSG7Sl4oMBoiwd+gEYGBgqWIoCLDDCBEUIqUJqB6TOEe1QCQGOEyeRHBTHl/OLfTkvcNwz2NDkzrGTpVItPeXknPy7/3t/vXdjY//V43nNWR/gyHGcEwzSbJ6QRuMp5DtXvuMcVkaAeO8BoM59X4QyuCZC/hDdrreRG0IpneEereKHXkyxgWH9KUcw33ULmSHtdvutTJeVFRLHr5+lGLY5EEIIecMpLeeBXBZNcfbNSNZRGhlsaSGu604Frmv2Qki1KjiAMH6bWtiPY0NAoxGtLxIY3e6DnJ/vjAcAhkoJQpLiy7UKtLFuCN9xxFXRfFLW9QVjsPoXSFr3FTrrYO9amZrnLlNBLov9akPmC7S/ErS2WlKeOFHVo6ggIVdFdUpxvxKEf3zWgJ6H32olkFalot0XpoEwDVmchspa0iRpexDk6EBfMoBQCToRbm7SDzOKot7Cx5bWgRzZB3PXqKBxF9Tr+RSFKYrQjd8P9WqJol48UIBs237JO3BLbFv5odPDwbXrPeRDR4evfd0h6HTmpJtuaUreswZCeNeryAb9TtnvOpTOI4yMAIsgAGWcAqkdHChdkBvvhoFxhs3UM3EKZJpJjNLFvDAsPg+gJCHTuaasTOMHhFFpELT9yrIZ/a49rj99LEfr/V1DhI8QHDVD3RsY85YQ1ndPiNcIwHEQgDE0JH5qtdpraf/JtLAsa+KfubrdA6qupf9mD9mBAAAAAElFTkSuQmCC",graphicOpacity:"1"}}},{title:"TopoJSON (World 110m)",caption:"Voici un exemple de jeu de données chargé à partir d'une ressource TopoJSON distante.",type:"topojson",url:"demo/topojson.json",accessible:!1,visible:!1,style:{strokeColor:"#3399ff",strokeWidth:2}},{title:"Esri REST Tile Layer",caption:"Il s'agit d'un exemple de jeu de données chargé à partir d'un service de tuiles REST Esri distant.",type:"esritile",url:"//maps-cartes.services.geo.ca/server_serveur/rest/services/NRCan/Carte_climatique_HOT2000_Climate_Map_EN/MapServer/",params:{LAYERS:"show:0"},visible:!1,datatable:!1,options:{legendHTML:"<ul class='list-unstyled'><li><small>Weather Station</small><img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAFZJREFUOI3t0TEKwDAMQ1F9yIFzlN5YHUqgpOA2TZaANdnLQ9hFi1MSTPCKbbcZYAq0bWq3B2gI9pgkUWN086cAPG54xI95bdjQ+/674VdkGBxJgvM5AZAOH6jK5pnSAAAAAElFTkSuQmCC'></li></ul>"}},{title:"EsriJSON",caption:"Ceci est un exemple de jeu de données chargé à partir d'une ressource Esri JSON distante.",type:"esrijson",url:"https://geoappext.nrcan.gc.ca/arcgis/rest/services/FGP/TMX_EN/MapServer/2/query?where=OBJECTID>0&f=pjson",attributes:{Type:"Type"},visible:!1,zoom:!0,style:{strokeColor:"#FF0000",strokeWidth:2,strokeDash:[6,6]}},{title:"JSON (Earthquakes)",caption:"Il s'agit d'un exemple de jeu de données chargé à partir d'une ressource JSON distante, dans ce cas l'API USGS Earthquakes.",type:"json",url:"https://earthquake.usgs.gov/earthquakes/feed/v1.0/summary/all_day.geojson",visible:!1,popups:!0,datatable:!0,zoom:!0,root:"features",attributes:{title:{path:"properties",alias:"Titre"},mag:{path:"properties",alias:"Magnitude"},time:{path:"properties",alias:"Temps"}},style:{type:"rule",rule:[{field:"Magnitude",value:[2],filter:"LESS_THAN",name:"M < 2",init:{strokeColor:"#333333",fillColor:"#000066",pointRadius:2.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[2,2.9],filter:"BETWEEN",name:"M-2",init:{strokeColor:"#333333",fillColor:"#6600cc",pointRadius:4.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[3,3.9],filter:"BETWEEN",name:"M-3",init:{strokeColor:"#333333",fillColor:"#990099",pointRadius:6.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[4,4.9],filter:"BETWEEN",name:"M-4",init:{strokeColor:"#333333",fillColor:"#ff0000",pointRadius:8,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[5,5.9],filter:"BETWEEN",name:"M-5",init:{graphicName:"star",strokeColor:"#333333",fillColor:"#ff6600",pointRadius:14,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[5.9],filter:"GREATER_THAN",name:"M-6+",init:{graphicName:"star",strokeColor:"#333333",fillColor:"#ff9933",pointRadius:18,fillOpacity:.8,strokeWidth:1}}]}},{title:"GeoJSON (CartoDB)",caption:"Ceci est un exemple de jeu de données chargé à partir d'une ressource GeoJSON distante, dans ce cas les caméras de circulation dans la ville d'Ottawa à partir de l'API CartoDB",type:"geojson",url:"//stephenott.cartodb.com/api/v2/sql",params:{format:"GeoJSON",q:"SELECT * FROM traffic_cameras LIMIT 25"},attributes:{location_desc:"Emplacement",longitude:"Latitude",latitude:"Longitude",updated_at:"Dernière mise à jour"},visible:!1,zoom:!0,datatable:!0,style:{type:"symbol",init:{graphicWidth:32,graphicHeight:37,externalGraphic:"demo/trafficcamera.png",graphicOpacity:1},select:{graphicWidth:32,graphicHeight:37,externalGraphic:"demo/trafficcamera_active.png",graphicOpacity:1}}}]},wet_boew_geomap={basemap:{title:"OSM Map",type:"osm",mapOptions:{center:[-52.7222765,47.5410882],zoomLevel:11}}},wet_boew_geomap={basemap:{title:"OSM Map",type:"osm",mapOptions:{center:[-52.7222765,47.5410882],zoomLevel:11}}},wet_boew_geomap={basemap:{title:"Tile (XYZ) Source Map",type:"osm",url:["//otile1.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile2.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile3.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile4.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png"],mapOptions:{projection:"EPSG:900913",center:[-123,49],zoomLevel:5}}},wet_boew_geomap={basemap:{title:"Tile (XYZ) Source Map",type:"osm",url:["//otile1.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile2.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile3.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile4.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png"],mapOptions:{projection:"EPSG:900913",center:[-123,49],zoomLevel:5}}},wet_boew_geomap={basemap:{title:"WMS-Toporama",type:"wms",url:"//maps.geogratis.gc.ca/wms/toporama_en",version:"1.3.0",format:"image/png",layers:"limits",mapOptions:{maxExtent:"-2650000.0, -900000.0, 3600000.0, 4630000.0",restrictedExtent:"-2750000.0, -1000000.0, 3700000.0, 4730000.0",maxResolution:"auto",projection:"EPSG:3978",units:"m",displayProjection:"EPSG:4269",aspectRatio:.8}},overlays:[]},wet_boew_geomap={basemap:{title:"WMS-Toporama",type:"wms",url:"//maps.geogratis.gc.ca/wms/toporama_fr",version:"1.3.0",format:"image/png",layers:"WMS-Toporama",mapOptions:{maxExtent:"-2650000.0, -900000.0, 3600000.0, 4630000.0",restrictedExtent:"-2750000.0, -1000000.0, 3700000.0, 4730000.0",maxResolution:"auto",projection:"EPSG:3978",units:"m",displayProjection:"EPSG:4269",aspectRatio:.8}},overlays:[]},wet_boew_geomap=((i=>{var l,e=wb.doc;e.on("wb-ready.wb-geomap","#sample_map",function(e,t){var t=i("#geomap-aoi-extent-"+(l=t).id),a=i("#geomap-aoi-extent-lonlat-"+l.id);t&&(t.on("change",function(){}),a.on("change",function(){}))}),e.on("wb-ready.wb-geomap","#location_map",function(e,t){t.getView().setCenter(ol.proj.transform([-75.70535,45.3995],"EPSG:4326","EPSG:3978")),t.getView().setZoom(5)})})(jQuery),{overlays:[{title:"WMS",caption:"This is a sample WMS service loaded by Geomap.",type:"wms",url:"//geo.weather.gc.ca/geomet?lang=en",visible:!1,version:"1.3.0",format:"image/png",layers:"GDPS.ETA_PR",transparent:!0,options:{opacity:.5,legendHTML:"<small>GeoMet Precipitation (mm)</small><ul class='list-unstyled'><li><span style='background-color:#800000;display:inline-block;height:20px;width:20px'/> <small>100.0</small></li><li><span style='background-color:#FF0000;display:inline-block;height:20px;width:20px'/> <small>50.0</small></li><li><span style='background-color:#FF4500;display:inline-block;height:20px;width:20px'/> <small>25.0</small></li><li><span style='background-color:#FFA500;display:inline-block;height:20px;width:20px'/> <small>20.0</small></li><li><span style='background-color:#FFD700;display:inline-block;height:20px;width:20px'/> <small>15.0</small></li><li><span style='background-color:#E5E500;display:inline-block;height:20px;width:20px'/> <small>10.0</small></li><li><span style='background-color:#7FFF00;display:inline-block;height:20px;width:20px'/> <small>7.5</small></li><li><span style='background-color:#7FFFD4;display:inline-block;height:20px;width:20px'/> <small>5.0</small></li><li><span style='background-color:#00FFFF;display:inline-block;height:20px;width:20px'/> <small>2.5</small></li><li><span style='background-color:#87CEFA;display:inline-block;height:20px;width:20px'/> <small>1.0</small></li><li><span style='background-color:#1E90FF;display:inline-block;height:20px;width:20px'/> <small>0.5</small></li><li><span style='background-color:#0000CD;display:inline-block;height:20px;width:20px'/> <small>0.25</small></li><li><span style='background-color:#000080;display:inline-block;height:20px;width:20px'/> <small>0.10</small></li></ul>"}},{title:"KML",caption:'This data is extracted from the <a href="http://geogratis.gc.ca/api/en/nrcan-rncan/ess-sst/457ede2f-fd65-5936-ab60-3fe71da0e98b">Principal mineral areas of Canada</a> publication.',type:"kml",url:"demo/producing-mines.kml",visible:!1,datatable:!0,tooltips:!0,tooltipText:"Operation",popups:!0,attributes:{Type_:"Type",OwnersE:"Owner",OperationE:"Operation",ComGroupE:"Commodity Group",CommodityE:"Commodity"},style:{type:"unique",field:"Type",init:{Coal:{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA+hJREFUSInN1n9MVWUYwPHvOfeC51zvubeQiwtDCS37sZxbP5YaKbVqaw1W/DKCQiaEBfYD19ZabrlaMp0tNREItJBoDabWrC2pK1qbxXRMmoMwDav1R8GFe4DzAvee0x/iTbpwuZRrPf++z/t8znPe57w7dv6jsP8vICH8i0GqxLIiP5AknVYUbcs/goQYvgHL9AJJIEV0sMgQhn6tomovzgoSYij5MnLU20bzkc+nNeKucfNSaQme+HkvCEOXFVV7PirIMIYWYlleYFHrsTaaDn8asRlPXBzbqqrZtKGEhPj4jcLQJUXVNkaEDENPkrC8QPKXbcf58NAlRJJlLNMMQ2LsdvJzMgkGgjR83ExBThYJ8fHlwvBLiuoqnxIaGfEvkMALpHx14gSNBz8BYMeW13BpGn19Pj472krbt+2hzeOBAFt27OSVslJKCwuo3t/Ak9mZzPd4yiawskmQZf0WMyokL7D42IlvONByOFTMpWnIkownfh4Fa7NJuj5x0vr42BgjQpAQm0B+Thbb99RQsaGY+R7Pc8LQ+xVV2xyCDMM9R5aCN17o7eWDloOTXs/vf/Qx3+MBQJZkfL7B0Fqs3U7FsyW4nE5efv1N0h96gBV3LGdbVQ3bN78KWMumPKOxsfGwczjyRSuFebnIkoxpmYyOjU5C3C4XW3dVMajrNLQcYvU9d6H79bA6M94MX7efIikxEb+uY1om2RnpBIImK+++E7fLReW7exkY/KvLtpPtxNjDy0Z1BV054rIkU5CbRX+/j7d2V9Hf74umxOzuuli7neW334ZvYBBVVViUeN3Vh2ImzsQ5dy5vvL2LZUuXsO6JHKzGj+g423V1IJvNRkXpelxOJ5W79zLg93P8u1OMBYKsy8ul7kATZ7p++HeQzWaj4pn1aJoWmq7LcfJ0B8FAgMK8texrbKKzuycyZJqmKdswFyYtkG9OSabr/E+hhFuXpKCqClt37kEfHg4r0H7me8YDAR59+MEQlJP+yKVFSQp9L3YAp9M5IoRepCpqfVlxkby7tj6EdXb30Nn9TsSuO852hc4pPzOD+1NTAesCyJsmQQCKor0vhC45VEddWXGRvKu2nu4rOosmnsp8jDWpqwDOW8hpquK8GAZNYPsvY+XFRdJssKezHmf1vSsBfrQgTVWdP1+5HjYMiqLtE4YuO1RHbbRYYXYm961aAXDuEqL98vecKadOUbU6Yfglh+qoKS8uknovhu0LxRwllpRFyQDnTMta43C4fp0qb9rxVlTXexNY9S1Lb5rhp4Ee07LSpkMiQhNYrRCDzZJks0XKEyI45Ha7RaScGT9YRXFHd5nNEH8C+eGD9m6tNTgAAAAASUVORK5CYII=",fillOpacity:"1",graphicWidth:"25",name:"Coal mines"},"Metal mines and mills":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA9FJREFUSInN1ltoHFUYwPH/mdndzKwzs6l7Iy1N25S+FCLaF69gg5cHq6S2FUSaS5OqD6YxASklgUpTCgqCIki1uTRF0QcDIlJpaWpES4UgrljBh7RRqsVmN5fNbpo52ezO+JBkcZtkk2gRv9fznfObc77vHMbDfxSe/wUkZWoriDdx3eIfJMQPmmZ2/CNIyltbcJ0BYCOIog4u1dJOr9N0s3VNkJRTmxeQcxPf8vFU/7JGSLE4Eq4n4gu2SDutaLr56qog254qx3UHgE3nJy5xJn2+6Gai6jpOJHpojzQQ8QabpZ0Wmm42F4VsO71R4A4Amy8kL9GbPgeAgoKDswjxCpUDwWqybo7TY59RH9xN1Bs6JO2U0HTr0JLQ9HRqg4ABoKI/eZme1Bxycv1hLI/JaGacLya/pt+O5SfPujnab77P65EGmiIv8F78E+ruribqCzXNY00FkOv+6Z2RYgDYejH5Hd2pL/OLWR4DBUHEF+RAeA+bkmUF4zNkmHYkURGmLljNG/FejoTrifpCr0g7Pa7p5tE8ZNuBEkXktg3L63SlzhYcTyIzTtQXmj9CwUR2Mj9WIry0hWqxVIPmG2+xz3iUR7RKTiR6eHfDYcC9Z8kaZZzZRXX4PPkVByPPoSBwcLDdmQKk1GPRMdJN0pmiK3WWJ/T7SDq3Fq2z4sswIH+ifKKMdG6KHA7Ph3bhjDo8bOyg1GNyPN7NmJPK51+wY3iFunYIKGhxBUFd+FnGZpN0xDtJ5FJFZq4RWogS4WWHsZ2J7CR+tYQtnrI7D3mFSluoFkO5i6M3T3Gvr4IXQ3sg0cdgZujOQB6h0h6qw1INjsd7GHfSXJQ/MjuWozG8FyfxKd9nrv07yCNU2oI1WKrBsfhcdy3EN/IK2dEsjeG95BJ9xDLDxSHHcRxFxSnX1iuV3nKuzF7PJ1R6N+FXdY6NdDPpLm7byzO/kE1k2R2oIpaYg2rNJ+cGhcjfFw+AYRjTUqYb/Ire0xLdr7wz8lEei2WGiY2cLLrrwcwQg4m5OjVaT/F46UOA+ysorxVAAJpmnpEyLfyKv7slul95e+RDfp79vShwexy0dvFY6YMAwy5Kla4Z+aMpqJGmmb0LWGu0RqwFe8l6mqrSBwCuuVCl60bBxEXNoGnmaWmnFb/i71wt9nLgGXYG7ge4OoeYf9yes2TXabrZLe2U8Cv+U63RGvGbvLEsoiklVGjlAFcd193p91tLJi/b3ppudc1jH2z3b1vhp4Ehx3WrlkOKQvNYp5STfUKoi1/Jv4WUualAICCL5ax4YTUtMLFSzmriL1z5gWQ67XYNAAAAAElFTkSuQmCC",fillOpacity:"1",graphicWidth:"25",name:"Metal mines"},"Industrial minerals":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA79JREFUSInN1ktMXFUYwPH/uVxmzoV5tBUGDe+SGmnwFWVBqQHUhYmJxo1xKZg2Pmh91IVJFWx140JXTapC62NtoombYmgHmoZQSUxUKDVWpuVheQ2tzAz3zMDc42KmIAwMUBvjtz3f/X7nnu87N9fkPwrzfwEpNV8F4iO0zr4hIX6S0nv8tiClYpVoJwiUgsjqoHlW2ZGd0vK+uS1IqWjFLWRy8Azhy19vaOS4A1Q0HEX6Ct9QdsSQlvf1LUG2HS1D6yBQPjnURXi4M+vLmHn3EOo5TmXje0hf4LCyI0Ja3sNZIduOlAp0EKiYvNRF+FJHakEYoJ1MRbgoqW1GJ5cYHzhNSW0L0hc4pOx5IS3foXWhhYX5YgFBYPfUcDfhoRSy5+lOci0f8UiYqcHviE50rTytE4ycfYeKhmNU7G/l2oUTFD/ajPQXtaax1lWQ1tdz40oEgaqp4bPMDn66XCvX8iGEgfQVUlb3EtOXK1et66QiuWhjmHdTXNtCqOdDKhuOIv1Fryk7Mictb9syZNt+tyGSe6LTI8wOnlx1OvH5GaS/KHVSwmApNrdycoakpL4N0+3lt+9b2XXv8/hKGgj1fkD1MycA/cC6PUomExltmPz1W8rrDyKEgdYOySW1jJTubyPX2kEo+D7OYpjZoZN4ip/CScxl1Nn0yxC73s3UUAVJNY/WSUpqX2DMWWJX1WMppOcYTmJ6OT86cQaEa/sQsGbEDcr3NROPzjFyrh0nPrmVEtv71glD4i9/hHjsBjkuidu3G3vmTkPCleqJ28NI97vkFTxEad0BRvsc1Gz/HYKESWl9O6bbm278LNE/f2DixyXK6g4y2qdR4Yv/EhImJfvayZVervakputWxCbPMX5xidK6A4z1Oai5geyQ4ziOkYOTf1eZ4drxIImbPy8nyJ0PY7osQr3t6MUbGQUWps8z1p8gUPMco+dTUMHeF9ObFIurII/Hs6BUpMV05Z2ubHjLCPV+soypuQGuBjfeKYCa7Wf0fKpPBTUvU1T9JKBDYLy9CgKQ0vuVUhFhuvJPpbCPSdz8JSuwNgpqXqGo+gmAEY3RZEnPaAaUxr5cwY6I7WCFNa8SqH4c4A8NTZblGfvnesYwSOn9QtkRw3Tld2wVK7y/lcB9jQBXUoh3fG3OulMnLe8pZc8L05X/eWXDERGbubYhkpPrxhOoArjiaN2Yl+ebWC9vw/GWlq8zjX3mL967yU8DvztaN22EZIXSWIdSf30jRE5OtjylklG/36+y5Wx6YaX0Z16e24i/AWg1hRJKTWppAAAAAElFTkSuQmCC",fillOpacity:"1",graphicWidth:"25",name:"Nonmetal Mines"},"Oil Sands":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA9VJREFUSInN1l9QVFUcwPHvucDuucveXZqCHXOkZakexFkcmHypRon+PFYPNT1UY0zAiEBKJmjiKDqF04zUWA+IWk0PvTiNNtWI5hDj+CCwFskb/8JyKvk37gL3uLD39sC2hguLmNN0Xn+/+/ucP79z5qbzH430/wWkVDgfxCFsO/WEhLgspdF0V5BS03nYVgewBkRKB5vnlRm5T+rG9hVBSk35/0b6v23natsXSxoZq7Ip2r8bd072NmVGNKkbb90RZJpTudh2B/BQ/3ftjHx8POVinA+u4vKeAxQd3IM7J6dWmREhdaM2JWSakTUCuwPwD5xpZ+RIHNE0sKwkRDgdFFRuJjY3R1/rCdZVlOH25dQoMyyk7qlZFJqZCa8W0AEEBs9+zy8fzSOPf3kU6fUwPTrOwFenGTt9NvGxfTNKV+0uij7YR1FdNT+2fMLa8s0YPl91HKteANn27xk3legA8ofOnWe45WiimPR6EELDnZNNYWUZQwH/gritbjJrKtKdTgrK3+By43sUHdiF2+fbqszIhNSNvQnINL1OTcQeGR8cYuhw64Ltmb4+itvnm98qoaHGJ25tnS4Jvr8H6THoLKsh99WXeKD0SULvHmTjsSOAHVz0jGLRaNI59J88xfqqcoTQsG2LmKluIc2N6FleeuqbiI1OMNzSSvYLzzE3NplUZ9mXYfyb8wz4/UTDN8CyKXjtFey5GKs3PYHMyiK0u4m5P64n8kdPtSOcjpVDwMIWFxCs2Iw5NkF3wz5mr/15JyVW9tYJXeJ7rBhzYpJ0l8T1aIAb9xoSTgfB5kachpuut/eStaGQYNWb9FoW4c5L9wjKSCfY3Ij0GITqm5i7PsbY1+foi85SWFXOT5ZF5EL3v4TiiNNj0LNzH7HRW+09eeYH+mIxglXl/GzZRC72pIYsy7K0NKwsf66WuaGQ6a7eRIKxYT0ZLp3Qzv3ExpPbdvLcBa5EowRefpHeOOTf8vp8UIjZBZDb7Z5RKlLm0F0niuu3aaFDHyawyMUeQilmChDuvERv/JzytleQ/+zTgD0M2o4FEICUxudKRYTDlXm8uH6bFmo+zHT3lZTA7SNQV0ngmVKAIRutRJfuq0lQHPssgTXUiZVggR1bCJSWAAzaUKLr7l//GU9qBimNT5UZ0RyuzLY7xfLf2UreUxsBBuYR47fbcxbtOqkbx5UZFg5X5tHihjoxOTSyJJIundz/cD7AgGXbm1wuz7VF85YqIHXPsTjW6lu3dpmfBvot2y5ZCkkJxbE2pW6cFCItLVWeUrEpr9erUuUse2Gl9CZfnrsYfwFqWYQCxROHDQAAAABJRU5ErkJggg==",fillOpacity:"1",graphicWidth:"25",name:"Oil sands mines"}},select:{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAACKElEQVR42uWWMU/bQBTHWVBR1aUSG7Sl4oMBoiwd+gEYGBgqWIoCLDDCBEUIqUJqB6TOEe1QCQGOEyeRHBTHl/OLfTkvcNwz2NDkzrGTpVItPeXknPy7/3t/vXdjY//V43nNWR/gyHGcEwzSbJ6QRuMp5DtXvuMcVkaAeO8BoM59X4QyuCZC/hDdrreRG0IpneEereKHXkyxgWH9KUcw33ULmSHtdvutTJeVFRLHr5+lGLY5EEIIecMpLeeBXBZNcfbNSNZRGhlsaSGu604Frmv2Qki1KjiAMH6bWtiPY0NAoxGtLxIY3e6DnJ/vjAcAhkoJQpLiy7UKtLFuCN9xxFXRfFLW9QVjsPoXSFr3FTrrYO9amZrnLlNBLov9akPmC7S/ErS2WlKeOFHVo6ggIVdFdUpxvxKEf3zWgJ6H32olkFalot0XpoEwDVmchspa0iRpexDk6EBfMoBQCToRbm7SDzOKot7Cx5bWgRzZB3PXqKBxF9Tr+RSFKYrQjd8P9WqJol48UIBs237JO3BLbFv5odPDwbXrPeRDR4evfd0h6HTmpJtuaUreswZCeNeryAb9TtnvOpTOI4yMAIsgAGWcAqkdHChdkBvvhoFxhs3UM3EKZJpJjNLFvDAsPg+gJCHTuaasTOMHhFFpELT9yrIZ/a49rj99LEfr/V1DhI8QHDVD3RsY85YQ1ndPiNcIwHEQgDE0JH5qtdpraf/JtLAsa+KfubrdA6qupf9mD9mBAAAAAElFTkSuQmCC",graphicOpacity:"1"}}},{title:"TopoJSON (World 110m)",caption:"This is a sample dataset loaded from a remote TopoJSON resource.",type:"topojson",url:"demo/topojson.json",accessible:!1,visible:!1,style:{strokeColor:"#3399ff",strokeWidth:2}},{title:"Esri REST Tile Layer",caption:"This is a sample dataset loaded from a remote Esri REST tile service.",type:"esritile",url:"//maps-cartes.services.geo.ca/server_serveur/rest/services/NRCan/Carte_climatique_HOT2000_Climate_Map_EN/MapServer/",params:{LAYERS:"show:0"},visible:!1,datatable:!1,options:{legendHTML:"<ul class='list-unstyled'><li><small>Weather Station</small><img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAFZJREFUOI3t0TEKwDAMQ1F9yIFzlN5YHUqgpOA2TZaANdnLQ9hFi1MSTPCKbbcZYAq0bWq3B2gI9pgkUWN086cAPG54xI95bdjQ+/674VdkGBxJgvM5AZAOH6jK5pnSAAAAAElFTkSuQmCC'></li></ul>"}},{title:"EsriJSON",caption:"This is a sample dataset loaded from a remote Esri JSON resource.",type:"esrijson",url:"https://geoappext.nrcan.gc.ca/arcgis/rest/services/FGP/TMX_EN/MapServer/2/query?where=OBJECTID>0&f=pjson",attributes:{Type:"Type"},visible:!1,zoom:!0,style:{strokeColor:"#FF0000",strokeWidth:2,strokeDash:[6,6]}},{title:"JSON (Earthquakes)",caption:"This is a sample dataset loaded from a remote JSON resource, in this case the USGS Earthquakes API.",type:"json",url:"https://earthquake.usgs.gov/earthquakes/feed/v1.0/summary/all_day.geojson",visible:!1,popups:!0,datatable:!0,zoom:!0,root:"features",attributes:{title:{path:"properties",alias:"Title"},mag:{path:"properties",alias:"Magnitude"},time:{path:"properties",alias:"Time"}},style:{type:"rule",rule:[{field:"Magnitude",value:[2],filter:"LESS_THAN",name:"M < 2",init:{strokeColor:"#333333",fillColor:"#000066",pointRadius:2.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[2,2.9],filter:"BETWEEN",name:"M-2",init:{strokeColor:"#333333",fillColor:"#6600cc",pointRadius:4.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[3,3.9],filter:"BETWEEN",name:"M-3",init:{strokeColor:"#333333",fillColor:"#990099",pointRadius:6.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[4,4.9],filter:"BETWEEN",name:"M-4",init:{strokeColor:"#333333",fillColor:"#ff0000",pointRadius:8,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[5,5.9],filter:"BETWEEN",name:"M-5",init:{graphicName:"star",strokeColor:"#333333",fillColor:"#ff6600",pointRadius:14,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[5.9],filter:"GREATER_THAN",name:"M-6+",init:{graphicName:"star",strokeColor:"#333333",fillColor:"#ff9933",pointRadius:18,fillOpacity:.8,strokeWidth:1}}]}},{title:"GeoJSON (CartoDB)",caption:"This is a sample dataset loaded from a remote GeoJSON resource, in this case traffic cameras in the city of Ottawa from the CartoDB API.",type:"geojson",url:"//stephenott.cartodb.com/api/v2/sql",params:{format:"GeoJSON",q:"SELECT * FROM traffic_cameras LIMIT 25"},attributes:{location_desc:"Location",longitude:"Latitude",latitude:"Longitude",updated_at:"Last updated"},visible:!1,zoom:!0,datatable:!0,style:{type:"symbol",init:{graphicWidth:32,graphicHeight:37,externalGraphic:"demo/trafficcamera.png",graphicOpacity:1},select:{graphicWidth:32,graphicHeight:37,externalGraphic:"demo/trafficcamera_active.png",graphicOpacity:1}}}]}),wet_boew_geomap={basemap:{title:"Basic Map",type:"esri",url:"//geoappext.nrcan.gc.ca/arcgis/rest/services/BaseMaps/provinces1c/MapServer/export",options:{singleTile:!1,ratio:1,projection:"EPSG:3978",fractionalZoom:!0},mapOptions:{maxExtent:"-3000000.0, -800000.0, 4000000.0, 3900000.0",maxResolution:"auto",projection:"EPSG:3978",restrictedExtent:"-3000000.0, -800000.0, 4000000.0, 3900000.0",units:"m",displayProjection:"EPSG:4269",numZoomLevels:2}}},wet_boew_geomap={basemap:{title:"Basic Map",type:"esri",url:"//geoappext.nrcan.gc.ca/arcgis/rest/services/BaseMaps/provinces1c/MapServer/export",options:{singleTile:!1,ratio:1,projection:"EPSG:3978",fractionalZoom:!0},mapOptions:{maxExtent:"-3000000.0, -800000.0, 4000000.0, 3900000.0",maxResolution:"auto",projection:"EPSG:3978",restrictedExtent:"-3000000.0, -800000.0, 4000000.0, 3900000.0",units:"m",displayProjection:"EPSG:4269",numZoomLevels:2}}},wet_boew_geomap={overlays:[{title:"WMS",caption:"Ceci est un exemple de service WMS chargé par Geomap.",type:"wms",url:"//geo.weather.gc.ca/geomet?lang=en",visible:!1,version:"1.3.0",format:"image/png",layers:"GDPS.ETA_PR",transparent:!0,options:{opacity:.5,legendHTML:"<small>GeoMet Precipitation (mm)</small><ul class='list-unstyled'><li><span style='background-color:#800000;display:inline-block;height:20px;width:20px'/> <small>100.0</small></li><li><span style='background-color:#FF0000;display:inline-block;height:20px;width:20px'/> <small>50.0</small></li><li><span style='background-color:#FF4500;display:inline-block;height:20px;width:20px'/> <small>25.0</small></li><li><span style='background-color:#FFA500;display:inline-block;height:20px;width:20px'/> <small>20.0</small></li><li><span style='background-color:#FFD700;display:inline-block;height:20px;width:20px'/> <small>15.0</small></li><li><span style='background-color:#E5E500;display:inline-block;height:20px;width:20px'/> <small>10.0</small></li><li><span style='background-color:#7FFF00;display:inline-block;height:20px;width:20px'/> <small>7.5</small></li><li><span style='background-color:#7FFFD4;display:inline-block;height:20px;width:20px'/> <small>5.0</small></li><li><span style='background-color:#00FFFF;display:inline-block;height:20px;width:20px'/> <small>2.5</small></li><li><span style='background-color:#87CEFA;display:inline-block;height:20px;width:20px'/> <small>1.0</small></li><li><span style='background-color:#1E90FF;display:inline-block;height:20px;width:20px'/> <small>0.5</small></li><li><span style='background-color:#0000CD;display:inline-block;height:20px;width:20px'/> <small>0.25</small></li><li><span style='background-color:#000080;display:inline-block;height:20px;width:20px'/> <small>0.10</small></li></ul>"}},{title:"KML",caption:'Ces données sont extraites de la publication <a href="http://geogratis.gc.ca/api/en/nrcan-rncan/ess-sst/457ede2f-fd65-5936-ab60-3fe71da0e98b">Principales régions minérales du Canada</a>.',type:"kml",url:"demo/producing-mines.kml",visible:!1,datatable:!0,tooltips:!0,tooltipText:"Opération",popups:!0,attributes:{Type_:"Type",OwnersF:"Opérateur / propriétaire",OperationF:"Opération",ComGroupF:"Groupe de produits minéraux",CommodityF:"Produit minérale"},style:{type:"unique",field:"Type",init:{Coal:{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA+hJREFUSInN1n9MVWUYwPHvOfeC51zvubeQiwtDCS37sZxbP5YaKbVqaw1W/DKCQiaEBfYD19ZabrlaMp0tNREItJBoDabWrC2pK1qbxXRMmoMwDav1R8GFe4DzAvee0x/iTbpwuZRrPf++z/t8znPe57w7dv6jsP8vICH8i0GqxLIiP5AknVYUbcs/goQYvgHL9AJJIEV0sMgQhn6tomovzgoSYij5MnLU20bzkc+nNeKucfNSaQme+HkvCEOXFVV7PirIMIYWYlleYFHrsTaaDn8asRlPXBzbqqrZtKGEhPj4jcLQJUXVNkaEDENPkrC8QPKXbcf58NAlRJJlLNMMQ2LsdvJzMgkGgjR83ExBThYJ8fHlwvBLiuoqnxIaGfEvkMALpHx14gSNBz8BYMeW13BpGn19Pj472krbt+2hzeOBAFt27OSVslJKCwuo3t/Ak9mZzPd4yiawskmQZf0WMyokL7D42IlvONByOFTMpWnIkownfh4Fa7NJuj5x0vr42BgjQpAQm0B+Thbb99RQsaGY+R7Pc8LQ+xVV2xyCDMM9R5aCN17o7eWDloOTXs/vf/Qx3+MBQJZkfL7B0Fqs3U7FsyW4nE5efv1N0h96gBV3LGdbVQ3bN78KWMumPKOxsfGwczjyRSuFebnIkoxpmYyOjU5C3C4XW3dVMajrNLQcYvU9d6H79bA6M94MX7efIikxEb+uY1om2RnpBIImK+++E7fLReW7exkY/KvLtpPtxNjDy0Z1BV054rIkU5CbRX+/j7d2V9Hf74umxOzuuli7neW334ZvYBBVVViUeN3Vh2ImzsQ5dy5vvL2LZUuXsO6JHKzGj+g423V1IJvNRkXpelxOJ5W79zLg93P8u1OMBYKsy8ul7kATZ7p++HeQzWaj4pn1aJoWmq7LcfJ0B8FAgMK8texrbKKzuycyZJqmKdswFyYtkG9OSabr/E+hhFuXpKCqClt37kEfHg4r0H7me8YDAR59+MEQlJP+yKVFSQp9L3YAp9M5IoRepCpqfVlxkby7tj6EdXb30Nn9TsSuO852hc4pPzOD+1NTAesCyJsmQQCKor0vhC45VEddWXGRvKu2nu4rOosmnsp8jDWpqwDOW8hpquK8GAZNYPsvY+XFRdJssKezHmf1vSsBfrQgTVWdP1+5HjYMiqLtE4YuO1RHbbRYYXYm961aAXDuEqL98vecKadOUbU6Yfglh+qoKS8uknovhu0LxRwllpRFyQDnTMta43C4fp0qb9rxVlTXexNY9S1Lb5rhp4Ee07LSpkMiQhNYrRCDzZJks0XKEyI45Ha7RaScGT9YRXFHd5nNEH8C+eGD9m6tNTgAAAAASUVORK5CYII=",fillOpacity:"1",graphicWidth:"25",name:"Mines de charbon"},"Metal mines and mills":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA9FJREFUSInN1ltoHFUYwPH/mdndzKwzs6l7Iy1N25S+FCLaF69gg5cHq6S2FUSaS5OqD6YxASklgUpTCgqCIki1uTRF0QcDIlJpaWpES4UgrljBh7RRqsVmN5fNbpo52ezO+JBkcZtkk2gRv9fznfObc77vHMbDfxSe/wUkZWoriDdx3eIfJMQPmmZ2/CNIyltbcJ0BYCOIog4u1dJOr9N0s3VNkJRTmxeQcxPf8vFU/7JGSLE4Eq4n4gu2SDutaLr56qog254qx3UHgE3nJy5xJn2+6Gai6jpOJHpojzQQ8QabpZ0Wmm42F4VsO71R4A4Amy8kL9GbPgeAgoKDswjxCpUDwWqybo7TY59RH9xN1Bs6JO2U0HTr0JLQ9HRqg4ABoKI/eZme1Bxycv1hLI/JaGacLya/pt+O5SfPujnab77P65EGmiIv8F78E+ruribqCzXNY00FkOv+6Z2RYgDYejH5Hd2pL/OLWR4DBUHEF+RAeA+bkmUF4zNkmHYkURGmLljNG/FejoTrifpCr0g7Pa7p5tE8ZNuBEkXktg3L63SlzhYcTyIzTtQXmj9CwUR2Mj9WIry0hWqxVIPmG2+xz3iUR7RKTiR6eHfDYcC9Z8kaZZzZRXX4PPkVByPPoSBwcLDdmQKk1GPRMdJN0pmiK3WWJ/T7SDq3Fq2z4sswIH+ifKKMdG6KHA7Ph3bhjDo8bOyg1GNyPN7NmJPK51+wY3iFunYIKGhxBUFd+FnGZpN0xDtJ5FJFZq4RWogS4WWHsZ2J7CR+tYQtnrI7D3mFSluoFkO5i6M3T3Gvr4IXQ3sg0cdgZujOQB6h0h6qw1INjsd7GHfSXJQ/MjuWozG8FyfxKd9nrv07yCNU2oI1WKrBsfhcdy3EN/IK2dEsjeG95BJ9xDLDxSHHcRxFxSnX1iuV3nKuzF7PJ1R6N+FXdY6NdDPpLm7byzO/kE1k2R2oIpaYg2rNJ+cGhcjfFw+AYRjTUqYb/Ire0xLdr7wz8lEei2WGiY2cLLrrwcwQg4m5OjVaT/F46UOA+ysorxVAAJpmnpEyLfyKv7slul95e+RDfp79vShwexy0dvFY6YMAwy5Kla4Z+aMpqJGmmb0LWGu0RqwFe8l6mqrSBwCuuVCl60bBxEXNoGnmaWmnFb/i71wt9nLgGXYG7ge4OoeYf9yes2TXabrZLe2U8Cv+U63RGvGbvLEsoiklVGjlAFcd193p91tLJi/b3ppudc1jH2z3b1vhp4Ehx3WrlkOKQvNYp5STfUKoi1/Jv4WUualAICCL5ax4YTUtMLFSzmriL1z5gWQ67XYNAAAAAElFTkSuQmCC",fillOpacity:"1",graphicWidth:"25",name:"Mines de métaux"},"Industrial minerals":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA79JREFUSInN1ktMXFUYwPH/uVxmzoV5tBUGDe+SGmnwFWVBqQHUhYmJxo1xKZg2Pmh91IVJFWx140JXTapC62NtoombYmgHmoZQSUxUKDVWpuVheQ2tzAz3zMDc42KmIAwMUBvjtz3f/X7nnu87N9fkPwrzfwEpNV8F4iO0zr4hIX6S0nv8tiClYpVoJwiUgsjqoHlW2ZGd0vK+uS1IqWjFLWRy8Azhy19vaOS4A1Q0HEX6Ct9QdsSQlvf1LUG2HS1D6yBQPjnURXi4M+vLmHn3EOo5TmXje0hf4LCyI0Ja3sNZIduOlAp0EKiYvNRF+FJHakEYoJ1MRbgoqW1GJ5cYHzhNSW0L0hc4pOx5IS3foXWhhYX5YgFBYPfUcDfhoRSy5+lOci0f8UiYqcHviE50rTytE4ycfYeKhmNU7G/l2oUTFD/ajPQXtaax1lWQ1tdz40oEgaqp4bPMDn66XCvX8iGEgfQVUlb3EtOXK1et66QiuWhjmHdTXNtCqOdDKhuOIv1Fryk7Mictb9syZNt+tyGSe6LTI8wOnlx1OvH5GaS/KHVSwmApNrdycoakpL4N0+3lt+9b2XXv8/hKGgj1fkD1MycA/cC6PUomExltmPz1W8rrDyKEgdYOySW1jJTubyPX2kEo+D7OYpjZoZN4ip/CScxl1Nn0yxC73s3UUAVJNY/WSUpqX2DMWWJX1WMppOcYTmJ6OT86cQaEa/sQsGbEDcr3NROPzjFyrh0nPrmVEtv71glD4i9/hHjsBjkuidu3G3vmTkPCleqJ28NI97vkFTxEad0BRvsc1Gz/HYKESWl9O6bbm278LNE/f2DixyXK6g4y2qdR4Yv/EhImJfvayZVervakputWxCbPMX5xidK6A4z1Oai5geyQ4ziOkYOTf1eZ4drxIImbPy8nyJ0PY7osQr3t6MUbGQUWps8z1p8gUPMco+dTUMHeF9ObFIurII/Hs6BUpMV05Z2ubHjLCPV+soypuQGuBjfeKYCa7Wf0fKpPBTUvU1T9JKBDYLy9CgKQ0vuVUhFhuvJPpbCPSdz8JSuwNgpqXqGo+gmAEY3RZEnPaAaUxr5cwY6I7WCFNa8SqH4c4A8NTZblGfvnesYwSOn9QtkRw3Tld2wVK7y/lcB9jQBXUoh3fG3OulMnLe8pZc8L05X/eWXDERGbubYhkpPrxhOoArjiaN2Yl+ebWC9vw/GWlq8zjX3mL967yU8DvztaN22EZIXSWIdSf30jRE5OtjylklG/36+y5Wx6YaX0Z16e24i/AWg1hRJKTWppAAAAAElFTkSuQmCC",fillOpacity:"1",graphicWidth:"25",name:"Mines de non-métaux"},"Oil Sands":{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAA9VJREFUSInN1l9QVFUcwPHvucDuucveXZqCHXOkZakexFkcmHypRon+PFYPNT1UY0zAiEBKJmjiKDqF04zUWA+IWk0PvTiNNtWI5hDj+CCwFskb/8JyKvk37gL3uLD39sC2hguLmNN0Xn+/+/ucP79z5qbzH430/wWkVDgfxCFsO/WEhLgspdF0V5BS03nYVgewBkRKB5vnlRm5T+rG9hVBSk35/0b6v23natsXSxoZq7Ip2r8bd072NmVGNKkbb90RZJpTudh2B/BQ/3ftjHx8POVinA+u4vKeAxQd3IM7J6dWmREhdaM2JWSakTUCuwPwD5xpZ+RIHNE0sKwkRDgdFFRuJjY3R1/rCdZVlOH25dQoMyyk7qlZFJqZCa8W0AEEBs9+zy8fzSOPf3kU6fUwPTrOwFenGTt9NvGxfTNKV+0uij7YR1FdNT+2fMLa8s0YPl91HKteANn27xk3legA8ofOnWe45WiimPR6EELDnZNNYWUZQwH/gritbjJrKtKdTgrK3+By43sUHdiF2+fbqszIhNSNvQnINL1OTcQeGR8cYuhw64Ltmb4+itvnm98qoaHGJ25tnS4Jvr8H6THoLKsh99WXeKD0SULvHmTjsSOAHVz0jGLRaNI59J88xfqqcoTQsG2LmKluIc2N6FleeuqbiI1OMNzSSvYLzzE3NplUZ9mXYfyb8wz4/UTDN8CyKXjtFey5GKs3PYHMyiK0u4m5P64n8kdPtSOcjpVDwMIWFxCs2Iw5NkF3wz5mr/15JyVW9tYJXeJ7rBhzYpJ0l8T1aIAb9xoSTgfB5kachpuut/eStaGQYNWb9FoW4c5L9wjKSCfY3Ij0GITqm5i7PsbY1+foi85SWFXOT5ZF5EL3v4TiiNNj0LNzH7HRW+09eeYH+mIxglXl/GzZRC72pIYsy7K0NKwsf66WuaGQ6a7eRIKxYT0ZLp3Qzv3ExpPbdvLcBa5EowRefpHeOOTf8vp8UIjZBZDb7Z5RKlLm0F0niuu3aaFDHyawyMUeQilmChDuvERv/JzytleQ/+zTgD0M2o4FEICUxudKRYTDlXm8uH6bFmo+zHT3lZTA7SNQV0ngmVKAIRutRJfuq0lQHPssgTXUiZVggR1bCJSWAAzaUKLr7l//GU9qBimNT5UZ0RyuzLY7xfLf2UreUxsBBuYR47fbcxbtOqkbx5UZFg5X5tHihjoxOTSyJJIundz/cD7AgGXbm1wuz7VF85YqIHXPsTjW6lu3dpmfBvot2y5ZCkkJxbE2pW6cFCItLVWeUrEpr9erUuUse2Gl9CZfnrsYfwFqWYQCxROHDQAAAABJRU5ErkJggg==",fillOpacity:"1",graphicWidth:"25",name:"Mines de sables bitumineux"}},select:{externalGraphic:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAACKElEQVR42uWWMU/bQBTHWVBR1aUSG7Sl4oMBoiwd+gEYGBgqWIoCLDDCBEUIqUJqB6TOEe1QCQGOEyeRHBTHl/OLfTkvcNwz2NDkzrGTpVItPeXknPy7/3t/vXdjY//V43nNWR/gyHGcEwzSbJ6QRuMp5DtXvuMcVkaAeO8BoM59X4QyuCZC/hDdrreRG0IpneEereKHXkyxgWH9KUcw33ULmSHtdvutTJeVFRLHr5+lGLY5EEIIecMpLeeBXBZNcfbNSNZRGhlsaSGu604Frmv2Qki1KjiAMH6bWtiPY0NAoxGtLxIY3e6DnJ/vjAcAhkoJQpLiy7UKtLFuCN9xxFXRfFLW9QVjsPoXSFr3FTrrYO9amZrnLlNBLov9akPmC7S/ErS2WlKeOFHVo6ggIVdFdUpxvxKEf3zWgJ6H32olkFalot0XpoEwDVmchspa0iRpexDk6EBfMoBQCToRbm7SDzOKot7Cx5bWgRzZB3PXqKBxF9Tr+RSFKYrQjd8P9WqJol48UIBs237JO3BLbFv5odPDwbXrPeRDR4evfd0h6HTmpJtuaUreswZCeNeryAb9TtnvOpTOI4yMAIsgAGWcAqkdHChdkBvvhoFxhs3UM3EKZJpJjNLFvDAsPg+gJCHTuaasTOMHhFFpELT9yrIZ/a49rj99LEfr/V1DhI8QHDVD3RsY85YQ1ndPiNcIwHEQgDE0JH5qtdpraf/JtLAsa+KfubrdA6qupf9mD9mBAAAAAElFTkSuQmCC",graphicOpacity:"1"}}},{title:"TopoJSON (World 110m)",caption:"Voici un exemple de jeu de données chargé à partir d'une ressource TopoJSON distante.",type:"topojson",url:"demo/topojson.json",accessible:!1,visible:!1,style:{strokeColor:"#3399ff",strokeWidth:2}},{title:"Esri REST Tile Layer",caption:"Il s'agit d'un exemple de jeu de données chargé à partir d'un service de tuiles REST Esri distant.",type:"esritile",url:"//maps-cartes.services.geo.ca/server_serveur/rest/services/NRCan/Carte_climatique_HOT2000_Climate_Map_EN/MapServer/",params:{LAYERS:"show:0"},visible:!1,datatable:!1,options:{legendHTML:"<ul class='list-unstyled'><li><small>Weather Station</small><img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAFZJREFUOI3t0TEKwDAMQ1F9yIFzlN5YHUqgpOA2TZaANdnLQ9hFi1MSTPCKbbcZYAq0bWq3B2gI9pgkUWN086cAPG54xI95bdjQ+/674VdkGBxJgvM5AZAOH6jK5pnSAAAAAElFTkSuQmCC'></li></ul>"}},{title:"EsriJSON",caption:"Ceci est un exemple de jeu de données chargé à partir d'une ressource Esri JSON distante.",type:"esrijson",url:"https://geoappext.nrcan.gc.ca/arcgis/rest/services/FGP/TMX_EN/MapServer/2/query?where=OBJECTID>0&f=pjson",attributes:{Type:"Type"},visible:!1,zoom:!0,style:{strokeColor:"#FF0000",strokeWidth:2,strokeDash:[6,6]}},{title:"JSON (Earthquakes)",caption:"Il s'agit d'un exemple de jeu de données chargé à partir d'une ressource JSON distante, dans ce cas l'API USGS Earthquakes.",type:"json",url:"https://earthquake.usgs.gov/earthquakes/feed/v1.0/summary/all_day.geojson",visible:!1,popups:!0,datatable:!0,zoom:!0,root:"features",attributes:{title:{path:"properties",alias:"Titre"},mag:{path:"properties",alias:"Magnitude"},time:{path:"properties",alias:"Temps"}},style:{type:"rule",rule:[{field:"Magnitude",value:[2],filter:"LESS_THAN",name:"M < 2",init:{strokeColor:"#333333",fillColor:"#000066",pointRadius:2.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[2,2.9],filter:"BETWEEN",name:"M-2",init:{strokeColor:"#333333",fillColor:"#6600cc",pointRadius:4.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[3,3.9],filter:"BETWEEN",name:"M-3",init:{strokeColor:"#333333",fillColor:"#990099",pointRadius:6.5,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[4,4.9],filter:"BETWEEN",name:"M-4",init:{strokeColor:"#333333",fillColor:"#ff0000",pointRadius:8,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[5,5.9],filter:"BETWEEN",name:"M-5",init:{graphicName:"star",strokeColor:"#333333",fillColor:"#ff6600",pointRadius:14,fillOpacity:.8,strokeWidth:1}},{field:"Magnitude",value:[5.9],filter:"GREATER_THAN",name:"M-6+",init:{graphicName:"star",strokeColor:"#333333",fillColor:"#ff9933",pointRadius:18,fillOpacity:.8,strokeWidth:1}}]}},{title:"GeoJSON (CartoDB)",caption:"Ceci est un exemple de jeu de données chargé à partir d'une ressource GeoJSON distante, dans ce cas les caméras de circulation dans la ville d'Ottawa à partir de l'API CartoDB",type:"geojson",url:"//stephenott.cartodb.com/api/v2/sql",params:{format:"GeoJSON",q:"SELECT * FROM traffic_cameras LIMIT 25"},attributes:{location_desc:"Emplacement",longitude:"Latitude",latitude:"Longitude",updated_at:"Dernière mise à jour"},visible:!1,zoom:!0,datatable:!0,style:{type:"symbol",init:{graphicWidth:32,graphicHeight:37,externalGraphic:"demo/trafficcamera.png",graphicOpacity:1},select:{graphicWidth:32,graphicHeight:37,externalGraphic:"demo/trafficcamera_active.png",graphicOpacity:1}}}]},wet_boew_geomap={basemap:{title:"OSM Map",type:"osm",mapOptions:{center:[-52.7222765,47.5410882],zoomLevel:11}}},wet_boew_geomap={basemap:{title:"OSM Map",type:"osm",mapOptions:{center:[-52.7222765,47.5410882],zoomLevel:11}}},wet_boew_geomap={basemap:{title:"Tile (XYZ) Source Map",type:"osm",url:["//otile1.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile2.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile3.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile4.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png"],mapOptions:{projection:"EPSG:900913",center:[-123,49],zoomLevel:5}}},wet_boew_geomap={basemap:{title:"Tile (XYZ) Source Map",type:"osm",url:["//otile1.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile2.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile3.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png","//otile4.mqcdn.com/tiles/1.0.0/map/${z}/${x}/${y}.png"],mapOptions:{projection:"EPSG:900913",center:[-123,49],zoomLevel:5}}},wet_boew_geomap={basemap:{title:"WMS-Toporama",type:"wms",url:"//maps.geogratis.gc.ca/wms/toporama_en",version:"1.3.0",format:"image/png",layers:"limits",mapOptions:{maxExtent:"-2650000.0, -900000.0, 3600000.0, 4630000.0",restrictedExtent:"-2750000.0, -1000000.0, 3700000.0, 4730000.0",maxResolution:"auto",projection:"EPSG:3978",units:"m",displayProjection:"EPSG:4269",aspectRatio:.8}},overlays:[]},wet_boew_geomap={basemap:{title:"WMS-Toporama",type:"wms",url:"//maps.geogratis.gc.ca/wms/toporama_fr",version:"1.3.0",format:"image/png",layers:"WMS-Toporama",mapOptions:{maxExtent:"-2650000.0, -900000.0, 3600000.0, 4630000.0",restrictedExtent:"-2750000.0, -1000000.0, 3700000.0, 4730000.0",maxResolution:"auto",projection:"EPSG:3978",units:"m",displayProjection:"EPSG:4269",aspectRatio:.8}},overlays:[]};