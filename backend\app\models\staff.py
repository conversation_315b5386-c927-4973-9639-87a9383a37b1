"""
Model of Staff

This script describes the DB model for the staff table
"""
from sqlalchemy import String, ForeignKey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.dialects.postgresql import UUID
from typing import Dict, Any
import uuid

from .base import Base
from ..common.constants import UserRole, ERROR_MESSAGES

class Staff(Base):
    __tablename__ = "staff"

    staff_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("users.user_id"))
    lab_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("labs.lab_id"), nullable=True)
    role: Mapped[str] = mapped_column(String, default=UserRole.SCIENTIST.value)

    # Define the unique constraint on user_id, lab_id, and role
    __table_args__ = (
        UniqueConstraint('user_id', 'lab_id', 'role', name='staff_user_id_lab_id_role_key'),
    )

    #Helper functions
    def to_dict(self) -> Dict[str, Any]:
        return {
            "staff_id": str(self.staff_id),
            "user_id": str(self.user_id),
            "lab_id": str(self.lab_id) if self.lab_id else None,
            "role": self.role
        }
    
    # Class variable for valid roles
    VALID_ROLES = [role.value for role in UserRole]

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        if 'role' in kwargs:
            role = kwargs['role']
            lab_id = kwargs.get('lab_id')

            if isinstance(role, str):
                if role not in self.VALID_ROLES:
                    raise ValueError(f"Invalid role. Must be one of {self.VALID_ROLES}")
            elif isinstance(role, UserRole):
                kwargs['role'] = role.value
            else:
                raise ValueError(ERROR_MESSAGES["staff"]["invalid_role"])
            
            # Scientists don't belong to a lab
            if (role == UserRole.SCIENTIST.value or (isinstance(role, UserRole) and role == UserRole.SCIENTIST)):
                kwargs['lab_id'] = None
            # For non-scientists, ensure lab_id is provided
            elif lab_id is None:
                raise ValueError(ERROR_MESSAGES["staff"]["non_scientist_lab"])

        super().__init__(*args, **kwargs)