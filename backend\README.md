# Sed Lab LIMS Backend

This repository contains the Python backend for the Laboratory Information Management System (LIMS) service used in the Sediment Lab (Sed Lab). It is a requisition management, sample testing, and results tracking system.

---

## Table of Contents

- [Sed Lab LIMS Backend](#sed-lab-lims-backend)
  - [Table of Contents](#table-of-contents)
  - [Important Definitions](#important-definitions)
  - [Installation](#installation)
  - [Running the Application](#running-the-application)
  - [API Documentation](#api-documentation)
  - [Running Tests](#running-tests)
  - [Development Workflow](#development-workflow)
  - [Project Structure](#project-structure)
  - [Contributing](#contributing)

---

## Important Definitions

- **User**: Any person who interacts with the system. Users are categorized into three roles:  
  - `Scientist`: A user who submits requisitions to the Sed Lab.  
  - `Lab Personnel`: A worker in the Sed Lab that handle the physical sample and tests  
  - `Lab Admin`: An administrator overseeing operations of the system and/or Sed Lab itself  

- **Requisition**: A request submitted by a scientist for testing sediment samples. Requisitions can have the following statuses:  
  - `submitted`: When a scientist submits a requisition to the lab.  
  - `in progress`: When lab personnel start working on the requisition (e.g., running tests on samples).  
  - `complete`: When all tests are finished, and the data is sent back to the scientist.  

- **Sample**: Physical sediment submitted for testing.  

- **Tests**: The specific analyses or experiments performed on a sample.  

---

## Installation

Follow these steps to set up the backend locally:

1. Set up a Python virtual environment:  
   ```bash
   python -m venv env
   ```

2. Activate the virtual environment:  
   - On Windows:  
     ```bash
     .\env\Scripts\activate
     ```
   - On macOS/Linux:  
     ```bash
     source env/bin/activate
     ```

3. Install the required dependencies:  
   ```bash
   pip install -r requirements.txt
   ```

4. Set up a `.env` file by following the structure of the provided `.env.example`.

---

## Running the Application

To start the development server, run:  
```bash
uvicorn app.main:app --reload
```

The application will be available at `http://localhost:8000`.

---

## API Documentation

Once the application is running, you can access the interactive API documentation:

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

For a detailed list of all API endpoints, refer to the [API README](app/api/README.md).

---

## Running Tests

To execute the test suite, use:  
```bash
python -m pytest -vv tests
```

---

## Project Structure

The project is organized as follows:

- **app/**: Main application package
  - **api/**: API endpoint definitions and route handlers
  - **common/**: Application-wide constants, enumerations, and exceptions
  - **core/**: Core components and infrastructure
    - **logging/**: Comprehensive logging system
    - **security.py**: Authentication and authorization
    - **dependencies.py**: FastAPI dependency injection functions
  - **crud/**: Database CRUD (Create, Read, Update, Delete) operations
  - **middleware/**: Request/response processing middleware
  - **models/**: SQLAlchemy ORM models for database tables
  - **schemas/**: Pydantic models for request/response validation
  - **services/**: Business logic and external service integrations
  - **utils/**: Utility functions and helpers
  - **config.py**: Application configuration
  - **database.py**: Database connection and session management
  - **main.py**: Application entry point and setup

- **logs/**: Application log files
- **scripts/**: Utility scripts for development and deployment
- **tests/**: Test suite
  - **conftest.py**: Test fixtures and configuration
  - **api/**: API endpoint tests
  - **crud/**: Database operation tests
  - **services/**: Service tests

---
