/*!
 * jQuery Validation Plugin v1.21.0
 *
 * https://jqueryvalidation.org/
 *
 * Copyright (c) 2024 <PERSON><PERSON><PERSON>
 * Released under the MIT license
 */
!function(t){"function"==typeof define&&define.amd?define(["jquery","./jquery.validate"],t):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(jQuery)}(function(o){function r(t){return t.replace(/<.[^<>]*?>/g," ").replace(/&nbsp;|&#160;/gi," ").replace(/[.(),;:!?%#$'\"_+=\/\-“”’]*/g,"")}return o.validator.addMethod("maxWords",function(t,e,a){return this.optional(e)||r(t).match(/\b\w+\b/g).length<=a},o.validator.format("Please enter {0} words or less.")),o.validator.addMethod("minWords",function(t,e,a){return this.optional(e)||r(t).match(/\b\w+\b/g).length>=a},o.validator.format("Please enter at least {0} words.")),o.validator.addMethod("rangeWords",function(t,e,a){var t=r(t),d=/\b\w+\b/g;return this.optional(e)||t.match(d).length>=a[0]&&t.match(d).length<=a[1]},o.validator.format("Please enter between {0} and {1} words.")),o.validator.addMethod("abaRoutingNumber",function(t){var e=0,a=t.split(""),d=a.length;if(9!==d)return!1;for(var r=0;r<d;r+=3)e+=3*parseInt(a[r],10)+7*parseInt(a[r+1],10)+parseInt(a[r+2],10);return 0!==e&&e%10==0},"Please enter a valid routing number."),o.validator.addMethod("accept",function(t,e,a){var d,r,a="string"==typeof a?a.replace(/\s/g,""):"image/*",i=this.optional(e);if(i)return i;if("file"===o(e).attr("type")&&(a=a.replace(/[\-\[\]\/\{\}\(\)\+\?\.\\\^\$\|]/g,"\\$&").replace(/,/g,"|").replace(/\/\*/g,"/.*"),e.files)&&e.files.length)for(r=new RegExp(".?("+a+")$","i"),d=0;d<e.files.length;d++)if(!e.files[d].type.match(r))return!1;return!0},o.validator.format("Please enter a value with a valid mimetype.")),o.validator.addMethod("alphanumeric",function(t,e){return this.optional(e)||/^\w+$/i.test(t)},"Letters, numbers, and underscores only please."),o.validator.addMethod("bankaccountNL",function(t,e){if(this.optional(e))return!0;if(!/^[0-9]{9}|([0-9]{2} ){3}[0-9]{3}$/.test(t))return!1;for(var a=t.replace(/ /g,""),d=0,r=a.length,i=0;i<r;i++)d+=(r-i)*a.substring(i,i+1);return d%11==0},"Please specify a valid bank account number."),o.validator.addMethod("bankorgiroaccountNL",function(t,e){return this.optional(e)||o.validator.methods.bankaccountNL.call(this,t,e)||o.validator.methods.giroaccountNL.call(this,t,e)},"Please specify a valid bank or giro account number."),o.validator.addMethod("bic",function(t,e){return this.optional(e)||/^([A-Z]{6}[A-Z2-9][A-NP-Z1-9])(X{3}|[A-WY-Z0-9][A-Z0-9]{2})?$/.test(t.toUpperCase())},"Please specify a valid BIC code."),o.validator.addMethod("cifES",function(t,e){"use strict";if(this.optional(e))return!0;var a,d,e=new RegExp(/^([ABCDEFGHJKLMNPQRSUVW])(\d{7})([0-9A-J])$/gi),r=t.substring(0,1),i=t.substring(1,8),n=t.substring(8,9),o=0,s=0;if(9!==t.length||!e.test(t))return!1;for(a=0;a<i.length;a++)d=parseInt(i[a],10),a%2==0?s+=(d*=2)<10?d:d-9:o+=d;return e=(10-(o+s).toString().substr(-1)).toString(),e=9<parseInt(e,10)?"0":e,t="JABCDEFGHI".substr(e,1).toString(),r.match(/[ABEH]/)?n===e:!r.match(/[KPQS]/)&&n===e||n===t},"Please specify a valid CIF number."),o.validator.addMethod("cnhBR",function(t){if(11!==(t=t.replace(/([~!@#$%^&*()_+=`{}\[\]\-|\\:;'<>,.\/? ])+/g,"")).length)return!1;var e,a,d,r=0,i=0,n=t.charAt(0);if(new Array(12).join(n)===t)return!1;for(d=9,a=0;a<9;++a,--d)r+=t.charAt(a)*d;for(10<=(n=r%11)&&(n=0,i=2),a=r=0,d=1;a<9;++a,++d)r+=t.charAt(a)*d;return 10<=(e=r%11)?e=0:e-=i,String(n).concat(e)===t.substr(-2)},"Please specify a valid CNH number."),o.validator.addMethod("cnpjBR",function(t,e){"use strict";if(this.optional(e))return!0;if(14!==(t=t.replace(/[^\d]+/g,"")).length)return!1;if("00000000000000"===t||"11111111111111"===t||"22222222222222"===t||"33333333333333"===t||"44444444444444"===t||"55555555555555"===t||"66666666666666"===t||"77777777777777"===t||"88888888888888"===t||"99999999999999"===t)return!1;for(var a=t.length-2,d=t.substring(0,a),e=t.substring(a),r=0,i=a-7,n=a;1<=n;n--)r+=d.charAt(a-n)*i--,i<2&&(i=9);if((r%11<2?0:11-r%11)!==parseInt(e.charAt(0),10))return!1;for(var d=t.substring(0,a+=1),r=0,i=a-7,o=a;1<=o;o--)r+=d.charAt(a-o)*i--,i<2&&(i=9);return(r%11<2?0:11-r%11)===parseInt(e.charAt(1),10)},"Please specify a CNPJ value number."),o.validator.addMethod("cpfBR",function(t,e){"use strict";if(this.optional(e))return!0;if(11===(t=t.replace(/([~!@#$%^&*()_+=`{}\[\]\-|\\:;'<>,.\/? ])+/g,"")).length){var a,d=0,e=parseInt(t.substring(9,10),10),r=parseInt(t.substring(10,11),10),i=function(t,e){t=10*t%11;return(t=10!==t&&11!==t?t:0)===e};if(""!==t&&"00000000000"!==t&&"11111111111"!==t&&"22222222222"!==t&&"33333333333"!==t&&"44444444444"!==t&&"55555555555"!==t&&"66666666666"!==t&&"77777777777"!==t&&"88888888888"!==t&&"99999999999"!==t){for(a=1;a<=9;a++)d+=parseInt(t.substring(a-1,a),10)*(11-a);if(i(d,e)){for(d=0,a=1;a<=10;a++)d+=parseInt(t.substring(a-1,a),10)*(12-a);return i(d,r)}}}return!1},"Please specify a valid CPF number."),o.validator.addMethod("creditcard",function(t,e){if(this.optional(e))return"dependency-mismatch";if(/[^0-9 \-]+/.test(t))return!1;var a,d,r=0,i=0,n=!1;if((t=t.replace(/\D/g,"")).length<13||19<t.length)return!1;for(a=t.length-1;0<=a;a--)d=t.charAt(a),i=parseInt(d,10),n&&9<(i*=2)&&(i-=9),r+=i,n=!n;return r%10==0},"Please enter a valid credit card number."),o.validator.addMethod("creditcardtypes",function(t,e,a){if(/[^0-9\-]+/.test(t))return!1;t=t.replace(/\D/g,"");var d=0;return a.mastercard&&(d|=1),a.visa&&(d|=2),a.amex&&(d|=4),a.dinersclub&&(d|=8),a.enroute&&(d|=16),a.discover&&(d|=32),a.jcb&&(d|=64),a.unknown&&(d|=128),1&(d=a.all?255:d)&&(/^(5[12345])/.test(t)||/^(2[234567])/.test(t))||2&d&&/^(4)/.test(t)?16===t.length:4&d&&/^(3[47])/.test(t)?15===t.length:8&d&&/^(3(0[012345]|[68]))/.test(t)?14===t.length:16&d&&/^(2(014|149))/.test(t)?15===t.length:32&d&&/^(6011)/.test(t)||64&d&&/^(3)/.test(t)?16===t.length:64&d&&/^(2131|1800)/.test(t)?15===t.length:!!(128&d)},"Please enter a valid credit card number."),o.validator.addMethod("currency",function(t,e,a){var d="string"==typeof a,r=d?a:a[0],d=d||a[1],r=r.replace(/,/g,""),a="^["+(r=d?r+"]":r+"]?")+"([1-9]{1}[0-9]{0,2}(\\,[0-9]{3})*(\\.[0-9]{0,2})?|[1-9]{1}[0-9]{0,}(\\.[0-9]{0,2})?|0(\\.[0-9]{0,2})?|(\\.[0-9]{1,2})?)$";return a=new RegExp(a),this.optional(e)||a.test(t)},"Please specify a valid currency."),o.validator.addMethod("dateFA",function(t,e){return this.optional(e)||/^[1-4]\d{3}\/((0?[1-6]\/((3[0-1])|([1-2][0-9])|(0?[1-9])))|((1[0-2]|(0?[7-9]))\/(30|([1-2][0-9])|(0?[1-9]))))$/.test(t)},o.validator.messages.date),o.validator.addMethod("dateITA",function(t,e){var a,d,r,i=!1,i=!!/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(t)&&(t=t.split("/"),a=parseInt(t[0],10),d=parseInt(t[1],10),t=parseInt(t[2],10),(r=new Date(Date.UTC(t,d-1,a,12,0,0,0))).getUTCFullYear()===t)&&r.getUTCMonth()===d-1&&r.getUTCDate()===a;return this.optional(e)||i},o.validator.messages.date),o.validator.addMethod("dateNL",function(t,e){return this.optional(e)||/^(0?[1-9]|[12]\d|3[01])[\.\/\-](0?[1-9]|1[012])[\.\/\-]([12]\d)?(\d\d)$/.test(t)},o.validator.messages.date),o.validator.addMethod("extension",function(t,e,a){return a="string"==typeof a?a.replace(/,/g,"|"):"png|jpe?g|gif",this.optional(e)||t.match(new RegExp("\\.("+a+")$","i"))},o.validator.format("Please enter a value with a valid extension.")),o.validator.addMethod("giroaccountNL",function(t,e){return this.optional(e)||/^[0-9]{1,7}$/.test(t)},"Please specify a valid giro account number."),o.validator.addMethod("greaterThan",function(t,e,a){a=o(a);return this.settings.onfocusout&&a.not(".validate-greaterThan-blur").length&&a.addClass("validate-greaterThan-blur").on("blur.validate-greaterThan",function(){o(e).valid()}),t>a.val()},"Please enter a greater value."),o.validator.addMethod("greaterThanEqual",function(t,e,a){a=o(a);return this.settings.onfocusout&&a.not(".validate-greaterThanEqual-blur").length&&a.addClass("validate-greaterThanEqual-blur").on("blur.validate-greaterThanEqual",function(){o(e).valid()}),t>=a.val()},"Please enter a greater value."),o.validator.addMethod("iban",function(t,e){if(this.optional(e))return!0;var a,d,r,i,e=t.replace(/ /g,"").toUpperCase(),n="",o=!0,s="";if(e.length<5)return!1;if(void 0!==(t={AL:"\\d{8}[\\dA-Z]{16}",AD:"\\d{8}[\\dA-Z]{12}",AT:"\\d{16}",AZ:"[\\dA-Z]{4}\\d{20}",BE:"\\d{12}",BH:"[A-Z]{4}[\\dA-Z]{14}",BA:"\\d{16}",BR:"\\d{23}[A-Z][\\dA-Z]",BG:"[A-Z]{4}\\d{6}[\\dA-Z]{8}",CR:"\\d{17}",HR:"\\d{17}",CY:"\\d{8}[\\dA-Z]{16}",CZ:"\\d{20}",DK:"\\d{14}",DO:"[A-Z]{4}\\d{20}",EE:"\\d{16}",FO:"\\d{14}",FI:"\\d{14}",FR:"\\d{10}[\\dA-Z]{11}\\d{2}",GE:"[\\dA-Z]{2}\\d{16}",DE:"\\d{18}",GI:"[A-Z]{4}[\\dA-Z]{15}",GR:"\\d{7}[\\dA-Z]{16}",GL:"\\d{14}",GT:"[\\dA-Z]{4}[\\dA-Z]{20}",HU:"\\d{24}",IS:"\\d{22}",IE:"[\\dA-Z]{4}\\d{14}",IL:"\\d{19}",IT:"[A-Z]\\d{10}[\\dA-Z]{12}",KZ:"\\d{3}[\\dA-Z]{13}",KW:"[A-Z]{4}[\\dA-Z]{22}",LV:"[A-Z]{4}[\\dA-Z]{13}",LB:"\\d{4}[\\dA-Z]{20}",LI:"\\d{5}[\\dA-Z]{12}",LT:"\\d{16}",LU:"\\d{3}[\\dA-Z]{13}",MK:"\\d{3}[\\dA-Z]{10}\\d{2}",MT:"[A-Z]{4}\\d{5}[\\dA-Z]{18}",MR:"\\d{23}",MU:"[A-Z]{4}\\d{19}[A-Z]{3}",MC:"\\d{10}[\\dA-Z]{11}\\d{2}",MD:"[\\dA-Z]{2}\\d{18}",ME:"\\d{18}",NL:"[A-Z]{4}\\d{10}",NO:"\\d{11}",PK:"[\\dA-Z]{4}\\d{16}",PS:"[\\dA-Z]{4}\\d{21}",PL:"\\d{24}",PT:"\\d{21}",RO:"[A-Z]{4}[\\dA-Z]{16}",SM:"[A-Z]\\d{10}[\\dA-Z]{12}",SA:"\\d{2}[\\dA-Z]{18}",RS:"\\d{18}",SK:"\\d{20}",SI:"\\d{15}",ES:"\\d{20}",SE:"\\d{20}",CH:"\\d{5}[\\dA-Z]{12}",TN:"\\d{20}",TR:"\\d{5}[\\dA-Z]{17}",AE:"\\d{3}\\d{16}",GB:"[A-Z]{4}\\d{14}",VG:"[\\dA-Z]{4}\\d{16}"}[e.substring(0,2)])&&!new RegExp("^[A-Z]{2}\\d{2}"+t+"$","").test(e))return!1;for(a=e.substring(4,e.length)+e.substring(0,4),r=0;r<a.length;r++)(o="0"!==(d=a.charAt(r))?!1:o)||(n+="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ".indexOf(d));for(i=0;i<n.length;i++)s=(""+s+n.charAt(i))%97;return 1===s},"Please specify a valid IBAN."),o.validator.addMethod("integer",function(t,e){return this.optional(e)||/^-?\d+$/.test(t)},"A positive or negative non-decimal number please."),o.validator.addMethod("ipv4",function(t,e){return this.optional(e)||/^(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)$/i.test(t)},"Please enter a valid IP v4 address."),o.validator.addMethod("ipv6",function(t,e){return this.optional(e)||/^((([0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$/i.test(t)},"Please enter a valid IP v6 address."),o.validator.addMethod("lessThan",function(t,e,a){a=o(a);return this.settings.onfocusout&&a.not(".validate-lessThan-blur").length&&a.addClass("validate-lessThan-blur").on("blur.validate-lessThan",function(){o(e).valid()}),t<a.val()},"Please enter a lesser value."),o.validator.addMethod("lessThanEqual",function(t,e,a){a=o(a);return this.settings.onfocusout&&a.not(".validate-lessThanEqual-blur").length&&a.addClass("validate-lessThanEqual-blur").on("blur.validate-lessThanEqual",function(){o(e).valid()}),t<=a.val()},"Please enter a lesser value."),o.validator.addMethod("lettersonly",function(t,e){return this.optional(e)||/^[a-z]+$/i.test(t)},"Letters only please."),o.validator.addMethod("letterswithbasicpunc",function(t,e){return this.optional(e)||/^[a-z\-.,()'"\s]+$/i.test(t)},"Letters or punctuation only please."),o.validator.addMethod("maxfiles",function(t,e,a){return!!this.optional(e)||!("file"===o(e).attr("type")&&e.files&&e.files.length>a)},o.validator.format("Please select no more than {0} files.")),o.validator.addMethod("maxsize",function(t,e,a){if(!this.optional(e)&&"file"===o(e).attr("type")&&e.files&&e.files.length)for(var d=0;d<e.files.length;d++)if(e.files[d].size>a)return!1;return!0},o.validator.format("File size must not exceed {0} bytes each.")),o.validator.addMethod("maxsizetotal",function(t,e,a){if(!this.optional(e)&&"file"===o(e).attr("type")&&e.files&&e.files.length)for(var d=0,r=0;r<e.files.length;r++)if(a<(d+=e.files[r].size))return!1;return!0},o.validator.format("Total size of all files must not exceed {0} bytes.")),o.validator.addMethod("mobileNL",function(t,e){return this.optional(e)||/^((\+|00(\s|\s?\-\s?)?)31(\s|\s?\-\s?)?(\(0\)[\-\s]?)?|0)6((\s|\s?\-\s?)?[0-9]){8}$/.test(t)},"Please specify a valid mobile number."),o.validator.addMethod("mobileRU",function(t,e){t=t.replace(/\(|\)|\s+|-/g,"");return this.optional(e)||9<t.length&&/^((\+7|7|8)+([0-9]){10})$/.test(t)},"Please specify a valid mobile number."),o.validator.addMethod("mobileUK",function(t,e){return t=t.replace(/\(|\)|\s+|-/g,""),this.optional(e)||9<t.length&&t.match(/^(?:(?:(?:00\s?|\+)44\s?|0)7(?:[1345789]\d{2}|624)\s?\d{3}\s?\d{3})$/)},"Please specify a valid mobile number."),o.validator.addMethod("netmask",function(t,e){return this.optional(e)||/^(254|252|248|240|224|192|128)\.0\.0\.0|255\.(254|252|248|240|224|192|128|0)\.0\.0|255\.255\.(254|252|248|240|224|192|128|0)\.0|255\.255\.255\.(254|252|248|240|224|192|128|0)/i.test(t)},"Please enter a valid netmask."),o.validator.addMethod("nieES",function(t,e){"use strict";var a;return!(!this.optional(e)&&(e=new RegExp(/^[MXYZ]{1}[0-9]{7,8}[TRWAGMYFPDXBNJZSQVHLCKET]{1}$/gi),a=t.substr(t.length-1).toUpperCase(),10<(t=t.toString().toUpperCase()).length||t.length<9||!e.test(t)||(e=9===(t=t.replace(/^[X]/,"0").replace(/^[Y]/,"1").replace(/^[Z]/,"2")).length?t.substr(0,8):t.substr(0,9),"TRWAGMYFPDXBNJZSQVHLCKET".charAt(parseInt(e,10)%23)!==a)))},"Please specify a valid NIE number."),o.validator.addMethod("nifES",function(t,e){"use strict";return!!this.optional(e)||!!(t=t.toUpperCase()).match("((^[A-Z]{1}[0-9]{7}[A-Z0-9]{1}$|^[T]{1}[A-Z0-9]{8}$)|^[0-9]{8}[A-Z]{1}$)")&&(/^[0-9]{8}[A-Z]{1}$/.test(t)?"TRWAGMYFPDXBNJZSQVHLCKE".charAt(t.substring(8,0)%23)===t.charAt(8):!!/^[KLM]{1}/.test(t)&&t[8]==="TRWAGMYFPDXBNJZSQVHLCKE".charAt(t.substring(8,1)%23))},"Please specify a valid NIF number."),o.validator.addMethod("nipPL",function(t){"use strict";if(10!==(t=t.replace(/[^0-9]/g,"")).length)return!1;for(var e=[6,5,7,2,3,4,5,6,7],a=0,d=0;d<9;d++)a+=e[d]*t[d];var r=a%11;return(10==r?0:r)===parseInt(t[9],10)},"Please specify a valid NIP number."),o.validator.addMethod("nisBR",function(t){var e,a,d,r,i=0;if(11!==(t=t.replace(/([~!@#$%^&*()_+=`{}\[\]\-|\\:;'<>,.\/? ])+/g,"")).length)return!1;for(a=parseInt(t.substring(10,11),10),e=parseInt(t.substring(0,10),10),d=2;d<12;d++)10===(r=d)&&(r=2),i+=e%10*(r=11===d?3:r),e=parseInt(e/10,10);return a===(t=1<(t=i%11)?11-t:0)},"Please specify a valid NIS/PIS number."),o.validator.addMethod("notEqualTo",function(t,e,a){return this.optional(e)||!o.validator.methods.equalTo.call(this,t,e,a)},"Please enter a different value, values must not be the same."),o.validator.addMethod("nowhitespace",function(t,e){return this.optional(e)||/^\S+$/i.test(t)},"No white space please."),o.validator.addMethod("pattern",function(t,e,a){return!!this.optional(e)||(a="string"==typeof a?new RegExp("^(?:"+a+")$"):a).test(t)},"Invalid format."),o.validator.addMethod("phoneNL",function(t,e){return this.optional(e)||/^((\+|00(\s|\s?\-\s?)?)31(\s|\s?\-\s?)?(\(0\)[\-\s]?)?|0)[1-9]((\s|\s?\-\s?)?[0-9]){8}$/.test(t)},"Please specify a valid phone number."),o.validator.addMethod("phonePL",function(t,e){t=t.replace(/\s+/g,"");return this.optional(e)||/^(?:(?:(?:\+|00)?48)|(?:\(\+?48\)))?(?:1[2-8]|2[2-69]|3[2-49]|4[1-68]|5[0-9]|6[0-35-9]|[7-8][1-9]|9[145])\d{7}$/.test(t)},"Please specify a valid phone number."),o.validator.addMethod("phonesUK",function(t,e){return t=t.replace(/\(|\)|\s+|-/g,""),this.optional(e)||9<t.length&&t.match(/^(?:(?:(?:00\s?|\+)44\s?|0)(?:1\d{8,9}|[23]\d{9}|7(?:[1345789]\d{8}|624\d{6})))$/)},"Please specify a valid uk phone number."),o.validator.addMethod("phoneUK",function(t,e){return t=t.replace(/\(|\)|\s+|-/g,""),this.optional(e)||9<t.length&&t.match(/^(?:(?:(?:00\s?|\+)44\s?)|(?:\(?0))(?:\d{2}\)?\s?\d{4}\s?\d{4}|\d{3}\)?\s?\d{3}\s?\d{3,4}|\d{4}\)?\s?(?:\d{5}|\d{3}\s?\d{3})|\d{5}\)?\s?\d{4,5})$/)},"Please specify a valid phone number."),o.validator.addMethod("phoneUS",function(t,e){return t=t.replace(/\s+/g,""),this.optional(e)||9<t.length&&t.match(/^(\+?1-?)?(\([2-9]([02-9]\d|1[02-9])\)|[2-9]([02-9]\d|1[02-9]))-?[2-9]\d{2}-?\d{4}$/)},"Please specify a valid phone number."),o.validator.addMethod("postalcodeBR",function(t,e){return this.optional(e)||/^\d{2}.\d{3}-\d{3}?$|^\d{5}-?\d{3}?$/.test(t)},"Informe um CEP válido."),o.validator.addMethod("postalCodeCA",function(t,e){return this.optional(e)||/^[ABCEGHJKLMNPRSTVXY]\d[ABCEGHJKLMNPRSTVWXYZ] *\d[ABCEGHJKLMNPRSTVWXYZ]\d$/i.test(t)},"Please specify a valid postal code."),o.validator.addMethod("postalcodeIT",function(t,e){return this.optional(e)||/^\d{5}$/.test(t)},"Please specify a valid postal code."),o.validator.addMethod("postalcodeNL",function(t,e){return this.optional(e)||/^[1-9][0-9]{3}\s?[a-zA-Z]{2}$/.test(t)},"Please specify a valid postal code."),o.validator.addMethod("postcodeUK",function(t,e){return this.optional(e)||/^((([A-PR-UWYZ][0-9])|([A-PR-UWYZ][0-9][0-9])|([A-PR-UWYZ][A-HK-Y][0-9])|([A-PR-UWYZ][A-HK-Y][0-9][0-9])|([A-PR-UWYZ][0-9][A-HJKSTUW])|([A-PR-UWYZ][A-HK-Y][0-9][ABEHMNPRVWXY]))\s?([0-9][ABD-HJLNP-UW-Z]{2})|(GIR)\s?(0AA))$/i.test(t)},"Please specify a valid UK postcode."),o.validator.addMethod("require_from_group",function(t,e,a){var d=o(a[1],e.form),r=d.eq(0),i=r.data("valid_req_grp")?r.data("valid_req_grp"):o.extend({},this),a=d.filter(function(){return i.elementValue(this)}).length>=a[0];return r.data("valid_req_grp",i),o(e).data("being_validated")||(d.data("being_validated",!0),d.each(function(){i.element(this)}),d.data("being_validated",!1)),a},o.validator.format("Please fill at least {0} of these fields.")),o.validator.addMethod("skip_or_fill_minimum",function(t,e,a){var d=o(a[1],e.form),r=d.eq(0),i=r.data("valid_skip")?r.data("valid_skip"):o.extend({},this),n=d.filter(function(){return i.elementValue(this)}).length,n=0===n||n>=a[0];return r.data("valid_skip",i),o(e).data("being_validated")||(d.data("being_validated",!0),d.each(function(){i.element(this)}),d.data("being_validated",!1)),n},o.validator.format("Please either skip these fields or fill at least {0} of them.")),o.validator.addMethod("stateUS",function(t,e,a){var d=void 0===a,r=!d&&void 0!==a.caseSensitive&&a.caseSensitive,i=!d&&void 0!==a.includeTerritories&&a.includeTerritories,d=!d&&void 0!==a.includeMilitary&&a.includeMilitary,a=i||d?i&&d?"^(A[AEKLPRSZ]|C[AOT]|D[CE]|FL|G[AU]|HI|I[ADLN]|K[SY]|LA|M[ADEINOPST]|N[CDEHJMVY]|O[HKR]|P[AR]|RI|S[CD]|T[NX]|UT|V[AIT]|W[AIVY])$":i?"^(A[KLRSZ]|C[AOT]|D[CE]|FL|G[AU]|HI|I[ADLN]|K[SY]|LA|M[ADEINOPST]|N[CDEHJMVY]|O[HKR]|P[AR]|RI|S[CD]|T[NX]|UT|V[AIT]|W[AIVY])$":"^(A[AEKLPRZ]|C[AOT]|D[CE]|FL|GA|HI|I[ADLN]|K[SY]|LA|M[ADEINOST]|N[CDEHJMVY]|O[HKR]|PA|RI|S[CD]|T[NX]|UT|V[AT]|W[AIVY])$":"^(A[KLRZ]|C[AOT]|D[CE]|FL|GA|HI|I[ADLN]|K[SY]|LA|M[ADEINOST]|N[CDEHJMVY]|O[HKR]|PA|RI|S[CD]|T[NX]|UT|V[AT]|W[AIVY])$";return a=r?new RegExp(a):new RegExp(a,"i"),this.optional(e)||a.test(t)},"Please specify a valid state."),o.validator.addMethod("strippedminlength",function(t,e,a){return o(t).text().length>=a},o.validator.format("Please enter at least {0} characters.")),o.validator.addMethod("time",function(t,e){return this.optional(e)||/^([01]\d|2[0-3]|[0-9])(:[0-5]\d){1,2}$/.test(t)},"Please enter a valid time, between 00:00 and 23:59."),o.validator.addMethod("time12h",function(t,e){return this.optional(e)||/^((0?[1-9]|1[012])(:[0-5]\d){1,2}(\ ?[AP]M))$/i.test(t)},"Please enter a valid time in 12-hour am/pm format."),o.validator.addMethod("url2",function(t,e){return this.optional(e)||/^(?:(?:(?:https?|ftp):)?\/\/)(?:(?:[^\]\[?\/<~#`!@$^&*()+=}|:";',>{ ]|%[0-9A-Fa-f]{2})+(?::(?:[^\]\[?\/<~#`!@$^&*()+=}|:";',>{ ]|%[0-9A-Fa-f]{2})*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?)|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff])|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62}\.)))(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(t)},o.validator.messages.url),o.validator.addMethod("vinUS",function(t){if(17!==t.length)return!1;for(var e,a,d,r,i=["A","B","C","D","E","F","G","H","J","K","L","M","N","P","R","S","T","U","V","W","X","Y","Z"],n=[1,2,3,4,5,6,7,8,1,2,3,4,5,7,9,2,3,4,5,6,7,8,9],o=[8,7,6,5,4,3,2,10,0,9,8,7,6,5,4,3,2],s=0,l=0;l<17;l++)d=o[l],a=t.slice(l,l+1),e=isNaN(a)?(a=a.toUpperCase(),n[i.indexOf(a)]):parseInt(a,10),8===l&&(r=e,"X"===a)&&(r=10),s+=e*d;return s%11===r},"The specified vehicle identification number (VIN) is invalid."),o.validator.addMethod("zipcodeUS",function(t,e){return this.optional(e)||/^\d{5}(-\d{4})?$/.test(t)},"The specified US ZIP Code is invalid."),o.validator.addMethod("ziprange",function(t,e){return this.optional(e)||/^90[2-5]\d\{2\}-\d{4}$/.test(t)},"Your ZIP-code must be in the range 902xx-xxxx to 905xx-xxxx."),o});