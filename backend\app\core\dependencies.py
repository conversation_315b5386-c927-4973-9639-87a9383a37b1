"""
Dependencies for all API function call

This script handles these functions:
    get_current_user -> Gets information about the current user and sets an User object with information
    enforce_role_selection: Checks if users role and lab are set. If not, returns an error and blocks access to functions

enforce_role_selection calls get_current_user. Enforce_role_selection basically allows access to API function based on the results of reading get_current_user

"""
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session
from ..database import get_db
from ..core.security import verify_token
from ..crud.user import get_user_by_email
from ..crud.staff import has_role
from ..schemas.user import User
from ..common.constants import UserRole
from ..common.constants import ERROR_MESSAGES
from ..core.logging import logger, log_error

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")


def get_current_user(
        token: str = Depends(oauth2_scheme),
        db: Session = Depends(get_db),
        request: Request = None
):
    """
    Get information from authentication token and create an usable User object

    ### Response Model:
    - Returns an User object

    ### Errors:
    - 401 Unauthorized: Could not validate credentials
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=ERROR_MESSAGES["auth"]["credentials_invalid"],
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    payload = verify_token(token)
    if payload is None:
        log_error(f"Server error: Payload is not set", exc_info=True)
        raise credentials_exception

    email: str = payload.get("sub")
    if email is None:
        log_error(f"Server error: Email is not set in token", exc_info=True)
        raise credentials_exception

    user = get_user_by_email(db, email)
    if user is None:
        log_error(f"Server error: User with email {email} is not in the DB", exc_info=True)
        raise credentials_exception
    
    lab_roles = payload.get("lab_roles")
    if lab_roles is None:
        log_error(f"Server error: Lab roles are not set in token", exc_info=True)
        raise credentials_exception
    
    azure_ad_id = payload.get("azure_ad_id")
    if azure_ad_id is None:
        azure_ad_id = "fake-id" #this is here to allow old can still work
        #log_error(f"Server error: Azure ad id are not set in token", exc_info=True)
        #raise credentials_exception
    
    #These 2 do not need a check, since they have a chance of being None
    lab = payload.get("lab")
    role = payload.get("role")

    #Create an User object with all the info from the DB and token to be used
    new_user = User(
        email=email,
        azure_ad_id=azure_ad_id,
        role=role if user is not None else None,
        is_active=user.is_active,
        lab=lab if lab is not None else None,
        lab_roles=lab_roles,
        user_id = user.user_id
    )
    return new_user

def enforce_role_selection(
    user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Returns a user IFF user role and user lab have been selected AND the role exists in Staff table
    
    - **user**: The response from get_current_user
    - **db**: Database session

    ### Response Model
    - Returns an User Object created in get_current_user

    ### Errors:
    - 403 Forbidden: User is not signed in OR role is not set OR role is set but without lab OR role/lab combination doesn't exist in Staff table
    """
    if user.role is None:
        log_error(f"Server error: User Role is not set", exc_info=True)
        raise HTTPException(status_code = 403, detail=ERROR_MESSAGES["auth"]["role_not_set"])
    
    if user.role is not UserRole.SCIENTIST and user.lab is None:
        log_error(f"Server error: User's Lab is not set", exc_info=True)
        raise HTTPException(status_code = 403, detail=ERROR_MESSAGES["auth"]["lab_not_set"])
    
    # Additional validation: Check if role exists in Staff table
    if user.lab is not None:  # For non-scientist roles or scientists with lab
        if not has_role(db, str(user.user_id), str(user.lab), UserRole(user.role)):
            log_error(f"Server error: User does not have role {user.role} for lab {user.lab}", exc_info=True)
            raise HTTPException(status_code = 403, detail=ERROR_MESSAGES["auth"]["invalid_role_lab"])
    else:  # For scientists without lab, this is allowed
        if user.role != UserRole.SCIENTIST.value:
            log_error(f"Server error: Non-scientist role {user.role} requires lab assignment", exc_info=True)
            raise HTTPException(status_code = 403, detail=ERROR_MESSAGES["auth"]["non_scientist_no_lab"])
    
    return user