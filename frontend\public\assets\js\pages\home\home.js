/*
 * Home Page JavaScript
 * Handles initialization of requisition tables based on user role
 */
import { REQUISITION_CONFIGS } from '../../core/config/requisition-configs.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { createRequisitionTable } from '../requisition/requisition-table.js';
import { getLabId } from '../lab/shared/lab-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS
};

// Configure page authentication requirements
// This will be processed by authentication.js middleware
window.PAGE_AUTH_CONFIG = {
    onAuthSuccess: initializeHomePage
};

// Initialize home page
async function initializeHomePage(userInfo) {
    try {
        await initializeRequisitionTables(userInfo);
    } catch (error) {
        console.error('Home page initialization failed:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// Initialize requisition tables based on user role
async function initializeRequisitionTables(userInfo) {
    const baseConfig = {
        role: userInfo.role,
        labId: getLabId(userInfo)
    };

    // Check lab assignment for lab staff roles
    const isLabStaff = userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_ADMIN ||
                       userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_PERSONNEL;

    if (isLabStaff && !baseConfig.labId) {
        showMessage('#page-error-container', 'auth.messages.errors', 'missingLabAssignment', MESSAGE_CONFIG_MAPS);
        return;
    }

    // Determine which tables to show based on role
    const showBothTables = userInfo.role === GLOBAL_CONFIGS.application.roles.SCIENTIST;
    const $openSection = $('#open-requisitions-section');
    const $closedSection = $('#closed-requisitions-section');

    // Show appropriate sections
    if ($openSection.length) {
        $openSection.prop('hidden', false).attr('aria-hidden', 'false');
    }

    if (showBothTables && $closedSection.length) {
        $closedSection.prop('hidden', false).attr('aria-hidden', 'false');
    }

    // Create tables
    const openTable = createRequisitionTable();
    const tables = [
        openTable.create({
            ...REQUISITION_CONFIGS.ui.requisitionTable.queues.open,
            ...baseConfig
        })
    ];

    // Add closed table for scientists
    if (showBothTables) {
        const closedTable = createRequisitionTable();
        tables.push(
            closedTable.create({
                ...REQUISITION_CONFIGS.ui.requisitionTable.queues.closed,
                ...baseConfig
            })
        );
    }

    // Create all tables concurrently
    await Promise.all(tables);
    console.log('Requisition tables initialized successfully');
}