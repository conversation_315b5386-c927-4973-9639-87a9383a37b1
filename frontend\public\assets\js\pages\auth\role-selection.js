//This code adds the proper roles and labs for the current user signed in
//It will then handle getting the new token from the back end and setting it in local storage
import { getLocalizedRoleName, getLocalizedUIText } from '../../core/i18n/i18n-helpers.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { parseJWT } from '../../core/auth/auth-helpers.js';
import { redirectToPageByKey } from '../../core/helpers/navigation-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS
};

$(document).ready(function () {
    initializeRoleSelectionPage();
});

// Initialize role selection page functionality
function initializeRoleSelectionPage() {
    // Get DOM elements
    const labSelect = document.getElementById("lab-select");
    const roleSelect = document.getElementById("role-select");
    const submitButton = document.getElementById("submit-role");
    const labDiv = document.getElementById("lab-select-div");

    // Get user data from localStorage
    const lab = localStorage.getItem("lab");
    const role = localStorage.getItem("role");
    const lab_roles = JSON.parse(localStorage.getItem("lab_roles"));

    if (lab_roles.length == 0){
        labSelect.innerHTML = `<option selected>${lab}</option>`
        roleSelect.innerHTML = `<option selected>${role}</option>`

        labSelect.disabled = true
        roleSelect.disabled = true
        submitButton.disabled = true
    } else {
        const roles = Array.from(new Set(lab_roles.map(r => { return r.role; })))
        roleSelect.innerHTML = `<option disabled selected>${getLocalizedUIText('selectRole', GLOBAL_CONFIGS.ui.common)}</option>`+
            roles.map(role => `<option value="${role}">${getLocalizedRoleName(role, GLOBAL_CONFIGS.ui.roles)}</option>`).join("")
        
        //Hide the labs
        labDiv.hidden = true
        
        //Just making sure
        roleSelect.disabled = false
        submitButton.disabled = false

        //Now add the event listener that checks if the role has changed
        //if so, it will change what options they have here
        roleSelect.addEventListener("change", function(){
            const selectedRole = roleSelect.value
            const labsForRole = lab_roles.filter(r => r.role === selectedRole).map(l => l.lab)
            let selectedLab = null

            if (selectedRole === GLOBAL_CONFIGS.application.roles.SCIENTIST){
                selectedLab = null
                labDiv.hidden = true
                labSelect.disabled = true
                labSelect.required = false
                labSelect.innerHTML = `<option disabled selected value="">${getLocalizedUIText('selectLab', GLOBAL_CONFIGS.ui.common)}</option>`

            } else {
                labDiv.hidden = false
                labSelect.disabled = false
                labSelect.innerHTML = `<option disabled selected value="">${getLocalizedUIText('selectLab', GLOBAL_CONFIGS.ui.common)}</option>`+
                    labsForRole.map(lab => `<option value="${lab}">${lab}</option>`).join("")
                labSelect.required = true
            }

        })
    }

    // Setup form submission handler
    setupFormSubmission();
}

// Handle form submission
function setupFormSubmission() {
    $(document).on("submit", "#role-selection", function (e) {
    try{
        e.preventDefault();
        //Lets make sure stuff it selected
        const role =  document.getElementById("role-select").value
        const lab =  document.getElementById("lab-select").value

        let url = `${GLOBAL_CONFIGS.api.baseUrl}/auth/select-lab-role?role=${role}`

        //Add lab if there is a lab selected
        if(!(lab == "")){
            url += `&lab_id=${lab}`
        }

        //Send request
        $.ajax({
            url: url,
            method: "POST",
            headers: {
                'Authorization': `Bearer ${localStorage.getItem("access_token")}`
            },
            success: function(response){
                localStorage.setItem("access_token", response.access_token)
                const payload = parseJWT(response.access_token);

                //This is what the user is signed in as
                localStorage.setItem("lab", payload.lab)
                localStorage.setItem("role", payload.role)

                // Redirect to home page after successful role selection
                redirectToPageByKey('home', 0); 
            },
            error: function(){
                showMessage("#page-error-container", 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
            }
        })
    }
    catch(error) {
        showMessage("#page-error-container", 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
    });
}

