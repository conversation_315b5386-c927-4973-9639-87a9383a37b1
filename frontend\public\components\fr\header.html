<!-- French Header for all pages. More info here: https://wet-boew.github.io/GCWeb/sites/header/header-docs-en.html-->
<html lang="fr">
	<!-- GCWeb and WET-BOEW Stylesheets -->
    <link rel="stylesheet" href="/libraries/GCWeb/css/theme.min.css">
<header>
	<div id="wb-bnr" class="container">
		<div class="row">
			<!-- Language toggle [version 1.1] -->
			<section id="wb-lng" class="col-xs-3 col-sm-12 pull-right text-right">
				<h2 class="wb-inv">Sélection de la langue</h2>
				<ul class="list-inline mrgn-bttm-0">
					<li><a id="language-switcher" lang="en" hreflang="en" href="#">
							<span class="hidden-xs" translate="no">English</span>
							<abbr title="Français" translate="no" class="visible-xs h3 mrgn-tp-sm mrgn-bttm-0 text-uppercase">en</abbr>
					</a></li>
				</ul>
			</section>
			<!-- Branding [version 1.0] -->
			<div class="brand col-xs-9 col-sm-5 col-md-4" property="publisher" typeof="GovernmentOrganization">
                <a href="https://wet-boew.github.io/GCWeb/" property="url">
                    <img src="https://wet-boew.github.io/themes-dist/GCWeb/GCWeb/assets/sig-blk-fr.svg" alt="Gouvernement du Canada" property="logo" /><span class="wb-inv"> / <span lang="en">Government of Canada</span></span>
                </a>
                <meta property="name" content="Gouvernement du Canada">
                <meta property="areaServed" typeof="Country" content="Canada" />
                <link property="logo" href="https://wet-boew.github.io/themes-dist/GCWeb/GCWeb/assets/wmms-blk.svg" />
            </div>
			<!-- Search [version 1.0] -->
		</div>
	</div>
	<hr>
	<div class="container">
		<div class="row">
			<div class="col-md-8">
				<!-- Site Menu [version 2.0] -->
		</div>
			<div id="auth-section" class="col-xs-6 col-xs-offset-6 col-md-offset-0 col-md-4">
				<!-- Optional Authentication Section [version 1.0] -->
			</div>
		</div>
	</div>
	<!-- Breadcrumbs [version 1.0] -->

	<!-- Scripts -->
	<script src="../../libraries/wet-boew/js/jquery/2.2.4/jquery.min.js"></script>
    <script src="../../libraries/wet-boew/js/wet-boew.min.js"></script>
    <script src="../../libraries/GCWeb/js/theme.min.js"></script>
	<script src="../../assets/js/core/i18n/language-switcher.js"></script>
</header>
</html>