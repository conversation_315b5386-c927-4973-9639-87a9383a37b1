#Opening Doc string
from app.models.requisition import Requisition
from sqlalchemy.orm import Session
from sqlalchemy import or_
from sqlalchemy import select
from app.models.user import User
from app.models.staff import Staff
from app.schemas.user import UserCreate, UserUpdate
from app.common.constants import UserRole, ERROR_MESSAGES
from app.common.exceptions import NotFoundError, AccessDeniedError, ValidationError as AppValidationError
from ..utils.hashing import get_password_hash
from . import staff as staff_crud
from ..core.logging import log_info, log_error, log_warning

def check_admin_access(db: Session, user_id: str, lab_id: str) -> bool:
    """Check if user has admin access in the given lab"""
    log_info(f"Checking admin access for user: {user_id} in lab: {lab_id}")
    
    if not staff_crud.has_role(db, user_id, lab_id, UserRole.LAB_ADMIN):
        log_warning(f"Access denied for user: {user_id} in lab: {lab_id}")
        raise AccessDeniedError(ERROR_MESSAGES["user"]["unauthorized_access"])
    
    log_info(f"Admin access confirmed for user: {user_id} in lab: {lab_id}")
    return True

def get_users(db: Session, user_id: str, lab_id: str, role: UserRole):
    """Get all users with role-based access control and lab filtering"""
    log_info(f"Retrieving users with role: {role} for lab: {lab_id} by user: {user_id}")
    check_admin_access(db, user_id, lab_id)
    
    # Get users for the current lab only
    users_in_lab = db.query(User).join(Staff, User.user_id == Staff.user_id).filter(Staff.lab_id == lab_id).all()
    log_info(f"Successfully retrieved {len(users_in_lab)} users for lab: {lab_id}")
    return users_in_lab

def get_user_requisitions(db: Session, user_id: str, requesting_user_id: str, requesting_lab_id: str, requesting_role: UserRole):
    """Get user's requisitions with role-based access control and lab filtering"""
    log_info(f"Retrieving requisitions for user: {user_id} requested by: {requesting_user_id} with role: {requesting_role} in lab: {requesting_lab_id}")
    
    check_admin_access(db, requesting_user_id, requesting_lab_id)
    # Get user and verify they belong to the admin's lab
    user = get_user(db, user_id, requesting_user_id, requesting_lab_id, requesting_role)  # This will handle lab check
    # Query requisitions directly
    requisitions = db.query(Requisition).filter(Requisition.submitted_by == user.user_id).all()
    
    log_info(f"Successfully retrieved {len(requisitions)} requisitions for user: {user_id}")
    return requisitions

def get_user(db: Session, user_id: str, user_id_requester: str = None, lab_id_requester: str = None, role_requester: UserRole = None):
    """Get user by ID with optional role check and lab filtering"""
    log_info(f"Retrieving user: {user_id} requested by: {user_id_requester} with role: {role_requester} in lab: {lab_id_requester}")
    
    user = db.query(User).filter(User.user_id == user_id).first()
    if not user:
        log_warning(f"User not found: {user_id}")
        raise NotFoundError(ERROR_MESSAGES["user"]["not_found"])
    
    if user_id_requester and role_requester:
        log_info(f"Performing additional access checks for requester: {user_id_requester}")
        check_admin_access(db, user_id_requester, lab_id_requester)
        # Check if user belongs to the admin's lab
        user_in_lab = db.query(Staff).filter(
            Staff.user_id == user_id,
            or_(Staff.lab_id == lab_id_requester, Staff.lab_id == None) #If the user is a scientist, they won't have a lab
        ).first()
        if not user_in_lab:
            log_warning(f"User {user_id} not found in lab: {lab_id_requester}")
            raise NotFoundError(ERROR_MESSAGES["user"]["not_found"])
    
    log_info(f"Successfully retrieved user: {user_id}")
    return user

def get_user_by_email(db: Session, email: str):
    """Get user by email (internal use)"""
    log_info(f"Attempting to find user with email: {email}")
    
    user = db.query(User).filter(User.email == email).first()
    if not user:
        log_warning(f"User with email {email} not found")
        raise NotFoundError(ERROR_MESSAGES["user"]["not_found"])
    
    return user

def get_user_by_azure_ad_id(db: Session, id: str):
    """Get user by azure ad id"""
    log_info(f"Attempting to find user with azure ad id: {id}")

    user = db.query(User).filter(User.azure_ad_id == id).first()
    if not user:
        log_warning(f"User with azure ad id {id} not found")
        return None
    
    return user
    

def user_exists(db: Session, email: str) -> bool:
    """Check if a user with the given email exists."""
    log_info(f"Verifying existence of user with email: {email}")
    
    user = db.query(User).filter(User.email == email).first()
    exists = user is not None
    
    log_info(f"User with email {email} exists: {exists}")
    return exists

def create_user_jit(db: Session, user_in: UserCreate) -> User:
    """
    Create a new user for Just-In-Time (JIT) provisioning.
    This function is intended for creating users from Azure AD and does not handle passwords.
    """
    log_info(f"Attempting to create user (JIT/internal) with email: {user_in.email}")

    # to prevent duplicate emails if this function is ever called directly elsewhere.
    existing_user = db.query(User).filter(User.email == user_in.email).first()
    if existing_user:
        log_warning(f"User creation failed: email {user_in.email} already exists.")
        raise AppValidationError(ERROR_MESSAGES["user"]["email_exists"])

    db_user = User(
        email=user_in.email,
        is_active=user_in.is_active,
        azure_ad_id=user_in.azure_ad_id
    )
    try:
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        log_info(f"Successfully created user: {db_user.email} with ID: {db_user.user_id}")
        return db_user
    except Exception as e:
        db.rollback()
        log_error(f"Database error creating user {user_in.email}: {str(e)}")
        raise AppValidationError(ERROR_MESSAGES["user"]["error_creating"])


def create_user(db: Session, user: UserCreate, user_id: str, lab_id: str, role: UserRole) -> User:
    """Create a new user with role-based access control"""
    log_info(f"Creating user with data: {user.model_dump(exclude={'password'})} by user: {user_id} with role: {role} in lab: {lab_id}")
    
    check_admin_access(db, user_id, lab_id)

    # Check if user with this email already exists
    if user_exists(db, user.email):
        log_warning(f"User with email {user.email} already exists")
        raise AppValidationError(ERROR_MESSAGES["user"]["email_exists"])

    try:
        hashed_password = get_password_hash(user.password)
        db_user = User(**user.model_dump(exclude={'password', 'role'}))
        db_user.password = hashed_password

        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        log_info(f"Successfully created user with email: {user.email}")
        return db_user
    except Exception as e:
        db.rollback()
        log_error(f"Error creating user: {str(e)}")
        raise AppValidationError(ERROR_MESSAGES["user"]["error_creating"])

def update_user(db: Session, user_id: str, user: UserUpdate, user_id_updater: str, lab_id_updater: str, role_updater: UserRole) -> User:
    """Update a user with role-based access control"""
    log_info(f"Updating user: {user_id} with data: {user.model_dump(exclude={'password'} if hasattr(user, 'password') else {})} by user: {user_id_updater} with role: {role_updater} in lab: {lab_id_updater}")
    
    check_admin_access(db, user_id_updater, lab_id_updater)

    db_user = get_user(db, user_id)
    if not db_user:
        log_warning(f"User not found for update: {user_id}")
        raise NotFoundError(ERROR_MESSAGES["user"]["not_found"])

    try:
        # We only allow updating is_active
        if user.is_active is not None:
            log_info(f"Updating is_active status to: {user.is_active} for user: {user_id}")
            db_user.is_active = user.is_active

        db.commit()
        db.refresh(db_user)
        log_info(f"Successfully updated user: {user_id}")
        return db_user
    except Exception as e:
        db.rollback()
        log_error(f"Error updating user: {str(e)}")
        raise AppValidationError(ERROR_MESSAGES["user"]["error_updating"])

def delete_user(db: Session, user_id: str, user_id_deleter: str, lab_id_deleter: str, role_deleter: UserRole) -> bool:
    """Delete a user with role-based access control"""
    log_info(f"Attempting to delete user: {user_id} by user: {user_id_deleter} with role: {role_deleter} in lab: {lab_id_deleter}")
    
    check_admin_access(db, user_id_deleter, lab_id_deleter)

    db_user = get_user(db, user_id)
    if not db_user:
        log_warning(f"User not found for deletion: {user_id}")
        raise NotFoundError(ERROR_MESSAGES["user"]["not_found"])

    try:
        db.delete(db_user)
        db.commit()
        log_info(f"Successfully deleted user: {user_id}")
        return True
    except Exception as e:
        db.rollback()
        log_error(f"Error deleting user: {str(e)}")
        raise AppValidationError(ERROR_MESSAGES["user"]["error_deleting"])