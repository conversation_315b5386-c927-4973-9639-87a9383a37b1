from pydantic import BaseModel, UUID4, ConfigDict, field_validator, field_serializer
from pydantic.types import UUID
from typing import Optional, List
from datetime import datetime
from ..common.constants import RequisitionStatus, PriorityLevel, SampleTestStatus

class RequisitionBase(BaseModel):
    lsa_number: str
    project_number: Optional[str] = None
    project_name: Optional[str] = None
    priority: Optional[PriorityLevel] = PriorityLevel.MEDIUM
    deadline: Optional[datetime] = None
    expected_completion_date: Optional[datetime] = None
    comments: Optional[str] = None

class RequisitionCreate(RequisitionBase):
    pass

class RequisitionUpdate(BaseModel):
    project_number: Optional[str] = None
    project_name: Optional[str] = None
    status: Optional[RequisitionStatus] = None
    priority: Optional[PriorityLevel] = None
    deadline: Optional[datetime] = None
    expected_completion_date: Optional[datetime] = None
    comments: Optional[str] = None
    is_archived: Optional[bool] = None

class Requisition(RequisitionBase):
    req_id: UUID4
    status: RequisitionStatus
    submitted_by: UUID4
    is_archived: bool
    lab_id: UUID4
    created_at: datetime
    updated_at: Optional[datetime]

    @field_validator('req_id', 'submitted_by', mode='before')
    def convert_str_to_uuid(cls, value):
        if isinstance(value, str):
            return UUID(value)
        return value

    model_config = ConfigDict(from_attributes=True)

    @field_serializer('req_id', 'submitted_by')
    def serialize_uuid(self, value: UUID) -> str:
        return str(value)


class RequisitionReadWithDetails(Requisition):
    lab_name: Optional[str] = None
    req_name: str

class RequisitionSampleResponse(BaseModel):
    req_sample_id: UUID4
    requisition_id: UUID4
    sample_id: UUID4
    survey_id: Optional[str] = None
    status: str
    created_at: datetime

    @field_validator('req_sample_id', 'requisition_id', 'sample_id', mode='before')
    def convert_str_to_uuid(cls, value):
        if isinstance(value, str):
            return UUID(value)
        return value

    model_config = ConfigDict(from_attributes=True)

    @field_serializer('req_sample_id', 'requisition_id', 'sample_id')
    def serialize_uuid(self, value: UUID) -> str:
        return str(value)

class RequisitionSampleTestCreate(BaseModel):
    test_type_ids: List[UUID4]
    status: Optional[SampleTestStatus] = SampleTestStatus.SUBMITTED

class RequisitionSampleTestResponse(BaseModel):
    req_sample_test_id: UUID4
    req_sample_id: UUID4
    requisition_id: UUID4
    sample_id: UUID4
    test_type_id: UUID4
    status: SampleTestStatus
    processed_by: Optional[UUID4]
    completed_at: Optional[datetime]
    created_at: datetime

    @field_validator('req_sample_test_id', 'req_sample_id', 'requisition_id', 'sample_id', 'test_type_id', 'processed_by', mode='before')
    def convert_str_to_uuid(cls, value):
        if isinstance(value, str):
            return UUID(value)
        return value

    model_config = ConfigDict(from_attributes=True)

    @field_serializer('req_sample_test_id', 'req_sample_id', 'requisition_id', 'sample_id', 'test_type_id', 'processed_by')
    def serialize_uuid(self, value: Optional[UUID]) -> Optional[str]:
        return str(value) if value else None

class RequisitionSampleTestUpdate(BaseModel):
    status: SampleTestStatus
    completed_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)