/*
 * Unified Message Helper following GC standards
 * Uses standard Canada.ca alert patterns with bilingual support
 * All messages display at page level with auto-scroll
 * Based on Canada.ca Contextual alerts: https://design.canada.ca/common-design-patterns/contextual-alerts.html
 */
import { getLocalizedText } from '../i18n/i18n-helpers.js';

// Display a message using configuration-based approach
// All messages now display at page level and auto-scroll to top
// Auto-behavior: danger messages persist, others disappear after 3 seconds
// placeholders: optional object for template placeholder substitution (e.g., {entity: 'test types', field: 'Test name', maxLength: 50})
export function showMessage(container, category, type, configMaps, placeholders = {}) {
    // Parse category for nested configs
    const categoryParts = category.split('.');
    let config;

    // Navigate through the nested configuration structure
    let currentConfig = configMaps;
    for (const part of categoryParts) {
        if (currentConfig && typeof currentConfig === 'object') {
            currentConfig = currentConfig[part];
        } else {
            currentConfig = undefined;
            break;
        }
    }

    // Get the final message config
    if (currentConfig && typeof currentConfig === 'object') {
        config = currentConfig[type];
    }

    if (!config) {
        console.warn(`Message configuration not found: ${category}.${type}`);
        return;
    }

    // Get container element
    const $container = typeof container === 'string' ? $(container) : container;
    if (!$container?.length) {
        console.warn('Message container not found');
        return;
    }

    // Display standard GC-compliant alert
    let message = getLocalizedText(config, 'message');

    // Handle template placeholder substitution
    if (Object.keys(placeholders).length > 0) {
        Object.entries(placeholders).forEach(([key, value]) => {
            message = message.replace(new RegExp(`{${key}}`, 'g'), value);
        });
    }

    const alertHtml = `<section class="alert alert-${config.type || 'info'}" role="alert">
        <p>${message}</p>
    </section>`;

    $container.html(alertHtml).show();

    // Auto-scroll to top so user can see the message
    $('html, body').animate({ scrollTop: 0 }, 300);

    // Auto-behavior based on message type:
    // - danger messages persist (critical errors, system issues)
    // - other messages (warning, info, success) disappear after 3 seconds
    if (config.type !== 'danger') {
        setTimeout(() => {
            $container.fadeOut(500, function() {
                $(this).hide().empty();
            });
        }, 3000); // 3 seconds for non-critical messages
    }
}

// Clear message from container
export function clearMessage(container) {
    const $container = typeof container === 'string' ? $(container) : container;
    $container?.empty().hide();
}


