/*!
 * Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v4.0.85 - 2025-02-20
 *
 */
!function(t){"use strict";for(var r,a,e,c=t("object[data$='.svg']"),g=t("img[src$='.svg']"),s=c.length,i=0;i!==s;i+=1)r=(e=c.eq(i)).attr("aria-label"),a=e.attr("id"),e.replaceWith("<img src='"+e.attr("data").replace(".svg",".png")+"' alt='"+(r||"")+"'"+(a?"id='"+a+"'":"")+"/>");for(s=g.length,i=0;i!==s;i+=1)(e=g.eq(i)).attr("src",e.attr("src").replace(".svg",".png"))}(jQuery);
//# sourceMappingURL=svg.min.js.map