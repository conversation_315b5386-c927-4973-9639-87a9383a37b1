/*!
 * @title Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * @license wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v16.3.0 - 2025-02-20
 *
 */
((d,e)=>{var a,i=e.doc,o="gc-empathy",r=".provisional."+o;i.on("timerpoke.wb wb-init .provisional.gc-empathy",r,function(i){var n=e.init(i,o,r);n&&i.currentTarget===i.target&&(i=d(n),(a=d(".quiz-content"))&&(a.clone().addClass("hidden-md hidden-lg").insertBefore(" .services-and-information "),a.addClass("hidden-xs hidden-sm")),n.classList.add("no-blink"),e.ready(i,o))}),e.add(r)})(jQ<PERSON>y,(window,wb));