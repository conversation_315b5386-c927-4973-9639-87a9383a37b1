# Database
DATABASE_URL=

# Test Database (Optional: override the auto-generated test database URL)
TEST_DATABASE_URL=

# CORS
BACKEND_CORS_ORIGINS=http://localhost:3000

# SECRET_KEY
SECRET_KEY=

#MSAL Sign in
TENANT_ID= 
CLIENT_ID= 
CLIENT_SECRET_ID= #ONly used if accessing other Microsoft APIs

# Default admin account
DEFAULT_ADMIN_EMAIL=
DEFAULT_ADMIN_PASSWORD=

# Logging
LIMS_LOG_LEVEL=INFO

# Storage
GSC_LIMS_DAS_API=
DAS_USERNAME=
DAS_PASSWORD=
DAS_UPLOAD_ENDPOINT=/uploadBinaryObject
DAS_DOWNLOAD_ENDPOINT=/downloadBinaryObject
DAS_DELETE_ENDPOINT=/deleteBinaryObject