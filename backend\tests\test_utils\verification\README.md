# Test Verification Utilities

This directory contains utilities for verifying test responses and error handling in the LIMS application.

## Overview

## Entity Verifiers

Each entity type has its own verifier class that inherits from `EntityVerifier`:

- `UserVerifier`: Verifies user entity responses
- `RequisitionVerifier`: Verifies requisition entity responses
- `SampleVerifier`: Verifies sample entity responses
- `TestTypeVerifier`: Verifies test type entity responses
- `FileVerifier`: Verifies file entity responses
- `RequisitionSampleVerifier`: Verifies requisition sample entity responses
- `RequisitionSampleTestVerifier`: Verifies requisition sample test entity responses

These verifiers check that:
- All required fields are present
- Fields have the correct data types
- Expected values match actual values

## Error Verifiers

There are two error verification functions:

1. `verify_api_error_response`: Verifies HTTP error responses from API endpoints
   - Use this for API tests that receive HTTP responses

2. `verify_crud_error_response`: Verifies exception objects from CRUD operations
   - Use this for CRUD tests that catch exceptions

## Usage Examples

### Entity Verification

```python
# Verify a user response has all required fields
UserVerifier.verify_common_fields(user_data)

# Verify a user response has specific values
UserVerifier.verify_response(user_data, {
    "email": "<EMAIL>",
    "is_active": True
})

# Verify a requisition sample response
RequisitionSampleVerifier.verify_response(requisition_sample_data, {
    "status": "SUBMITTED",
    "sample_id": "sample-uuid-here"
})

# Verify a requisition sample test response
RequisitionSampleTestVerifier.verify_response(requisition_sample_test_data, {
    "status": "SUBMITTED",
    "test_type_id": "test-type-uuid-here"
})
```

### Error Verification

#### API Tests

```python
# Verify an API error response
TestHelpers.Verification.verify_api_error_response(
    response,
    404,
    ERROR_MESSAGES["user"]["not_found"]
)
```

#### CRUD Tests

```python
# Verify a CRUD exception
with pytest.raises(NotFoundError) as exc_info:
    get_user(db, str(uuid.uuid4()))
verify_crud_error_response(
    exc_info.value,
    404,
    ERROR_MESSAGES["user"]["not_found"]
)
```

## Best Practices

1. Always use the appropriate verification function for the context:
   - `verify_api_error_response` for HTTP responses in API tests
   - `verify_crud_error_response` for exceptions in CRUD tests

2. Always verify both the status code and the error message when possible.

3. For validation errors where the exact message is not important, we can pass `None` as the expected message to only verify the status code.

4. Use the entity verifiers to ensure consistent validation of response structures across the test suite. 
