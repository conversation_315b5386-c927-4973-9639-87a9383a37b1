/*!
 * @title Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * @license wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v16.3.0 - 2025-02-20
 *
 */
((b,l,f)=>{var s={en:{"future-msg":"Some Future Messages"},fr:{"future-msg":"(FR) Some Future Messages"}}[f.lang],c={},u="wb-tables-utility",h="."+u,t="wb-init"+h,a=f.doc,d={filteredsum:!0},g=function(t){return!!c.debug&&("object"==typeof t?console.table(t):console.log(`DEBUG '${u}': `+t))};function m(t){return"string"==typeof(t="string"==typeof t&&"fr"==f.lang?t.replace(/[,]/g,"."):t)?+t.replace(/[\$,\s%]/g,""):"number"==typeof t?t:0}l["wb-tables"]?console.error("Can not initialize wb-table global settings as it is already define."):(l["wb-tables"]={footerCallback:function(t,a,e,i,n){var o,r,l;b(this).hasClass("wb-tables-utility")&&(o=b(this).data("wb-tables-utility"),g("wb-datatable-utility: footerCallback Executed"),r=this.api(),l={search:"applied"},void 0!==o&&o&&!o.filteredsum&&(l={}),r.columns(".wb-col-sum",{}).every(function(t){var a=r.column(t,l).data().reduce(function(t,a){return m(t)+m(a)},0),e=0,e=b(this.header()).hasClass("wb-col-money")?("fr"==f.lang?function(t,a){return 1==arguments.length&&(a=2),m(t).toFixed(a).replace(/\d(?=(\d{3})+\.)/g,"$& ").replace(/\./,",")+" $"}:function(t,a){return 1==arguments.length&&(a=2),"$"+m(t).toFixed(a).replace(/\d(?=(\d{3})+\.)/g,"$&,")})(a):b(this.header()).hasClass("cur-thousand-col")||b(this.header()).hasClass("wb-col-cur-thousand")?(a=r.column(t,l).data().reduce(function(t,a){return m(t)+m(a)},0),formatedNumber=parseFloat(a).toFixed(2),Number(formatedNumber).toLocaleString(f.lang,{minimumFractionDigits:2,maximumFractionDigits:2})):a;b(r.column(t).footer()).html("<strong>"+e.toString().replace(/\s/g,"&nbsp;")+"</strong>")}))}},l["wb-tables"][u]=!0),b(".wb-tables.wb-tables-utility").on("xhr.dt",function(){var t=b(this).DataTable(),a="data",i=(void 0!==t.settings().init().ajax.dataSrc&&(a=t.settings().init().ajax.dataSrc),t.data().row().ajax.json()[a]);if(void 0===i)g("wb-pspc-plugin: Could Not Get wb-table JSON Data Bailing Out");else{var e,n=[],o=[],r=[],l=[],s=t.settings().init().columns,c=t.columns().visible(),u=[];for(e in s)c[e]&&u.push(s[e]);b(this).find("th.mailto-col, th.wb-col-mailto").each(function(){n.push(u[b(this).index()].data)}),b(this).find("th.cur-thousand-col, th.wb-col-cur-thousand").each(function(){o.push(u[b(this).index()].data)}),b(this).find("th.wb-col-money").each(function(){r.push(u[b(this).index()].data)}),b(this).find("th.url-col, th.wb-col-url").each(function(){l.push(u[b(this).index()].data)});for(var h=0,d=i.length;h<d;h++)r.forEach(function(t){var a=i[h][t].replace(/[\s,]/g,""),a=parseFloat(a).toFixed(2),a=Number(a).toLocaleString(f.lang,{minimumFractionDigits:2,maximumFractionDigits:2});"fr"==f.lang?i[h][t]=a+" $":i[h][t]="$"+a}),o.forEach(function(t){var a=i[h][t].replace(/[\s,]/g,""),a=parseFloat(a).toFixed(2);i[h][t]=Number(a).toLocaleString(f.lang,{minimumFractionDigits:2,maximumFractionDigits:2})}),n.forEach(function(t){var a,e=i[h][t];e.match(/mailto:/)||(a=e.match(/(?<!"\>)([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4})/g))&&a.forEach(function(t){var a='<a href="mailto:'+t.toLowerCase()+'">'+t.toLowerCase()+"</a>";e=e.replace(t,a)}),i[h][t]=e}),l.forEach(function(a){var e=i[h][a],t="";(t=i[h][a+"-url"]?i[h][a+"-url"]:t)?i[h][a]=`<a href="${t}">${e}</a>`:(t=e.match(/(https?:\/\/[^ ]*)/))&&t.forEach(function(t){i[h][a]=e.replace(t,`<a href="${t}">${t}</a>`)})})}}),a.on("timerpoke.wb "+t,h,function(t){var a,t=f.init(t,u,h);if(t){if(t=b(t),a=b.extend(!0,{},d,l[u],f.getData(t,u)),c=a,l["wb-tables"]&&l["wb-tables"][u]){for(var e,i=a,n=Object.keys(i),o=n.length,r={};o--;)e=n[o],r[e.toLowerCase().trim()]=i[e];r&&r.pluginlabels&&Object.keys(r.pluginlabels[0]).forEach(t=>{s[t]=r.pluginlabels[0][t]}),g("PSPC: wb-pspc-datatable-utility plugin Version 1.1.1 Initialized"),g(r)}f.ready(t,u)}}),f.add(h)})(jQuery,window,wb);