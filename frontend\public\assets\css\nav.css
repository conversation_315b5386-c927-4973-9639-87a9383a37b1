/* Main nav bar */
#wb-sm .menu{
    background-color: #F1F2F3;
    color: #284162;
}

/* Top-level nav links */

#wb-sm .menu > li {
    position: relative;
}

#wb-sm .menu > li > a {
    color: #284162;
    background-color:  #F1F2F3;
    text-shadow: none;
}

/* Hover state */
#wb-sm .menu > li > a:focus,
#wb-sm .menu > li:hover > a,
#wb-sm .menu > li:focus > a,
#wb-sm .menu > li:active > a {
    background-color: #D6D9DD !important;
    color: #284162;
}

/* Dropdown items */
#wb-sm .menu > li > ul {
    /*For sizing*/
    width: auto;
    min-width: 100%;
}

#wb-sm .menu > li > ul > li > a {
    background-color:  #D6D9DD;
}