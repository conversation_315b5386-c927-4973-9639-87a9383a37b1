/*
 * Core Table Class
 * Handles basic table functionality
 */
import { showMessage } from '../helpers/message-helpers.js';
import { getLocalizedText } from '../i18n/i18n-helpers.js';
import { escapeHtml, formatDate, formatNotAvailable, getRoleDisplayName } from '../helpers/format-helpers.js';
import { GLOBAL_CONFIGS } from '../config/global-configs.js';

export class Table {
    constructor(config) {
        // Core properties only
        this.containerId = config.containerId;
        this.tableId = config.tableId;
        this.columns = config.columns || [];
        this.entityTerms = config.entityTerms;
        this.tableOptions = config.tableOptions || {};
        this.messageConfigMaps = config.messageConfigMaps || { global: GLOBAL_CONFIGS };
        
        // Optional entity-specific formatters
        this.entityFormatters = config.entityFormatters || {};
    }

    // Main method to create table
    async create(config = {}) {
        const containerId = config.containerId || this.containerId;
        const $container = $(`#${containerId}`);
        
        if ($container.length === 0) {
            throw new Error(`Container "${containerId}" not found`);
        }

        // Store config for data fetching and column visibility
        this.currentConfig = config;

        try {
            this.showLoadingState($container);
            
            // Get data from subclass
            const data = await this.fetchData();
            
            if (!data || data.length === 0) {
                this.showEmptyState($container);
                return;
            }

            // Build and display table
            const tableHtml = this.buildTable(data);
            $container.html(tableHtml);

            // Initialize WET-BOEW
            await this.initializeWETTable();

        } catch (error) {
            this.handleError($container, error);
            throw error;
        }
    }

    // Build complete table HTML
    buildTable(data) {
        const visibleColumns = this.getVisibleColumns();
        const headerCells = this.buildHeaderCells(visibleColumns);
        const tableRows = this.buildTableRows(data, visibleColumns);
        
        return this.buildTableHtml(headerCells, tableRows, visibleColumns);
    }

    // Get visible columns based on role conditions
    getVisibleColumns() {
        return this.columns.filter(column => {
            // Handle role-based column visibility
            if (column.showForRole && this.currentConfig && this.currentConfig.role) {
                return this.currentConfig.role === column.showForRole;
            }
            return true;
        });
    }

    // Build table header cells
    buildHeaderCells(columns) {
        return columns.map(column => {
            const headerText = getLocalizedText({ message: column.header }, 'message');
            return `<th>${escapeHtml(headerText)}</th>`;
        }).join('');
    }

    // Build table rows
    buildTableRows(data, columns) {
        return data.map(row => {
            const cells = columns.map(column => {
                const value = this.getCellValue(row, column);
                const formattedValue = this.formatCellValue(value, column, row);
                return `<td>${formattedValue}</td>`;
            }).join('');
            return `<tr>${cells}</tr>`;
        }).join('');
    }

    // Get cell value from row data
    getCellValue(row, column) {
        // Complex data transformation via accessor function
        if (column.accessor && typeof column.accessor === 'function') {
            return column.accessor(row);
        }
        
        // Simple field mapping
        if (column.field) {
            return row[column.field] || '';
        }
        
        // Default: use column key as property name
        return row[column.key] || '';
    }

    // Format cell value based on column type
    formatCellValue(value, column, row) {
        // Entity-specific formatters first
        if (column.formatter && this.entityFormatters[column.formatter]) {
            return this.entityFormatters[column.formatter](value, row);
        }

        // Built-in formatters
        switch (column.formatter) {
            case 'formatDate':
                return formatDate(value);
            case 'roleDisplay':
                return getRoleDisplayName(value);
            case 'notAvailable':
                return formatNotAvailable(value);
            default:
                return escapeHtml(value || '');
        }
    }

    // Build complete table HTML with WET-BOEW configuration
    buildTableHtml(headerCells, tableRows, visibleColumns) {
        const messageConfig = this.buildMessageConfig();
        const tableOptions = this.buildTableOptions(visibleColumns, messageConfig);
        const dataWbTables = JSON.stringify(tableOptions);

        return `
            <div class="table-responsive">
                <table id="${this.tableId}" class="table table-striped table-hover wb-tables"
                    data-wb-tables='${dataWbTables}'>
                    <thead><tr>${headerCells}</tr></thead>
                    <tbody>${tableRows}</tbody>
                </table>
            </div>
        `;
    }

    // Build DataTables options with column index adjustment
    buildTableOptions(visibleColumns, messageConfig) {
        const defaultOptions = {
            ordering: true,
            order: [[0, "asc"]],
            paging: true,
            pageLength: 10,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, messageConfig.all]],
            info: true,
            searching: true,
            language: {
                emptyTable: messageConfig.emptyTable,
                search: messageConfig.search,
                lengthMenu: messageConfig.lengthMenu,
                info: messageConfig.info
            }
        };

        // Merge with custom options and adjust column indices for visible columns
        const customOptions = { ...this.tableOptions };

        // Adjust column indices in sort order when columns are hidden by role-based visibility
        if (customOptions.order && visibleColumns.length !== this.columns.length) {
            customOptions.order = customOptions.order.map(([originalIndex, direction]) => {
                const originalColumn = this.columns[originalIndex];
                const visibleIndex = visibleColumns.findIndex(col => col.key === originalColumn?.key);
                return visibleIndex >= 0 ? [visibleIndex, direction] : [0, direction];
            });
        }

        return { ...defaultOptions, ...customOptions };
    }

    // Build message configuration with entity terms
    buildMessageConfig() {
        const entityTerm = getLocalizedText({ message: this.entityTerms }, 'message');
        const templates = GLOBAL_CONFIGS.ui.table.messageTemplates;

        return {
            all: getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.all }, 'message'),
            emptyTable: getLocalizedText({ message: templates.emptyTable }, 'message').replace('{entity}', entityTerm),
            search: getLocalizedText({ message: templates.search }, 'message').replace('{entity}', entityTerm),
            lengthMenu: getLocalizedText({ message: templates.lengthMenu }, 'message').replace('{entity}', entityTerm),
            info: getLocalizedText({ message: templates.info }, 'message').replace('{entity}', entityTerm)
        };
    }

    // Initialize WET-BOEW DataTables
    async initializeWETTable() {
        return new Promise((resolve) => {
            const $table = $(`#${this.tableId}`);
            if ($table.length === 0) {
                resolve();
                return;
            }

            $table.one('wb-ready.wb-tables', () => resolve());

            $table.delay(100).queue(function() {
                $(this).trigger('wb-init.wb-tables').dequeue();
                setTimeout(() => resolve(), 1000);
            });
        });
    }

    // Utility methods
    showLoadingState($container) {
        const loadingText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.loading }, 'message');
        $container.html(`<div class="text-center"><p>${loadingText}</p></div>`);
    }

    showEmptyState($container) {
        const messageConfig = this.buildMessageConfig();
        $container.html(`<div class="text-center"><p>${messageConfig.emptyTable}</p></div>`);
    }

    handleError($container, error) {
        $container.empty();
        console.error(`Failed to create table "${this.tableId}":`, error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', this.messageConfigMaps);
    }

    // Abstract method - must be implemented by subclasses
    async fetchData() {
        throw new Error('fetchData() method must be implemented by subclasses');
    }
}
