"""
Health check API endpoints for monitoring the system status.
"""
from fastapi import APIRouter

router = APIRouter()

@router.get("")
async def health_check():
    """
    Check the health status of the API and its dependencies.

    Returns:
        dict: Health status information
            - status (str): Current API status
            - database (str): Database connection status
    """
    return {
        "status": "healthy",
        "database": "connected"
    }