"""
Requisition verification helpers for API response testing.
"""

from typing import Any, Dict, Optional
from tests.test_utils.verification.base import EntityVerifier


class RequisitionVerifier(EntityVerifier):
    """Verifier for requisition entity responses"""

    COMMON_FIELDS = ["req_id", "status", "submitted_by", "created_at", "is_archived"]
    DATETIME_FIELDS = ["created_at", "deadline", "expected_completion_date"]
    OPTIONAL_FIELDS = ["deadline", "expected_completion_date", "project_name", "project_number", "lsa_number"]
    TYPE_VALIDATIONS = {
        "req_id": str,
        "status": str,
        "submitted_by": str,
        "is_archived": bool,
        "project_name": str,
        "project_number": str,
        "lsa_number": str
    }


class RequisitionReadWithDetailsVerifier(RequisitionVerifier):
    """Verifier for RequisitionReadWithDetails entity responses (includes lab_name and req_name)"""

    COMMON_FIELDS = RequisitionVerifier.COMMON_FIELDS + ["lab_name", "req_name"]
    DATETIME_FIELDS = RequisitionVerifier.DATETIME_FIELDS
    OPTIONAL_FIELDS = RequisitionVerifier.OPTIONAL_FIELDS + ["lab_name"]  # lab_name can be None
    TYPE_VALIDATIONS = {
        **RequisitionVerifier.TYPE_VALIDATIONS,
        "lab_name": str,
        "req_name": str
    }