"""
The logger implementation for the LIMS API.

This module sets up a basic logging system with rotation and compression for log files.
It provides a single log file (lims_api.log) that contains all logs with standard levels:
- INFO: Confirmation that things are working as expected
- WARNING: An indication that something unexpected happened, or may happen in the near future
- ERROR: Due to a more serious problem, the software has not been able to perform a function

Log Rotation and Retention:
- Log Rotation: When a log file reaches 20MB, it's compressed, renamed with a .gz extension, and a new log file is created
- Log Retention: Keeps 10 backup files (approximately 200MB total)
"""

import logging
import os
from pathlib import Path
from logging.handlers import RotatingFileHandler
import gzip
import shutil
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Create logs directory if it doesn't exist
LOGS_DIR = Path("logs")
LOGS_DIR.mkdir(exist_ok=True)

# Log format
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Get log level from environment variable, default to INFO
LOG_LEVEL = os.getenv('LIMS_LOG_LEVEL', 'INFO').upper()
VALID_LOG_LEVELS = {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}
if LOG_LEVEL not in VALID_LOG_LEVELS:
    LOG_LEVEL = 'INFO'

class CompressedRotatingFileHandler(RotatingFileHandler):
    def rotation_filename(self, default_name):
        """Compress rotated files with .gz extension."""
        return default_name + ".gz"

    def rotate(self, source, dest):
        """Compress the source file and move it to destination."""
        with open(source, 'rb') as f_in:
            with gzip.open(dest, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        os.remove(source)

def setup_logger():
    """Set up the application logger with rotation by size and compression."""
    # Create main application logger
    logger = logging.getLogger("lims_api")
    logger.setLevel(getattr(logging, LOG_LEVEL))
    
    # Clear any existing handlers
    if logger.handlers:
        logger.handlers.clear()
    
    # Console handler for immediate feedback
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(LOG_FORMAT))
    logger.addHandler(console_handler)
    
    # Single file handler for all logs with rotation and compression
    # 20 MB per file, keep 10 backup files (total ~200 MB max)
    log_file = LOGS_DIR / "lims_api.log"
    file_handler = CompressedRotatingFileHandler(
        log_file, 
        maxBytes=20 * 1024 * 1024,  # 20 MB
        backupCount=10
    )
    file_handler.setFormatter(logging.Formatter(LOG_FORMAT))
    logger.addHandler(file_handler)
    
    return logger

# Create the main logger
logger = setup_logger()

def log_info(message):
    """Log an informational message."""
    logger.info(message)

def log_warning(message):
    """Log a warning message."""
    logger.warning(message)

def log_error(message, exc_info=None):
    """Log an error with optional exception info."""
    logger.error(message, exc_info=exc_info)
