/*!
 * @title Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * @license wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v16.3.0 - 2025-02-20
 *
 */
((N,p,h,M)=>{function s(t){for(var e=h.getElementById(t.getAttribute("aria-controls")),a=N(t).parent(),r=N(t);!r.hasClass("wb5React");)r=r.parent();var i,n=I[r.get(0).id],o=e.getAttribute("data-wb5-template");if(!(i=o?n.template(o)||h.getElementById(o)||n.tmplDefault(o):i)||!o)throw"No template defined to show listbox options";n.template(o,i),R(i),i=i.content.cloneNode(!0),n.data.filter=t.value,S=[],j(i,n,o),i.querySelectorAll("[role=option]").length?(e.innerHTML="",e.appendChild(i),N(e).removeClass("hidden"),c=a.get(0)):c&&d()}function d(){var t;c&&(t=c.getAttribute("aria-owns"),N("#"+t).addClass("hidden"),c=null)}function r(t){if((t=>{var e=t.form&&t.form.parentNode.classList.contains("wb-frmvld");if(null===t.getAttribute("required")&&""===t.value||null===t.getAttribute("data-rule-mustExist"))return t.setCustomValidity(""),e&&N(t).valid();for(var a=N(t);!a.hasClass("wb5React");)a=a.parent();for(var r,i=I[a.get(0).id].data.options,n=t.value,o=i.length,l=0;l<o;l+=1)if(n===i[l].value){r=!0;break}r?(t.setCustomValidity(""),e&&N(t).valid()):(t.setCustomValidity(g.errValid),e&&N(t).valid())})(t),document.activeElement!==t){for(var e=N(t).parent(),a=N(t);!a.hasClass("wb5React");)a=a.parent();for(var r=I[a.get(0).id],i=t.value,n={},o=r.data.options,l=0;l<o.length;l+=1)if(i===o[l].value){n=o[l];break}e.trigger("wb.change",{value:t.value,item:n})}}var g,c,u,m="wb-combobox",v="."+m,t=M.doc,w={template:'<div class="combobox-wrapper"><div role="combobox" aria-expanded="false" aria-haspopup="listbox" data-wb5-bind="aria-owns@popupId"><input autocomplete="off" data-rule-fromListbox="true" data-wb5-bind="id@fieldId, aria-controls@popupId, value@filter" aria-autocomplete="list" aria-activedescendant="" /></div><div data-wb5-bind="id@popupId" role="listbox" class="hidden"><template data-slot-elm="" data-wb5-template="sub-template-listbox"><ul class="list-unstyled mrgn-bttm-0">\x3c!-- <li class="brdr-bttm" role="option" data-wb5-for="option in wbLoad" data-wb5-if="!parent.filter.length || parent.config.compareLowerCase(option,parent.filter)" data-wb5-on="select@select(option); <EMAIL>(wb-nbNode)" >{{ option }}</li> --\x3e<li class="" role="option" data-wb5-for="option in options" data-wb5-if="!parent.filter.length || parent.config.compareLowerCase(option.value,parent.filter)" data-wb5-on="select@select(option.value); <EMAIL>(wb-nbNode)" >{{ option.textContent }}</li></ul></template></div></div>',i18n:{en:{errValid:"You need to choose a valid options."},fr:{errValid:"Veuillez choisir une option valide."}},compareLowerCase:function(t,e){return-1!==t.toLowerCase().indexOf(e.toLowerCase())},similarText:function(t,e,a){t=((t,e)=>{t=t.replace(/[\-\/]|_/g," ").replace(/[^\w\s]|_/g,"").trim().toLowerCase(),e=e.replace(/[\-\/]|_/g," ").replace(/[^\w\s]|_/g,"").trim().toLowerCase();var a=t.split(" "),r=e.split(" ");if(t.length>e.length&&(a=e.split(" "),r=t.split(" ")),!r.length||!a.length)return 100;for(var i=0,n=0,o="",l=0;l<a.length;l+=1){for(var s=0,d=0,c=!1,u=0;u<r.length;u+=1){var b,f=a[l];0<=(o=r[u]).indexOf(f)?(b=o.length,(!c||b<s)&&(s=o.length,d=o.length),c=!0):c||s<(b=o.length-((t,e)=>{for(var a=[],r=0;r<=t.length;r++){for(var i,n=r,o=0;o<=e.length;o++)0===r?a[o]=o:0<o&&(i=a[o-1],t.charAt(r-1)!==e.charAt(o-1)&&(i=Math.min(Math.min(i,n),a[o])+1),a[o-1]=n,n=i);0<r&&(a[e.length]=n)}return a[e.length]})(f,o))&&(s=b,d=o.length)}i+=s,n+=d}return 0===i?0:i/n*100})(t,e);return(a=parseInt(a))<=t}},b=9,f=13,y=27,x=35,C=36,A=38,k=40,I={},T=h.createDocumentFragment(),S=[],j=function(t,a,e){for(var r,i,n=t.childNodes,o=n.length,l=[],s=0;s<o;s+=1)if(3===(r=n[s]).nodeType&&-1!==r.textContent.indexOf("{{")&&(r.textContent=r.textContent.replace(/{{\s?([^}]*)\s?}}/g,function(t,e){return _(a.data,e.trim())})),"TEMPLATE"===r.nodeName){R(r);var d=(d=q(r,"data-wb5-template"))||M.getId();r.parentNode.hasAttribute("data-wb5-template")||r.parentNode.setAttribute("data-wb5-template",d),a.tmplDefault(d,r)}else if(1===r.nodeType)if(r.hasAttribute("data-wb5-for")){var d=q(r,"data-wb5-for"),c=B(d),u=_(a.data,c.for);if(!u)throw"Iterator not found";var b,f=u.length,p=0;for(u.wbLen=parseInt(f),N.isArray(u)&&(u.active=p),b=0;b<f;b+=1){var h=r.cloneNode(!0),g=U(h),m={"wb-idx":b,"wb-nbNode":p,parent:a.data};m[c.alias]=u[b],m=V(m),e&&(a.data[e]=m),g.if&&!O(g.if,m.data,a.data)||(p+=1,j(h,m,e),t.appendChild(h))}u.wbActive=p,l.push(r)}else r.hasAttribute("data-wb5-if")||r.hasAttribute("data-wb5-else")||r.hasAttribute("data-wb5-ifelse"),j(r,a,e);for(o=l.length,s=0;s!==o;s+=1)t.removeChild(l[s]);if(1===t.nodeType&&t.hasAttribute("data-wb5-bind"))for(var v=q(t,"data-wb5-bind").split(", "),w=0;w<v.length;w+=1){var y=v[w].split("@");t[y[0]]?(t[y[0]]=_(a.data,y[1]),a.observe(y[1],function(){return t[y[0]]=_(a.data,y[1])||""})):(t.setAttribute(y[0],_(a.data,y[1])),a.observe(y[1],function(){return void 0!==t[y[0]]?t[y[0]]=_(a.data,y[1])||"":t.setAttribute(y[0],_(a.data,y[1]))||""}))}if(1===t.nodeType&&t.hasAttribute("data-wb5-text")&&(i=q(t,"data-wb5-text"),t.textContent=_(a.data,i),a.observe(i,function(){return t.textContent=_(a.data,i)||""})),1===t.nodeType&&t.hasAttribute("data-wb5-on"))for(var x=q(t,"data-wb5-on").split("; "),C=x.length,A=0;A<C;A+=1){var k,I,T=x[A].split("@"),E=T[0],T=T[1],L=T.indexOf("("),D=T.lastIndexOf(")");if(L&&D&&(k=T.substring(0,L).trim(),I=T.substring(L+1,D).trim()),!k)throw"Error, an event handler need to call a function";I=I&&z(I,a.data),"live"===E?_(a.data,k).call(a.data,I):S.push({nd:t,evt:E,trigger:k,attr:I})}},q=function(t,e){var a=t.getAttribute(e);return t.removeAttribute(e),a},O=function(t,e,a){return!!z(t,e,a)},z=function(t,r,i){var n=/{{-\s?([^}]*)\s?-}}/g,o=[];return t=(t=(t=t.replace(/"([^"\\]*(\\.[^"\\]*)*)"|\'([^\'\\]*(\\.[^\'\\]*)*)\'/g,function(t){var e="{{-"+o.length+"-}}";return o.push(t),e})).replace(/[a-zA-Z]([^\s]+)/g,function(e){var a,e=(e=e.trim()).replace(n,function(t,e){return o[e]});try{a=_(r,e)}catch(t){try{a=_(i,e)}catch(t){throw"Information in the DATA obj not found"}}return"string"==typeof(a="object"==typeof a?JSON.stringify(a):a)?'"'+a+'"':a})).replace(n,function(t,e){return o[e]}),new Function("return "+t)()},U=function(t){var e={},a=q(t,"data-wb5-if");return a?(e.if=a,i(e,{exp:a,block:t})):(null!==q(t,"data-wb5-else")&&(e.else=!0),(a=q(t,"data-wb5-elseif"))&&(e.elseif=a)),e},i=function(t,e){t.ifConditions||(t.ifConditions=[]),t.ifConditions.push(e)},E=function(t,e){for(var a,r=t.options,i=r.length,n=0;n<i;n+=1)a=r[n],e.data.options.push({value:a.value,textContent:a.textContent});e.data.fieldId=t.id||M.getId(),e.data.fieldName=t.name,e.data.mustExist=!0},B=function(t){var e,a,r=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,t=t.match(/([^]*?)\s+(?:in|of)\s+([^]*)/);if(t)return(e={}).for=t[2].trim(),(a=(t=t[1].trim().replace(/^\(|\)$/g,"")).match(r))?(e.alias=t.replace(r,""),e.iterator1=a[1].trim(),a[2]&&(e.iterator2=a[2].trim())):e.alias=t,e},_=function(t,e){var a=(e=e.trim()).substring(0,1),r=e.substring(-1);if("'"===a||'"'===a||"'"===r||'"'===r)return e.substring(1,e.length-1);var a=e.indexOf("("),r=e.lastIndexOf(")"),i=[];if(-1!==a&&-1!==r&&a+1!==r){for(var n=e.substring(0,a),o=(i=e.substring(a+1,r).split(",")).length,l=0;l<o;l+=1){var s=i[l],s=_(t,s);i[l]=s}e=n+"()"}for(var d,c,u=e.split("."),b=u.length,f=0;f<b;f+=1){if(d=u[f],!t)return;t=-1!==d.lastIndexOf("()")?(c=d.substring(0,d.length-2),("string"==typeof t?String.prototype:t)[c].apply(t,i)):t[d]}return t},R=function(t){if(!t.content){for(var e=t.childNodes,a=h.createDocumentFragment();e[0];)a.appendChild(e[0]);t.content=a}},V=function(t){var e,a={},r={},i={},n=t;for(e in n)n.hasOwnProperty(e)&&!function t(e,a,r){var i=e[a];if(Array.isArray(i))return i.wbLen=parseInt(i.length),i.wbActive=0,t(i,"wbLen",a),void t(i,"wbActive",a);Object.defineProperty(e,a,{get:function(){return i},set:function(t){i=t,o(r?r+"."+a:a)}})}(n,e);return{data:t,observe:function(t,e){a[t]||(a[t]=[]);a[t].push(e)},notify:o,template:function(t,e){{if(!e)return r[t]||!1;r[t]=e}},tmplDefault:function(t,e){{if(!e)return i[t]||!1;i[t]=e}},debug_signals:a};function o(t){!a[t]||a[t].length<1||a[t].forEach(function(t){return t()})}};t.on("wb-ready.wb",function(){N.validator&&N.validator.addMethod("fromListbox",function(t,e){return e.checkValidity()},"You need to choose a valid options")}),t.on("json-fetched.wb","[role=combobox]",function(t){for(var e=t.target,t=t.fetch.response,a=N(e);!a.hasClass("wb5React");)a=a.parent();I[a.get(0).id].data.wbLoad=t}),t.on("click vclick touchstart focusin","body",function(t){c&&!c.parentElement.contains(t.target)&&setTimeout(function(){d()},1)}),t.on("focus","[role=combobox] input",function(t){c||setTimeout(function(){s(t.target)},1)}),t.on("blur","[role=combobox] input",function(t){r(t.target)}),t.on("keyup","[role=combobox] input",function(t){var e=t.which||t.keyCode,a=t.target.classList.contains("error");switch(e){case A:case k:case y:case f:case C:case x:return t.preventDefault(),void(a&&setTimeout(function(){r(t.target)},100));case b:return void(a&&setTimeout(function(){r(t.target)},100));default:setTimeout(function(){s(t.target),a&&r(t.target)},100)}}),t.on("keydown","[role=combobox] input",function(t){var e=t.which||t.keyCode,a=t.target;if(e===y)d();else{c||s(a);var r=a.getAttribute("aria-activedescendant"),i=r?h.getElementById(r):null,n=h.getElementById(a.getAttribute("aria-controls")).querySelectorAll("[role=option]"),o=n.length,l=-1;if(i){for(l=0;l<o&&n[l].id!==i.id;l+=1);o<=l&&(l=-1)}switch(e){case A:-1!==l&&0!==l?l--:l=o-1;break;case k:-1===l?l=0:l<o&&l++;break;case C:l=0;break;case x:l=o-1;break;case f:return N(n[l]).trigger("wb.select"),d(),void t.preventDefault();case b:return u&&N(n[l]).trigger("wb.select"),void d();default:return}t.preventDefault(),r=n[l],i&&i.setAttribute("aria-selected","false"),r?(r.id||(r.id=M.getId()),a.setAttribute("aria-activedescendant",r.id),r.setAttribute("aria-selected","true"),u=!0):a.setAttribute("aria-activedescendant","")}}),t.on("mouseover","[role=listbox] [role=option]",function(t){var e=c.querySelector("input"),t=t.target,a=e.getAttribute("aria-activedescendant"),a=a?h.getElementById(a):null;a&&a.setAttribute("aria-selected","false"),t.id||(t.id=M.getId()),a&&a.id!==t.id&&(u=!1),t.setAttribute("aria-selected","true"),e.setAttribute("aria-activedescendant",t.id)}),t.on("click","[role=listbox] [role=option]",function(t){var e=N(c.querySelector("input"));N(t.target).trigger("mouseover").trigger("wb.select"),e.trigger("focus"),d()}),t.on("wb.select","[role=listbox] [role=option]",function(t){for(var e=t.target,t=h.querySelector("[aria-activedescendant="+e.id+"]"),a=N(t);!a.hasClass("wb5React");)a=a.parent();for(var r,i=I[a.get(0).id],n=0;n<S.length;n+=1)if((r=S[n]).nd.isEqualNode(e)){i.data[r.trigger].call(i.data,r.attr);break}}),t.on("timerpoke.wb wb-init.wb-combobox",v,function(t,e){var a,r,i,n,o,t=M.init(t,m,v);if(t){a=N(t),r=N.extend(!0,{},w,p[m],M.getData(a,m)),g=g||r.i18n[M.lang];for(var l,s,e=e||(e=r,{popupId:M.getId(),fieldId:!1,fieldName:"",mustExist:!1,filter:"",cntdisplayeditem:0,options:[],config:e,i18n:{},horay:"testMe",select:function(t){this.filter=t},nbdispItem:function(t){this.cntdisplayeditem=t}}),d=V(e),c=(t.id||(t.id=M.getId()),r.parserUI&&"function"==typeof r.parserUI?r.parserUI(t,d):r.parserUI&&N.isArray(r.parserUI)&&r.parserUI[t.id]?r.parserUI[t.id].call(this,t,d):(e=d,"SELECT"===(o=t).nodeName&&E(o,e)),d.data.fieldId||(d.data.fieldId=M.getId()),o=d,n=(e=r).templateID,(c=n?h.getElementById(n):c)?(R(c),i=c.content.cloneNode(!0)):((n=h.createElement("div")).innerHTML=e.template,(i=h.createDocumentFragment()).appendChild(n)),S=[],j(i,o),i),u=c.childNodes,b=u.length,f=0;f<b;f+=1)1===(s=u[f]).nodeType&&((l=s.id)||(l=M.getId(),s.id=l),I[l]=d,s.classList.add("wb5React"));e=c.querySelector("[role=combobox]");d.data.mustExist&&c.querySelector("[role=combobox] input").setAttribute("data-rule-mustExist","true"),t.parentNode.insertBefore(c,t),r.hideSourceUI?a.addClass("hidden"):(t.id=M.getId(),T.appendChild(t)),a=N(c),t.dataset.wbLoad&&N(e).trigger({type:"json-fetch.wb",fetch:{url:t.dataset.wbLoad}}),"function"!=typeof T.getElementById&&(T.getElementById=function(t){for(var e,a=this.childNodes,r=a.length,i=0;i<r;i+=1)if((e=a[i]).id===t)return e;return!1}),Modernizr.addTest("stringnormalize","normalize"in String),Modernizr.load({test:Modernizr.stringnormalize,nope:["site!deps/unorm"+M.getMode()+".js"]}),M.ready(a,m)}}),M.add(v)})(jQuery,window,document,wb),((l,a,s)=>{function r(t,e){t.id||(t.id=s.getId());for(var a=0;a<i.items.length;a+=1){var r=i.items[a],r=l.extend({},r,{value:r.label,textContent:r.label});r.source||(r.source=t.id),e.data.options.push(r)}}var i,t=s.doc;t.on("combobox.createctrl.wb-fieldflow",".wb-fieldflow",function(t,e){i=e,a["wb-combobox"]||(a["wb-combobox"]={}),a["wb-combobox"].parserUI=[],a["wb-combobox"].parserUI[t.target.id]=r,a["wb-combobox"].hideSourceUI=!0,t.target.classList.add("wb-combobox"),l(t.target).trigger("wb-init.wb-combobox"),l(t.target).data().wbFieldflowRegister=[l(t.target).before().get(0).id],l(t.target).attr("data-wb-fieldflow-origin",l(t.target).before().get(0).id)}),t.on("wb.change","[role=combobox]:not(.wb-fieldflow-init)",function(t,e){t=t.currentTarget,e=e.item,t.id||(t.id=s.getId()),t=l("#"+e.bind).parentsUntil(".wb-fieldflow").parent();if(t.length){var a=t.get(0).id,r=l("#"+e.bind).data().wbFieldflow;l.isArray(r)||(r=[r]);for(var i=0;i<r.length;i+=1){var n=r[i],o=n.action+".action.wb-fieldflow";n.provEvt="#"+a,l("#"+a).trigger(o,n)}}})})(jQuery,window,(document,wb)),((o,t)=>{function i(t){return"click"===t.type||"keypress"===t.type&&(32===(t=t.charCode||t.keyCode)||13===t)?1:void 0}o.fn.extend({relatives:function(){var a=o();return this.each(function(t,e){e.wb5&&e.wb5.relatives&&e.wb5.relatives.each(function(){a=a.add(this)})}),a},related:function(a,r){return this.each(function(t,e){r&&e.wb5&&e.wb5.relatives&&e.wb5.relatives.each(function(){a=a.add(this)}),o.extend(e,{wb5:{relatives:a}})}),this}}),o.fn.extend({shuffle:function(){var a=this.get(),e=o.map(a,function(){var t=Math.floor(Math.random()*a.length),e=o(a[t]).clone(!0)[0];return a.splice(t,1),e});return this.each(function(t){o(this).replaceWith(o(e[t]))}),o(e)}}),o.fn.extend({rand:function(t){var e=this,a=e.size();if(a<(t=t?parseInt(t):1))return e.pushStack(e);if(1===t)return e.filter(":eq("+Math.floor(Math.random()*a)+")");for(var r=e.get(),i=0;i<a-1;i++){var n=Math.floor(Math.random()*(a-i))+i;r[n]=r.splice(i,1,r[n])[0]}return r=r.slice(0,t),e.filter(function(t){return-1<o.inArray(e.get(t),r)})}}),o.fn.extend({notEmpty:function(){return 0!==this.length}}),o.fn.extend({command:function(t){return t.length?{command:(t=t.split("@"))[0],selector:t[1],options:t[2]}:{command:!1,selector:!1}}}),o("[data-wb5-bind]").each(function(t,e){var a,e=o(e);e.parents().is("template")||(a=o(e.attr("data-wb5-bind"))).notEmpty()&&e.related(a)}),o(t).on("keypress click","[data-wb5-debug]",function(t){if(!i(t))return!0;for(var e=o(this),a=e.attr("data-wb5-debug").split(","),r=a.length-1;0<=r;r--)console.log(e[a[r]]())}),o("[data-wb5-click]").on("click",function(){var t=o(this).data("wb5-click").split("@"),e=t[0],a=t[2];if("postback"===e)return e=o(t[1]),o.ajax({type:e.attr("method"),url:e.attr("action"),data:e.serialize()}),!a.block}),o(t).on("updated.wb5","[data-wb5-update]",function(){var t=o(this).relatives(),e=this.getAttribute("max"),a=this.getAttribute("value"),e=Math.ceil(a/e*100)||0;t.find(".meter").text(a),t.removeClass(function(t,e){return(e.match(/(^|\s)p\d+/g)||[]).join(" ")}).attr("data-percentage","p"+e),t.addClass("p"+e)}),o(t).on("keypress click","[data-wb5-ajax]",function(t){if(!i(t))return!0;var t=o(this),e=t.command(this.getAttribute("data-wb5-ajax")),a=t.relatives();a.attr("aria-live","polite"),"replace"===e.command&&o.get(e.options,function(t){a.html(t)})}),o(t).on("keypress click","[data-wb5-load]",function(t){if(!i(t))return!0;var t=o(this),e=t.command(this.getAttribute("data-wb5-load")),t=t.relatives();t.attr({"aria-relevant":"all","aria-live":"polite","aria-atomic":"true"}),"replace"===e.command&&t.load(e.options)}),o(t).on("keypress click","[data-wb5-trigger]",function(t){if(!i(t))return!0;var t=o(this),e=t.command(this.getAttribute("data-wb5-trigger"));t.relatives().trigger(e.command)}),o(t).on("keypress click","[data-wb5-profile]",function(t){if(!i(t))return!0;var e=o(this),a=e.command(this.getAttribute("data-wb5-profile"));"single"===a.command&&(e.relatives().removeClass(a.options).filter("[role=button]").attr("aria-pressed","false"),o(t.target).closest(e.relatives()).addClass(a.options).filter("[role=button]").attr("aria-pressed","true"))})})(jQuery,document),((s,d,a)=>{function n(){var e=new RegExp("{{\\s*([a-z0-9_$][\\.a-z0-9_]*)\\s*}}","gi");return function(t,o,l){return t.replace(e,function(t,e){for(var a=e.split("."),r=a.length,i=o,n=0;n<r;n+=1){if(void 0===(i=i[a[n]]))throw"tim: '"+a[n]+"' not found in "+t;if(n===r-1)return s.isNumeric(i)&&l?((t,e)=>(e=Math.pow(10,e||0),Math.round(t*e)/e))(i,l).toLocaleString(d.documentElement.lang):s.isNumeric(i)?i.toLocaleString(d.documentElement.lang):i}})}}s(d).on("wb-ready.wb-charts","[data-wb-charts-interactive]",function(){var t=s(this),e=t.command(this.getAttribute("data-wb-charts-interactive")),i=a.getId().replace(/-/g,""),e=(s(e.selector).html(),e.options,t.prev());s("<div id='"+i+"' class='wbchrt-tpl'></div>").css({position:"absolute",display:"none"}).appendTo("body"),s("canvas:eq(1)",e).css("position","relative"),e.on("plothover",function(t,e,a){var r;a?(r=s("#"+i),a.datapoint[0],a.datapoint[1],a.series.label,r.html(n()),r.css({top:a.pageY+7,left:a.pageX+7}).show()):s("#"+i).hide()})})})(jQuery,document,wb),((b,t,i)=>{function n(t){return"click"===t.type||"keypress"===t.type&&(32===(t=t.charCode||t.keyCode)||13===t)?1:void 0}function o(t,a,e,r){var i=b('[data-wbtbl-search$="@'+e+"@"+r+'"],[data-wbtbl-highlight$="@'+e+"@"+r+'"]');a?i.removeClass("pick"):i.addClass("pick"),b('form[data-wb5-bind="#'+t.table().node().id+'"]').find('[value^="'+e+"@"+r+'"],[data-wbtbl-bind^="'+e+"@"+r+'"]').each(function(t,e){e=b(e);return e.is("[type=checkbox],[type=radio]")?e.prop("checked",a):e.is("[type=text],[type=textarea]")?e.val(a?r:""):e.get(0).selected=a,!0})}function l(){var t=b(this),a=[],e=t.relatives().DataTable(),r=t.command(this.getAttribute("data-wbtbl-post")),i=[];if(t.find(r.selector).each(function(t,e){e=b(e);if(e.is("[type=checkbox]")){if(e.is(":checked")){if(e.is("[data-xor]"))return i.push(e.val()),!0;a.push(e.val())}return!0}a.push(e.val())}),1<i.length){for(var n={},o=!1,l=i.length-1;0<=l;l--){var s=i[l].split("@");n.hasOwnProperty(s[0])||(n[s[0]]=[]),n[s[0]].push("("+s[1]+")")}for(o in n)a.push(o+"@"+n[o].join("|"))}else 1===i.length&&a.push(i[0]);for(var d=a.length-1;0<=d;d--){var c=a[d].split("@"),u=c[0],c=c[1];-1!==c.indexOf("|")?e.column(u).search(c,!0):(c=c.replace(/^\(/,"").replace(/\)$/,""),e.column(u).search(c))}}b(t).on("preInit.dt",".wb-tables",function(){var t=b("form[data-wbtbl-post]").get(0);t&&l.call(t)}),b(t).on("draw.dt",".wb-tables",function(t){var e=b(this),a=e.DataTable();e.relatives().trigger({type:t.type,table:a,displayed:a.page.info().recordsDisplay,total:a.page.info().recordsTotal})}),Modernizr.details||b(t).on("draw.dt",".wb-tables",function(){b(this).find("summary").removeClass("wb-init").removeClass("wb-details-inited").trigger("wb-init.wb-details")}),b(t).on("draw.dt","[data-wbtbl-draw]",function(t){var e,a,r=b(this),i=r.command(r.attr("data-wbtbl-draw")),n=t.table,o=b(n.table().node()),l=t.displayed!==t.total,s=0,d=t.total;switch(i.command){case"display":return e=r,a=n.page.info()[i.options],e.is(":input")?e.val(a):e.text(a),!0;case"count":if((l?n.rows({search:"applied"}):n.rows({page:"all"})).iterator("row",function(t,e){-1<b(this.row(e).node()).text().indexOf(i.options)&&s++}),r.is("progress")){if(r.attr({max:d,value:s}),o.hasClass("wbtbl-silent"))return!0;r.trigger("updated.wb5")}return r.text(s)}}),b("[data-wbtbl-reset]").on("click",function(){var t=this.getAttribute("data-wbtbl-reset").split("@"),e=b(t[0]).DataTable();return e.search("").columns().search(""),("all"===t[1]?e.search("").columns():e.column(t[1])).search("").draw()}),b(t).on("wb-ready.wb-tables","[data-wbtbl-tag=enable]",function(){var t=b(this),e=t.closest(".dataTables_wrapper");e.addClass("tagcloud").find(".top").append("<div data-wbtbl-tagcloud='active' aria-live='polite'></div>"),t.related(e.find("[data-wbtbl-tagcloud]").eq(0),!0),e.find("[data-wbtbl-tagcloud]").eq(0).trigger({type:"draw.dt",table:t.eq(0).dataTable().api()})}),b(t).on("draw.dt","[data-wbtbl-tagcloud]",function(t){function e(){var t=this.search();t&&(t=t.split("|"),t="<li><span class='tagitem' data-wbtbl-col-idx='"+a+"'><span class='content'>"+t.map(function(t){return t.replace(/[{()}]/g,"")}).join(" &amp; ")+"</span> <button type='button' class='close' tabindex='0' aria-label='"+i.i18n("geo-aoi-btnclear")+"'><span aria-hidden='true'>&times;</span></button></span></li>",r.find("ul").append(t)),a++}var t=t.table,a=-1,r=b(this);r.html("<ul class='list-inline tags' aria-live='polite'></ul><div class='clearfix'></div>"),e.call(t),t.columns().every(e)}),b(t).on("keypress click","[data-wbtbl-highlight]",function(t){if(!n(t))return!0;var t=b(this),e=t.command(this.getAttribute("data-wbtbl-highlight")),a=t.relatives().DataTable();t.relatives().removeClass("wbtbl-silent").addClass("wbtbl-silent"),a.column(e.selector).search(e.options,!1,!1,!0).draw()}),b(t).on("keypress click","[data-wbtbl-search]",function(t){if(!n(t))return!0;var t=b(this),e=t.command(this.getAttribute("data-wbtbl-search")),a=t.relatives().DataTable();t.relatives().removeClass("wbtbl-silent"),a.search("").columns().search(""),a.column(e.selector).search(e.options,!1,!1,!0).draw()}),b(t).on("submit","[data-wbtbl-submit]",function(){var t=b(this),e=t.command(this.getAttribute("data-wbtbl-submit")),a=t.relatives().DataTable(),r=e.selector,e=e.options;return t.relatives(),a.search("").columns().search(""),(a=r?a.column(r):a).search(t.find("input").val(),!1,e&&"smart"===e,!0).draw(),!1}),b(t).on("keypress click","[data-wbtbl-tagcloud] li button",function(t){if(!n(t))return!0;var t=b(this),e=t.closest("li"),a=e.find(".content").text(),r=parseInt(t.closest("[data-wbtbl-col-idx]").attr("data-wbtbl-col-idx")),t=t.closest(".dataTables_wrapper").find(".wb-tables").removeClass("wbtbl-silent").DataTable();(t=-1!==r?t.column(r):t).search("",!1,!1,!0).draw(),e.remove(),o(t,!1,r,a)}),b(t).on("refreshCtrl.wbtbl","table",function(t){var e,a,r,i;e=b(t.currentTarget).DataTable(),a=void 0===t.isSelected||t.isSelected,r&&i?o(e,a,r,i):e.columns().every(function(t){i=e.column(t).search(),o(e,a&&i,t,i)})}),b(t).on("submit","[data-wbtbl-post]",function(){var t=b(this);return l.call(this),t.relatives().DataTable().draw(),!!t.is("[action]")}),b(t).on("reset","[data-wbtbl-post]",function(){var i=this,n=b(i).relatives().DataTable();n.search(""),n.columns().every(function(){this.search("")}),n.draw(),setTimeout(function(){for(var t,e=i.querySelectorAll("[data-wb5-default-checked]"),a=e.length,r=0;r<a;r++)(t=e[r]).checked="false"!==t.dataset.wb5DefaultChecked;l.call(i),n.draw()},1)})})(jQuery,document,wb),((a,t)=>{a(t).on("do.wb-actionmng","table[data-wb-urlmapping][data-wb5-bind]",function(t){var e=a(t.currentTarget);e.one("draw.dt",function(){e.trigger("refreshCtrl.wbtbl")})})})(jQuery,document);