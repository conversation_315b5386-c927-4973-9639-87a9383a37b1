"""
Sample verification helpers for API response testing.
"""

from typing import Any, Dict, Optional
from tests.test_utils.verification.base import EntityVerifier


class SampleVerifier(EntityVerifier):
    """Verifier for sample entity responses"""
    
    COMMON_FIELDS = ["sample_id", "created_at"]
    DATETIME_FIELDS = ["created_at", "updated_at"]
    OPTIONAL_FIELDS = ["updated_at", "description", "sample_type", "sample_name"]
    TYPE_VALIDATIONS = {
        "sample_id": str,
        "sample_name": str,
        "description": str,
        "sample_type": str
    } 