from datetime import datetime
from typing import Optional, Literal, List
from sqlalchemy import String, Enum, DateTime, Text, ForeignKey, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
import uuid
from .base import Base
from ..common.constants import RequisitionStatus, PriorityLevel
from .requisition_sample import RequisitionSample


class Requisition(Base):
    __tablename__ = "requisitions"

    req_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lsa_number: Mapped[str] = mapped_column(String, nullable=False)
    project_number: Mapped[Optional[str]] = mapped_column(String)
    project_name: Mapped[Optional[str]] = mapped_column(String)
    status: Mapped[str] = mapped_column(
        Enum(RequisitionStatus, name='requisition_status', values_callable=lambda x: [e.value for e in x]),
        default=RequisitionStatus.SUBMITTED.value
    )
    priority: Mapped[str] = mapped_column(
        Enum(PriorityLevel, name='priority_level', values_callable=lambda x: [e.value for e in x]),
        default=PriorityLevel.MEDIUM.value
    )
    submitted_by: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey('users.user_id'))
    deadline: Mapped[Optional[datetime]] = mapped_column(DateTime)
    expected_completion_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    comments: Mapped[Optional[str]] = mapped_column(Text)
    is_archived: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, onupdate=func.now())
    lab_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey('labs.lab_id'))

    samples: Mapped[List["RequisitionSample"]] = relationship("RequisitionSample", backref="requisition", cascade="all, delete-orphan")