/*!
 * @title Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * @license wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v16.3.0 - 2025-02-20
 *
 */
((n,t,o,e)=>{var l,i="campaign-menu",h="."+i+".gcweb-menu",a="wb-init"+h,s=e.doc,d=function(){n("#mb-pnl").remove(),n("#wb-glb-mn").remove()};n(t).on("resize",function(){"A"==o.activeElement.nodeName&&(l=o.activeElement)}),s.on(e.resizeEvents,function(){if(null!=l){var t=(t=>{let e=t.parentElement;for(;"NAV"!=e.nodeName&&"BODY"!=e.nodeName;)e=e.parentElement;return"NAV"==e.nodeName&&e})(l);if(t){var e=l.getAttribute("href");if(t.classList.contains("wb-menu")&&n(".wb-menu").is(":hidden")){for(var i,a,s=n(".gcweb-menu a[aria-expanded=true]"),r=0;r<s.length;r++)n(s).eq(r).attr("aria-expanded","false");n(h).find("button").attr("aria-expanded","true"),e.match("^#")?(i=e.substring(14),null!=(i=n(".gcweb-menu a[aria-controls='gc-sub-menu-"+i+"']"))?(i.focus(),i[0].hasAttribute("aria-haspopup")&&"true"==i[0].getAttribute("aria-haspopup")&&i.attr("aria-expanded","true")):console.warn("Unable to find equivalent link in GCWeb menu top level items")):(i=n('.gcweb-menu a[href="'+e+'"]'),1<(a=n(i).parents("ul")).length&&a.eq(0).parent().children("a").attr("aria-expanded","true"),null!=i?i.focus():console.warn("Unable to find equivalent link in GCWeb menu submenu items"))}else t.classList.contains("gcweb-menu")&&n(".gcweb-menu").is(":hidden")&&(n(".wb-menu li").removeClass("active sm-open"),n(".wb-menu ul.open").attr("aria-expanded","false"),n(".wb-menu ul.open").attr("aria-hidden","true"),n(".wb-menu ul").removeClass("open"),e.match("^#")?(a=n(l).attr("aria-controls").substring(12),null!=(i=n(".wb-menu a[href='#wet-sub-menu-"+a+"']"))?i.focus():console.warn("Unable to find equivalent link in WET megamenu top level items")):(t=n('.wb-menu a[href="'+e+'"]'),1<(a=n(t).parents("ul")).length&&((i=a.eq(0)).attr("aria-expanded","true"),i.attr("aria-hidden","false"),i.addClass("open"),i.parent().addClass("active sm-open")),null!=t?t.focus():console.warn("Unable to find equivalent link in WET megamenu submenu items")))}}}),s.on("timerpoke.wb "+a,h,function(t){var r,t=e.init(t,i,h);t&&(t=n(t),1<o.querySelectorAll(".gcweb-menu").length?(console.warn(i+" - gcweb menu already exsits on the page, hiding gcweb campaign menu and aborting"),t.addClass("hidden")):null!=o.querySelector("#wb-sm")?(console.warn(i+" - megamenu already exsits on the page, aborting"),t.addClass("hidden")):n(o).on("wb-ready.wb",(r=t,function(t){var e=r.find("> ul > li"),a="",e=(n.each(e,function(t,e){var e=e.querySelector("a"),i=e.getAttribute("href"),e=e.textContent;a+=`<li><a href="${i}">${e}</a></li>`}),o.querySelector(h+" > h2")),i=r[0].getAttribute("data-megamenu-ajax"),s="";r[0].hasAttribute("data-megamenu-bg-color")&&(s=r[0].getAttribute("data-megamenu-bg-color")),a=`
                <nav id="wb-sm" class="campaign-menu wb-menu visible-md visible-lg ${s}" data-trgt="mb-pnl" data-ajax-replace="${i}">
                    <div class="pnl-strt nvbar">
                        <h2>${e.textContent}</h2>
                        <ul role="menubar" class="list-inline menu">
                            ${a}
                        </ul>
                    </div>
                </nav>`,r.addClass("visible-sm visible-xs"),n(".gcweb-menu").after('<div id="mb-pnl" hidden></div>'),n(".gcweb-menu").after('<div id="wb-glb-mn" hidden><h2> </h2></div>'),r.after(a),n(".wb-menu").trigger("wb-init.wb-menu"),n(o).on("wb-ready.wb-menu",d)})),e.ready(t,i))}),e.add(h)})(jQuery,window,document,wb),((i,a,s)=>{var r="collection-sort",n="."+r,o=s.doc,l={};o.on("collection-sort",n,function(t,s){var e=t.currentTarget;function i(){e.querySelectorAll(s.section).forEach(function(t){let e=t.querySelectorAll(s.selector),i=[],a=[];e.forEach(function(t){a.push(t.parentElement);t={elm:t,sortVal:""};i.push(t)}),s.sort.forEach(function(e){i.forEach(function(t){t.sortVal=t.elm.querySelector(e.selector).innerHTML}),"numeric"===e.type?"desc"===e.order?i.sort((t,e)=>e.sortVal-t.sortVal):i.sort((t,e)=>t.sortVal-e.sortVal):"desc"===e.order?i.sort((t,e)=>e.sortVal.localeCompare(t.sortVal)):i.sort((t,e)=>t.sortVal.localeCompare(e.sortVal))}),i.forEach(function(t,e){a[e].append(t.elm)})})}s.section&&s.selector&&s.sort&&(i(),o.on("wb-contentupdated",n,function(t,e){i()}))}),o.on("timerpoke.wb wb-init.collection-sort",n,function(t){var e,t=s.init(t,r,n);t&&(t=i(t),e=i.extend(!0,{},l,a[r],s.getData(t,r)),t.trigger("collection-sort",e),s.ready(t,r))}),s.add(n)})(jQuery,window,wb),((r,i,u)=>{var n="distance-calculator",a="."+n,t=u.doc,s={};t.on("distance-calculator",a,function(t,d){var a=t.currentTarget,s=r(a);function c(t){return t*(Math.PI/180)}s.find(d.form).on("submit",function(t){var t=t.currentTarget.querySelector(d.location).value,t=encodeURIComponent(t),i=a.querySelector(d.section).querySelectorAll(d.selector),e="fr"===u.lang?"https://geogratis.gc.ca/services/geolocation/fr/locate?q=":"https://geogratis.gc.ca/services/geolocation/en/locate?q=";return r.getJSON(e+t,function(t){var l,h,e;0==t.length?console.log("Empty response from geogratis"):(l=t[0].geometry.coordinates[0],h=t[0].geometry.coordinates[1],e=t[0].title,i.forEach(function(t){var e,i,a,s,r,n,o=t.querySelector(d.target),t=t.querySelector(d.sort);null!=o&&null!=t&&null!=o.dataset.distanceCoordinates&&(r=(s=JSON.parse(o.dataset.distanceCoordinates)).longtitude,e="fr"===u.lang?" ":",",a=l,r=r,n=c((s=s.latitude)-(i=h)),r=c(r-a),a=Math.sin(n/2)*Math.sin(n/2)+Math.cos(c(i))*Math.cos(c(s))*Math.sin(r/2)*Math.sin(r/2),n=6371*(2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a))),t.innerHTML=Math.round(n),o.innerHTML=((t,e)=>{for(var i=(t=(t+="").split("."))[0],t=1<t.length?"."+t[1]:"",a=/(\d+)(\d{3})/;a.test(i);)i=i.replace(a,"$1"+e+"$2");return i+t})(Math.round(n),e))}),null!=d.name&&a.querySelectorAll(d.name).forEach(function(t){t.innerHTML=e}),"object"==typeof d.display&&void 0!==d.display.selector&&void 0!==d.display.removeClass&&null!==d.display.selector&&null!==d.display.removeClass&&a.querySelectorAll(d.display.selector).forEach(function(t){t.classList.remove(d.display.removeClass)}),s.trigger("wb-contentupdated",[{source:n}]))}),!1})}),t.on("timerpoke.wb wb-init.distance-calculator",a,function(t){var e,t=u.init(t,n,a);t&&(t=r(t),e=r.extend(!0,{},s,i[n],u.getData(t,n)),t.trigger("distance-calculator",e),u.ready(t,n))}),u.add(a)})(jQuery,window,wb),((m,s,b)=>{function v(t){return"inline"===(t.currentStyle||s.getComputedStyle(t,"")).display.substr(0,6).toLowerCase()}function x(t,e){t.copyBtn=t.innerHTML,t.copyBtnStyle=t.getAttribute("class"),"SPAN"===t.parentNode.tagName?t.copyBtnSize=M:t.copyBtnSize="btn",e.copiedBtnStyle&&(t.copyCompleteBtnStyle=e.copiedBtnStyle),e.copiedBtn&&(t.copyCompleteBtn=e.copiedBtn),m(t).trigger("click vclick touchstart")}function a(t){c(t),g(t),f(t)}function e(t){var e,i;!0!==m(t).hasClass("wb-clipboard")||!0!=(e=t,i=s.getSelection().toString(),e=document.getElementById(e.copySegmentId),(e=!0===S(e)?e.value:e.textContent).trim()===i.trim())&&!0!==h||(a(t),h=!1)}function y(t){t.focus(),!0===S(t)?t.select():s.getSelection().selectAllChildren(t)}async function i(t){if(!0===await async function(t){var e,t=document.getElementById(t.copySegmentId);if(y(t),h=!0,document.execCommand("Copy"))return!0;if(!0===await navigator.permissions.query({name:"clipboard-write"}).then(function(t){return"granted"==t.state||"prompt"==t.state})){t=new Blob([t.innerHTML],{type:"text/html"});try{return e=new ClipboardItem({[t.type]:t}),navigator.clipboard.write([e]),!0}catch(t){}}return!1}(document.getElementById(t)))return s.getSelection(),!0}var r="wb-clipboard",n="."+r,_=r+"-config",t=b.doc,w=r+"-shell",o=r+"-btn",l="copied",M="btn-xs wb-clipboard-btn-inline",h=!1,k=!0,d={i18n:{en:{clipStart:"Start of clipboard text",clipEnd:"End of clipboard text",copiedStart:"Start of copied text",copiedEnd:"End of copied text",copyBtnTxt:"Copy",copyCompleteBtnTxt:"Copied"},fr:{clipStart:"D&eacute;but du texte du presse-papiers",clipEnd:"Fin du texte du presse-papiers",copiedStart:"D&eacute;but du texte copi&eacute;",copiedEnd:"Fin du texte copi&eacute;",copyBtnTxt:"Copier",copyCompleteBtnTxt:"Copi&eacute;"}},btnAlign:"none",copyBtnStyle:"mrgn-tp-sm btn-default "+o,copiedBtnStyle:"mrgn-tp-sm "+o+" btn-success"},S=function(t){return"TEXTAREA"===t.tagName||"INPUT"===t.tagName&&!0===["text","search","url","tel","password"].includes(t.type)},c=function(t){m(".wb-clipboard").each(function(){void 0!==t&&t===this?!0===t.addcopiedSectionStyle&&m(t).addClass(l):m(this).removeClass(l)})},u=function(t,e){t.innerHTML!==e&&(t.innerHTML=e)},g=function(t){m("."+w).each(function(){void 0!==t&&t.id===this.copyAreaId?u(this,this.copiedTxt):u(this,this.clipTxt)})},p=function(t,e,i){("resetBtn"in e==!1||"resetBtn"in e==!1&&void 0!==i&&i!==t)&&(m(t).removeAttr("class"),m(t).addClass(t.copyBtnStyle),m(t).html(t.copyBtn),m(t).parent().attr("aria-live","off"))},f=function(i){m("."+o).each(function(){var t,e=b.getData(m(this),"wb-clipboard");void 0!==i&&i.id===e.copyAreaId?(m(t=this).parent().attr("aria-live","polite"),t.copyCompleteBtn?m(t).html(t.copyCompleteBtn):m(t).html("<span class='glyphicon glyphicon-ok mrgn-rght-sm'></span>"+b.escapeAttribute(d.i18n.copyCompleteBtnTxt)),m(t).removeAttr("class"),m(t).addClass(t.copyBtnSize),t.copyCompleteBtnStyle?m(t).addClass(t.copyCompleteBtnStyle):m(t).addClass(d.copiedBtnStyle)):p(this,e)})};t.on("wb-clipboard",n,function(t,e){var i,t=t.currentTarget,a=m(t),s=a.data(_).i18n,r=b.getId(),n=s.clipStart,o=s.copiedStart,l=b.getId(),h=b.getId(),d=s.clipEnd,c=s.copiedEnd,u="div",g="",p="",f="btn";if(t.hasAttribute("id")||(t.id=b.getId()),"resetFocusOut"in e==!0&&!0===e.resetFocusOut&&(k=!0),"PRE"===t.tagName||"border"in e!=!1&&!0!==e.border?t.addcopiedSectionStyle=!1:(!0===v(t)?a.addClass("copyarea-inline"):a.addClass("copyarea wb-clipboard-init"),t.addcopiedSectionStyle=!0),!0===S(t)?t.copySegmentId=t.id:(a.wrapInner('<span id="'+l+'"></span>'),t.copySegmentId=l,"noTextStyle"in e!=!1&&!1!==e.noTextStyle||m("#"+l).addClass("wb-clipboard-text"),e.clipStartText&&(n=e.clipStartText),e.copiedStartText&&(o=e.copiedStartText),a.prepend('<span id="'+r+'" class="wb-inv '+w+'">'+b.escapeAttribute(n)+"</span>"),document.getElementById(r).copyAreaId=t.id,document.getElementById(r).clipTxt=b.escapeAttribute(d),document.getElementById(r).copiedTxt=b.escapeAttribute(o),e.clipEndText&&(d=e.clipEndText),e.copiedEndText&&(c=e.copiedEndText),a.append('<span id="'+h+'" class="wb-inv '+w+'">'+b.escapeAttribute(d)+"</span>"),document.getElementById(h).copyAreaId=t.id,document.getElementById(h).clipTxt=b.escapeAttribute(d),document.getElementById(h).copiedTxt=b.escapeAttribute(c)),"noButton"in e==!1){switch(n=b.getId(),e.btnAlign){case"left":i="text-left ";break;case"right":i="text-right ";break;case"center":i="text-center ";break;default:i=""}!0===v(t)&&(u="span",g=" mrgn-lft-md",p=' class="'+(f=M)+'"'),r="<"+u+' class="'+i+g+'"/><button'+p+' type="button" aria-live="polite" id="'+n+'" data-wb-clipboard=\'{"copyAreaId": "'+t.id+"\"}'></button></"+u+">",!0===S(t)?a.after(r):a.append(r),m("#"+n).addClass(e.copyBtnStyle+" "+f),o=e.copyBtn||"<span class='glyphicon glyphicon-copy mrgn-rght-sm'></span>"+b.escapeAttribute(s.copyBtnTxt),m("#"+n).html(o),x(document.getElementById(n),e)}("selectAllContent"in e==!0&&!0===e.selectAllContent||"selectAllContent"in e==!1&&"noButton"in e==!0)&&m("#"+l).on("click vclick touchstart",function(){y(document.getElementById(l))})}),m("body").on("copy","*",function(t){e(t.currentTarget)}),t.on("focus",n+"[contenteditable]",function(){var t=m(this);t.data("before",t.html())}).on("blur keyup paste input",n+"[contenteditable]",function(){var t=m(this);t.data("before")!==t.html()&&(t.data("before",t.html()),t.trigger("change"))}),t.on("change",n,function(t){e(t.currentTarget)}),t.on("click","."+o,async function(t){var e=b.getData(m(t.currentTarget),"wb-clipboard");e.resetBtn?m(t.currentTarget).trigger("reset"+n):e.copyAreaId&&!0===await i(e.copyAreaId)&&!0===k&&m(":focus").one("focusout",function(t){t.stopPropagation(),document.getSelection().removeAllRanges(),a()})}),t.on("reset"+n,function(e){m("."+o).each(function(){var t=b.getData(m(this),"wb-clipboard");p(this,t,e.target)}),m("."+w).each(function(){u(this,this.clipTxt)}),m(".wb-clipboard").each(function(){m(this).removeClass(l)}),document.getSelection().removeAllRanges()}),t.on("timerpoke.wb wb-init.wb-clipboard",n,function(t){var e,i,a,t=b.init(t,r,n);t&&(e=m(t),i=t.id,d.i18n[b.lang]&&(d.i18n=d.i18n[b.lang]),(t=b.getData(e,r))&&t.i18n&&(t.i18n=m.extend({},d.i18n,t.i18n)),(t=m.extend({},d,t)).defaultIfNone&&!m.isArray(t.defaultIfNone)&&(t.defaultIfNone=[t.defaultIfNone]),a=m.extend(!0,{},d,s[r],b.getData(e,r)),e.data(_,t),m("."+o).each(function(){var t=b.getData(m(this),"wb-clipboard");void 0!==t.copyAreaId&&t.copyAreaId===i?x(this,b.getData(m("#"+t.copyAreaId),"wb-clipboard")):void 0!==t.resetBtn&&!0===t.resetBtn&&m(this).trigger("click vclick touchstart"),void 0!==t.resetFocusOut&&!0===t.resetFocusOut&&(k=!0)}),e.trigger("wb-clipboard",a),b.ready(e,r))}),b.add(n)})(jQuery,window,wb),((t,e)=>{"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Chart=e()})(this,function(){var a=Object.freeze({__proto__:null,get Colors(){return xr},get Decimation(){return wr},get Filler(){return zr},get Legend(){return Nr},get SubTitle(){return $r},get Title(){return Hr},get Tooltip(){return an}});function t(){}let F=(()=>{let t=0;return()=>t++})();function S(t){return null==t}function A(t){return!(!Array.isArray||!Array.isArray(t))||"[object"===(t=Object.prototype.toString.call(t)).slice(0,7)&&"Array]"===t.slice(-6)}function T(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function f(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function u(t,e){return f(t)?t:e}function O(t,e){return void 0===t?e:t}let B=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:+t/e,V=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function c(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function k(t,e,i,a){let s,r,n;if(A(t))if(r=t.length,a)for(s=r-1;0<=s;s--)e.call(i,t[s],s);else for(s=0;s<r;s++)e.call(i,t[s],s);else if(T(t))for(n=Object.keys(t),r=n.length,s=0;s<r;s++)e.call(i,t[n[s]],n[s])}function N(t,e){let i,a,s,r;if(!t||!e||t.length!==e.length)return!1;for(i=0,a=t.length;i<a;++i)if(s=t[i],r=e[i],s.datasetIndex!==r.datasetIndex||s.index!==r.index)return!1;return!0}function W(e){if(A(e))return e.map(W);if(T(e)){var i=Object.create(null),a=Object.keys(e),s=a.length;let t=0;for(;t<s;++t)i[a[t]]=W(e[a[t]]);return i}return e}function H(t){return-1===["__proto__","prototype","constructor"].indexOf(t)}function j(t,e,i,a){var s;H(t)&&(s=e[t],i=i[t],T(s)&&T(i)?$(s,i,a):e[t]=W(i))}function $(a,t,s){var e=A(t)?t:[t],i=e.length;if(T(a)){var r,n=(s=s||{}).merger||j;for(let t=0;t<i;++t)if(T(r=e[t])){let i=Object.keys(r);for(let t=0,e=i.length;t<e;++t)n(i[t],a,r,s)}}return a}function U(t,e){return $(t,e,{merger:Y})}function Y(t,e,i){var a;H(t)&&(a=e[t],i=i[t],T(a)&&T(i)?U(a,i):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=W(i)))}let q={"":t=>t,x:t=>t.x,y:t=>t.y};function X(t){var e=t.split("."),i=[];let a="";for(let t of e)a+=t,a=a.endsWith("\\")?a.slice(0,-1)+".":(i.push(a),"");return i}function p(t,e){return(q[e]||(q[e]=(()=>{let i=X(e);return t=>{for(var e of i){if(""===e)break;t=t&&t[e]}return t}})()))(t)}function K(t){return t.charAt(0).toUpperCase()+t.slice(1)}let G=t=>void 0!==t,g=t=>"function"==typeof t,J=(t,e)=>{if(t.size!==e.size)return!1;for(var i of t)if(!e.has(i))return!1;return!0};function Q(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}let P=Math.PI,x=2*P,Z=x+P,tt=Number.POSITIVE_INFINITY,et=P/180,C=P/2,it=P/4,at=2*P/3,o=Math.log10,y=Math.sign;function st(t,e,i){return Math.abs(t-e)<i}function rt(t){var e=Math.round(t),e=(t=st(t,e,t/1e3)?e:t,Math.pow(10,Math.floor(o(t)))),t=t/e;return(t<=1?1:t<=2?2:t<=5?5:10)*e}function nt(t){var e=[],i=Math.sqrt(t);let a;for(a=1;a<i;a++)t%a==0&&(e.push(a),e.push(t/a));return i===(0|i)&&e.push(i),e.sort((t,e)=>t-e).pop(),e}function ot(t){return!isNaN(parseFloat(t))&&isFinite(t)}function lt(t,e){var i=Math.round(t);return i-e<=t&&t<=i+e}function ht(t,e,i){let a,s,r;for(a=0,s=t.length;a<s;a++)r=t[a][i],isNaN(r)||(e.min=Math.min(e.min,r),e.max=Math.max(e.max,r))}function E(t){return t*(P/180)}function dt(t){return t*(180/P)}function ct(i){if(f(i)){let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}}function ut(t,e){var i=e.x-t.x,e=e.y-t.y,t=Math.sqrt(i*i+e*e);let a=Math.atan2(e,i);return a<-.5*P&&(a+=x),{angle:a,distance:t}}function gt(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function pt(t,e){return(t-e+Z)%x-P}function v(t){return(t%x+x)%x}function ft(t,e,i,a){var t=v(t),e=v(e),i=v(i),s=v(e-t),r=v(i-t),n=v(t-e),o=v(t-i);return t===e||t===i||a&&e===i||r<s&&n<o}function D(t,e,i){return Math.max(e,Math.min(i,t))}function mt(t){return D(t,-32768,32767)}function d(t,e,i,a=1e-6){return t>=Math.min(e,i)-a&&t<=Math.max(e,i)+a}function bt(e,i,t){t=t||(t=>e[t]<i);let a,s=e.length-1,r=0;for(;1<s-r;)t(a=r+s>>1)?r=a:s=a;return{lo:r,hi:s}}let m=(i,a,s,t)=>bt(i,s,t?t=>{var e=i[t][a];return e<s||e===s&&i[t+1][a]===s}:t=>i[t][a]<s),vt=(e,i,a)=>bt(e,a,t=>e[t][i]>=a);function xt(t,e,i){let a=0,s=t.length;for(;a<s&&t[a]<e;)a++;for(;s>a&&t[s-1]>i;)s--;return 0<a||s<t.length?t.slice(a,s):t}let yt=["push","pop","shift","splice","unshift"];function _t(s,t){s._chartjs?s._chartjs.listeners.push(t):(Object.defineProperty(s,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),yt.forEach(t=>{let i="_onData"+K(t),a=s[t];Object.defineProperty(s,t,{configurable:!0,enumerable:!1,value(...e){var t=a.apply(this,e);return s._chartjs.listeners.forEach(t=>{"function"==typeof t[i]&&t[i](...e)}),t}})}))}function wt(e,t){var i=e._chartjs;i&&(-1!==(t=(i=i.listeners).indexOf(t))&&i.splice(t,1),0<i.length||(yt.forEach(t=>{delete e[t]}),delete e._chartjs))}function Mt(t){var e=new Set(t);return e.size===t.length?t:Array.from(e)}let kt="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function St(e,i){let a,s=!1;return function(...t){a=t,s||(s=!0,kt.call(window,()=>{s=!1,e.apply(i,a)}))}}function Pt(e,i){let a;return function(...t){return i?(clearTimeout(a),a=setTimeout(e,i,t)):e.apply(this,t),i}}let Ct=t=>"start"===t?"left":"end"===t?"right":"center",L=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2,Dt=(t,e,i,a)=>t===(a?"left":"right")?i:"center"===t?(e+i)/2:e;function At(t,e,i){var a,s,r,n,o,l,h=e.length;let d=0,c=h;return t._sorted&&({iScale:t,_parsed:a}=t,s=t.axis,{min:r,max:n,minDefined:o,maxDefined:l}=t.getUserBounds(),o&&(d=D(Math.min(m(a,s,r).lo,i?h:m(e,s,t.getPixelForValue(r)).lo),0,h-1)),c=l?D(Math.max(m(a,t.axis,n,!0).hi+1,i?0:m(e,s,t.getPixelForValue(n),!0).hi+1),d,h)-d:h-d),{start:d,count:c}}function Tt(t){var{xScale:e,yScale:i,_scaleRanges:a}=t,s={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};return a?(e=a.xmin!==e.min||a.xmax!==e.max||a.ymin!==i.min||a.ymax!==i.max,Object.assign(a,s),e):(t._scaleRanges=s,!0)}var r=new class{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,i,a,t){let s=i.listeners[t],r=i.duration;s.forEach(t=>t({chart:e,initial:i.initial,numSteps:r,currentStep:Math.min(a-i.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=kt.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(n=Date.now()){let o=0;this._charts.forEach((a,s)=>{if(a.running&&a.items.length){var r=a.items;let t,e=r.length-1,i=!1;for(;0<=e;--e)(t=r[e])._active?(t._total>a.duration&&(a.duration=t._total),t.tick(n),i=!0):(r[e]=r[r.length-1],r.pop());i&&(s.draw(),this._notify(s,a,n,"progress")),r.length||(a.running=!1,this._notify(s,a,n,"complete"),a.initial=!1),o+=r.length}}),this._lastDate=n,0===o&&(this._running=!1)}_getAnims(t){var e=this._charts;let i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return 0<this._getAnims(t).items.length}start(t){t=this._charts.get(t);t&&(t.running=!0,t.start=Date.now(),t.duration=t.items.reduce((t,e)=>Math.max(t,e._duration),0),this._refresh())}running(t){return!!this._running&&!!((t=this._charts.get(t))&&t.running&&t.items.length)}stop(e){var i=this._charts.get(e);if(i&&i.items.length){var a=i.items;let t=a.length-1;for(;0<=t;--t)a[t].cancel();i.items=[],this._notify(e,i,Date.now(),"complete")}}remove(t){return this._charts.delete(t)}};function Ot(t){return t+.5|0}let Et=(t,e,i)=>Math.max(Math.min(t,i),e);function Lt(t){return Et(Ot(2.55*t),0,255)}function It(t){return Et(Ot(255*t),0,255)}function n(t){return Et(Ot(t/2.55)/100,0,1)}function Rt(t){return Et(Ot(100*t),0,100)}let l={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},zt=[..."0123456789ABCDEF"],Ft=t=>zt[15&t],Bt=t=>zt[(240&t)>>4]+zt[15&t],Vt=t=>(240&t)>>4==(15&t);let Nt=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Wt(i,t,a){let s=t*Math.min(a,1-a),e=(t,e=(t+i/30)%12)=>a-s*Math.max(Math.min(e-3,9-e,1),-1);return[e(0),e(8),e(4)]}function Ht(i,a,s){var t=(t,e=(t+i/60)%6)=>s-s*a*Math.max(Math.min(e,4-e,1),0);return[t(5),t(3),t(1)]}function jt(t,e,i){var a=Wt(t,1,.5);let s;for(1<e+i&&(e*=s=1/(e+i),i*=s),s=0;s<3;s++)a[s]*=1-e-i,a[s]+=e;return a}function $t(t){var e=t.r/255,i=t.g/255,t=t.b/255,a=Math.max(e,i,t),s=Math.min(e,i,t),r=(a+s)/2;let n,o,l;return a!==s&&(l=a-s,o=.5<r?l/(2-a-s):l/(a+s),n=60*(n=(s=i,i=t,(t=e)===a?(s-i)/l+(s<i?6:0):s===a?(i-t)/l+2:(t-s)/l+4))+.5),[0|n,o||0,r]}function Ut(t,e,i,a){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,a)).map(It)}function Yt(t,e,i){return Ut(Wt,t,e,i)}function qt(t){return(t%360+360)%360}let Xt={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Kt={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},Gt;function Jt(t){Gt||((Gt=(()=>{var t={},e=Object.keys(Kt),i=Object.keys(Xt);let a,s,r,n,o;for(a=0;a<e.length;a++){for(n=o=e[a],s=0;s<i.length;s++)r=i[s],o=o.replace(r,Xt[r]);r=parseInt(Kt[n],16),t[o]=[r>>16&255,r>>8&255,255&r]}return t})()).transparent=[0,0,0,0]);t=Gt[t.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:4===t.length?t[3]:255}}let Qt=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,Zt=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,te=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function ee(e,i,a){if(e){let t=$t(e);t[i]=Math.max(0,Math.min(t[i]+t[i]*a,0===i?360:1)),t=Yt(t),e.r=t[0],e.g=t[1],e.b=t[2]}}function ie(t,e){return t&&Object.assign(e||{},t)}function ae(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?3<=t.length&&(e={r:t[0],g:t[1],b:t[2],a:255},3<t.length)&&(e.a=It(t[3])):(e=ie(t,{r:0,g:0,b:0,a:1})).a=It(e.a),e}function se(t){return("r"===t.charAt(0)?t=>{var e=Qt.exec(t);let i,a,s,r=255;if(e){if(void 0!==e[7]){let t=+e[7];r=e[8]?Lt(t):Et(255*t,0,255)}return i=+e[1],a=+e[3],s=+e[5],{r:255&(e[2]?Lt(i):Et(i,0,255)),g:255&(e[4]?Lt(a):Et(a,0,255)),b:255&(e[6]?Lt(s):Et(s,0,255)),a:r}}}:t=>{var e,i,a,t=Nt.exec(t);let s,r=255;if(t)return void 0!==t[5]&&(r=(t[6]?Lt:It)(+t[5])),e=qt(+t[2]),i=+t[3]/100,a=+t[4]/100,{r:(s="hwb"===t[1]?Ut(jt,e,i,a):"hsv"===t[1]?Ut(Ht,e,i,a):Yt(e,i,a))[0],g:s[1],b:s[2],a:r}})(t)}class re{constructor(t){if(t instanceof re)return t;var e,i,a=typeof t;let s;"object"==a?s=ae(t):"string"==a&&(i=(a=t).length,"#"===a[0]&&(4===i||5===i?e={r:255&17*l[a[1]],g:255&17*l[a[2]],b:255&17*l[a[3]],a:5===i?17*l[a[4]]:255}:7!==i&&9!==i||(e={r:l[a[1]]<<4|l[a[2]],g:l[a[3]]<<4|l[a[4]],b:l[a[5]]<<4|l[a[6]],a:9===i?l[a[7]]<<4|l[a[8]]:255})),s=e||Jt(t)||se(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=ie(this._rgb);return t&&(t.a=n(t.a)),t}set rgb(t){this._rgb=ae(t)}rgbString(){return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${n(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0;var t}hexString(){return this._valid&&(t=this._rgb,e=t,e=Vt(e.r)&&Vt(e.g)&&Vt(e.b)&&Vt(e.a)?Ft:Bt,t)?"#"+e(t.r)+e(t.g)+e(t.b)+((t=t.a)<255?e(t):""):void 0;var t,e}hslString(){if(this._valid){var t,e,i,a=this._rgb;if(a)return t=(i=$t(a))[0],e=Rt(i[1]),i=Rt(i[2]),a.a<255?`hsla(${t}, ${e}%, ${i}%, ${n(a.a)})`:`hsl(${t}, ${e}%, ${i}%)`}}mix(t,e){var i,a,s;return t&&(i=this.rgb,t=t.rgb,s=i.a-t.a,i.r=255&(a=(1+((a=2*(e=void 0===e?.5:e)-1)*s==-1?a:(a+s)/(1+a*s)))/2)*i.r+(s=1-a)*t.r+.5,i.g=255&a*i.g+s*t.g+.5,i.b=255&a*i.b+s*t.b+.5,i.a=e*i.a+(1-e)*t.a,this.rgb=i),this}interpolate(t,e){return t&&(this._rgb=(i=this._rgb,t=t._rgb,e=e,a=te(n(i.r)),s=te(n(i.g)),r=te(n(i.b)),{r:It(Zt(a+e*(te(n(t.r))-a))),g:It(Zt(s+e*(te(n(t.g))-s))),b:It(Zt(r+e*(te(n(t.b))-r))),a:i.a+e*(t.a-i.a)})),this;var i,a,s,r}clone(){return new re(this.rgb)}alpha(t){return this._rgb.a=It(t),this}clearer(t){return this._rgb.a*=1-t,this}greyscale(){var t=this._rgb,e=Ot(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){return this._rgb.a*=1+t,this}negate(){var t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return ee(this._rgb,2,t),this}darken(t){return ee(this._rgb,2,-t),this}saturate(t){return ee(this._rgb,1,t),this}desaturate(t){return ee(this._rgb,1,-t),this}rotate(t){return e=this._rgb,t=t,(i=$t(e))[0]=qt(i[0]+t),i=Yt(i),e.r=i[0],e.g=i[1],e.b=i[2],this;var e,i}}function ne(t){return!(!t||"object"!=typeof t||"[object CanvasPattern]"!==(t=t.toString())&&"[object CanvasGradient]"!==t)}function oe(t){return ne(t)?t:new re(t)}function le(t){return ne(t)?t:new re(t).saturate(.5).darken(.1).hexString()}let he=["x","y","borderWidth","radius","tension"],de=["color","borderColor","backgroundColor"],ce=new Map;function ue(t,e,s){return((t,e)=>{e=s||{};var i=t+JSON.stringify(e);let a=ce.get(i);return a||(a=new Intl.NumberFormat(t,e),ce.set(i,a)),a})(e).format(t)}let ge={values:t=>A(t)?t:""+t,numeric(e,t,i){if(0===e)return"0";var a=this.chart.options.locale;let s,r=e;if(1<i.length){let t=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(t<1e-4||1e15<t)&&(s="scientific"),r=(t=>{let e=3<i.length?i[2].value-i[1].value:i[1].value-i[0].value;return e=1<=Math.abs(e)&&t!==Math.floor(t)?t-Math.floor(t):e})(e)}var n=o(Math.abs(r)),n=isNaN(n)?1:Math.max(Math.min(-1*Math.floor(n),20),0),n={notation:s,minimumFractionDigits:n,maximumFractionDigits:n};return Object.assign(n,this.options.ticks.format),ue(e,a,n)},logarithmic(t,e,i){var a;return 0===t?"0":(a=i[e].significand||t/Math.pow(10,Math.floor(o(t))),[1,2,3,5,10,15].includes(a)||e>.8*i.length?ge.numeric.call(this,t,e,i):"")}};var pe={formatters:ge};let fe=Object.create(null),me=Object.create(null);function be(i,t){if(t){var a=t.split(".");for(let t=0,e=a.length;t<e;++t){var s=a[t];i=i[s]||(i[s]=Object.create(null))}}return i}function ve(t,e,i){return"string"==typeof e?$(be(t,e),i):$(be(t,""),e)}var I=new class{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>le(e.backgroundColor),this.hoverBorderColor=(t,e)=>le(e.borderColor),this.hoverColor=(t,e)=>le(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return ve(this,t,e)}get(t){return be(this,t)}describe(t,e){return ve(me,t,e)}override(t,e){return ve(fe,t,e)}route(t,e,i,a){let s=be(this,t),r=be(this,i),n="_"+e;Object.defineProperties(s,{[n]:{value:s[e],writable:!0},[e]:{enumerable:!0,get(){var t=this[n],e=r[a];return T(t)?Object.assign({},e,t):O(t,e)},set(t){this[n]=t}}})}apply(t){t.forEach(t=>t(this))}}({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:de},numbers:{type:"number",properties:he}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:pe.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function xe(){return"undefined"!=typeof window&&"undefined"!=typeof document}function ye(t){let e=t.parentNode;return e=e&&"[object ShadowRoot]"===e.toString()?e.host:e}function _e(t,e,i){let a;return"string"==typeof t?(a=parseInt(t,10),-1!==t.indexOf("%")&&(a=a/100*e.parentNode[i])):a=t,a}let we=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);function Me(t,e){return we(t).getPropertyValue(e)}let ke=["top","right","bottom","left"];function Se(e,i,a){var s={};a=a?"-"+a:"";for(let t=0;t<4;t++){var r=ke[t];s[r]=parseFloat(e[i+"-"+r+a])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}function Pe(t,e){if("native"in t)return t;var{canvas:i,currentDevicePixelRatio:a}=e,s=we(i),r="border-box"===s.boxSizing,n=Se(s,"padding"),s=Se(s,"border","width"),{x:t,y:o,box:l}=((t,e)=>{var i,a=t.touches,{offsetX:s,offsetY:r}=a=a&&a.length?a[0]:t;let n,o,l=!1;if(i=r,t=t.target,!(0<s||0<i)||t&&t.shadowRoot){let t=e.getBoundingClientRect();n=a.clientX-t.left,o=a.clientY-t.top,l=!0}else n=s,o=r;return{x:n,y:o,box:l}})(t,i),h=n.left+(l&&s.left),l=n.top+(l&&s.top);let{width:d,height:c}=e;return r&&(d-=n.width+s.width,c-=n.height+s.height),{x:Math.round((t-h)/d*i.width/a),y:Math.round((o-l)/c*i.height/a)}}let Ce=t=>Math.round(10*t)/10;function De(t,e,i,a){var s=we(t),r=Se(s,"margin"),n=_e(s.maxWidth,t,"clientWidth")||tt,o=_e(s.maxHeight,t,"clientHeight")||tt,t=((t,s,r)=>{let n,o;if(void 0===s||void 0===r){var l=ye(t);if(l){let t=l.getBoundingClientRect(),e=we(l),i=Se(e,"border","width"),a=Se(e,"padding");s=t.width-a.width-i.width,r=t.height-a.height-i.height,n=_e(e.maxWidth,l,"clientWidth"),o=_e(e.maxHeight,l,"clientHeight")}else s=t.clientWidth,r=t.clientHeight}return{width:s,height:r,maxWidth:n||tt,maxHeight:o||tt}})(t,e,i);let{width:l,height:h}=t;if("content-box"===s.boxSizing){let t=Se(s,"border","width"),e=Se(s,"padding");l-=e.width+t.width,h-=e.height+t.height}return l=Math.max(0,l-r.width),h=Math.max(0,a?l/a:h-r.height),l=Ce(Math.min(l,n,t.maxWidth)),h=Ce(Math.min(h,o,t.maxHeight)),l&&!h&&(h=Ce(l/2)),(void 0!==e||void 0!==i)&&a&&t.height&&h>t.height&&(h=t.height,l=Ce(Math.floor(h*a))),{width:l,height:h}}function Ae(t,e,i){var e=e||1,a=Math.floor(t.height*e),s=Math.floor(t.width*e),r=(t.height=Math.floor(t.height),t.width=Math.floor(t.width),t.canvas);return r.style&&(i||!r.style.height&&!r.style.width)&&(r.style.height=t.height+"px",r.style.width=t.width+"px"),(t.currentDevicePixelRatio!==e||r.height!==a||r.width!==s)&&(t.currentDevicePixelRatio=e,r.height=a,r.width=s,t.ctx.setTransform(e,0,0,e,0,0),!0)}var Te=(()=>{let t=!1;try{var e={get passive(){return!(t=!0)}};xe()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t})();function Oe(t,e){t=Me(t,e),e=t&&t.match(/^(\d+)(\.\d+)?px$/);return e?+e[1]:void 0}function Ee(t){return!t||S(t.size)||S(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}function Le(t,e,i,a,s){let r=e[s];return r||(r=e[s]=t.measureText(s).width,i.push(s)),a=r>a?r:a}function Ie(t,e,i,a){let s=(a=a||{}).data=a.data||{},r=a.garbageCollect=a.garbageCollect||[],n=(a.font!==e&&(s=a.data={},r=a.garbageCollect=[],a.font=e),t.save(),t.font=e,0);var o=i.length;let l,h,d,c,u;for(l=0;l<o;l++)if(null==(c=i[l])||A(c)){if(A(c))for(h=0,d=c.length;h<d;h++)null==(u=c[h])||A(u)||(n=Le(t,s,r,n,u))}else n=Le(t,s,r,n,c);t.restore();var g=r.length/2;if(g>i.length){for(l=0;l<g;l++)delete s[r[l]];r.splice(0,g)}return n}function Re(t,e,i){t=t.currentDevicePixelRatio,i=0!==i?Math.max(i/2,.5):0;return Math.round((e-i)*t)/t+i}function ze(t,e){(e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore()}function Fe(t,e,i,a){Be(t,e,i,a,null)}function Be(t,e,i,a,s){let r,n,o,l,h,d,c,u;var g=e.pointStyle,p=e.rotation,f=e.radius;let m=(p||0)*et;if(g&&"object"==typeof g&&("[object HTMLImageElement]"===(r=g.toString())||"[object HTMLCanvasElement]"===r))t.save(),t.translate(i,a),t.rotate(m),t.drawImage(g,-g.width/2,-g.height/2,g.width,g.height),t.restore();else if(!(isNaN(f)||f<=0)){switch(t.beginPath(),g){default:s?t.ellipse(i,a,s/2,f,0,0,x):t.arc(i,a,f,0,x),t.closePath();break;case"triangle":d=s?s/2:f,t.moveTo(i+Math.sin(m)*d,a-Math.cos(m)*f),m+=at,t.lineTo(i+Math.sin(m)*d,a-Math.cos(m)*f),m+=at,t.lineTo(i+Math.sin(m)*d,a-Math.cos(m)*f),t.closePath();break;case"rectRounded":h=.516*f,l=f-h,n=Math.cos(m+it)*l,c=Math.cos(m+it)*(s?s/2-h:l),o=Math.sin(m+it)*l,u=Math.sin(m+it)*(s?s/2-h:l),t.arc(i-c,a-o,h,m-P,m-C),t.arc(i+u,a-n,h,m-C,m),t.arc(i+c,a+o,h,m,m+C),t.arc(i-u,a+n,h,m+C,m+P),t.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*f,d=s?s/2:l,t.rect(i-d,a-l,2*d,2*l);break}m+=it;case"rectRot":c=Math.cos(m)*(s?s/2:f),n=Math.cos(m)*f,o=Math.sin(m)*f,u=Math.sin(m)*(s?s/2:f),t.moveTo(i-c,a-o),t.lineTo(i+u,a-n),t.lineTo(i+c,a+o),t.lineTo(i-u,a+n),t.closePath();break;case"crossRot":m+=it;case"cross":c=Math.cos(m)*(s?s/2:f),n=Math.cos(m)*f,o=Math.sin(m)*f,u=Math.sin(m)*(s?s/2:f),t.moveTo(i-c,a-o),t.lineTo(i+c,a+o),t.moveTo(i+u,a-n),t.lineTo(i-u,a+n);break;case"star":c=Math.cos(m)*(s?s/2:f),n=Math.cos(m)*f,o=Math.sin(m)*f,u=Math.sin(m)*(s?s/2:f),t.moveTo(i-c,a-o),t.lineTo(i+c,a+o),t.moveTo(i+u,a-n),t.lineTo(i-u,a+n),m+=it,c=Math.cos(m)*(s?s/2:f),n=Math.cos(m)*f,o=Math.sin(m)*f,u=Math.sin(m)*(s?s/2:f),t.moveTo(i-c,a-o),t.lineTo(i+c,a+o),t.moveTo(i+u,a-n),t.lineTo(i-u,a+n);break;case"line":n=s?s/2:Math.cos(m)*f,o=Math.sin(m)*f,t.moveTo(i-n,a-o),t.lineTo(i+n,a+o);break;case"dash":t.moveTo(i,a),t.lineTo(i+Math.cos(m)*(s?s/2:f),a+Math.sin(m)*f);break;case!1:t.closePath()}t.fill(),0<e.borderWidth&&t.stroke()}}function _(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function Ve(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function Ne(t){t.restore()}function We(e,i,a,t,s){if(!i)return e.lineTo(a.x,a.y);if("middle"===s){let t=(i.x+a.x)/2;e.lineTo(t,i.y),e.lineTo(t,a.y)}else"after"===s!=!!t?e.lineTo(i.x,a.y):e.lineTo(a.x,i.y);e.lineTo(a.x,a.y)}function He(t,e,i,a){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(a?e.cp1x:e.cp2x,a?e.cp1y:e.cp2y,a?i.cp2x:i.cp1x,a?i.cp2y:i.cp1y,i.x,i.y)}function je(t,e,i,a,s,r={}){var n,o,l,h,d,c,u,g,p=A(e)?e:[e],f=0<r.strokeWidth&&""!==r.strokeColor;let m,b;for(t.save(),t.font=s.string,e=t,(g=r).translation&&e.translate(g.translation[0],g.translation[1]),S(g.rotation)||e.rotate(g.rotation),g.color&&(e.fillStyle=g.color),g.textAlign&&(e.textAlign=g.textAlign),g.textBaseline&&(e.textBaseline=g.textBaseline),m=0;m<p.length;++m)b=p[m],r.backdrop&&(d=t,c=r.backdrop,u=void 0,u=d.fillStyle,d.fillStyle=c.color,d.fillRect(c.left,c.top,c.width,c.height),d.fillStyle=u),f&&(r.strokeColor&&(t.strokeStyle=r.strokeColor),S(r.strokeWidth)||(t.lineWidth=r.strokeWidth),t.strokeText(b,i,a,r.maxWidth)),t.fillText(b,i,a,r.maxWidth),c=t,d=i,u=a,n=b,h=l=void 0,((o=r).strikethrough||o.underline)&&(l=d-(n=c.measureText(n)).actualBoundingBoxLeft,d=d+n.actualBoundingBoxRight,h=u+n.actualBoundingBoxDescent,u=o.strikethrough?(u-n.actualBoundingBoxAscent+h)/2:h,c.strokeStyle=c.fillStyle,c.beginPath(),c.lineWidth=o.decorationWidth||2,c.moveTo(l,u),c.lineTo(d,u),c.stroke()),a+=Number(s.lineHeight);t.restore()}function $e(t,e){var{x:e,y:i,w:a,h:s,radius:r}=e;t.arc(e+r.topLeft,i+r.topLeft,r.topLeft,1.5*P,P,!0),t.lineTo(e,i+s-r.bottomLeft),t.arc(e+r.bottomLeft,i+s-r.bottomLeft,r.bottomLeft,P,C,!0),t.lineTo(e+a-r.bottomRight,i+s),t.arc(e+a-r.bottomRight,i+s-r.bottomRight,r.bottomRight,C,0,!0),t.lineTo(e+a,i+r.topRight),t.arc(e+a-r.topRight,i+r.topRight,r.topRight,0,-C,!0),t.lineTo(e+r.topLeft,i)}function Ue(o,l=[""],t,e,s=()=>o[0]){let i=t||o;void 0===e&&(e=ei("_fallback",o));t={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:o,_rootScopes:i,_fallback:e,_getTarget:s,override:t=>Ue([t,...o],l,i,e)};return new Proxy(t,{deleteProperty:(t,e)=>(delete t[e],delete t._keys,delete o[0][e],!0),get:(r,n)=>Ge(r,n,()=>{var t,e,i=n,a=o,s=r;for(e of l)if(t=ei(Xe(e,i),a),void 0!==t)return Ke(i,t)?Ze(a,s,i,t):t}),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(o[0]),has:(t,e)=>ii(t).includes(e),ownKeys:t=>ii(t),set(t,e,i){var a=t._storage||(t._storage=s());return t[e]=a[e]=i,delete t._keys,!0}})}function Ye(a,e,i,s){var t={_cacheable:!1,_proxy:a,_context:e,_subProxy:i,_stack:new Set,_descriptors:qe(a,s),setContext:t=>Ye(a,t,i,s),override:t=>Ye(a.override(t),e,i,s)};return new Proxy(t,{deleteProperty:(t,e)=>(delete t[e],delete a[e],!0),get:(o,h,d)=>Ge(o,h,()=>{{var l=o,e=h,i=d,{_proxy:a,_context:s,_subProxy:r,_descriptors:n}=l;let t=a[e];return A(t=g(t)&&n.isScriptable(e)?((t,e,i)=>{var{_proxy:a,_context:s,_subProxy:r,_stack:n}=l;if(n.has(t))throw new Error("Recursion detected: "+Array.from(n).join("->")+"->"+t);n.add(t);let o=e(s,r||i);return n.delete(t),o=Ke(t,o)?Ze(a._scopes,a,t,o):o})(e,t,i):t)&&t.length&&(t=((s,r,t,e)=>{var{_proxy:n,_context:o,_subProxy:l,_descriptors:h}=t;if(void 0!==o.index&&e(s))return r[o.index%r.length];if(T(r[0])){let i=r,a=n._scopes.filter(t=>t!==i);r=[];for(let e of i){let t=Ze(a,n,s,e);r.push(Ye(t,o,l&&l[s],h))}}return r})(e,t,l,n.isIndexable)),t=Ke(e,t)?Ye(t,s,r&&r[e],n):t}}),getOwnPropertyDescriptor:(t,e)=>t._descriptors.allKeys?Reflect.has(a,e)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(a,e),getPrototypeOf:()=>Reflect.getPrototypeOf(a),has:(t,e)=>Reflect.has(a,e),ownKeys:()=>Reflect.ownKeys(a),set:(t,e,i)=>(a[e]=i,delete t[e],!0)})}function qe(t,e={scriptable:!0,indexable:!0}){let{_scriptable:i=e.scriptable,_indexable:a=e.indexable,_allKeys:s=e.allKeys}=t;return{allKeys:s,scriptable:i,indexable:a,isScriptable:g(i)?i:()=>i,isIndexable:g(a)?a:()=>a}}let Xe=(t,e)=>t?t+K(e):e,Ke=(t,e)=>T(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function Ge(t,e,i){return Object.prototype.hasOwnProperty.call(t,e)?t[e]:(i=i(),t[e]=i)}function Je(t,e,i){return g(t)?t(e,i):t}let Qe=(t,e)=>!0===t?e:"string"==typeof t?p(e,t):void 0;function Ze(t,a,s,r){var e=a._rootScopes,i=Je(a._fallback,s,r),t=[...t,...e],n=new Set,o=(n.add(r),ti(n,t,s,i||s,r));return null!==o&&(void 0===i||i===s||null!==(o=ti(n,t,i,o,r)))&&Ue(Array.from(n),[""],e,i,()=>{return t=s,e=r,i=a._getTarget(),t in i||(i[t]={}),A(i=i[t])&&T(e)?e:i||{};var t,e,i})}function ti(t,e,i,a,s){for(;i;)i=((e,t,i,a,s)=>{for(var r of t){let t=Qe(i,r);if(t){e.add(t);r=Je(t._fallback,i,s);if(void 0!==r&&r!==i&&r!==a)return r}else if(!1===t&&void 0!==a&&i!==a)return null}return!1})(t,e,i,a,s);return i}function ei(e,t){for(var i of t)if(i){let t=i[e];if(void 0!==t)return t}}function ii(t){let e=t._keys;return e=e||(t._keys=(t=>{var e,i=new Set;for(e of t)for(let t of Object.keys(e).filter(t=>!t.startsWith("_")))i.add(t);return Array.from(i)})(t._scopes))}function ai(t,e,i,a){var s=t.iScale,{key:r="r"}=this._parsing,n=new Array(a);let o,l,h,d;for(o=0,l=a;o<l;++o)d=e[h=o+i],n[o]={r:s.parse(p(d,r),h)};return n}let si=Number.EPSILON||1e-14,ri=(t,e)=>e<t.length&&!t[e].skip&&t[e],ni=t=>"x"===t?"y":"x";function oi(t,e,i,a){var t=t.skip?e:t,s=e,e=i.skip?e:i,i=gt(s,t),r=gt(e,s),n=i/(i+r),i=r/(i+r),r=a*(isNaN(n)?0:n),n=a*(isNaN(i)?0:i);return{previous:{x:s.x-r*(e.x-t.x),y:s.y-r*(e.y-t.y)},next:{x:s.x+n*(e.x-t.x),y:s.y+n*(e.y-t.y)}}}function li(l,e="x"){var i=ni(e),t=l.length,o=Array(t).fill(0),h=Array(t);let a,s,r,n=ri(l,0);for(a=0;a<t;++a)if(s=r,r=n,n=ri(l,a+1),r){if(n){let t=n[e]-r[e];o[a]=0!=t?(n[i]-r[i])/t:0}h[a]=s?n?y(o[a-1])!==y(o[a])?0:(o[a-1]+o[a])/2:o[a-1]:o[a]}{var d=l,c=o,u=h,g=d.length;let e,i,a,s,r,n=ri(d,0);for(let t=0;t<g-1;++t)r=n,n=ri(d,t+1),r&&n&&(st(c[t],0,si)?u[t]=u[t+1]=0:(e=u[t]/c[t],i=u[t+1]/c[t],(s=Math.pow(e,2)+Math.pow(i,2))<=9||(a=3/Math.sqrt(s),u[t]=e*a*c[t],u[t+1]=i*a*c[t])))}{var[p,f,m="x"]=[l,h,e];let a=ni(m),t=p.length,s,r,n,o=ri(p,0);for(let i=0;i<t;++i)if(r=n,n=o,o=ri(p,i+1),n){let t=n[m],e=n[a];r&&(s=(t-r[m])/3,n["cp1"+m]=t-s,n["cp1"+a]=e-s*f[i]),o&&(s=(o[m]-t)/3,n["cp2"+m]=t+s,n["cp2"+a]=e+s*f[i])}}}function hi(t,e,i){return Math.max(Math.min(t,i),e)}function di(n,e,o,i,t){let a,s,r,l;if(e.spanGaps&&(n=n.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)li(n,t);else{let t=i?n[n.length-1]:n[0];for(a=0,s=n.length;a<s;++a)r=n[a],l=oi(t,r,n[Math.min(a+1,s-(i?0:1))%s],e.tension),r.cp1x=l.previous.x,r.cp1y=l.previous.y,r.cp2x=l.next.x,r.cp2y=l.next.y,t=r}if(e.capBezierPoints){var h=n,d=o;let t,e,i,a,s,r=_(h[0],d);for(t=0,e=h.length;t<e;++t)s=a,a=r,r=t<e-1&&_(h[t+1],d),a&&(i=h[t],s&&(i.cp1x=hi(i.cp1x,d.left,d.right),i.cp1y=hi(i.cp1y,d.top,d.bottom)),r)&&(i.cp2x=hi(i.cp2x,d.left,d.right),i.cp2y=hi(i.cp2y,d.top,d.bottom))}}let ci=t=>0===t||1===t,ui=(t,e,i)=>-Math.pow(2,10*--t)*Math.sin((t-e)*x/i),gi=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*x/i)+1,pi={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>--t*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-(--t*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>--t*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>1-Math.cos(t*C),easeOutSine:t=>Math.sin(t*C),easeInOutSine:t=>-.5*(Math.cos(P*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:1-Math.pow(2,-10*t),easeInOutExpo:t=>ci(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(2-Math.pow(2,-10*(2*t-1))),easeInCirc:t=>1<=t?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1- --t*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>ci(t)?t:ui(t,.075,.3),easeOutElastic:t=>ci(t)?t:gi(t,.075,.3),easeInOutElastic(t){return ci(t)?t:t<.5?.5*ui(2*t,.1125,.45):.5+.5*gi(2*t-1,.1125,.45)},easeInBack(t){return t*t*(2.70158*t-1.70158)},easeOutBack(t){return--t*t*(2.70158*t********)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?t*t*((1+(e*=1.525))*t-e)*.5:.5*((t-=2)*t*((1+(e*=1.525))*t+e)+2)},easeInBounce:t=>1-pi.easeOutBounce(1-t),easeOutBounce(t){var e=7.5625,i=2.75;return t<1/i?e*t*t:t<2/i?e*(t-=1.5/i)*t+.75:t<2.5/i?e*(t-=2.25/i)*t+.9375:e*(t-=2.625/i)*t+.984375},easeInOutBounce:t=>t<.5?.5*pi.easeInBounce(2*t):.5*pi.easeOutBounce(2*t-1)+.5};function fi(t,e,i,a){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function mi(t,e,i,a){return{x:t.x+i*(e.x-t.x),y:("middle"===a?i<.5?t:e:"after"===a?i<1?t:e:0<i?e:t).y}}function bi(t,e,i,a){var s={x:t.cp2x,y:t.cp2y},r={x:e.cp1x,y:e.cp1y},t=fi(t,s,i),s=fi(s,r,i),r=fi(r,e,i),e=fi(t,s,i),t=fi(s,r,i);return fi(e,t,i)}let vi=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,xi=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function yi(t,e){var i=(""+t).match(vi);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}let _i=t=>+t||0;function wi(e,i){var a={},t=T(i),s=t?Object.keys(i):i,r=T(e)?t?t=>O(e[t],e[i[t]]):t=>e[t]:()=>e;for(let t of s)a[t]=_i(r(t));return a}function Mi(t){return wi(t,{top:"y",right:"x",bottom:"y",left:"x"})}function ki(t){return wi(t,["topLeft","topRight","bottomLeft","bottomRight"])}function R(t){t=Mi(t);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function z(t,e){e=e||I.font;let i=O((t=t||{}).size,e.size),a=("string"==typeof i&&(i=parseInt(i,10)),O(t.style,e.style));a&&!(""+a).match(xi)&&(console.warn('Invalid font style specified: "'+a+'"'),a=void 0);t={family:O(t.family,e.family),lineHeight:yi(O(t.lineHeight,e.lineHeight),i),size:i,style:a,weight:O(t.weight,e.weight),string:""};return t.string=Ee(t),t}function Si(t,e,i,a){let s,r,n,o=!0;for(s=0,r=t.length;s<r;++s)if(void 0!==(n=t[s])&&(void 0!==e&&"function"==typeof n&&(n=n(e),o=!1),void 0!==i&&A(n)&&(n=n[i%n.length],o=!1),void 0!==n))return a&&!o&&(a.cacheable=!1),n}function Pi(t,e,i){var{min:t,max:a}=t,e=V(e,(a-t)/2),s=(t,e)=>i&&0===t?0:t+e;return{min:s(t,-Math.abs(e)),max:s(a,e)}}function Ci(t,e){return Object.assign(Object.create(t),e)}function Di(t,e,i){return t?(a=e,s=i,{x:t=>a+a+s-t,setWidth(t){s=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t};var a,s}function Ai(t,e){var i,a;"ltr"!==e&&"rtl"!==e||(a=[(i=t.canvas.style).getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=a)}function Ti(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function Oi(t){return"angle"===t?{between:ft,compare:pt,normalize:v}:{between:d,compare:(t,e)=>t-e,normalize:t=>t}}function Ei({start:t,end:e,count:i,loop:a,style:s}){return{start:t%i,end:e%i,loop:a&&(e-t+1)%i==0,style:s}}function Li(t,i,g){if(!g)return[t];let{property:a,start:s,end:r}=g,n=i.length,{compare:o,between:l,normalize:h}=Oi(a),{start:d,end:c,loop:u,style:p}=((t,e)=>{var{property:i,start:a,end:s}=g,{between:r,normalize:n}=Oi(i),o=e.length;let l,h,{start:d,end:c,loop:u}=t;if(u){for(d+=o,c+=o,l=0,h=o;l<h&&r(n(e[d%o][i]),a,s);++l)d--,c--;d%=o,c%=o}return c<d&&(c+=o),{start:d,end:c,loop:u,style:t.style}})(t,i),f=[],m,b,v,x,y=null;for(let t=d,e=d;t<=c;++t)(b=i[t%n]).skip||(m=h(b[a]))!==v&&(x=l(m,s,r),null===(y=null===y&&(x||l(s,v,m)&&0!==o(s,v))?0===o(m,s)?t:e:y)||x&&0!==o(r,m)&&!l(r,v,m)||(f.push(Ei({start:y,end:t,loop:u,count:n,style:p})),y=null),e=t,v=m);return null!==y&&f.push(Ei({start:y,end:c,loop:u,count:n,style:p})),f}function Ii(e,i){var a=[],s=e.segments;for(let t=0;t<s.length;t++){var r=Li(s[t],e.points,i);r.length&&a.push(...r)}return a}function Ri(t,e){var i,a,s,r=t.points,n=t.options.spanGaps,o=r.length;return o?({start:a,end:s}=((t,e,i)=>{let a=0,s=e-1;if(i&&!n)for(;a<e&&!t[a].skip;)a++;for(;a<e&&t[a].skip;)a++;for(a%=e,i&&(s+=a);s>a&&t[s%e].skip;)s--;return s%=e,{start:a,end:s}})(r,o,i=!!t._loop),zi(t,!0===n?[{start:a,end:s,loop:i}]:((e,i,t,a)=>{var s=e.length,r=[];let n,o=i,l=e[i];for(n=i+1;n<=t;++n){let t=e[n%s];t.skip||t.stop?l.skip||(a=!1,r.push({start:i%s,end:(n-1)%s,loop:a}),i=o=t.stop?n:null):(o=n,l.skip&&(i=n)),l=t}return null!==o&&r.push({start:i%s,end:o%s,loop:a}),r})(r,a,s<a?s+o:s,!!t._fullLoop&&0===a&&s===o-1),r,e)):[]}function zi(e,i,a,u){if(u&&u.setContext&&a){var g=e,e=i,p=a,f=u;let s=g._chart.getContext(),t=Fi(g.options),{_datasetIndex:r,options:{spanGaps:n}}=g,o=p.length,l=[],h=t,d=e[0].start,c=d;function m(t,e,i,a){var s=n?-1:1;if(t!==e){for(t+=o;p[t%o].skip;)t-=s;for(;p[e%o].skip;)e+=s;t%o!=e%o&&(l.push({start:t%o,end:e%o,loop:i,style:a}),h=a,d=e%o)}}for(let a of e){d=n?d:a.start;let e,i=p[d%o];for(c=d+1;c<=a.end;c++){let t=p[c%o];((t,e)=>{if(e){let i=[];return JSON.stringify(t,a)!==JSON.stringify(e,a);function a(t,e){return ne(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e}}})(e=Fi(f.setContext(Ci(s,{type:"segment",p0:i,p1:t,p0DataIndex:(c-1)%o,p1DataIndex:c%o,datasetIndex:r}))),h)&&m(d,c-1,a.loop,h),i=t,h=e}d<c-1&&m(d,c-1,a.loop,h)}return l}return i}function Fi(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}var Bi=Object.freeze({__proto__:null,HALF_PI:C,INFINITY:tt,PI:P,PITAU:Z,QUARTER_PI:it,RAD_PER_DEG:et,TAU:x,TWO_THIRDS_PI:at,_addGrace:Pi,_alignPixel:Re,_alignStartEnd:L,_angleBetween:ft,_angleDiff:pt,_arrayUnique:Mt,_attachContext:Ye,_bezierCurveTo:He,_bezierInterpolation:bi,_boundSegment:Li,_boundSegments:Ii,_capitalize:K,_computeSegments:Ri,_createResolver:Ue,_decimalPlaces:ct,_deprecated:function(t,e,i,a){void 0!==e&&console.warn(t+': "'+i+'" is deprecated. Please use "'+a+'" instead')},_descriptors:qe,_elementsEqual:N,_factorize:nt,_filterBetween:xt,_getParentNode:ye,_getStartAndCountOfVisiblePoints:At,_int16Range:mt,_isBetween:d,_isClickEvent:Q,_isDomSupported:xe,_isPointInArea:_,_limitValue:D,_longestText:Ie,_lookup:bt,_lookupByKey:m,_measureText:Le,_merger:j,_mergerIf:Y,_normalizeAngle:v,_parseObjectDataRadialScale:ai,_pointInLine:fi,_readValueToProps:wi,_rlookupByKey:vt,_scaleRangesChanged:Tt,_setMinAndMaxByKey:ht,_splitKey:X,_steppedInterpolation:mi,_steppedLineTo:We,_textX:Dt,_toLeftRightCenter:Ct,_updateBezierControlPoints:di,addRoundedRectPath:$e,almostEquals:st,almostWhole:lt,callback:c,clearCanvas:ze,clipArea:Ve,clone:W,color:oe,createContext:Ci,debounce:Pt,defined:G,distanceBetweenPoints:gt,drawPoint:Fe,drawPointLegend:Be,each:k,easingEffects:pi,finiteOrDefault:u,fontString:function(t,e,i){return e+" "+t+"px "+i},formatNumber:ue,getAngleFromPoint:ut,getHoverColor:le,getMaximumSize:De,getRelativePosition:Pe,getRtlAdapter:Di,getStyle:Me,isArray:A,isFinite:f,isFunction:g,isNullOrUndef:S,isNumber:ot,isObject:T,isPatternOrGradient:ne,listenArrayEvents:_t,log10:o,merge:$,mergeIf:U,niceNum:rt,noop:t,overrideTextDirection:Ai,readUsedSize:Oe,renderText:je,requestAnimFrame:kt,resolve:Si,resolveObjectKey:p,restoreTextDirection:Ti,retinaScale:Ae,setsEqual:J,sign:y,splineCurve:oi,splineCurveMonotone:li,supportsEventListenerOptions:Te,throttled:St,toDegrees:dt,toDimension:V,toFont:z,toFontString:Ee,toLineHeight:yi,toPadding:R,toPercentage:B,toRadians:E,toTRBL:Mi,toTRBLCorners:ki,uid:F,unclipArea:Ne,unlistenArrayEvents:wt,valueOrDefault:O});function Vi(t,r,e,n,o){var l=t.getSortedVisibleDatasetMetas(),h=e[r];for(let e=0,t=l.length;e<t;++e){let{index:i,data:a}=l[e],{lo:t,hi:s}=((t,s,r,e)=>{let{controller:i,data:n,_sorted:a}=t,o=i._cachedMeta.iScale;if(o&&s===o.axis&&"r"!==s&&a&&n.length){let a=o._reversePixels?vt:m;if(!e)return a(n,s,r);if(i._sharedOptions){let t=n[0],i="function"==typeof t.getRange&&t.getRange(s);if(i){let t=a(n,s,r-i),e=a(n,s,r+i);return{lo:t.lo,hi:e.hi}}}}return{lo:0,hi:n.length-1}})(l[e],r,h,o);for(let e=t;e<=s;++e){let t=a[e];t.skip||n(t,i,e)}}}function Ni(a,s,t,r,n){let o=[];return(n||a.isPointInArea(s))&&Vi(a,t,s,function(t,e,i){(n||_(t,a.chartArea,0))&&t.inRange(s.x,s.y,r)&&o.push({element:t,datasetIndex:e,index:i})},!0),o}function Wi(r,n,t,o,l,h){let d=[],c=(t=>{let a=-1!==t.indexOf("x"),s=-1!==t.indexOf("y");return function(t,e){var i=a?Math.abs(t.x-e.x):0,t=s?Math.abs(t.y-e.y):0;return Math.sqrt(Math.pow(i,2)+Math.pow(t,2))}})(t),u=Number.POSITIVE_INFINITY;return Vi(r,t,n,function(t,e,i){var a,s=t.inRange(n.x,n.y,l);(!o||s)&&(a=t.getCenterPoint(l),h||r.isPointInArea(a)||s)&&((s=c(n,a))<u?(d=[{element:t,datasetIndex:e,index:i}],u=s):s===u&&d.push({element:t,datasetIndex:e,index:i}))}),d}function Hi(t,e,i,a,s,r){{if(r||t.isPointInArea(e)){if("r"!==i||a)return Wi(t,e,i,a,s,r);{var o=e,l=s;let n=[];return Vi(t,i,o,function(t,e,i){var{startAngle:a,endAngle:s}=t.getProps(["startAngle","endAngle"],l),r=ut(t,{x:o.x,y:o.y}).angle;ft(r,a,s)&&n.push({element:t,datasetIndex:e,index:i})}),n}}return[]}}function ji(t,a,s,e,r){let n=[],o="x"===s?"inXRange":"inYRange",l=!1;return Vi(t,s,a,(t,e,i)=>{t[o](a[s],r)&&(n.push({element:t,datasetIndex:e,index:i}),l=l||t.inRange(a.x,a.y,r))}),e&&!l?[]:n}var $i={evaluateInteractionItems:Vi,modes:{index(t,e,i,a){let s=Pe(e,t),r=i.axis||"x",n=i.includeInvisible||!1,o=i.intersect?Ni(t,s,r,a,n):Hi(t,s,r,!1,a,n),l=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach(t=>{var e=o[0].index,i=t.data[e];i&&!i.skip&&l.push({element:i,datasetIndex:t.index,index:e})}),l):[]},dataset(t,e,i,a){var e=Pe(e,t),s=i.axis||"xy",r=i.includeInvisible||!1;let n=i.intersect?Ni(t,e,s,a,r):Hi(t,e,s,!1,a,r);if(0<n.length){let e=n[0].datasetIndex,i=t.getDatasetMeta(e).data;n=[];for(let t=0;t<i.length;++t)n.push({element:i[t],datasetIndex:e,index:t})}return n},point:(t,e,i,a)=>Ni(t,Pe(e,t),i.axis||"xy",a,i.includeInvisible||!1),nearest(t,e,i,a){return Hi(t,Pe(e,t),i.axis||"xy",i.intersect,a,i.includeInvisible||!1)},x:(t,e,i,a)=>ji(t,Pe(e,t),"x",i.intersect,a),y:(t,e,i,a)=>ji(t,Pe(e,t),"y",i.intersect,a)}};let Ui=["left","top","right","bottom"];function Yi(t,e){return t.filter(t=>t.pos===e)}function qi(t,e){return t.filter(t=>-1===Ui.indexOf(t.pos)&&t.box.axis===e)}function Xi(t,a){return t.sort((t,e)=>{var i=a?e:t,t=a?t:e;return i.weight===t.weight?i.index-t.index:i.weight-t.weight})}function Ki(a,s){var r=(t=>{var a,s,r={};for(a of t){let{stack:t,pos:e,stackWeight:i}=a;t&&Ui.includes(e)&&((s=r[t]||(r[t]={count:0,placed:0,weight:0,size:0})).count++,s.weight+=i)}return r})(a),{vBoxMaxWidth:n,hBoxMaxHeight:o}=s;let l,t,h;for(l=0,t=a.length;l<t;++l){let t=(h=a[l]).box.fullSize,e=r[h.stack],i=e&&h.stackWeight/e.weight;h.horizontal?(h.width=i?i*n:t&&s.availableWidth,h.height=o):(h.width=n,h.height=i?i*o:t&&s.availableHeight)}return r}function Gi(t,e,i,a){return Math.max(t[i],e[i])+Math.max(t[a],e[a])}function Ji(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function Qi(i,a,s,r){var n=[];let o,t,l,h,d,c;for(o=0,t=i.length,d=0;o<t;++o){l=i[o],(h=l.box).update(l.width||a.w,l.height||a.h,((t,i)=>{let a=i.maxPadding;{let e={left:0,top:0,right:0,bottom:0};return(t=t?["left","right"]:["top","bottom"]).forEach(t=>{e[t]=Math.max(i[t],a[t])}),e}})(l.horizontal,a));let{same:t,other:e}=((e,t,i,a)=>{var{pos:s,box:r}=i,n=e.maxPadding;if(!T(s)){i.size&&(e[s]-=i.size);let t=a[i.stack]||{size:0,count:1};t.size=Math.max(t.size,i.horizontal?r.height:r.width),i.size=t.size/t.count,e[s]+=i.size}return r.getPadding&&Ji(n,r.getPadding()),a=Math.max(0,t.outerWidth-Gi(n,e,"left","right")),s=Math.max(0,t.outerHeight-Gi(n,e,"top","bottom")),r=a!==e.w,t=s!==e.h,e.w=a,e.h=s,i.horizontal?{same:r,other:t}:{same:t,other:r}})(a,s,l,r);d|=t&&n.length,c=c||e,h.fullSize||n.push(l)}return d&&Qi(n,a,s,r)||c}function Zi(t,e,i,a,s){t.top=i,t.left=e,t.right=e+a,t.bottom=i+s,t.width=a,t.height=s}function ta(t,r,n,e){var o,l=n.padding;let{x:h,y:d}=r;for(o of t){let i=o.box,a=e[o.stack]||{count:1,placed:0,weight:1},s=o.stackWeight/a.weight||1;if(o.horizontal){let t=r.w*s,e=a.size||i.height;G(a.start)&&(d=a.start),i.fullSize?Zi(i,l.left,d,n.outerWidth-l.right-l.left,e):Zi(i,r.left+a.placed,d,t,e),a.start=d,a.placed+=t,d=i.bottom}else{let t=r.h*s,e=a.size||i.width;G(a.start)&&(h=a.start),i.fullSize?Zi(i,h,l.top,e,n.outerHeight-l.bottom-l.top):Zi(i,h,r.top+a.placed,e,t),a.start=h,a.placed+=t,h=i.right}}r.x=h,r.y=d}var s={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){e=t.boxes?t.boxes.indexOf(e):-1;-1!==e&&t.boxes.splice(e,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(i,a,s,r){if(i){var n,o,l=R(i.options.layout.padding),h=Math.max(a-l.width,0),d=Math.max(s-l.height,0),c=(u=(t=>{var e=[];let i,a,s,r,n,o;for(i=0,a=(t||[]).length;i<a;++i)({position:r,options:{stack:n,stackWeight:o=1}}=s=t[i]),e.push({index:i,box:s,pos:r,horizontal:s.isHorizontal(),weight:s.weight,stack:n&&r+n,stackWeight:o});return e})(i.boxes),c=Xi(u.filter(t=>t.box.fullSize),!0),g=Xi(Yi(u,"left"),!0),p=Xi(Yi(u,"right")),m=Xi(Yi(u,"top"),!0),n=Xi(Yi(u,"bottom")),o=qi(u,"x"),f=qi(u,"y"),{fullSize:c,leftAndTop:g.concat(m),rightAndBottom:p.concat(f).concat(n).concat(o),chartArea:Yi(u,"chartArea"),vertical:g.concat(p).concat(f),horizontal:m.concat(n).concat(o)}),u=c.vertical,g=c.horizontal,p=(k(i.boxes,t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()}),u.reduce((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1,0)||1),f=Object.freeze({outerWidth:a,outerHeight:s,padding:l,availableWidth:h,availableHeight:d,vBoxMaxWidth:h/2/p,hBoxMaxHeight:d/2}),m=Object.assign({},l);Ji(m,R(r));let e=Object.assign({maxPadding:m,w:h,h:d,x:l.left,y:l.top},l),t=Ki(u.concat(g),f);Qi(c.fullSize,e,f,t),Qi(u,e,f,t),Qi(g,e,f,t)&&Qi(u,e,f,t);{var b=e;let i=b.maxPadding;function v(t){var e=Math.max(i[t]-b[t],0);return b[t]+=e,e}b.y+=v("top"),b.x+=v("left"),v("right"),v("bottom")}ta(c.leftAndTop,e,f,t),e.x+=e.w,e.y+=e.h,ta(c.rightAndBottom,e,f,t),i.chartArea={left:e.left,top:e.top,right:e.left+e.w,bottom:e.top+e.h,height:e.h,width:e.w},k(c.chartArea,t=>{t=t.box;Object.assign(t,i.chartArea),t.update(e.w,e.h,{left:0,top:0,right:0,bottom:0})})}}};class ea{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,a){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,a?Math.floor(e/a):i)}}isAttached(t){return!0}updateConfig(t){}}class ia extends ea{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}let aa="$chartjs",sa={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},ra=t=>null===t||""===t,na=!!Te&&{passive:!0};function oa(t,e){for(var i of t)if(i===e||i.contains(e))return!0}function la(t,e,a){let s=t.canvas,i=new MutationObserver(e=>{let i=!1;for(let t of e)i=i||oa(t.addedNodes,s),i=i&&!oa(t.removedNodes,s);i&&a()});return i.observe(document,{childList:!0,subtree:!0}),i}function ha(t,e,a){let s=t.canvas,i=new MutationObserver(e=>{let i=!1;for(let t of e)i=i||oa(t.removedNodes,s),i=i&&!oa(t.addedNodes,s);i&&a()});return i.observe(document,{childList:!0,subtree:!0}),i}let da=new Map,ca=0;function ua(){let i=window.devicePixelRatio;i!==ca&&(ca=i,da.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function ga(e,t,a){let i=e.canvas,s=i&&ye(i);if(s){let i=St((t,e)=>{var i=s.clientWidth;a(t,e),i<s.clientWidth&&a()},window),t=new ResizeObserver(t=>{var t=t[0],e=t.contentRect.width,t=t.contentRect.height;0===e&&0===t||i(e,t)});return t.observe(s),e=e,r=i,da.size||window.addEventListener("resize",ua),da.set(e,r),t;var r}}function pa(t,e,i){i&&i.disconnect(),"resize"===e&&(i=t,da.delete(i),da.size||window.removeEventListener("resize",ua))}function fa(e,t,i){var a=e.canvas,s=St(t=>{null!==e.ctx&&i(((t,e)=>{var i=sa[t.type]||t.type,{x:a,y:s}=Pe(t,e);return{type:i,chart:e,native:t,x:void 0!==a?a:null,y:void 0!==s?s:null}})(t,e))},e);return a.addEventListener(t,s,na),s}class ma extends ea{acquireContext(e,t){var i=e&&e.getContext&&e.getContext("2d");{if(i&&i.canvas===e){var a=t,t=e.style,s=e.getAttribute("height"),r=e.getAttribute("width");if(e[aa]={initial:{height:s,width:r,style:{display:t.display,height:t.height,width:t.width}}},t.display=t.display||"block",t.boxSizing=t.boxSizing||"border-box",ra(r)){let t=Oe(e,"width");void 0!==t&&(e.width=t)}if(ra(s))if(""===e.style.height)e.height=e.width/(a||2);else{let t=Oe(e,"height");void 0!==t&&(e.height=t)}return i}return null}}releaseContext(t){let i=t.canvas;if(!i[aa])return!1;let a=i[aa].initial,e=(["height","width"].forEach(t=>{var e=a[t];S(e)?i.removeAttribute(t):i.setAttribute(t,e)}),a.style||{});return Object.keys(e).forEach(t=>{i.style[t]=e[t]}),i.width=i.width,delete i[aa],!0}addEventListener(t,e,i){this.removeEventListener(t,e);var a=t.$proxies||(t.$proxies={}),s={attach:la,detach:ha,resize:ga}[e]||fa;a[e]=s(t,e,i)}removeEventListener(t,e){var i=t.$proxies||(t.$proxies={}),a=i[e];a&&(({attach:pa,detach:pa,resize:pa}[e]||function(t,e,i){t.canvas.removeEventListener(e,i,na)})(t,e,a),i[e]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,a){return De(t,e,i,a)}isAttached(t){t=ye(t);return!(!t||!t.isConnected)}}function ba(t){return!xe()||"undefined"!=typeof OffscreenCanvas&&t instanceof OffscreenCanvas?ia:ma}let va=Object.freeze({__proto__:null,BasePlatform:ea,BasicPlatform:ia,DomPlatform:ma,_detectPlatform:ba}),xa="transparent",ya={boolean:(t,e,i)=>.5<i?e:t,color(t,e,i){var t=oe(t||xa),a=t.valid&&oe(e||xa);return a&&a.valid?a.mix(t,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class _a{constructor(t,e,i,a){var s=e[i],s=(a=Si([t.to,a,s,t.from]),Si([t.from,s,a]));this._active=!0,this._fn=t.fn||ya[t.type||typeof s],this._easing=pi[t.easing]||pi.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=s,this._to=a,this._promises=void 0}active(){return this._active}update(t,e,i){var a,s,r;this._active&&(this._notify(!1),a=this._target[this._prop],s=i-this._start,r=this._duration-s,this._start=i,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=s,this._loop=!!t.loop,this._to=Si([t.to,e,a,t.from]),this._from=Si([t.from,a,e]))}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){var t=t-this._start,e=this._duration,i=this._prop,a=this._from,s=this._loop,r=this._to;this._active=a!==r&&(s||t<e),this._active?t<0?this._target[i]=a:(t=t/e%2,t=this._easing(Math.min(1,Math.max(0,s&&1<t?2-t:t))),this._target[i]=this._fn(a,r,t)):(this._target[i]=r,this._notify(!0))}wait(){let i=this._promises||(this._promises=[]);return new Promise((t,e)=>{i.push({res:t,rej:e})})}_notify(t){var e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}class wa{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(T(t)){let s=Object.keys(I.animation),r=this._properties;Object.getOwnPropertyNames(t).forEach(i=>{var a=t[i];if(T(a)){let e={};for(let t of s)e[t]=a[t];(A(a.properties)&&a.properties||[i]).forEach(t=>{t!==i&&r.has(t)||r.set(t,e)})}})}}_animateOptions(t,e){let i=e.options,a=((e,i)=>{if(i){let t=e.options;if(t)return t.$shared&&(e.options=t=Object.assign({},t,{$shared:!1,$animations:{}})),t;e.options=i}})(t,i);return a?(e=this._createAnimations(a,i),i.$shared&&((e,t)=>{var i=[],a=Object.keys(t);for(let t=0;t<a.length;t++){var s=e[a[t]];s&&s.active()&&i.push(s.wait())}return Promise.all(i)})(t.options.$animations,i).then(()=>{t.options=i},()=>{}),e):[]}_createAnimations(e,i){var a=this._properties,s=[],r=e.$animations||(e.$animations={}),t=Object.keys(i),n=Date.now();let o;for(o=t.length-1;0<=o;--o){var l=t[o];if("$"!==l.charAt(0))if("options"===l)s.push(...this._animateOptions(e,i));else{var h=i[l];let t=r[l];var d=a.get(l);if(t){if(d&&t.active()){t.update(d,h,n);continue}t.cancel()}d&&d.duration?(r[l]=t=new _a(d,e,l,h),s.push(t)):e[l]=h}}return s}update(t,e){var i;if(0!==this._properties.size)return(i=this._createAnimations(t,e)).length?(r.add(this._chart,i),!0):void 0;Object.assign(t,e)}}function Ma(t,e){var t=t&&t.options||{},i=t.reverse,a=void 0===t.min?e:0,t=void 0===t.max?e:0;return{start:i?t:a,end:i?a:t}}function ka(t,e){var i=[],a=t._getSortedDatasetMetas(e);let s,r;for(s=0,r=a.length;s<r;++s)i.push(a[s].index);return i}function Sa(t,e,i,a={}){var s=t.keys,r="single"===a.mode;let n,o,l,h;if(null!==e){for(n=0,o=s.length;n<o;++n){if((l=+s[n])===i){if(a.all)continue;break}f(h=t.values[l])&&(r||0===e||y(e)===y(h))&&(e+=h)}return e}}function Pa(t,e){t=t&&t.options.stacked;return t||void 0===t&&void 0!==e.stack}function Ca(e,t,i,a){for(var s of t.getMatchingVisibleMetas(a).reverse()){let t=e[s.index];if(i&&0<t||!i&&t<0)return s.index}return null}function Da(t,s){let{chart:e,_cachedMeta:r}=t,n=e._stacks||(e._stacks={}),{iScale:i,vScale:o,index:l}=r,h=i.axis,d=o.axis,c=i.id+`.${o.id}.`+(r.stack||r.type),u=s.length;var g,p,f;for(let a=0;a<u;++a){let t=s[a],{[h]:e,[d]:i}=t;(p=(t._stacks||(t._stacks={}))[d]=(f=e,(g=(g=n)[p=c]||(g[p]={}))[f]||(g[f]={})))[l]=i,p._top=Ca(p,o,!0,r.type),p._bottom=Ca(p,o,!1,r.type),(p._visualValues||(p._visualValues={}))[l]=i}}function Aa(t,e){let i=t.scales;return Object.keys(i).filter(t=>i[t].axis===e).shift()}function Ta(t,i){var a=t.controller.index,s=t.vScale&&t.vScale.axis;if(s){i=i||t._parsed;for(let e of i){let t=e._stacks;if(!t||void 0===t[s]||void 0===t[s][a])return;delete t[s][a],void 0!==t[s]._visualValues&&void 0!==t[s]._visualValues[a]&&delete t[s]._visualValues[a]}}}let Oa=t=>"reset"===t||"none"===t,Ea=(t,e)=>e?t:Object.assign({},t);class La{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){var t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Pa(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Ta(this._cachedMeta),this.index=t}linkScales(){var t=this.chart,e=this._cachedMeta,i=this.getDataset(),a=(t,e,i,a)=>"x"===t?e:"r"===t?a:i,s=e.xAxisID=O(i.xAxisID,Aa(t,"x")),r=e.yAxisID=O(i.yAxisID,Aa(t,"y")),i=e.rAxisID=O(i.rAxisID,Aa(t,"r")),t=e.indexAxis,n=e.iAxisID=a(t,s,r,i),a=e.vAxisID=a(t,r,s,i);e.xScale=this.getScaleForId(s),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(i),e.iScale=this.getScaleForId(n),e.vScale=this.getScaleForId(a)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){var e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){var t=this._cachedMeta;this._data&&wt(this._data,this),t._stacked&&Ta(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(T(e))this._data=(t=>{var e=Object.keys(t),i=new Array(e.length);let a,s,r;for(a=0,s=e.length;a<s;++a)r=e[a],i[a]={x:r,y:t[r]};return i})(e);else if(i!==e){if(i){wt(i,this);let t=this._cachedMeta;Ta(t),t._parsed=[]}e&&Object.isExtensible(e)&&_t(e,this),this._syncList=[],this._data=e}}addElements(){var t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){var e=this._cachedMeta,i=this.getDataset();let a=!1;this._dataCheck();var s=e._stacked;e._stacked=Pa(e.vScale,e),e.stack!==i.stack&&(a=!0,Ta(e),e.stack=i.stack),this._resyncElements(t),!a&&s===e._stacked||Da(this,e._parsed)}configure(){var t=this.chart.config,e=t.datasetScopeKeys(this._type),e=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(e,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,i){let{_cachedMeta:a,_data:s}=this,{iScale:t,_stacked:r}=a,n=t.axis,o,l,h,d=0===e&&i===s.length||a._sorted,c=0<e&&a._parsed[e-1];if(!1===this._parsing)a._parsed=s,a._sorted=!0,h=s;else{h=A(s[e])?this.parseArrayData(a,s,e,i):T(s[e])?this.parseObjectData(a,s,e,i):this.parsePrimitiveData(a,s,e,i);let t=()=>null===l[n]||c&&l[n]<c[n];for(o=0;o<i;++o)a._parsed[o+e]=l=h[o],d&&(t()&&(d=!1),c=l);a._sorted=d}r&&Da(this,h)}parsePrimitiveData(t,e,i,a){var{iScale:s,vScale:r}=t,n=s.axis,o=r.axis,l=s.getLabels(),h=s===r,d=new Array(a);let c,u,g;for(c=0,u=a;c<u;++c)g=c+i,d[c]={[n]:h||s.parse(l[g],g),[o]:r.parse(e[g],g)};return d}parseArrayData(t,e,i,a){var{xScale:s,yScale:r}=t,n=new Array(a);let o,l,h,d;for(o=0,l=a;o<l;++o)d=e[h=o+i],n[o]={x:s.parse(d[0],h),y:r.parse(d[1],h)};return n}parseObjectData(t,e,i,a){var{xScale:s,yScale:r}=t,{xAxisKey:n="x",yAxisKey:o="y"}=this._parsing,l=new Array(a);let h,d,c,u;for(h=0,d=a;h<d;++h)u=e[c=h+i],l[h]={x:s.parse(p(u,n),c),y:r.parse(p(u,o),c)};return l}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){var a=this.chart,s=this._cachedMeta,r=e[t.axis];return Sa({keys:ka(a,!0),values:e._stacks[t.axis]._visualValues},r,s.index,{mode:i})}updateRangeFromParsed(t,e,i,a){var s=i[e.axis];let r=null===s?NaN:s;i=a&&i._stacks[e.axis];a&&i&&(a.values=i,r=Sa(a,s,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(e,t){let i=this._cachedMeta,a=i._parsed,s=i._sorted&&e===i.iScale,r=a.length,n=this._getOtherScale(e),o=(c=this.chart,t&&!i.hidden&&i._stacked&&{keys:ka(c,!0),values:null}),l={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=(()=>{var{min:t,max:e,minDefined:i,maxDefined:a}=n.getUserBounds();return{min:i?t:Number.NEGATIVE_INFINITY,max:a?e:Number.POSITIVE_INFINITY}})();var c;let u,g;function p(){var t=(g=a[u])[n.axis];return!f(g[e.axis])||h>t||d<t}for(u=0;u<r&&(p()||(this.updateRangeFromParsed(l,e,g,o),!s));++u);if(s)for(u=r-1;0<=u;--u)if(!p()){this.updateRangeFromParsed(l,e,g,o);break}return l}getAllParsedValues(t){var e=this._cachedMeta._parsed,i=[];let a,s,r;for(a=0,s=e.length;a<s;++a)f(r=e[a][t.axis])&&i.push(r);return i}getMaxOverflow(){return!1}getLabelAndValue(t){var e=this._cachedMeta,i=e.iScale,e=e.vScale,t=this.getParsed(t);return{label:i?""+i.getLabelForValue(t[i.axis]):"",value:e?""+e.getLabelForValue(t[e.axis]):""}}_update(t){var e,i=this._cachedMeta;this.update(t||"default"),i._clip=(t=>{let e,i,a,s;return T(t)?(e=t.top,i=t.right,a=t.bottom,s=t.left):e=i=a=s=t,{top:e,right:i,bottom:a,left:s,disabled:!1===t}})(O(this.options.clip,(t=i.xScale,i=i.yScale,!1!==(e=this.getMaxOverflow())&&(t=Ma(t,e),{top:(i=Ma(i,e)).end,right:t.end,bottom:i.start,left:t.start}))))}update(t){}draw(){let e=this._ctx,t=this.chart,i=this._cachedMeta,a=i.data||[],s=t.chartArea,r=[],n=this._drawStart||0,o=this._drawCount||a.length-n,l=this.options.drawActiveElementsOnTop,h;for(i.dataset&&i.dataset.draw(e,s,n,o),h=n;h<n+o;++h){let t=a[h];t.hidden||(t.active&&l?r.push(t):t.draw(e,s))}for(h=0;h<r.length;++h)r[h].draw(e,s)}getStyle(t,e){e=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(e):this.resolveDataElementOptions(t||0,e)}getContext(e,t,i){var a,s=this.getDataset();let r;if(0<=e&&e<this._cachedMeta.data.length){let t=this._cachedMeta.data[e];(r=t.$context||(t.$context=Ci(this.getContext(),{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:t,index:e,mode:"default",type:"data"}))).parsed=this.getParsed(e),r.raw=s.data[e],r.index=r.dataIndex=e}else(r=this.$context||(this.$context=(e=this.chart.getContext(),a=this.index,Ci(e,{active:!1,dataset:void 0,datasetIndex:a,index:a,mode:"default",type:"dataset"})))).dataset=s,r.index=r.datasetIndex=this.index;return r.active=!!t,r.mode=i,r}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let a="active"===e,s=this._cachedDataOpts,r=t+"-"+e,n=s[r],o=this.enableOptionSharing&&G(i);var l,h,d;return n?Ea(n,o):(h=(d=this.chart.config).datasetElementScopeKeys(this._type,t),l=a?[t+"Hover","hover",t,""]:[t,""],h=d.getOptionScopes(this.getDataset(),h),t=Object.keys(I.elements[t]),(d=d.resolveNamedOptions(h,t,()=>this.getContext(i,a,e),l)).$shared&&(d.$shared=o,s[r]=Object.freeze(Ea(d,o))),d)}_resolveAnimations(a,s,r){let t=this.chart,e=this._cachedDataOpts,i="animation-"+s,n=e[i];if(n)return n;let o;if(!1!==t.options.animation){let t=this.chart.config,e=t.datasetAnimationScopeKeys(this._type,s),i=t.getOptionScopes(this.getDataset(),e);o=t.createResolver(i,this.getContext(a,r,s))}a=new wa(t,o&&o.animations);return o&&o._cacheable&&(e[i]=Object.freeze(a)),a}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Oa(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){var t=this.resolveDataElementOptions(t,e),i=this._sharedOptions,a=this.getSharedOptions(t),i=this.includeOptions(e,a)||a!==i;return this.updateSharedOptions(a,e,t),{sharedOptions:a,includeOptions:i}}updateElement(t,e,i,a){Oa(a)?Object.assign(t,i):this._resolveAnimations(e,a).update(t,i)}updateSharedOptions(t,e,i){t&&!Oa(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,a){t.active=a;var s=this.getStyle(e,a);this._resolveAnimations(e,i,a).update(t,{options:!a&&this.getSharedOptions(s)||s})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){var t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){var t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];var a=i.length,s=e.length,r=Math.min(s,a);r&&this.parse(0,r),a<s?this._insertElements(a,s-a,t):s<a&&this._removeElements(s,a-s)}_insertElements(t,e,i=!0){let a=this._cachedMeta,s=a.data,r=t+e,n;var o=t=>{for(t.length+=e,n=t.length-1;n>=r;n--)t[n]=t[n-e]};for(o(s),n=t;n<r;++n)s[n]=new this.dataElementType;this._parsing&&o(a._parsed),this.parse(t,e),i&&this.updateElements(s,t,e,"reset")}updateElements(t,e,i,a){}_removeElements(t,e){var i,a=this._cachedMeta;this._parsing&&(i=a._parsed.splice(t,e),a._stacked)&&Ta(a,i),a.data.splice(t,e)}_sync(t){var e,i,a;this._parsing?this._syncList.push(t):([e,i,a]=t,this[e](i,a)),this.chart._dataChanges.push([this.index,...t])}_onDataPush(){var t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);e=arguments.length-2;e&&this._sync(["_insertElements",t,e])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}class e{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){var{x:t,y:e}=this.getProps(["x","y"],t);return{x:t,y:e}}hasValue(){return ot(this.x)&&ot(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let a={};return t.forEach(t=>{a[t]=i[t]&&i[t].active()?i[t]._to:this[t]}),a}}function Ia(t,a){let e=t.options.ticks,i=(d=(t=t).options.offset,c=t._tickSize(),d=t._length/c+(d?0:1),Math.floor(Math.min(d,t._maxLength/c))),s=Math.min(e.maxTicksLimit||i,i),r=e.major.enabled?(t=>{var e=[];let i,a;for(i=0,a=t.length;i<a;i++)t[i].major&&e.push(i);return e})(a):[],n=r.length,o=r[0],l=r[n-1],h=[];var d,c;if(s<n){var u=a;var g=h;var p=r;var f=n/s;let t,e=0,i=p[0];for(f=Math.ceil(f),t=0;t<u.length;t++)t===i&&(g.push(u[t]),e++,i=p[e*f])}else{var m=((t,e,i)=>{var t=(t=>{var e=t.length;let i,a;if(e<2)return!1;for(a=t[0],i=1;i<e;++i)if(t[i]-t[i-1]!==a)return!1;return a})(t),a=e.length/i;if(t){var s=nt(t);for(let e=0,t=s.length-1;e<t;e++){let t=s[e];if(t>a)return t}}return Math.max(a,1)})(r,a,s);if(0<n){let t,e,i=1<n?Math.round((l-o)/(n-1)):null;for(Ra(a,h,m,S(i)?0:o-i,o),t=0,e=n-1;t<e;t++)Ra(a,h,m,r[t],r[t+1]);Ra(a,h,m,l,S(i)?a.length:l+i)}else Ra(a,h,m)}return h}function Ra(t,e,i,a,s){var r=O(a,0),n=Math.min(O(s,t.length),t.length);let o,l,h,d=0;for(i=Math.ceil(i),s&&(i=(o=s-a)/Math.floor(o/i)),h=r;h<0;)d++,h=Math.round(r+d*i);for(l=Math.max(r,0);l<n;l++)l===h&&(e.push(t[l]),d++,h=Math.round(r+d*i))}let za=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,Fa=(t,e)=>Math.min(e||t,t);function Ba(t,e){var i=[],a=t.length/e,s=t.length;let r=0;for(;r<s;r+=a)i.push(t[Math.floor(r)]);return i}function Va(t){return t.drawTicks?t.tickLength:0}function Na(t,e){var i;return t.display?(e=z(t.font,e),i=R(t.padding),(A(t.text)?t.text.length:1)*e.lineHeight+i.height):0}class Wa extends e{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){var{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:a}=this,t=u(t,Number.POSITIVE_INFINITY),e=u(e,Number.NEGATIVE_INFINITY),i=u(i,Number.POSITIVE_INFINITY),a=u(a,Number.NEGATIVE_INFINITY);return{min:u(t,i),max:u(e,a),minDefined:f(t),maxDefined:f(e)}}getMinMax(i){let a,{min:s,max:r,minDefined:n,maxDefined:o}=this.getUserBounds();if(n&&o)return{min:s,max:r};var l=this.getMatchingVisibleMetas();for(let t=0,e=l.length;t<e;++t)a=l[t].controller.getMinMax(this,i),n||(s=Math.min(s,a.min)),o||(r=Math.max(r,a.max));return s=o&&s>r?r:s,r=n&&s>r?s:r,{min:u(s,u(r,s)),max:u(r,u(s,r))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){var t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){c(this.options.beforeUpdate,[this])}update(t,e,i){var{beginAtZero:a,grace:s,ticks:r}=this.options,n=r.sampleSize,t=(this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Pi(this,s,a),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks(),n<this.ticks.length);this._convertTicksToLabels(t?Ba(this.ticks,n):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||"auto"===r.source)&&(this.ticks=Ia(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),t&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){c(this.options.afterUpdate,[this])}beforeSetDimensions(){c(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){c(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),c(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){c(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){var e=this.options.ticks;let i,a,s;for(i=0,a=t.length;i<a;i++)(s=t[i]).label=c(e.callback,[s.value,i,t],this)}afterTickToLabelConversion(){c(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){c(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){var t,e,i,a,s=this.options,r=s.ticks,n=Fa(this.ticks.length,s.ticks.maxTicksLimit),o=r.minRotation||0,l=r.maxRotation;let h,d,c,u=o;!this._isVisible()||!r.display||l<=o||n<=1||!this.isHorizontal()?this.labelRotation=o:(e=(t=this._getLabelSizes()).widest.width,i=t.highest.height,a=D(this.chart.width-e,0,this.maxWidth),(s.offset?this.maxWidth/n:a/(n-1))<e+6&&(h=a/(n-(s.offset?.5:1)),d=this.maxHeight-Va(s.grid)-r.padding-Na(s.title,this.chart.options.font),c=Math.sqrt(e*e+i*i),u=dt(Math.min(Math.asin(D((t.highest.height+6)/h,-1,1)),Math.asin(D(d/c,-1,1))-Math.asin(D(i/c,-1,1)))),u=Math.max(o,Math.min(l,u))),this.labelRotation=u)}afterCalculateLabelRotation(){c(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){c(this.options.beforeFit,[this])}fit(){let l={width:0,height:0},{chart:e,options:{ticks:h,title:i,grid:a}}=this,t=this._isVisible(),d=this.isHorizontal();if(t){let t=Na(i,e.options.font);if(d?(l.width=this.maxWidth,l.height=Va(a)+t):(l.height=this.maxHeight,l.width=Va(a)+t),h.display&&this.ticks.length){let{first:t,last:e,widest:i,highest:a}=this._getLabelSizes(),s=2*h.padding,r=E(this.labelRotation),n=Math.cos(r),o=Math.sin(r);if(d){let t=h.mirror?0:o*i.width+n*a.height;l.height=Math.min(this.maxHeight,l.height+t+s)}else{let t=h.mirror?0:n*i.width+o*a.height;l.width=Math.min(this.maxWidth,l.width+t+s)}this._calculatePadding(t,e,o,n)}}this._handleMargins(),d?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=l.height):(this.width=l.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(s,r,n,o){let{ticks:{align:l,padding:h},position:t}=this.options,d=0!==this.labelRotation,c="top"!==t&&"x"===this.axis;if(this.isHorizontal()){let t=this.getPixelForTick(0)-this.left,e=this.right-this.getPixelForTick(this.ticks.length-1),i=0,a=0;d?a=c?(i=o*s.width,n*r.height):(i=n*s.height,o*r.width):"start"===l?a=r.width:"end"===l?i=s.width:"inner"!==l&&(i=s.width/2,a=r.width/2),this.paddingLeft=Math.max((i-t+h)*this.width/(this.width-t),0),this.paddingRight=Math.max((a-e+h)*this.width/(this.width-e),0)}else{let t=r.height/2,e=s.height/2;"start"===l?(t=0,e=s.height):"end"===l&&(t=r.height,e=0),this.paddingTop=t+h,this.paddingBottom=e+h}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){c(this.options.afterFit,[this])}isHorizontal(){var{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)S(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){var i=this.options.ticks.sampleSize;let t=this.ticks;i<t.length&&(t=Ba(t,i)),this._labelSizes=e=this._computeLabelSizes(t,t.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(t,e,i){let{ctx:a,_longestTextCache:s}=this,r=[],n=[],o=Math.floor(e/Fa(e,i)),l,h,d,c,u,g,p,f,m,b,v,x=0,y=0;for(l=0;l<e;l+=o){if(c=t[l].label,u=this._resolveTickFontOptions(l),a.font=g=u.string,p=s[g]=s[g]||{data:{},gc:[]},f=u.lineHeight,m=b=0,S(c)||A(c)){if(A(c))for(h=0,d=c.length;h<d;++h)S(v=c[h])||A(v)||(m=Le(a,p.data,p.gc,m,v),b+=f)}else m=Le(a,p.data,p.gc,m,c),b=f;r.push(m),n.push(b),x=Math.max(m,x),y=Math.max(b,y)}_=e,k(s,t=>{var e=t.gc,i=e.length/2;let a;if(_<i){for(a=0;a<i;++a)delete t.data[e[a]];e.splice(0,i)}});var _,i=r.indexOf(x),w=n.indexOf(y),M=t=>({width:r[t]||0,height:n[t]||0});return{first:M(0),last:M(e-1),widest:M(i),highest:M(w),widths:r,heights:n}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){var e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);t=this._startPixel+t*this._length;return mt(this._alignToPixels?Re(this.chart,t,0):t)}getDecimalForPixel(t){t=(t-this._startPixel)/this._length;return this._reversePixels?1-t:t}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){var{min:t,max:e}=this;return t<0&&e<0?e:0<t&&0<e?t:0}getContext(t){var e=this.ticks||[];return 0<=t&&t<e.length?(e=e[t]).$context||(e.$context=Ci(this.getContext(),{tick:e,index:t,type:"tick"})):this.$context||(this.$context=Ci(this.chart.getContext(),{scale:this,type:"scale"}))}_tickSize(){var t=this.options.ticks,e=E(this.labelRotation),i=Math.abs(Math.cos(e)),e=Math.abs(Math.sin(e)),a=this._getLabelSizes(),t=t.autoSkipPadding||0,s=a?a.widest.width+t:0,a=a?a.highest.height+t:0;return this.isHorizontal()?s*e<a*i?s/i:a/e:a*e<s*i?a/i:s/e}_isVisible(){var t=this.options.display;return"auto"!==t?!!t:0<this.getMatchingVisibleMetas().length}_computeGridLineItems(t){function i(t){return Re(c,t,l)}let e=this.axis,c=this.chart,a=this.options,{grid:u,position:s,border:g}=a,p=u.offset,f=this.isHorizontal(),r=this.ticks.length+(p?1:0),n=Va(u),m=[],o=g.setContext(this.getContext()),l=o.display?o.width:0,h=l/2,d,b,v,x,y,_,w,M,k,S,P,C;if("top"===s)d=i(this.bottom),_=this.bottom-n,M=d-h,S=i(t.top)+h,C=t.bottom;else if("bottom"===s)d=i(this.top),S=t.top,C=i(t.bottom)-h,_=d+h,M=this.top+n;else if("left"===s)d=i(this.right),y=this.right-n,w=d-h,k=i(t.left)+h,P=t.right;else if("right"===s)d=i(this.left),k=t.left,P=i(t.right)-h,y=d+h,w=this.left+n;else if("x"===e){if("center"===s)d=i((t.top+t.bottom)/2+.5);else if(T(s)){let t=Object.keys(s)[0],e=s[t];d=i(this.chart.scales[t].getPixelForValue(e))}S=t.top,C=t.bottom,_=d+h,M=_+n}else if("y"===e){if("center"===s)d=i((t.left+t.right)/2);else if(T(s)){let t=Object.keys(s)[0],e=s[t];d=i(this.chart.scales[t].getPixelForValue(e))}y=d-h,w=y-n,k=t.left,P=t.right}var t=O(a.ticks.maxTicksLimit,r),D=Math.max(1,Math.ceil(r/t));for(b=0;b<r;b+=D){let t=this.getContext(b),e=u.setContext(t),i=g.setContext(t),a=e.lineWidth,s=e.color,r=i.dash||[],n=i.dashOffset,o=e.tickWidth,l=e.tickColor,h=e.tickBorderDash||[],d=e.tickBorderDashOffset;void 0!==(v=((t,e,i)=>{var a=t.ticks.length,s=Math.min(e,a-1),r=t._startPixel,n=t._endPixel;let o,l=t.getPixelForTick(s);if(!(i&&(o=1===a?Math.max(l-r,n-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(s-1))/2,(l+=s<e?o:-o)<r-1e-6||l>n+1e-6)))return l})(this,b,p))&&(x=Re(c,v,a),f?y=w=k=P=x:_=M=S=C=x,m.push({tx1:y,ty1:_,tx2:w,ty2:M,x1:k,y1:S,x2:P,y2:C,width:a,color:s,borderDash:r,borderDashOffset:n,tickWidth:o,tickColor:l,tickBorderDash:h,tickBorderDashOffset:d}))}return this._ticksLength=r,this._borderValue=d,m}_computeLabelItems(t){let e=this.axis,i=this.options,{position:o,ticks:l}=i,h=this.isHorizontal(),d=this.ticks,{align:a,crossAlign:c,padding:s,mirror:u}=l,r=Va(i.grid),n=r+s,g=u?-s:n,p=-E(this.labelRotation),f=[],m,b,v,x,y,_,w,M,k,S,P,C="middle";if("top"===o)y=this.bottom-g,_=this._getXAxisLabelAlignment();else if("bottom"===o)y=this.top+g,_=this._getXAxisLabelAlignment();else if("left"===o){let t=this._getYAxisLabelAlignment(r);_=t.textAlign,x=t.x}else if("right"===o){let t=this._getYAxisLabelAlignment(r);_=t.textAlign,x=t.x}else if("x"===e){if("center"===o)y=(t.top+t.bottom)/2+n;else if(T(o)){let t=Object.keys(o)[0],e=o[t];y=this.chart.scales[t].getPixelForValue(e)+n}_=this._getXAxisLabelAlignment()}else if("y"===e){if("center"===o)x=(t.left+t.right)/2-n;else if(T(o)){let t=Object.keys(o)[0],e=o[t];x=this.chart.scales[t].getPixelForValue(e)}_=this._getYAxisLabelAlignment(r).textAlign}"y"===e&&("start"===a?C="top":"end"===a&&(C="bottom"));var D=this._getLabelSizes();for(m=0,b=d.length;m<b;++m){v=d[m].label;let r=l.setContext(this.getContext(m)),t=(w=this.getPixelForTick(m)+l.labelOffset,k=(M=this._resolveTickFontOptions(m)).lineHeight,(S=A(v)?v.length:1)/2),e=r.color,i=r.textStrokeColor,a=r.textStrokeWidth,n,s=_;if(h?(x=w,"inner"===_&&(s=m===b-1?this.options.reverse?"left":"right":0===m?this.options.reverse?"right":"left":"center"),P="top"===o?"near"===c||0!=p?-S*k+k/2:"center"===c?-D.highest.height/2-t*k+k:-D.highest.height+k/2:"near"===c||0!=p?k/2:"center"===c?D.highest.height/2-t*k:D.highest.height-S*k,u&&(P*=-1),0==p||r.showLabelBackdrop||(x+=k/2*Math.sin(p))):(y=w,P=(1-S)*k/2),r.showLabelBackdrop){let t=R(r.backdropPadding),e=D.heights[m],i=D.widths[m],a=P-t.top,s=0-t.left;switch(C){case"middle":a-=e/2;break;case"bottom":a-=e}switch(_){case"center":s-=i/2;break;case"right":s-=i;break;case"inner":m===b-1?s-=i:0<m&&(s-=i/2)}n={left:s,top:a,width:i+t.width,height:e+t.height,color:r.backdropColor}}f.push({label:v,font:M,textOffset:P,options:{rotation:p,color:e,strokeColor:i,strokeWidth:a,textAlign:s,textBaseline:C,translation:[x,y],backdrop:n}})}return f}_getXAxisLabelAlignment(){var{position:t,ticks:e}=this.options;if(-E(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){var{position:e,ticks:{crossAlign:i,mirror:a,padding:s}}=this.options,t=t+s,r=this._getLabelSizes().widest.width;let n,o;return"left"===e?a?(o=this.right+s,"near"===i?n="left":"center"===i?(n="center",o+=r/2):(n="right",o+=r)):(o=this.right-t,"near"===i?n="right":"center"===i?(n="center",o-=r/2):(n="left",o=this.left)):"right"===e?a?(o=this.left+s,"near"===i?n="right":"center"===i?(n="center",o-=r/2):(n="left",o-=r)):(o=this.left+t,"near"===i?n="left":"center"===i?(n="center",o+=r/2):(n="right",o=this.right)):n="right",{textAlign:n,x:o}}_computeLabelArea(){var t,e;if(!this.options.ticks.mirror)return t=this.chart,"left"===(e=this.options.position)||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){var{ctx:t,options:{backgroundColor:e},left:i,top:a,width:s,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,a,s,r),t.restore())}getLineWidthForValue(e){var t,i=this.options.grid;return this._isVisible()&&i.display&&0<=(t=this.ticks.findIndex(t=>t.value===e))?i.setContext(this.getContext(t)).lineWidth:0}drawGrid(t){let e=this.options.grid,a=this.ctx,i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),s,r;var n=(t,e,i)=>{i.width&&i.color&&(a.save(),a.lineWidth=i.width,a.strokeStyle=i.color,a.setLineDash(i.borderDash||[]),a.lineDashOffset=i.borderDashOffset,a.beginPath(),a.moveTo(t.x,t.y),a.lineTo(e.x,e.y),a.stroke(),a.restore())};if(e.display)for(s=0,r=i.length;s<r;++s){let t=i[s];e.drawOnChartArea&&n({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),e.drawTicks&&n({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){var{chart:s,ctx:r,options:{border:n,grid:o}}=this,l=n.setContext(this.getContext()),n=n.display?l.width:0;if(n){var o=o.setContext(this.getContext(0)).lineWidth,h=this._borderValue;let t,e,i,a;this.isHorizontal()?(t=Re(s,this.left,n)-n/2,e=Re(s,this.right,o)+o/2,i=a=h):(i=Re(s,this.top,n)-n/2,a=Re(s,this.bottom,o)+o/2,t=e=h),r.save(),r.lineWidth=l.width,r.strokeStyle=l.color,r.beginPath(),r.moveTo(t,i),r.lineTo(e,a),r.stroke(),r.restore()}}drawLabels(i){if(this.options.ticks.display){let a=this.ctx,t=this._computeLabelArea(),e=(t&&Ve(a,t),this.getLabelItems(i));for(let i of e){let t=i.options,e=i.font;je(a,i.label,0,i.textOffset,e,t)}t&&Ne(a)}}drawTitle(){var{ctx:e,options:{position:i,title:a,reverse:s}}=this;if(a.display){var r=z(a.font),n=R(a.padding),o=a.align;let t=r.lineHeight/2;"bottom"===i||"center"===i||T(i)?(t+=n.bottom,A(a.text)&&(t+=r.lineHeight*(a.text.length-1))):t+=n.top;var{titleX:n,titleY:l,maxWidth:h,rotation:d}=((t,i,a,e)=>{var{top:s,left:r,bottom:n,right:o,chart:l}=t,{chartArea:l,scales:h}=l;let d,c,u,g=0;var p=n-s,f=o-r;if(t.isHorizontal()){if(c=L(e,r,o),T(a)){let t=Object.keys(a)[0],e=a[t];u=h[t].getPixelForValue(e)+p-i}else u="center"===a?(l.bottom+l.top)/2+p-i:za(t,a,i);d=o-r}else{if(T(a)){let t=Object.keys(a)[0],e=a[t];c=h[t].getPixelForValue(e)-f+i}else c="center"===a?(l.left+l.right)/2-f+i:za(t,a,i);u=L(e,n,s),g="left"===a?-C:C}return{titleX:c,titleY:u,maxWidth:d,rotation:g}})(this,t,i,o);je(e,a.text,0,0,r,{color:a.color,maxWidth:h,rotation:d,textAlign:((t,e,i)=>{let a=Ct(t);return a=i&&"right"!==e||!i&&"right"===e?"left"===(t=a)?"right":"right"===t?"left":t:a})(o,i,s),textBaseline:"middle",translation:[n,l]})}}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){var t=this.options,e=t.ticks&&t.ticks.z||0,i=O(t.grid&&t.grid.z,-1),t=O(t.border&&t.border.z,0);return this._isVisible()&&this.draw===Wa.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:t,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(e){var i=this.chart.getSortedVisibleDatasetMetas(),a=this.axis+"AxisID",s=[];let r,t;for(r=0,t=i.length;r<t;++r){let t=i[r];t[a]!==this.id||e&&t.type!==e||s.push(t)}return s}_resolveTickFontOptions(t){return z(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){var t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Ha{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){var e=Object.getPrototypeOf(t);let i;"id"in(n=e)&&"defaults"in n&&(i=this.register(e));var a,s,r,n=this.items,e=t.id,o=this.scope+"."+e;if(e)return e in n||(n[e]=t,n=t,e=o,a=i,a=$(Object.create(null),[a?I.get(a):{},I.get(e),n.defaults]),I.set(e,a),n.defaultRoutes&&(s=e,r=n.defaultRoutes,Object.keys(r).forEach(t=>{var e=t.split("."),i=e.pop(),e=[s].concat(e).join("."),t=r[t].split("."),a=t.pop(),t=t.join(".");I.route(e,i,t,a)})),n.descriptors&&I.describe(e,n.descriptors),this.override&&I.override(t.id,t.overrides)),o;throw new Error("class does not have id: "+t)}get(t){return this.items[t]}unregister(t){var e=this.items,t=t.id,i=this.scope;t in e&&delete e[t],i&&t in I[i]&&(delete I[i][t],this.override)&&delete fe[t]}}var w=new class{constructor(){this.controllers=new Ha(La,"datasets",!0),this.elements=new Ha(e,"elements"),this.plugins=new Ha(Object,"plugins"),this.scales=new Ha(Wa,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(i,t,a){[...t].forEach(t=>{var e=a||this._getRegistryForType(t);a||e.isForType(t)||e===this.plugins&&t.id?this._exec(i,e,t):k(t,t=>{var e=a||this._getRegistryForType(t);this._exec(i,e,t)})})}_exec(t,e,i){var a=K(t);c(i["before"+a],[],i),e[t](i),c(i["after"+a],[],i)}_getRegistryForType(e){for(let t=0;t<this._typedRegistries.length;t++){var i=this._typedRegistries[t];if(i.isForType(e))return i}return this.plugins}_get(t,e,i){e=e.get(t);if(void 0===e)throw new Error('"'+t+'" is not a registered '+i+".");return e}};class ja{constructor(){this._init=[]}notify(t,e,i,a){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));a=a?this._descriptors(t).filter(a):this._descriptors(t),i=this._notify(a,t,e,i);return"afterDestroy"===e&&(this._notify(a,t,"stop"),this._notify(this._init,t,"uninstall")),i}_notify(t,e,i,a){a=a||{};for(var s of t){let t=s.plugin;if(!1===c(t[i],[e,a,s.options],t)&&a.cancelable)return!1}return!0}invalidate(){S(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){var e;return this._cache||(e=this._cache=this._createDescriptors(t),this._notifyStateChanges(t),e)}_createDescriptors(t,e){var i=t&&t.config,a=O(i.options&&i.options.plugins,{}),i=(t=>{let i={},a=[],e=Object.keys(w.plugins.items);for(let t=0;t<e.length;t++)a.push(w.getPlugin(e[t]));var s=t.plugins||[];for(let e=0;e<s.length;e++){let t=s[e];-1===a.indexOf(t)&&(a.push(t),i[t.id]=!0)}return{plugins:a,localIds:i}})(i);if(!1!==a||e){var s,r,n,o,l,h,d,[c,{plugins:u,localIds:g},p,f]=[t,i,a,e],m=[],b=c.getContext();for(s of u){let t=s.id,e=(d=p[t],f||!1!==d?!0===d?{}:d:null);null!==e&&m.push({plugin:s,options:([d,{plugin:r,local:n},o,l]=[c.config,{plugin:s,local:g[t]},e,b],h=void 0,h=d.pluginScopeKeys(r),o=d.getOptionScopes(o,h),n&&r.defaults&&o.push(r.defaults),d.createResolver(o,l,[""],{scriptable:!1,indexable:!1,allKeys:!0}))})}return m}return[]}_notifyStateChanges(t){var e=this._oldCache||[],i=this._cache,a=(t,i)=>t.filter(e=>!i.some(t=>e.plugin.id===t.plugin.id));this._notify(a(e,i),t,"stop"),this._notify(a(i,e),t,"start")}}function $a(t,e){var i=I.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function Ua(t){if("x"===t||"y"===t||"r"===t)return t}function Ya(e,...t){if(Ua(e))return e;for(var i of t){let t=i.axis||("top"===(i=i.position)||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0)||1<e.length&&Ua(e[0].toLowerCase());if(t)return t}throw new Error(`Cannot determine type of '${e}' axis. Please provide 'axis' or 'position' option.`)}function qa(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function Xa(n,e){let r=fe[n.type]||{scales:{}},o=e.scales||{},l=$a(n.type,e),h=Object.create(null);return Object.keys(o).forEach(t=>{var e=o[t];if(!T(e))return console.error("Invalid scale configuration for scale: "+t);if(e._proxy)return console.warn("Ignoring resolver passed as options for scale: "+t);let i=Ya(t,e,((e,t)=>{if(t.data&&t.data.datasets){t=t.data.datasets.filter(t=>t.xAxisID===e||t.yAxisID===e);if(t.length)return qa(e,"x",t[0])||qa(e,"y",t[0])}return{}})(t,n),I.scales[e.type]),a=i===l?"_index_":"_value_",s=r.scales||{};h[t]=U(Object.create(null),[{axis:i},e,s[i],s[a]])}),n.data.datasets.forEach(a=>{let t=a.type||n.type,s=a.indexAxis||$a(t,e),r=(fe[t]||{}).scales||{};Object.keys(r).forEach(t=>{var e=((t,e)=>{let i=t;return"_index_"===t?i=e:"_value_"===t&&(i="x"===e?"y":"x"),i})(t,s),i=a[e+"AxisID"]||e;h[i]=h[i]||Object.create(null),U(h[i],[{axis:e},o[i],r[t]])})}),Object.keys(h).forEach(t=>{t=h[t];U(t,[I.scales[t.type],I.scale])}),h}function Ka(t){var e=t.options||(t.options={});e.plugins=O(e.plugins,{}),e.scales=Xa(t,e)}function Ga(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}let Ja=new Map,Qa=new Set;function Za(t,e){let i=Ja.get(t);return i||(i=e(),Ja.set(t,i),Qa.add(i)),i}let ts=(t,e,i)=>{e=p(e,i);void 0!==e&&t.add(e)};class es{constructor(t){this._config=((t=(t=t)||{}).data=Ga(t.data),Ka(t),t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=Ga(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){var t=this._config;this.clearCache(),Ka(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Za(t,()=>[["datasets."+t,""]])}datasetAnimationScopeKeys(t,e){return Za(t+".transition."+e,()=>[[`datasets.${t}.transitions.`+e,"transitions."+e],["datasets."+t,""]])}datasetElementScopeKeys(t,e){return Za(t+"-"+e,()=>[[`datasets.${t}.elements.`+e,"datasets."+t,"elements."+e,""]])}pluginScopeKeys(t){let e=t.id;return Za(this.type+"-plugin-"+e,()=>[["plugins."+e,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){var i=this._scopeCache;let a=i.get(t);return a&&!e||(a=new Map,i.set(t,a)),a}getOptionScopes(e,t,i){let{options:a,type:s}=this,r=this._cachedScopes(e,i),n=r.get(t);if(n)return n;let o=new Set;t.forEach(t=>{e&&(o.add(e),t.forEach(t=>ts(o,e,t))),t.forEach(t=>ts(o,a,t)),t.forEach(t=>ts(o,fe[s]||{},t)),t.forEach(t=>ts(o,I,t)),t.forEach(t=>ts(o,me,t))});i=Array.from(o);return 0===i.length&&i.push(Object.create(null)),Qa.has(t)&&r.set(t,i),i}chartOptionScopes(){var{options:t,type:e}=this;return[t,fe[e]||{},I.datasets[e]||{},{type:e},I,me]}resolveNamedOptions(t,e,i,a=[""]){var s={$shared:!0},{resolver:a,subPrefixes:r}=is(this._resolverCache,t,a);let n=a;((a,t)=>{var s,{isScriptable:r,isIndexable:n}=qe(a);for(s of t){let t=r(s),e=n(s),i=(e||t)&&a[s];if(t&&(g(i)||as(i))||e&&A(i))return 1}})(a,e)&&(s.$shared=!1,n=Ye(a,i=g(i)?i():i,this.createResolver(t,i,r)));for(let t of e)s[t]=n[t];return s}createResolver(t,e,i=[""],a){t=is(this._resolverCache,t,i).resolver;return T(e)?Ye(t,e,void 0,a):t}}function is(t,e,i){let a=t.get(e);a||(a=new Map,t.set(e,a));t=i.join();let s=a.get(t);return s||(s={resolver:Ue(e,i),subPrefixes:i.filter(t=>!t.toLowerCase().includes("hover"))},a.set(t,s)),s}let as=e=>T(e)&&Object.getOwnPropertyNames(e).some(t=>g(e[t])),ss=["top","bottom","left","right","chartArea"];function rs(t,e){return"top"===t||"bottom"===t||-1===ss.indexOf(t)&&"x"===e}function ns(i,a){return function(t,e){return t[i]===e[i]?t[a]-e[a]:t[i]-e[i]}}function os(t){var e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),c(i&&i.onComplete,[t],e)}function ls(t){var e=t.chart,i=e.options.animation;c(i&&i.onProgress,[t],e)}function hs(t){return xe()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t=t&&t.canvas?t.canvas:t}let ds={},cs=t=>{let e=hs(t);return Object.values(ds).filter(t=>t.canvas===e).pop()};function us(t,e,i){return(t.options.clip?t:e)[i]}class i{static defaults=I;static instances=ds;static overrides=fe;static registry=w;static version="4.4.1";static getChart=cs;static register(...t){w.add(...t),gs()}static unregister(...t){w.remove(...t),gs()}constructor(t,e){var e=this.config=new es(e),t=hs(t),i=cs(t);if(i)throw new Error("Canvas is already in use. Chart with ID '"+i.id+"' must be destroyed before the canvas with ID '"+i.canvas.id+"' can be reused.");var i=e.createResolver(e.chartOptionScopes(),this.getContext()),e=(this.platform=new(e.platform||ba(t)),this.platform.updateConfig(e),this.platform.acquireContext(t,i.aspectRatio)),t=e&&e.canvas,a=t&&t.height,s=t&&t.width;this.id=F(),this.ctx=e,this.canvas=t,this.width=s,this.height=a,this._options=i,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new ja,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Pt(t=>this.update(t),i.resizeDelay||0),this._dataChanges=[],ds[this.id]=this,e&&t?(r.listen(this,"complete",os),r.listen(this,"progress",ls),this._initialize(),this.attached&&this.update()):console.error("Failed to create chart: can't acquire context from the given item")}get aspectRatio(){var{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:a,_aspectRatio:s}=this;return S(t)?e&&s?s:a?i/a:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return w}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Ae(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return ze(this.canvas,this.ctx),this}stop(){return r.stop(this),this}resize(t,e){r.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){var i=this.options,a=this.canvas,s=i.maintainAspectRatio&&this.aspectRatio,a=this.platform.getMaximumSize(a,t,e,s),t=i.devicePixelRatio||this.platform.getDevicePixelRatio(),e=this.width?"resize":"attach";this.width=a.width,this.height=a.height,this._aspectRatio=this.aspectRatio,Ae(this,t,!0)&&(this.notifyPlugins("resize",{size:a}),c(i.onResize,[this,a],this),this.attached)&&this._doResize(e)&&this.render()}ensureScalesHaveIDs(){k(this.options.scales||{},(t,e)=>{t.id=e})}buildOrUpdateScales(){let n=this.options,a=n.scales,o=this.scales,l=Object.keys(o).reduce((t,e)=>(t[e]=!1,t),{}),t=[];k(t=a?t.concat(Object.keys(a).map(t=>{var e=a[t],t=Ya(t,e),i="r"===t,t="x"===t;return{options:e,dposition:i?"chartArea":t?"bottom":"left",dtype:i?"radialLinear":t?"category":"linear"}})):t,t=>{var e=t.options,i=e.id,a=Ya(i,e),s=O(e.type,t.dtype);void 0!==e.position&&rs(e.position,a)===rs(t.dposition)||(e.position=t.dposition),l[i]=!0;let r=null;i in o&&o[i].type===s?r=o[i]:(r=new(w.getScale(s))({id:i,type:s,ctx:this.ctx,chart:this}),o[r.id]=r),r.init(e,n)}),k(l,(t,e)=>{t||delete o[e]}),k(o,t=>{s.configure(this,t,t.options),s.addBox(this,t)})}_updateMetasets(){var t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((t,e)=>t.index-e.index),e<i){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(ns("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:i}}=this;t.length>i.length&&delete this._stacks,t.forEach((e,t)=>{0===i.filter(t=>t===e._dataset).length&&this._destroyDatasetMeta(t)})}buildOrUpdateControllers(){let s=[],e=this.data.datasets,r,t;for(this._removeUnreferencedMetasets(),r=0,t=e.length;r<t;r++){let t=e[r],a=this.getDatasetMeta(r);var n=t.type||this.config.type;if(a.type&&a.type!==n&&(this._destroyDatasetMeta(r),a=this.getDatasetMeta(r)),a.type=n,a.indexAxis=t.indexAxis||$a(n,this.options),a.order=t.order||0,a.index=r,a.label=""+t.label,a.visible=this.isDatasetVisible(r),a.controller)a.controller.updateIndex(r),a.controller.linkScales();else{let t=w.getController(n),{datasetElementType:e,dataElementType:i}=I.datasets[n];Object.assign(t,{dataElementType:w.getElement(i),datasetElementType:e&&w.getElement(e)}),a.controller=new t(this,r),s.push(a.controller)}}return this._updateMetasets(),s}_resetElements(){k(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config,i=(e.update(),this._options=e.createResolver(e.chartOptionScopes(),this.getContext())),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1!==this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})){var r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let a=0;for(let i=0,t=this.data.datasets.length;i<t;i++){let t=this.getDatasetMeta(i).controller,e=!s&&-1===r.indexOf(t);t.buildOrUpdateElements(e),a=Math.max(+t.getMaxOverflow(),a)}a=this._minPadding=i.layout.autoPadding?a:0,this._updateLayout(a),s||k(r,t=>{t.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(ns("z","_idx"));var{_active:t,_lastEvent:n}=this;n?this._eventHandler(n,!0):t.length&&this._updateHoverStyles(t,t,!0),this.render()}}_updateScales(){k(this.scales,t=>{s.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){var t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);J(e,i)&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){var e,i,a,s,r=this._hiddenIndices;for({method:e,start:i,count:a}of this._getUniformDataChanges()||[]){n=void 0;o=void 0;l=void 0;s=void 0;h=void 0;var n=r;var o=i;var l="_removeElements"===e?-a:a;let t=Object.keys(n);for(var h of t){let t=+h;t>=o&&(s=n[h],delete n[h],0<l||t>o)&&(n[t+l]=s)}}}_getUniformDataChanges(){let t=this._dataChanges;if(t&&t.length){this._dataChanges=[];var e=this.data.datasets.length,i=e=>new Set(t.filter(t=>t[0]===e).map((t,e)=>e+","+t.splice(1).join(","))),a=i(0);for(let t=1;t<e;t++)if(!J(a,i(t)))return;return Array.from(a).map(t=>t.split(",")).map(t=>({method:t[1],start:+t[2],count:+t[3]}))}}_updateLayout(i){if(!1!==this.notifyPlugins("beforeLayout",{cancelable:!0})){s.update(this,this.width,this.height,i);let t=this.chartArea,e=t.width<=0||t.height<=0;this._layers=[],k(this.boxes,t=>{e&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))},this),this._layers.forEach((t,e)=>{t._idx=e}),this.notifyPlugins("afterLayout")}}_updateDatasets(i){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:i,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let t=0,e=this.data.datasets.length;t<e;++t)this._updateDataset(t,g(i)?i({datasetIndex:t}):i);this.notifyPlugins("afterDatasetsUpdate",{mode:i})}}_updateDataset(t,e){var i=this.getDatasetMeta(t),t={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",t)&&(i.controller._update(e),t.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",t))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(r.has(this)?this.attached&&!r.running(this)&&r.start(this):(this.draw(),os({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){let{width:t,height:e}=this._resizeBeforeDraw;this._resize(t,e),this._resizeBeforeDraw=null}if(this.clear(),!(this.width<=0||this.height<=0)&&!1!==this.notifyPlugins("beforeDraw",{cancelable:!0})){let t=this._layers;for(e=0;e<t.length&&t[e].z<=0;++e)t[e].draw(this.chartArea);for(this._drawDatasets();e<t.length;++e)t[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}}_getSortedDatasetMetas(e){var i=this._sortedMetasets,a=[];let s,t;for(s=0,t=i.length;s<t;++s){let t=i[s];e&&!t.visible||a.push(t)}return a}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1!==this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})){var e=this.getSortedVisibleDatasetMetas();for(let t=e.length-1;0<=t;--t)this._drawDataset(e[t]);this.notifyPlugins("afterDatasetsDraw")}}_drawDataset(a){var t=this.ctx,e=a._clip,i=!e.disabled,s=(t=>{var{xScale:e,yScale:i}=a;return e&&i?{left:us(e,t,"left"),right:us(e,t,"right"),top:us(i,t,"top"),bottom:us(i,t,"bottom")}:t})(this.chartArea),r={meta:a,index:a.index,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetDraw",r)&&(i&&Ve(t,{left:!1===e.left?0:s.left-e.left,right:!1===e.right?this.width:s.right+e.right,top:!1===e.top?0:s.top-e.top,bottom:!1===e.bottom?this.height:s.bottom+e.bottom}),a.controller.draw(),i&&Ne(t),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(t){return _(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,a){e=$i.modes[e];return"function"==typeof e?e(this,t,i,a):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,a=i.filter(t=>t&&t._dataset===e).pop();return a||(a={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(a)),a}getContext(){return this.$context||(this.$context=Ci(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){var e=this.data.datasets[t];return!!e&&("boolean"==typeof(t=this.getDatasetMeta(t)).hidden?!t.hidden:!e.hidden)}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(e,t,i){let a=i?"show":"hide",s=this.getDatasetMeta(e),r=s.controller._resolveAnimations(void 0,a);G(t)?(s.data[t].hidden=!i,this.update()):(this.setDatasetVisibility(e,i),r.update(s,{visible:i}),this.update(t=>t.datasetIndex===e?a:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){var e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),r.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");var{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),ze(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete ds[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let i=this._listeners,a=this.platform,e=(t,e)=>{a.addEventListener(this,t,e),i[t]=e},s=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};k(this.options.events,t=>e(t,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});let i=this._responsiveListeners,a=this.platform,t=(t,e)=>{a.addEventListener(this,t,e),i[t]=e},e=(t,e)=>{i[t]&&(a.removeEventListener(this,t,e),delete i[t])},s=(t,e)=>{this.canvas&&this.resize(t,e)},r,n=()=>{e("attach",n),this.attached=!0,this.resize(),t("resize",s),t("detach",r)};r=()=>{this.attached=!1,e("resize",s),this._stop(),this._resize(0,0),t("attach",n)},(a.isAttached(this.canvas)?n:r)()}unbindEvents(){k(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},k(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(e,t,i){var a=i?"set":"remove";let s,r,n;for("dataset"===t&&this.getDatasetMeta(e[0].datasetIndex).controller["_"+a+"DatasetHoverStyle"](),r=0,n=e.length;r<n;++r){let t=(s=e[r])&&this.getDatasetMeta(s.datasetIndex).controller;t&&t[a+"HoverStyle"](s.element,s.datasetIndex,s.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){var e=this._active||[],t=t.map(({datasetIndex:t,index:e})=>{var i=this.getDatasetMeta(t);if(i)return{datasetIndex:t,element:i.data[e],index:e};throw new Error("No dataset found at index "+t)});N(t,e)||(this._active=t,this._lastEvent=null,this._updateHoverStyles(t,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(e){return 1===this._plugins._cache.filter(t=>t.plugin.id===e).length}_updateHoverStyles(t,e,i){var a=this.options.hover,s=(t,i)=>t.filter(e=>!i.some(t=>e.datasetIndex===t.datasetIndex&&e.index===t.index)),r=s(e,t),i=i?t:s(t,e);r.length&&this.updateHoverStyle(r,a.mode,!1),i.length&&a.mode&&this.updateHoverStyle(i,a.mode,!0)}_eventHandler(e,t){var i={event:e,replay:t,cancelable:!0,inChartArea:this.isPointInArea(e)},a=t=>(t.options.events||this.options.events).includes(e.native.type);if(!1!==this.notifyPlugins("beforeEvent",i,a))return t=this._handleEvent(e,t,i.inChartArea),i.cancelable=!1,this.notifyPlugins("afterEvent",i,a),(t||i.changed)&&this.render(),this}_handleEvent(t,e,i){let{_active:a=[],options:s}=this,r=e,n=this._getActiveElements(t,a,i,r),o=Q(t),l=(h=t,d=this._lastEvent,i&&"mouseout"!==h.type?o?d:h:null);i&&(this._lastEvent=null,c(s.onHover,[t,n,this],this),o)&&c(s.onClick,[t,n,this],this);var h,d=!N(n,a);return(d||e)&&(this._active=n,this._updateHoverStyles(n,a,e)),this._lastEvent=l,d}_getActiveElements(t,e,i,a){return"mouseout"===t.type?[]:i?(i=this.options.hover,this.getElementsAtEventForMode(t,i.mode,i,a)):e}}function gs(){k(i.instances,t=>t._plugins.invalidate())}function ps(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}var fs={_date:class sn{static override(t){Object.assign(sn.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return ps()}parse(){return ps()}format(){return ps()}add(){return ps()}diff(){return ps()}startOf(){return ps()}endOf(){return ps()}}};function ms(t){var e=t.iScale,i=(a=>{if(!a._cache.$bar){var s=a.getMatchingVisibleMetas(t.type);let i=[];for(let t=0,e=s.length;t<e;t++)i=i.concat(s[t].controller.getAllParsedValues(a));a._cache.$bar=Mt(i.sort((t,e)=>t-e))}return a._cache.$bar})(e);let a,s,r,n,o=e._length;var l=()=>{32767!==r&&-32768!==r&&(G(n)&&(o=Math.min(o,Math.abs(r-n)||o)),n=r)};for(a=0,s=i.length;a<s;++a)r=e.getPixelForValue(i[a]),l();for(n=void 0,a=0,s=e.ticks.length;a<s;++a)r=e.getPixelForTick(a),l();return o}function bs(i,a,s,r){if(A(i)){var n=i,o=a,l=s,h=r,d=l.parse(n[0],h),n=l.parse(n[1],h),h=Math.min(d,n),c=Math.max(d,n);let t=h,e=c;Math.abs(h)>Math.abs(c)&&(t=c,e=h),o[l.axis]=e,o._custom={barStart:t,barEnd:e,start:d,end:n,min:h,max:c}}else a[s.axis]=s.parse(i,r);return a}function vs(t,e,i,a){var s=t.iScale,r=t.vScale,n=s.getLabels(),o=s===r,l=[];let h,d,c,u;for(d=(h=i)+a;h<d;++h)u=e[h],(c={})[s.axis]=o||s.parse(n[h],h),l.push(bs(u,c,r,h));return l}function xs(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function ys(r,t,e,i){let a=t.borderSkipped;var s,n,o,l,h,t={};return a?!0===a?r.borderSkipped={top:!0,right:!0,bottom:!0,left:!0}:({start:s,end:n,reverse:o,top:l,bottom:h}=(()=>{let t,e,i,a,s;return i=r.horizontal?(t=r.base>r.x,e="left","right"):(t=r.base<r.y,e="bottom","top"),s=t?(a="end","start"):(a="start","end"),{start:e,end:i,reverse:t,top:a,bottom:s}})(),"middle"===a&&e&&(r.enableBorderRadius=!0,a=(e._top||0)===i?l:(e._bottom||0)===i?h:(t[_s(h,s,n,o)]=!0,l)),t[_s(a,s,n,o)]=!0,void(r.borderSkipped=t)):r.borderSkipped=t}function _s(t,e,i,a){return t=a?ws(t=t===e?i:t===i?e:t,i,e):ws(t,e,i)}function ws(t,e,i){return"start"===t?e:"end"===t?i:t}class Ms extends La{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(r){var t=r.data;if(t.labels.length&&t.datasets.length){let{pointStyle:a,color:s}=r.legend.options.labels;return t.labels.map((t,e)=>{var i=r.getDatasetMeta(0).controller.getStyle(e);return{text:t,fillStyle:i.backgroundColor,strokeStyle:i.borderColor,fontColor:s,lineWidth:i.borderWidth,pointStyle:a,hidden:!r.getDataVisibility(e),index:e}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(a,s){let r=this.getDataset().data,n=this._cachedMeta;if(!1===this._parsing)n._parsed=r;else{let t,e,i=t=>+r[t];if(T(r[a])){let{key:e="value"}=this._parsing;i=t=>+p(r[t],e)}for(e=(t=a)+s;t<e;++t)n._parsed[t]=i(t)}}_getRotation(){return E(this.options.rotation-90)}_getCircumference(){return E(this.options.circumference)}_getRotationExtents(){let e=x,i=-x;for(let t=0;t<this.chart.data.datasets.length;++t){var a,s;this.chart.isDatasetVisible(t)&&this.chart.getDatasetMeta(t).type===this._type&&(a=(s=this.chart.getDatasetMeta(t).controller)._getRotation(),s=s._getCircumference(),e=Math.min(e,a),i=Math.max(i,a+s))}return{rotation:e,circumference:i-e}}update(t){var e=this.chart.chartArea,i=this._cachedMeta,a=i.data,s=this.getMaxBorderWidth()+this.getMaxOffset(a)+this.options.spacing,r=Math.max((Math.min(e.width,e.height)-s)/2,0),r=Math.min(B(this.options.cutout,r),1),n=this._getRingWeight(this.index),{circumference:o,rotation:l}=this._getRotationExtents(),{ratioX:l,ratioY:o,offsetX:h,offsetY:d}=((u,g,p)=>{let f=1,m=1,b=0,v=0;if(g<x){let a=u,s=a+g,t=Math.cos(a),e=Math.sin(a),i=Math.cos(s),r=Math.sin(s),n=(t,e,i)=>ft(t,a,s,!0)?1:Math.max(e,e*p,i,i*p),o=(t,e,i)=>ft(t,a,s,!0)?-1:Math.min(e,e*p,i,i*p),l=n(0,t,i),h=n(C,e,r),d=o(P,t,i),c=o(P+C,e,r);f=(l-d)/2,m=(h-c)/2,b=-(l+d)/2,v=-(h+c)/2}return{ratioX:f,ratioY:m,offsetX:b,offsetY:v}})(l,o,r),l=(e.width-s)/l,e=(e.height-s)/o,s=Math.max(Math.min(l,e)/2,0),o=V(this.options.radius,s),l=(o-Math.max(o*r,0))/this._getVisibleDatasetWeightTotal();this.offsetX=h*o,this.offsetY=d*o,i.total=this.calculateTotal(),this.outerRadius=o-l*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-l*n,0),this.updateElements(a,0,a.length,t)}_circumference(t,e){var i=this.options,a=this._cachedMeta,s=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===a._parsed[t]||a.data[t].hidden?0:this.calculateCircumference(a._parsed[t]*s/x)}updateElements(a,t,e,s){let r="reset"===s,i=this.chart,n=i.chartArea,o=i.options.animation,l=(n.left+n.right)/2,h=(n.top+n.bottom)/2,d=r&&o.animateScale,c=d?0:this.innerRadius,u=d?0:this.outerRadius,{sharedOptions:g,includeOptions:p}=this._getSharedOptions(t,s),f,m=this._getRotation();for(f=0;f<t;++f)m+=this._circumference(f,r);for(f=t;f<t+e;++f){let t=this._circumference(f,r),e=a[f],i={x:l+this.offsetX,y:h+this.offsetY,startAngle:m,endAngle:m+t,circumference:t,outerRadius:u,innerRadius:c};p&&(i.options=g||this.resolveDataElementOptions(f,e.active?"active":s)),m+=t,this.updateElement(e,f,i,s)}}calculateTotal(){var t=this._cachedMeta,e=t.data;let i,a=0;for(i=0;i<e.length;i++){var s=t._parsed[i];null===s||isNaN(s)||!this.chart.getDataVisibility(i)||e[i].hidden||(a+=Math.abs(s))}return a}calculateCircumference(t){var e=this._cachedMeta.total;return 0<e&&!isNaN(t)?x*(Math.abs(t)/e):0}getLabelAndValue(t){var e=this._cachedMeta,i=this.chart,a=i.data.labels||[],e=ue(e._parsed[t],i.options.locale);return{label:a[t]||"",value:e}}getMaxBorderWidth(t){let e=0;var i=this.chart;let a,s,r,n,o;if(!t)for(a=0,s=i.data.datasets.length;a<s;++a)if(i.isDatasetVisible(a)){t=(r=i.getDatasetMeta(a)).data,n=r.controller;break}if(!t)return 0;for(a=0,s=t.length;a<s;++a)"inner"!==(o=n.resolveDataElementOptions(a)).borderAlign&&(e=Math.max(e,o.borderWidth||0,o.hoverBorderWidth||0));return e}getMaxOffset(i){let a=0;for(let e=0,t=i.length;e<t;++e){let t=this.resolveDataElementOptions(e);a=Math.max(a,t.offset||0,t.hoverOffset||0)}return a}_getRingWeightOffset(e){let i=0;for(let t=0;t<e;++t)this.chart.isDatasetVisible(t)&&(i+=this._getRingWeight(t));return i}_getRingWeight(t){return Math.max(O(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class ks extends La{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(r){var t=r.data;if(t.labels.length&&t.datasets.length){let{pointStyle:a,color:s}=r.legend.options.labels;return t.labels.map((t,e)=>{var i=r.getDatasetMeta(0).controller.getStyle(e);return{text:t,fillStyle:i.backgroundColor,strokeStyle:i.borderColor,fontColor:s,lineWidth:i.borderWidth,pointStyle:a,hidden:!r.getDataVisibility(e),index:e}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){var e=this._cachedMeta,i=this.chart,a=i.data.labels||[],e=ue(e._parsed[t].r,i.options.locale);return{label:a[t]||"",value:e}}parseObjectData(t,e,i,a){return ai.bind(this)(t,e,i,a)}update(t){var e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,a={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((t,e)=>{var i=this.getParsed(e).r;!isNaN(i)&&this.chart.getDataVisibility(e)&&(i<a.min&&(a.min=i),i>a.max)&&(a.max=i)}),a}_updateRadius(){var t=this.chart,e=t.chartArea,i=t.options,e=Math.min(e.right-e.left,e.bottom-e.top),e=Math.max(e/2,0),i=(e-Math.max(i.cutoutPercentage?e/100*i.cutoutPercentage:1,0))/t.getVisibleDatasetCount();this.outerRadius=e-i*this.index,this.innerRadius=this.outerRadius-i}updateElements(s,t,e,r){var n="reset"===r,o=this.chart,l=o.options.animation,h=this._cachedMeta.rScale,d=h.xCenter,c=h.yCenter,u=h.getIndexAngle(0)-.5*P;let g,p=u;var f=360/this.countVisibleElements();for(g=0;g<t;++g)p+=this._computeAngle(g,r,f);for(g=t;g<t+e;g++){let t=s[g],e=p,i=p+this._computeAngle(g,r,f),a=o.getDataVisibility(g)?h.getDistanceFromCenterForValue(this.getParsed(g).r):0;p=i,n&&(l.animateScale&&(a=0),l.animateRotate)&&(e=i=u);var m={x:d,y:c,innerRadius:0,outerRadius:a,startAngle:e,endAngle:i,options:this.resolveDataElementOptions(g,t.active?"active":r)};this.updateElement(t,g,m,r)}}countVisibleElements(){var t=this._cachedMeta;let i=0;return t.data.forEach((t,e)=>{!isNaN(this.getParsed(e).r)&&this.chart.getDataVisibility(e)&&i++}),i}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?E(this.resolveDataElementOptions(t,e).angle||i):0}}Te=Object.freeze({__proto__:null,BarController:class extends La{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,a){return vs(t,e,i,a)}parseArrayData(t,e,i,a){return vs(t,e,i,a)}parseObjectData(t,e,i,a){var{iScale:s,vScale:r}=t,{xAxisKey:t="x",yAxisKey:n="y"}=this._parsing,o="x"===s.axis?t:n,l="x"===r.axis?t:n,h=[];let d,c,u,g;for(c=(d=i)+a;d<c;++d)g=e[d],(u={})[s.axis]=s.parse(p(g,o),d),h.push(bs(p(g,l),u,r,d));return h}updateRangeFromParsed(t,e,i,a){super.updateRangeFromParsed(t,e,i,a);a=i._custom;a&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,a.min),t.max=Math.max(t.max,a.max))}getMaxOverflow(){return 0}getLabelAndValue(t){var{iScale:e,vScale:i}=this._cachedMeta,t=this.getParsed(t),a=t._custom,a=xs(a)?"["+a.start+", "+a.end+"]":""+i.getLabelForValue(t[i.axis]);return{label:""+e.getLabelForValue(t[e.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){var e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(n,t,e,o){var l,h,d="reset"===o,{index:c,_cachedMeta:{vScale:u}}=this,g=u.getBasePixel(),p=u.isHorizontal(),f=this._getRuler(),{sharedOptions:m,includeOptions:b}=this._getSharedOptions(t,o);for(let r=t;r<t+e;r++){let t=this.getParsed(r),e=d||S(t[u.axis])?{base:g,head:g}:this._calculateBarValuePixels(r),i=this._calculateBarIndexPixels(r,f),a=(t._stacks||{})[u.axis],s={horizontal:p,base:e.base,enableBorderRadius:!a||xs(t._custom)||c===a._top||c===a._bottom,x:p?e.head:i.center,y:p?i.center:e.head,height:p?i.size:Math.abs(e.size),width:p?Math.abs(e.size):i.size};b&&(s.options=m||this.resolveDataElementOptions(r,n[r].active?"active":o));var v=s.options||n[r].options;ys(s,v,a,c),[v,{inflateAmount:l},h]=[s,v,f.ratio],v.inflateAmount="auto"===l?1===h?.33:0:l,this.updateElement(n[r],r,s,o)}}_getStacks(e,i){let t=this._cachedMeta.iScale,a=t.getMatchingVisibleMetas(this._type).filter(t=>t.controller.options.grouped),s=t.options.stacked,r=[];for(let t of a)if((void 0===i||!(t=>{var e=t.controller.getParsed(i),e=e&&e[t.vScale.axis];if(S(e)||isNaN(e))return!0})(t))&&((!1===s||-1===r.indexOf(t.stack)||void 0===s&&void 0===t.stack)&&r.push(t.stack),t.index===e))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,i){t=this._getStacks(t,i),i=void 0!==e?t.indexOf(e):-1;return-1===i?t.length-1:i}_getRuler(){var t=this.options,e=this._cachedMeta,i=e.iScale,a=[];let s,r;for(s=0,r=e.data.length;s<r;++s)a.push(i.getPixelForValue(this.getParsed(s)[i.axis],s));var n=t.barThickness;return{min:n||ms(e),pixels:a,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:t.grouped,ratio:n?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){let{_cachedMeta:{vScale:s,_stacked:r,index:n},options:{base:e,minBarLength:o}}=this,l=e||0,h=this.getParsed(t),i=h._custom,d=xs(i),c,u,g=h[s.axis],a=0,p=r?this.applyStack(s,h,r):g;p!==g&&(a=p-g,p=g),d&&(g=i.barStart,p=i.barEnd-i.barStart,0!==g&&y(g)!==y(i.barEnd)&&(a=0),a+=g);var f,m,b=S(e)||d?a:e;let v=s.getPixelForValue(b);if(c=this.chart.getDataVisibility(t)?s.getPixelForValue(a+p):v,u=c-v,Math.abs(u)<o){u=(b=u,f=s,m=l,(0!==b?y(b):(f.isHorizontal()?1:-1)*(f.min>=m?1:-1))*o),g===l&&(v-=u/2);let t=s.getPixelForDecimal(0),e=s.getPixelForDecimal(1),i=Math.min(t,e),a=Math.max(t,e);v=Math.max(Math.min(v,a),i),c=v+u,r&&!d&&(h._stacks[s.axis]._visualValues[n]=s.getValueForPixel(c)-s.getValueForPixel(v))}if(v===s.getPixelForValue(l)){let t=y(u)*s.getLineWidthForValue(l)/2;v+=t,u-=t}return{size:u,base:v,head:c,center:c+u/2}}_calculateBarIndexPixels(a,s){let t=s.scale,r=this.options,n=r.skipNull,o=O(r.maxBarThickness,1/0),l,h;if(s.grouped){let t=n?this._getStackCount(a):s.stackCount,e=("flex"===r.barThickness?(t,e,i,a)=>{var s=e.pixels,r=s[t];let n=0<t?s[t-1]:null,o=t<s.length-1?s[t+1]:null;return s=i.categoryPercentage,null===n&&(n=r-(null===o?e.end-e.start:o-r)),null===o&&(o=r+r-n),t=r-(r-Math.min(n,o))/2*s,{chunk:Math.abs(o-n)/2*s/a,ratio:i.barPercentage,start:t}}:(t,e,i,a)=>{var s=i.barThickness;let r,n;return n=S(s)?(r=e.min*i.categoryPercentage,i.barPercentage):(r=s*a,1),{chunk:r/a,ratio:n,start:e.pixels[t]-r/2}})(a,s,r,t),i=this._getStackIndex(this.index,this._cachedMeta.stack,n?a:void 0);l=e.start+e.chunk*i+e.chunk/2,h=Math.min(o,e.chunk*e.ratio)}else l=t.getPixelForValue(this.getParsed(a)[t.axis],a),h=Math.min(o,s.min*s.ratio);return{base:l-h/2,head:l+h/2,center:l,size:h}}draw(){var t=this._cachedMeta,e=t.vScale,i=t.data,a=i.length;let s=0;for(;s<a;++s)null!==this.getParsed(s)[e.axis]&&i[s].draw(this._ctx)}},BubbleController:class extends La{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,a){var s=super.parsePrimitiveData(t,e,i,a);for(let t=0;t<s.length;t++)s[t]._custom=this.resolveDataElementOptions(t+i).radius;return s}parseArrayData(t,i,a,e){var s=super.parseArrayData(t,i,a,e);for(let e=0;e<s.length;e++){let t=i[a+e];s[e]._custom=O(t[2],this.resolveDataElementOptions(e+a).radius)}return s}parseObjectData(t,i,a,e){var s=super.parseObjectData(t,i,a,e);for(let e=0;e<s.length;e++){let t=i[a+e];s[e]._custom=O(t&&t.r&&+t.r,this.resolveDataElementOptions(e+a).radius)}return s}getMaxOverflow(){var e=this._cachedMeta.data;let i=0;for(let t=e.length-1;0<=t;--t)i=Math.max(i,e[t].size(this.resolveDataElementOptions(t))/2);return 0<i&&i}getLabelAndValue(t){var e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:e,yScale:a}=e,s=this.getParsed(t),e=e.getLabelForValue(s.x),a=a.getLabelForValue(s.y),s=s._custom;return{label:i[t]||"",value:"("+e+", "+a+(s?", "+s:"")+")"}}update(t){var e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(n,t,e,o){var l="reset"===o,{iScale:h,vScale:d}=this._cachedMeta,{sharedOptions:c,includeOptions:u}=this._getSharedOptions(t,o),g=h.axis,p=d.axis;for(let r=t;r<t+e;r++){let t=n[r],e=!l&&this.getParsed(r),i={},a=i[g]=l?h.getPixelForDecimal(.5):h.getPixelForValue(e[g]),s=i[p]=l?d.getBasePixel():d.getPixelForValue(e[p]);i.skip=isNaN(a)||isNaN(s),u&&(i.options=c||this.resolveDataElementOptions(r,t.active?"active":o),l)&&(i.options.radius=0),this.updateElement(t,r,i,o)}}resolveDataElementOptions(t,e){var i=this.getParsed(t);let a=super.resolveDataElementOptions(t,e);t=(a=a.$shared?Object.assign({},a,{$shared:!1}):a).radius;return"active"!==e&&(a.radius=0),a.radius+=O(i&&i._custom,t),a}},DoughnutController:Ms,LineController:class extends La{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){var e=this._cachedMeta,{dataset:i,data:a=[],_dataset:s}=e,r=this.chart._animationsDisabled;let{start:n,count:o}=At(e,a,r);this._drawStart=n,this._drawCount=o,Tt(e)&&(n=0,o=a.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!s._decimated,i.points=a;e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0),e.segment=this.options.segment,this.updateElement(i,void 0,{animated:!r,options:e},t),this.updateElements(a,n,o,t)}updateElements(a,s,t,r){let n="reset"===r,{iScale:o,vScale:l,_stacked:h,_dataset:d}=this._cachedMeta,{sharedOptions:c,includeOptions:u}=this._getSharedOptions(s,r),g=o.axis,p=l.axis,{spanGaps:e,segment:f}=this.options,m=ot(e)?e:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||n||"none"===r,v=s+t,x=a.length,y=0<s&&this.getParsed(s-1);for(let i=0;i<x;++i){let t=a[i],e=b?t:{};var _,w,M,k;i<s||i>=v?e.skip=!0:(w=S((_=this.getParsed(i))[p]),M=e[g]=o.getPixelForValue(_[g],i),k=e[p]=n||w?l.getBasePixel():l.getPixelForValue(h?this.applyStack(l,_,h):_[p],i),e.skip=isNaN(M)||isNaN(k)||w,e.stop=0<i&&Math.abs(_[g]-y[g])>m,f&&(e.parsed=_,e.raw=d.data[i]),u&&(e.options=c||this.resolveDataElementOptions(i,t.active?"active":r)),b||this.updateElement(t,i,e,r),y=_)}}getMaxOverflow(){var t,e=this._cachedMeta,i=e.dataset,i=i.options&&i.options.borderWidth||0,e=e.data||[];return e.length?(t=e[0].size(this.resolveDataElementOptions(0)),e=e[e.length-1].size(this.resolveDataElementOptions(e.length-1)),Math.max(i,t,e)/2):i}draw(){var t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}},PieController:class extends Ms{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}},PolarAreaController:ks,RadarController:class extends La{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){var e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,a){return ai.bind(this)(t,e,i,a)}update(e){let t=this._cachedMeta,i=t.dataset,a=t.data||[],s=t.iScale.getLabels();if(i.points=a,"resize"!==e){let t=this.resolveDatasetElementOptions(e);this.options.showLine||(t.borderWidth=0);var r={_loop:!0,_fullLoop:s.length===a.length,options:t};this.updateElement(i,void 0,r,e)}this.updateElements(a,0,a.length,e)}updateElements(o,t,e,l){var h=this._cachedMeta.rScale,d="reset"===l;for(let n=t;n<t+e;n++){let t=o[n],e=this.resolveDataElementOptions(n,t.active?"active":l),i=h.getPointPositionForValue(n,this.getParsed(n).r),a=d?h.xCenter:i.x,s=d?h.yCenter:i.y,r={x:a,y:s,angle:i.angle,skip:isNaN(a)||isNaN(s),options:e};this.updateElement(t,n,r,l)}}},ScatterController:class extends La{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){var e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:e,yScale:a}=e,s=this.getParsed(t),e=e.getLabelForValue(s.x),a=a.getLabelForValue(s.y);return{label:i[t]||"",value:"("+e+", "+a+")"}}update(i){var a=this._cachedMeta,{data:s=[]}=a,r=this.chart._animationsDisabled;let{start:t,count:e}=At(a,s,r);if(this._drawStart=t,this._drawCount=e,Tt(a)&&(t=0,e=s.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:t,_dataset:e}=a;t._chart=this.chart,t._datasetIndex=this.index,t._decimated=!!e._decimated,t.points=s;var n=this.resolveDatasetElementOptions(i);n.segment=this.options.segment,this.updateElement(t,void 0,{animated:!r,options:n},i)}else this.datasetElementType&&(delete a.dataset,this.datasetElementType=!1);this.updateElements(s,t,e,i)}addElements(){var t=this.options.showLine;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(o,t,e,l){let h="reset"===l,{iScale:d,vScale:c,_stacked:u,_dataset:g}=this._cachedMeta,i=this.resolveDataElementOptions(t,l),p=this.getSharedOptions(i),f=this.includeOptions(l,p),m=d.axis,b=c.axis,{spanGaps:a,segment:v}=this.options,x=ot(a)?a:Number.POSITIVE_INFINITY,y=this.chart._animationsDisabled||h||"none"===l,_=0<t&&this.getParsed(t-1);for(let n=t;n<t+e;++n){let t=o[n],e=this.getParsed(n),i=y?t:{},a=S(e[b]),s=i[m]=d.getPixelForValue(e[m],n),r=i[b]=h||a?c.getBasePixel():c.getPixelForValue(u?this.applyStack(c,e,u):e[b],n);i.skip=isNaN(s)||isNaN(r)||a,i.stop=0<n&&Math.abs(e[m]-_[m])>x,v&&(i.parsed=e,i.raw=g.data[n]),f&&(i.options=p||this.resolveDataElementOptions(n,t.active?"active":l)),y||this.updateElement(t,n,i,l),_=e}this.updateSharedOptions(p,l,i)}getMaxOverflow(){var t,e,i=this._cachedMeta,a=i.data||[];if(this.options.showLine)return i=(i=i.dataset).options&&i.options.borderWidth||0,a.length?(t=a[0].size(this.resolveDataElementOptions(0)),e=a[a.length-1].size(this.resolveDataElementOptions(a.length-1)),Math.max(i,t,e)/2):i;{let e=0;for(let t=a.length-1;0<=t;--t)e=Math.max(e,a[t].size(this.resolveDataElementOptions(t))/2);return 0<e&&e}}}});function Ss(t,e,i,a){return{x:i+t*Math.cos(e),y:a+t*Math.sin(e)}}function Ps(s,t,e,i,a,r){var{x:n,y:o,startAngle:l,pixelMargin:h,innerRadius:d}=t,c=Math.max(t.outerRadius+i+e-h,0),h=0<d?d+i+e+h:0;let u=0;var g=a-l;if(i){let t=((0<d?d-i:0)+(0<c?c-i:0))/2;u=(g-(0!=t?g*t/(t+i):g))/2}var d=(g-Math.max(.001,g*c-e/P)/c)/2,g=l+d+u,l=a-d-u,{outerStart:d,outerEnd:p,innerStart:f,innerEnd:m}=((t,e,i,a)=>{t=wi(t.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]);let s=(i-e)/2,r=Math.min(s,a*e/2),n=t=>{var e=(i-Math.min(s,t))*a/2;return D(t,0,Math.min(s,e))};return{outerStart:n(t.outerStart),outerEnd:n(t.outerEnd),innerStart:D(t.innerStart,0,r),innerEnd:D(t.innerEnd,0,r)}})(t,h,c,l-g),b=c-d,v=c-p,x=g+d/b,y=l-p/v,_=h+f,w=h+m,M=g+f/_,k=l-m/w;if(s.beginPath(),r){let t=(x+y)/2;if(s.arc(n,o,c,x,t),s.arc(n,o,c,t,y),0<p){let t=Ss(v,y,n,o);s.arc(t.x,t.y,p,y,l+C)}let e=Ss(w,l,n,o);if(s.lineTo(e.x,e.y),0<m){let t=Ss(w,k,n,o);s.arc(t.x,t.y,m,l+C,k+Math.PI)}let i=(l-m/h+(g+f/h))/2;if(s.arc(n,o,h,l-m/h,i,!0),s.arc(n,o,h,i,g+f/h,!0),0<f){let t=Ss(_,M,n,o);s.arc(t.x,t.y,f,M+Math.PI,g-C)}let a=Ss(b,g,n,o);if(s.lineTo(a.x,a.y),0<d){let t=Ss(b,x,n,o);s.arc(t.x,t.y,d,g-C,x)}}else{s.moveTo(n,o);let t=Math.cos(x)*c+n,e=Math.sin(x)*c+o,i=(s.lineTo(t,e),Math.cos(y)*c+n),a=Math.sin(y)*c+o;s.lineTo(i,a)}s.closePath()}function Cs(t,e,i=e){t.lineCap=O(i.borderCapStyle,e.borderCapStyle),t.setLineDash(O(i.borderDash,e.borderDash)),t.lineDashOffset=O(i.borderDashOffset,e.borderDashOffset),t.lineJoin=O(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=O(i.borderWidth,e.borderWidth),t.strokeStyle=O(i.borderColor,e.borderColor)}function Ds(t,e,i){t.lineTo(i.x,i.y)}function As(t,e,i={}){var t=t.length,{start:i=0,end:a=t-1}=i,{start:s,end:r}=e,n=Math.max(i,s),o=Math.min(a,r);return{count:t,start:n,loop:e.loop,ilen:o<n&&!(i<s&&a<s||r<i&&r<a)?t+o-n:o-n}}function Ts(t,e,i,a){let{points:s,options:r}=e,{count:n,start:o,loop:l,ilen:h}=As(s,i,a),d=r.stepped?We:r.tension||"monotone"===r.cubicInterpolationMode?He:Ds,c,u,g,{move:p=!0,reverse:f}=a||{};for(c=0;c<=h;++c)(u=s[(o+(f?h-c:c))%n]).skip||(p?(t.moveTo(u.x,u.y),p=!1):d(t,g,u,f,r.stepped),g=u);return l&&(u=s[(o+(f?h:0))%n],d(t,g,u,f,r.stepped)),!!l}function Os(a,t,e,i){let s=t.points,{count:r,start:n,ilen:o}=As(s,e,i),{move:l=!0,reverse:h}=i||{},d,c,u,g,p,f,m=0,b=0;var v=t=>(n+(h?o-t:t))%r,x=()=>{g!==p&&(a.lineTo(m,p),a.lineTo(m,g),a.lineTo(m,f))};for(l&&(c=s[v(0)],a.moveTo(c.x,c.y)),d=0;d<=o;++d)if(!(c=s[v(d)]).skip){let t=c.x,e=c.y,i=0|t;i===u?(e<g?g=e:e>p&&(p=e),m=(b*m+t)/++b):(x(),a.lineTo(t,e),u=i,b=0,g=p=e),f=e}x()}function Es(t){var e=t.options,i=e.borderDash&&e.borderDash.length;return t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i?Ts:Os}let Ls="function"==typeof Path2D;function Is(t,e,i,a){(Ls&&!e.options.segment?(t,e,i,a)=>{let s=e._path;s||(s=e._path=new Path2D,e.path(s,i,a)&&s.closePath()),Cs(t,e.options),t.stroke(s)}:(t,e,i,a)=>{var s,{segments:r,options:n}=e,o=Es(e);for(s of r)Cs(t,n,s.style),t.beginPath(),o(t,e,s,{start:i,end:i+a-1})&&t.closePath(),t.stroke()})(t,e,i,a)}class Rs extends e{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){var i,a=this.options;!a.tension&&"monotone"!==a.cubicInterpolationMode||a.stepped||this._pointsUpdated||(i=a.spanGaps?this._loop:this._fullLoop,di(this._points,a,t,i,e),this._pointsUpdated=!0)}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Ri(this,this.options.segment))}first(){var t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){var t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(r,n){var o=this.options,l=r[n],h=this.points,d=Ii(this,{property:n,start:l,end:l});if(d.length){var c,u=[],g=o.stepped?mi:o.tension||"monotone"===o.cubicInterpolationMode?bi:fi;let s,t;for(s=0,t=d.length;s<t;++s){let{start:t,end:e}=d[s],i=h[t],a=h[e];i===a?u.push(i):((c=g(i,a,Math.abs((l-i[n])/(a[n]-i[n])),o.stepped))[n]=r[n],u.push(c))}return 1===u.length?u[0]:u}}pathSegment(t,e,i){return Es(this)(t,this,e,i)}path(t,e,i){var a,s=this.segments,r=Es(this);let n=this._loop;e=e||0,i=i||this.points.length-e;for(a of s)n&=r(t,this,a,{start:e,end:e+i-1});return!!n}draw(t,e,i,a){var s=this.options||{};(this.points||[]).length&&s.borderWidth&&(t.save(),Is(t,this,i,a),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function zs(t,e,i,a){var s=t.options,{[i]:t}=t.getProps([i],a);return Math.abs(e-t)<s.radius+s.hitRadius}function Fs(t,e){var{x:e,y:i,base:a,width:s,height:r}=t.getProps(["x","y","base","width","height"],e);let n,o,l,h,d;return h=t.horizontal?(d=r/2,n=Math.min(e,a),o=Math.max(e,a),l=i-d,i+d):(d=s/2,n=e-d,o=e+d,l=Math.min(i,a),Math.max(i,a)),{left:n,top:l,right:o,bottom:h}}function Bs(t,e,i,a){return t?0:D(e,i,a)}function Vs(t,e,i,a){var s=null===e,r=null===i,t=t&&!(s&&r)&&Fs(t,a);return t&&(s||d(e,t.left,t.right))&&(r||d(i,t.top,t.bottom))}function Ns(t,e){t.rect(e.x,e.y,e.w,e.h)}function Ws(t,e,i={}){var a=t.x!==i.x?-e:0,s=t.y!==i.y?-e:0;return{x:t.x+a,y:t.y+s,w:t.w+((t.x+t.w!==i.x+i.w?e:0)-a),h:t.h+((t.y+t.h!==i.y+i.h?e:0)-s),radius:t.radius}}var Hs=Object.freeze({__proto__:null,ArcElement:class extends e{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){var{angle:t,distance:e}=ut(this.getProps(["x","y"],i),{x:t,y:e}),{startAngle:i,endAngle:a,innerRadius:s,outerRadius:r,circumference:n}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),o=(this.options.spacing+this.options.borderWidth)/2,n=O(n,a-i)>=x||ft(t,i,a),t=d(e,s+o,r+o);return n&&t}getCenterPoint(t){var{x:t,y:e,startAngle:i,endAngle:a,innerRadius:s,outerRadius:r}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:n,spacing:o}=this.options,i=(i+a)/2,a=(s+r+o+n)/2;return{x:t+Math.cos(i)*a,y:e+Math.sin(i)*a}}tooltipPosition(t){return this.getCenterPoint(t)}draw(e){var{options:i,circumference:a}=this,s=(i.offset||0)/4,r=(i.spacing||0)/2,n=i.circular;if(this.pixelMargin="inner"===i.borderAlign?.33:0,this.fullCircles=a>x?Math.floor(a/x):0,!(0===a||this.innerRadius<0||this.outerRadius<0)){e.save();var o=(this.startAngle+this.endAngle)/2,o=(e.translate(Math.cos(o)*s,Math.sin(o)*s),s*(1-Math.sin(Math.min(P,a||0))));e.fillStyle=i.backgroundColor,e.strokeStyle=i.borderColor;{var l=e;s=this;a=o;i=r;var h=n;var{fullCircles:d,startAngle:c,circumference:u}=s;let t=s.endAngle;if(d){Ps(l,s,a,i,t,h);for(let t=0;t<d;++t)l.fill();isNaN(u)||(t=c+(u%x||x))}Ps(l,s,a,i,t,h),l.fill()}var g=e,c=this,u=o,s=r,a=n,{fullCircles:p,startAngle:i,circumference:h,options:o}=c,{borderWidth:r,borderJoinStyle:n,borderDash:f,borderDashOffset:m}=o,o="inner"===o.borderAlign;if(r){g.setLineDash(f||[]),g.lineDashOffset=m,o?(g.lineWidth=2*r,g.lineJoin=n||"round"):(g.lineWidth=r,g.lineJoin=n||"bevel");let t=c.endAngle;if(p){Ps(g,c,u,s,t,a);for(let t=0;t<p;++t)g.stroke();isNaN(h)||(t=i+(h%x||x))}if(o){f=g;m=c;r=t;var{startAngle:m,pixelMargin:n,x:i,y:h,outerRadius:o,innerRadius:b}=m,v=n/o;f.beginPath(),f.arc(i,h,o,m-v,r+v),n<b?f.arc(i,h,b,r+(v=n/b),m-v,!0):f.arc(i,h,n,r+C,m-C),f.closePath(),f.clip()}p||(Ps(g,c,u,s,t,a),g.stroke())}e.restore()}}},BarElement:class extends e{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){var e,i,a,s,r,n,o,{inflateAmount:l,options:{borderColor:h,backgroundColor:d}}=this,{inner:c,outer:u}=(a=Fs(e=this),s=a.right-a.left,u=s/2,n=(r=a.bottom-a.top)/2,o=(i=e).options.borderWidth,i=i.borderSkipped,o=Mi(o),n={t:Bs(i.top,o.top,0,n),r:Bs(i.right,o.right,0,u),b:Bs(i.bottom,o.bottom,0,n),l:Bs(i.left,o.left,0,u)},i=s/2,o=r/2,e=(u=e).getProps(["enableBorderRadius"]).enableBorderRadius,c=u.options.borderRadius,g=ki(c),i=Math.min(i,o),o=u.borderSkipped,e={topLeft:Bs(!(u=e||T(c))||o.top||o.left,g.topLeft,0,i),topRight:Bs(!u||o.top||o.right,g.topRight,0,i),bottomLeft:Bs(!u||o.bottom||o.left,g.bottomLeft,0,i),bottomRight:Bs(!u||o.bottom||o.right,g.bottomRight,0,i)},{outer:{x:a.left,y:a.top,w:s,h:r,radius:e},inner:{x:a.left+n.l,y:a.top+n.t,w:s-n.l-n.r,h:r-n.t-n.b,radius:{topLeft:Math.max(0,e.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,e.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,e.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,e.bottomRight-Math.max(n.b,n.r))}}}),g=(o=u.radius).topLeft||o.topRight||o.bottomLeft||o.bottomRight?$e:Ns;t.save(),u.w===c.w&&u.h===c.h||(t.beginPath(),g(t,Ws(u,l,c)),t.clip(),g(t,Ws(c,-l,u)),t.fillStyle=h,t.fill("evenodd")),t.beginPath(),g(t,Ws(c,l)),t.fillStyle=d,t.fill(),t.restore()}inRange(t,e,i){return Vs(this,t,e,i)}inXRange(t,e){return Vs(this,t,null,e)}inYRange(t,e){return Vs(this,null,t,e)}getCenterPoint(t){var{x:t,y:e,base:i,horizontal:a}=this.getProps(["x","y","base","horizontal"],t);return{x:a?(t+i)/2:t,y:a?e:(e+i)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}},LineElement:Rs,PointElement:class extends e{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){var a=this.options,{x:i,y:s}=this.getProps(["x","y"],i);return Math.pow(t-i,2)+Math.pow(e-s,2)<Math.pow(a.hitRadius+a.radius,2)}inXRange(t,e){return zs(this,t,"x",e)}inYRange(t,e){return zs(this,t,"y",e)}getCenterPoint(t){var{x:t,y:e}=this.getProps(["x","y"],t);return{x:t,y:e}}size(t){var e=(t=t||this.options||{}).radius||0;return 2*((e=Math.max(e,e&&t.hoverRadius||0))+(e&&t.borderWidth||0))}draw(t,e){var i=this.options;this.skip||i.radius<.1||!_(this,e,this.size(i)/2)||(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,Fe(t,i,this.x,this.y))}getRange(){var t=this.options||{};return t.radius+t.hitRadius}}});function js(t){var e=this.getLabels();return 0<=t&&t<e.length?e[t]:t}function $s(t,e,{horizontal:i,minRotation:a}){a=E(a),i=(i?Math.sin(a):Math.cos(a))||.001;return Math.min(e/i,.75*e*(""+t).length)}class Us extends Wa{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return S(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let t=this.options.beginAtZero,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:a,max:s}=this;var r,n=t=>a=e?a:t,o=t=>s=i?s:t;if(t){let t=y(a),e=y(s);t<0&&e<0?o(0):0<t&&0<e&&n(0)}a===s&&(r=0===s?1:Math.abs(.05*s),o(s+r),t||n(a-r)),this.min=a,this.max=s}getTickLimit(){let t,{maxTicksLimit:e,stepSize:i}=this.options.ticks;return i?1e3<(t=Math.ceil(this.max/i)-Math.floor(this.min/i)+1)&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${t} ticks. Limiting to 1000.`),t=1e3):(t=this.computeTickLimit(),e=e||11),t=e?Math.min(e,t):t}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){var t=this.options,e=t.ticks,i=this.getTickLimit(),i=((t,e)=>{var i=[],{bounds:a,step:s,min:r,max:n,precision:o,count:l,maxTicks:h,maxDigits:d,includeBounds:c}=t,u=s||1,g=h-1,{min:e,max:p}=e,f=!S(r),m=!S(n),b=!S(l),d=(p-e)/(d+1);let v,x,y,_,w=rt((p-e)/g/u)*u;if(w<1e-14&&!f&&!m)return[{value:e},{value:p}];(_=Math.ceil(p/w)-Math.floor(e/w))>g&&(w=rt(_*w/g/u)*u),S(o)||(v=Math.pow(10,o),w=Math.ceil(w*v)/v),y="ticks"===a?(x=Math.floor(e/w)*w,Math.ceil(p/w)*w):(x=e,p),f&&m&&s&&lt((n-r)/s,w/1e3)?(_=Math.round(Math.min((n-r)/w,h)),w=(n-r)/_,x=r,y=n):b?(x=f?r:x,y=m?n:y,_=l-1,w=(y-x)/_):_=st(_=(y-x)/w,Math.round(_),w/1e3)?Math.round(_):Math.ceil(_),g=Math.max(ct(w),ct(x)),v=Math.pow(10,S(o)?g:o),x=Math.round(x*v)/v,y=Math.round(y*v)/v;let M=0;for(f&&(c&&x!==r?(i.push({value:r}),x<r&&M++,st(Math.round((x+M*w)*v)/v,r,$s(r,d,t))&&M++):x<r&&M++);M<_;++M){let t=Math.round((x+M*w)*v)/v;if(m&&t>n)break;i.push({value:t})}return m&&c&&y!==n?i.length&&st(i[i.length-1].value,n,$s(n,d,t))?i[i.length-1].value=n:i.push({value:n}):m&&y!==n||i.push({value:y}),i})({maxTicks:Math.max(2,i),bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&ht(i,this,"value"),t.reverse?(i.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),i}configure(){var t=this.ticks;let e=this.min,i=this.max;super.configure(),this.options.offset&&t.length&&(t=(i-e)/Math.max(t.length-1,1)/2,e-=t,i+=t),this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return ue(t,this.chart.options.locale,this.options.ticks.format)}}class Ys extends Us{static id="linear";static defaults={ticks:{callback:pe.formatters.numeric}};determineDataLimits(){var{min:t,max:e}=this.getMinMax(!0);this.min=f(t)?t:0,this.max=f(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){var t=this.isHorizontal(),e=t?this.width:this.height,i=E(this.options.ticks.minRotation),t=(t?Math.sin(i):Math.cos(i))||.001,i=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,i.lineHeight/t))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}let qs=t=>Math.floor(o(t)),Xs=(t,e)=>Math.pow(10,qs(t)+e);function Ks(t){return 1==t/Math.pow(10,qs(t))}function Gs(t,e,i){i=Math.pow(10,i),t=Math.floor(t/i);return Math.ceil(e/i)-t}function Js(t,{min:e,max:i}){e=u(t.min,e);var a=[],s=qs(e);let r=((t,e)=>{let i=qs(e-t);for(;10<Gs(t,e,i);)i++;for(;Gs(t,e,i)<10;)i--;return Math.min(i,qs(t))})(e,i),n=r<0?Math.pow(10,Math.abs(r)):1;var o=Math.pow(10,r),l=s>r?Math.pow(10,s):0,s=Math.round((e-l)*n)/n,h=Math.floor((e-l)/o/10)*o*10;let d=Math.floor((s-h)/Math.pow(10,r)),c=u(t.min,Math.round((l+h+d*Math.pow(10,r))*n)/n);for(;c<i;)a.push({value:c,major:Ks(c),significand:d}),10<=d?d=d<15?15:20:d++,20<=d&&(r++,d=2,n=0<=r?1:n),c=Math.round((l+h+d*Math.pow(10,r))*n)/n;e=u(t.max,c);return a.push({value:e,major:Ks(e),significand:d}),a}class Qs extends Wa{static id="logarithmic";static defaults={ticks:{callback:pe.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){t=Us.prototype.parse.apply(this,[t,e]);if(0!==t)return f(t)&&0<t?t:null;this._zero=!0}determineDataLimits(){var{min:t,max:e}=this.getMinMax(!0);this.min=f(t)?Math.max(0,t):null,this.max=f(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!f(this._userMin)&&(this.min=t===Xs(this.min,0)?Xs(this.min,-1):Xs(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:e,maxDefined:i}=this.getUserBounds(),a=this.min,s=this.max;var t=t=>a=e?a:t,r=t=>s=i?s:t;a===s&&(a<=0?(t(1),r(10)):(t(Xs(a,-1)),r(Xs(s,1)))),a<=0&&t(Xs(s,-1)),s<=0&&r(Xs(a,1)),this.min=a,this.max=s}buildTicks(){var t=this.options,e=Js({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&ht(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":ue(t,this.chart.options.locale,this.options.ticks.format)}configure(){var t=this.min;super.configure(),this._startValue=o(t),this._valueRange=o(this.max)-o(t)}getPixelForValue(t){return null===(t=void 0!==t&&0!==t?t:this.min)||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(o(t)-this._startValue)/this._valueRange)}getValueForPixel(t){t=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+t*this._valueRange)}}function Zs(t){var e=t.ticks;if(e.display&&t.display){let t=R(e.backdropPadding);return O(e.font&&e.font.size,I.font.size)+t.height}return 0}function tr(t,e,i,a,s){return t===a||t===s?{start:e-i/2,end:e+i/2}:t<a||s<t?{start:e-i,end:e}:{start:e,end:e+i}}function er(i){let s={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},r=Object.assign({},s),a=[],n=[],t=i._pointLabels.length,o=i.options.pointLabels,l=o.centerPointLabels?P/t:0;for(let e=0;e<t;e++){let t=o.setContext(i.getPointLabelContext(e));n[e]=t.padding;var h=i.getPointPosition(e,i.drawingArea+n[e],l),d=z(t.font),c=(c=i.ctx,d=d,u=A(u=i._pointLabels[e])?u:[u],{w:Ie(c,d.string,u),h:u.length*d.lineHeight}),u=(a[e]=c,v(i.getIndexAngle(e)+l)),d=Math.round(dt(u));{g=void 0;p=void 0;f=void 0;m=void 0;b=void 0;var g=r;var p=s;var f=u;var m=tr(d,h.x,c.w,0,180);var b=tr(d,h.y,c.h,90,270);let t=Math.abs(Math.sin(f)),e=Math.abs(Math.cos(f)),i=0,a=0;m.start<p.l?(i=(p.l-m.start)/t,g.l=Math.min(g.l,p.l-i)):m.end>p.r&&(i=(m.end-p.r)/t,g.r=Math.max(g.r,p.r+i)),b.start<p.t?(a=(p.t-b.start)/e,g.t=Math.min(g.t,p.t-a)):b.end>p.b&&(a=(b.end-p.b)/e,g.b=Math.max(g.b,p.b+a))}}var c,u;i.setCenterPoint(s.l-r.l,r.r-s.r,s.t-r.t,r.b-s.b),i._pointLabelItems=((i,a,s)=>{let r=[],t=i._pointLabels.length,e=i.options,{centerPointLabels:n,display:o}=e.pointLabels,l={extra:Zs(e)/2,additionalAngle:n?P/t:0},h;for(let e=0;e<t;e++){l.padding=s[e],l.size=a[e];let t=((t,e,i)=>{let a=t.drawingArea,{extra:s,additionalAngle:r,padding:n,size:o}=i,l=t.getPointPosition(e,a+s+n,r),h=Math.round(dt(v(l.angle+C))),d=((t,e)=>(90===h||270===h?t-=e/2:(270<h||h<90)&&(t-=e),t))(l.y,o.h),c=(t=>0===t||180===t?"center":t<180?"left":"right")(h),u=((t,e)=>("right"===c?t-=e:"center"===c&&(t-=e/2),t))(l.x,o.w);return{visible:!0,x:l.x,y:d,textAlign:c,left:u,top:d,right:u+o.w,bottom:d+o.h}})(i,e,l);r.push(t),"auto"===o&&(t.visible=((t,e)=>{var i,a,s;return!(e&&({left:t,top:i,right:a,bottom:s}=t,_({x:t,y:i},e)||_({x:t,y:s},e)||_({x:a,y:i},e)||_({x:a,y:s},e)))})(t,h),t.visible)&&(h=t)}return r})(i,a,n)}function ir(e,i,t,a){var s=e.ctx;if(t)s.arc(e.xCenter,e.yCenter,i,0,x);else{var r=e.getPointPosition(0,i);s.moveTo(r.x,r.y);for(let t=1;t<a;t++)r=e.getPointPosition(t,i),s.lineTo(r.x,r.y)}}class ar extends Us{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:pe.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){var t=this._padding=R(Zs(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){var{min:t,max:e}=this.getMinMax(!1);this.min=f(t)&&!isNaN(t)?t:0,this.max=f(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Zs(this.options))}generateTickLabels(t){Us.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((t,e)=>{t=c(this.options.pointLabels.callback,[t,e],this);return t||0===t?t:""}).filter((t,e)=>this.chart.getDataVisibility(e))}fit(){var t=this.options;t.display&&t.pointLabels.display?er(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,a){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-a)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,a))}getIndexAngle(t){return v(t*(x/(this._pointLabels.length||1))+E(this.options.startAngle||0))}getDistanceFromCenterForValue(t){var e;return S(t)?NaN:(e=this.drawingArea/(this.max-this.min),this.options.reverse?(this.max-t)*e:(t-this.min)*e)}getValueForDistanceFromCenter(t){return S(t)?NaN:(t=t/(this.drawingArea/(this.max-this.min)),this.options.reverse?this.max-t:this.min+t)}getPointLabelContext(t){var e=this._pointLabels||[];if(0<=t&&t<e.length)return e=e[t],Ci(this.getContext(),{label:e,index:t,type:"pointLabel"})}getPointPosition(t,e,i=0){t=this.getIndexAngle(t)-C+i;return{x:Math.cos(t)*e+this.xCenter,y:Math.sin(t)*e+this.yCenter,angle:t}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){var{left:t,top:e,right:i,bottom:a}=this._pointLabelItems[t];return{left:t,top:e,right:i,bottom:a}}drawBackground(){var t,{backgroundColor:e,grid:{circular:i}}=this.options;e&&((t=this.ctx).save(),t.beginPath(),ir(this,this.getDistanceFromCenterForValue(this._endValue),i,this._pointLabels.length),t.closePath(),t.fillStyle=e,t.fill(),t.restore())}drawGrid(){let a=this.ctx,s=this.options,{angleLines:r,grid:l,border:h}=s,d=this._pointLabels.length,n,c,o;if(s.pointLabels.display){var i=this,t=d,{ctx:u,options:{pointLabels:g}}=i;for(let e=t-1;0<=e;e--){let t=i._pointLabelItems[e];if(t.visible){var p,f=g.setContext(i.getPointLabelContext(e)),m=(p=w=_=y=x=v=b=m=void 0,u),b=f,v=t,{left:x,top:y,right:_,bottom:w}=v;if(!S(p=b.backdropColor)){let t=ki(b.borderRadius),e=R(b.backdropPadding);m.fillStyle=p;b=x-e.left,p=y-e.top,_=_-x+e.width,x=w-y+e.height;Object.values(t).some(t=>0!==t)?(m.beginPath(),$e(m,{x:b,y:p,w:_,h:x,radius:t}),m.fill()):m.fillRect(b,p,_,x)}var v=z(f.font),{x:w,y,textAlign:m}=t;je(u,i._pointLabels[e],w,y+v.lineHeight/2,v,{color:f.color,textAlign:m,textBaseline:"middle"})}}}if(l.display&&this.ticks.forEach((t,e)=>{var i,a,s,r,n,o;0!==e&&(c=this.getDistanceFromCenterForValue(t.value),t=this.getContext(e),e=l.setContext(t),t=h.setContext(t),i=this,e=e,a=c,s=d,t=t,r=i.ctx,n=e.circular,{color:e,lineWidth:o}=e,!n&&!s||!e||!o||a<0||(r.save(),r.strokeStyle=e,r.lineWidth=o,r.setLineDash(t.dash),r.lineDashOffset=t.dashOffset,r.beginPath(),ir(i,a,n,s),r.closePath(),r.stroke(),r.restore()))}),r.display){for(a.save(),n=d-1;0<=n;n--){let t=r.setContext(this.getPointLabelContext(n)),{color:e,lineWidth:i}=t;i&&e&&(a.lineWidth=i,a.strokeStyle=e,a.setLineDash(t.borderDash),a.lineDashOffset=t.borderDashOffset,c=this.getDistanceFromCenterForValue(s.ticks.reverse?this.min:this.max),o=this.getPointPosition(n,c),a.beginPath(),a.moveTo(this.xCenter,this.yCenter),a.lineTo(o.x,o.y),a.stroke())}a.restore()}}drawBorder(){}drawLabels(){let n=this.ctx,o=this.options,l=o.ticks;if(l.display){var t=this.getIndexAngle(0);let s,r;n.save(),n.translate(this.xCenter,this.yCenter),n.rotate(t),n.textAlign="center",n.textBaseline="middle",this.ticks.forEach((e,t)=>{if(0!==t||o.reverse){var i=l.setContext(this.getContext(t)),a=z(i.font);if(s=this.getDistanceFromCenterForValue(this.ticks[t].value),i.showLabelBackdrop){n.font=a.string,r=n.measureText(e.label).width,n.fillStyle=i.backdropColor;let t=R(i.backdropPadding);n.fillRect(-r/2-t.left,-s-a.size/2-t.top,r+t.width,a.size+t.height)}je(n,e.label,0,-s,a,{color:i.color,strokeColor:i.textStrokeColor,strokeWidth:i.textStrokeWidth})}}),n.restore()}}drawTitle(){}}let sr={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},h=Object.keys(sr);function rr(t,e){return t-e}function nr(t,e){if(S(e))return null;var i=t._adapter,{parser:t,round:a,isoWeekday:s}=t._parseOpts;let r=e;return null===(r=f(r="function"==typeof t?t(r):r)?r:"string"==typeof t?i.parse(r,t):i.parse(r))?null:+(r=a?"week"!==a||!ot(s)&&!0!==s?i.startOf(r,a):i.startOf(r,"isoWeek",s):r)}function or(t,a,s,r){let e=h.length;for(let i=h.indexOf(t);i<e-1;++i){let t=sr[h[i]],e=t.steps||Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((s-a)/(e*t.size))<=r)return h[i]}return h[e-1]}function lr(t,e,i){var a,s;i?i.length&&({lo:a,hi:s}=bt(i,e),t[i[a]>=e?i[a]:i[s]]=!0):t[e]=!0}function hr(i,t,a){var s=[],r={},e=t.length;let n,o;for(n=0;n<e;++n)r[o=t[n]]=n,s.push({value:o,major:!1});if(0!==e&&a){var l=s,h=r,d=a,c=i._adapter,a=+c.startOf(l[0].value,d),u=l[l.length-1].value;let t,e;for(t=a;t<=u;t=+c.add(t,1,d))0<=(e=h[t])&&(l[e].major=!0);return l}return s}class dr extends Wa{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){var i=t.time||(t.time={}),a=this._adapter=new fs._date(t.adapters.date);a.init(e),U(i.displayFormats,a.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:nr(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){var t=this.options,e=this._adapter,i=t.time.unit||"day";let{min:a,max:s,minDefined:r,maxDefined:n}=this.getUserBounds();function o(t){r||isNaN(t.min)||(a=Math.min(a,t.min)),n||isNaN(t.max)||(s=Math.max(s,t.max))}r&&n||(o(this._getLabelBounds()),"ticks"===t.bounds&&"labels"===t.ticks.source)||o(this.getMinMax(!1)),a=f(a)&&!isNaN(a)?a:+e.startOf(Date.now(),i),s=f(s)&&!isNaN(s)?s:+e.endOf(Date.now(),i)+1,this.min=Math.min(a,s-1),this.max=Math.max(a+1,s)}_getLabelBounds(){var t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){var t=this.options,e=t.time,i=t.ticks,a="labels"===i.source?this.getLabelTimestamps():this._generate(),s=("ticks"===t.bounds&&a.length&&(this.min=this._userMin||a[0],this.max=this._userMax||a[a.length-1]),this.min),r=xt(a,s,this.max);return this._unit=e.unit||(i.autoSkip?or(e.minUnit,this.min,this.max,this._getLabelCapacity(s)):((i,a,t,s,r)=>{for(let e=h.length-1;e>=h.indexOf(t);e--){let t=h[e];if(sr[t].common&&i._adapter.diff(r,s,t)>=a-1)return t}return h[t?h.indexOf(t):0]})(this,r.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?(i=>{for(let t=h.indexOf(i)+1,e=h.length;t<e;++t)if(sr[h[t]].common)return h[t]})(this._unit):void 0,this.initOffsets(a),t.reverse&&r.reverse(),hr(this,r,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e,i,a=0,s=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),a=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),s=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);t=t.length<3?.5:.25;a=D(a,0,t),s=D(s,0,t),this._offsets={start:a,end:s,factor:1/(a+1+s)}}_generate(){var t=this._adapter,e=this.min,i=this.max,a=this.options,s=a.time,r=s.unit||or(s.minUnit,e,i,this._getLabelCapacity(e)),n=O(a.ticks.stepSize,1),s="week"===r&&s.isoWeekday,o=ot(s)||!0===s,l={};let h,d,c=e;if(o&&(c=+t.startOf(c,"isoWeek",s)),c=+t.startOf(c,o?"day":r),t.diff(i,e,r)>1e5*n)throw new Error(e+" and "+i+" are too far apart with stepSize of "+n+" "+r);var u="data"===a.ticks.source&&this.getDataTimestamps();for(h=c,d=0;h<i;h=+t.add(h,n,r),d++)lr(l,h,u);return h!==i&&"ticks"!==a.bounds&&1!==d||lr(l,h,u),Object.keys(l).sort(rr).map(t=>+t)}getLabelForValue(t){var e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){var i=this.options.time.displayFormats,a=this._unit,e=e||i[a];return this._adapter.format(t,e)}_tickFormatFunction(t,e,i,a){var s,r=this.options,n=r.ticks.callback;return n?c(n,[t,e,i],this):(n=r.time.displayFormats,r=this._unit,s=this._majorUnit,r=r&&n[r],n=s&&n[s],i=i[e],e=s&&n&&i&&i.major,this._adapter.format(t,a||(e?n:r)))}generateTickLabels(t){let e,i,a;for(e=0,i=t.length;e<i;++e)(a=t[e]).label=this._tickFormatFunction(a.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){var e=this._offsets,t=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+t)*e.factor)}getValueForPixel(t){var e=this._offsets,t=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+t*(this.max-this.min)}_getLabelSize(t){var e=this.options.ticks,t=this.ctx.measureText(t).width,e=E(this.isHorizontal()?e.maxRotation:e.minRotation),i=Math.cos(e),e=Math.sin(e),a=this._resolveTickFontOptions(0).size;return{w:t*i+a*e,h:t*e+a*i}}_getLabelCapacity(t){var e=this.options.time,i=e.displayFormats,e=i[e.unit]||i.millisecond,i=this._tickFormatFunction(t,0,hr(this,[t],this._majorUnit),e),t=this._getLabelSize(i),e=Math.floor(this.isHorizontal()?this.width/t.w:this.height/t.h)-1;return 0<e?e:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;var a=this.getMatchingVisibleMetas();if(this._normalized&&a.length)return this._cache.data=a[0].controller.getAllParsedValues(this);for(t=0,e=a.length;t<e;++t)i=i.concat(a[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){var t=this._cache.labels||[];let e,i;if(t.length)return t;var a=this.getLabels();for(e=0,i=a.length;e<i;++e)t.push(nr(this,a[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Mt(t.sort(rr))}}function cr(t,e,i){let a,s,r,n,o=0,l=t.length-1;i?(e>=t[o].pos&&e<=t[l].pos&&({lo:o,hi:l}=m(t,"pos",e)),{pos:a,time:r}=t[o],{pos:s,time:n}=t[l]):(e>=t[o].time&&e<=t[l].time&&({lo:o,hi:l}=m(t,"time",e)),{time:a,pos:r}=t[o],{time:s,pos:n}=t[l]);i=s-a;return i?r+(n-r)*(e-a)/i:r}let ur=Object.freeze({__proto__:null,CategoryScale:class extends Wa{static id="category";static defaults={ticks:{callback:js}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){var e=this._addedLabels;if(e.length){let t=this.getLabels();for(var{index:i,label:a}of e)t[i]===a&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){var i,a,s,r,n,o,l;return S(t)||(i=this.getLabels(),r=e=isFinite(e)&&i[e]===t?e:(a=i,s=O(e,t=t),r=this._addedLabels,-1===(l=a.indexOf(t))?(o=s,r=r,"string"==typeof(n=t)?(o=a.push(n)-1,r.unshift({index:o,label:n})):isNaN(n)&&(o=null),o):l!==a.lastIndexOf(t)?s:l),n=i.length-1,null===r)?null:D(Math.round(r),0,n)}determineDataLimits(){var{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:i,max:a}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(i=0),e||(a=this.getLabels().length-1)),this.min=i,this.max=a}buildTicks(){var e=this.min,i=this.max,t=this.options.offset,a=[];let s=this.getLabels();s=0===e&&i===s.length-1?s:s.slice(e,i+1),this._valueRange=Math.max(s.length-(t?0:1),1),this._startValue=this.min-(t?.5:0);for(let t=e;t<=i;t++)a.push({value:t});return a}getLabelForValue(t){return js.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return null===(t="number"!=typeof t?this.parse(t):t)?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){var e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}},LinearScale:Ys,LogarithmicScale:Qs,RadialLinearScale:ar,TimeScale:dr,TimeSeriesScale:class extends dr{static id="timeseries";static defaults=dr.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){var t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=cr(e,this.min),this._tableRange=cr(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){var{min:e,max:i}=this,a=[],s=[];let r,n,o,l,h;for(r=0,n=t.length;r<n;++r)(l=t[r])>=e&&l<=i&&a.push(l);if(a.length<2)return[{time:e,pos:0},{time:i,pos:1}];for(r=0,n=a.length;r<n;++r)h=a[r+1],o=a[r-1],l=a[r],Math.round((h+o)/2)!==l&&s.push({time:l,pos:r/(n-1)});return s}_generate(){var t=this.min,e=this.max,i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort((t,e)=>t-e)}_getTimestampsForTable(){var t,e,i=this._cache.all||[];return i.length||(t=this.getDataTimestamps(),e=this.getLabelTimestamps(),i=t.length&&e.length?this.normalize(t.concat(e)):t.length?t:e,i=this._cache.all=i),i}getDecimalForValue(t){return(cr(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){var e=this._offsets,t=this.getDecimalForPixel(t)/e.factor-e.end;return cr(this._table,t*this._tableRange+this._minPos,!0)}}}),gr=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],pr=gr.map(t=>t.replace("rgb(","rgba(").replace(")",", 0.5)"));function fr(t){return gr[t%gr.length]}function mr(t){return pr[t%pr.length]}function br(r){let n=0;return(t,e)=>{var i,a,s,e=r.getDatasetMeta(e).controller;e instanceof Ms?n=(a=t,s=n,a.backgroundColor=a.data.map(()=>fr(s++)),s):e instanceof ks?n=(a=t,i=n,a.backgroundColor=a.data.map(()=>mr(i++)),i):e&&(n=(e=t,t=n,e.borderColor=fr(t),e.backgroundColor=mr(t),++t))}}function vr(t){let e;for(e in t)if(t[e].borderColor||t[e].backgroundColor)return 1}var xr={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(t,e,i){var a,s,r;i.enabled&&({data:{datasets:a},options:s}=t.config,r=s.elements,!i.forceOverride&&(vr(a)||s&&(s.borderColor||s.backgroundColor)||r&&vr(r))||(i=br(t),a.forEach(i)))}};function yr(t){var e;t._decimated&&(e=t._data,delete t._decimated,delete t._data,Object.defineProperty(t,"data",{configurable:!0,enumerable:!0,writable:!0,value:e}))}function _r(t){t.data.datasets.forEach(t=>{yr(t)})}var wr={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(l,t,w)=>{if(w.enabled){let o=l.width;l.data.datasets.forEach((e,t)=>{var{_data:i,indexAxis:a}=e,h=l.getDatasetMeta(t),s=i||e.data;if("y"!==Si([a,l.options.indexAxis])&&h.controller.supportsDecimation){t=l.scales[h.xAxisID];if(("linear"===t.type||"time"===t.type)&&!l.options.parsing){var{start:r,count:n}=(t=>{var e=t.length;let i,a=0;var s=h.iScale,{min:r,max:n,minDefined:o,maxDefined:l}=s.getUserBounds();return o&&(a=D(m(t,s.axis,r).lo,0,e-1)),i=l?D(m(t,s.axis,n).hi+1,a,e)-a:e-a,{start:a,count:i}})(s);if(n<=(w.threshold||4*o))yr(e);else{let t;switch(S(i)&&(e._data=s,delete e.data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(t){this._data=t}})),w.algorithm){case"lttb":t=((n,o,l,t)=>{var e=w.samples||t;if(l<=e)return n.slice(o,o+l);var h=[],d=(l-2)/(e-2);let c=0,i=o+l-1,u,g,p,f,m,b=o;for(h[c++]=n[b],u=0;u<e-2;u++){let t,e=0,i=0,a=Math.floor((u+1)*d)+1+o,s=Math.min(Math.floor((u+2)*d)+1,l)+o,r=s-a;for(t=a;t<s;t++)e+=n[t].x,i+=n[t].y;e/=r,i/=r;var v=Math.floor(u*d)+1+o,x=Math.min(Math.floor((u+1)*d)+1,l)+o,{x:y,y:_}=n[b];for(p=-1,t=v;t<x;t++)(f=.5*Math.abs((y-e)*(n[t].y-_)-(y-n[t].x)*(i-_)))>p&&(p=f,g=n[t],m=t);h[c++]=g,b=m}return h[c++]=n[i],h})(s,r,n,o);break;case"min-max":t=((a,t,e,i)=>{let s,r,n,o,l,h,d,c,u,g,p=0,f=0;var m=[],b=a[t].x,v=a[t+e-1].x-b;for(s=t;s<t+e;++s){n=((r=a[s]).x-b)/v*i,o=r.y;let t=0|n;if(t===l)o<u?(u=o,h=s):o>g&&(g=o,d=s),p=(f*p+r.x)/++f;else{let i=s-1;if(!S(h)&&!S(d)){let t=Math.min(h,d),e=Math.max(h,d);t!==c&&t!==i&&m.push({...a[t],x:p}),e!==c&&e!==i&&m.push({...a[e],x:p})}0<s&&i!==c&&m.push(a[i]),m.push(r),l=t,f=0,u=g=o,h=d=c=s}}return m})(s,r,n,o);break;default:throw new Error(`Unsupported decimation algorithm '${w.algorithm}'`)}e._decimated=t}}}})}else _r(l)},destroy(t){_r(t)}};function Mr(i,a,s,t){if(!t){let t=a[i],e=s[i];return"angle"===i&&(t=v(t),e=v(e)),{property:i,start:t,end:e}}}function kr(t,e,i){for(;t<e;e--){let t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function Sr(t,e,i,a){return t&&e?a(t[i],e[i]):t?t[i]:e?e[i]:0}function Pr(e,t){let i=[],a=!1;return(i=A(e)?(a=!0,e):(t=>{let{x:i=null,y:a=null}=e||{},s=t.points,r=[];return t.segments.forEach(({start:t,end:e})=>{e=kr(t,e,s);t=s[t],e=s[e];null!==a?(r.push({x:t.x,y:a}),r.push({x:e.x,y:a})):null!==i&&(r.push({x:i,y:t.y}),r.push({x:i,y:e.y}))}),r})(t)).length?new Rs({points:i,options:{tension:0},_loop:a,_fullLoop:a}):null}function Cr(t){return t&&!1!==t.fill}function Dr(a,t,e){var i,s,r=(()=>{var t=a.options,e=t.fill;let i=O(e&&e.target,e);return!1!==(i=void 0===i?!!t.backgroundColor:i)&&null!==i&&(!0===i?"origin":i)})();return T(r)?!isNaN(r.value)&&r:f(s=parseFloat(r))&&Math.floor(s)===s?(i=r[0],s=s,!((s="-"!==i&&"+"!==i?s:t+s)===t||s<0||e<=s)&&s):0<=["origin","start","end","stack","shape"].indexOf(r)&&r}function Ar(e,i,a){var s=[];for(let t=0;t<a.length;t++){var{first:r,last:n,point:o}=((t,e,s)=>{let i=t.interpolate(e,s);if(!i)return{};let r=i[s],n=t.segments,o=t.points,l=!1,h=!1;for(let a=0;a<n.length;a++){let t=n[a],e=o[t.start][s],i=o[t.end][s];if(d(r,e,i)){l=r===e,h=r===i;break}}return{first:l,last:h,point:i}})(a[t],i,"x");if(!(!o||r&&n))if(r)s.unshift(o);else if(e.push(o),!n)break}e.push(...s)}class Tr{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){var{x:a,y:s,radius:r}=this;return e=e||{start:0,end:x},t.arc(a,s,r,e.end,e.start,!0),!i.bounds}interpolate(t){var{x:e,y:i,radius:a}=this,t=t.angle;return{x:e+Math.cos(t)*a,y:i+Math.sin(t)*a,angle:t}}}function Or(o){var t,e,{chart:i,fill:a,line:s}=o;if(f(a))return t=a,(e=(i=i).getDatasetMeta(t))&&i.isDatasetVisible(t)?e.dataset:null;if("stack"!==a)return"shape"===a||((i=(o=>{if((o.scale||{}).getPointPositionForValue){var l=o;let{scale:e,fill:t}=l,i=e.options,a=e.getLabels().length,s=i.reverse?e.max:e.min,r=(l=t,h=e,"start"===l?s:"end"===l?h.options.reverse?h.min:h.max:T(l)?l.value:h.getBaseValue()),n=[];if(i.grid.circular){let t=e.getPointPositionForValue(0,s);return new Tr({x:t.x,y:t.y,radius:e.getDistanceFromCenterForValue(r)})}for(let t=0;t<a;++t)n.push(e.getPointPositionForValue(t,r));return n}var t=o,{scale:l={},fill:t}=t,h=((t,e)=>{let i=null;return"start"===t?i=e.bottom:"end"===t?i=e.top:T(t)?i=e.getPixelForValue(t.value):e.getBasePixel&&(i=e.getBasePixel()),i})(t,l);if(f(h)){let t=l.isHorizontal();return{x:t?h:null,y:t?null:h}}return null})(o))instanceof Tr?i:Pr(i,s));{let{scale:t,index:e,line:i}=o,a=[],s=i.segments,r=i.points,n=((t,e)=>{var i=[],a=t.getMatchingVisibleMetas("line");for(let t=0;t<a.length;t++){var s=a[t];if(s.index===e)break;s.hidden||i.unshift(s.dataset)}return i})(t,e);n.push(Pr({x:null,y:t.bottom},i));for(let t=0;t<s.length;t++){let e=s[t];for(let t=e.start;t<=e.end;t++)Ar(a,r[t],n)}return new Rs({points:a,options:{}})}}function Er(t,e,i){var a,s=Or(e),{line:e,scale:r,axis:n}=e,o=e.options,l=o.fill,o=o.backgroundColor,{above:l=o,below:o=o}=l||{};s&&e.points.length&&(Ve(t,i),a=t,{line:s,target:l,above:o,below:i,area:r,scale:n}=e={line:e,target:s,above:l,below:o,area:i,scale:r,axis:n},e=s._loop?"angle":e.axis,a.save(),"x"===e&&i!==o&&(Lr(a,l,r.top),Ir(a,{line:s,target:l,color:o,scale:n,property:e}),a.restore(),a.save(),Lr(a,l,r.bottom)),Ir(a,{line:s,target:l,color:i,scale:n,property:e}),a.restore(),Ne(t))}function Lr(s,r,n){let{segments:t,points:o}=r,l=!0,h=!1;s.beginPath();for(var d of t){let{start:t,end:e}=d,i=o[t],a=o[kr(t,e,o)];l?(s.moveTo(i.x,i.y),l=!1):(s.lineTo(i.x,n),s.lineTo(i.x,i.y)),(h=!!r.pathSegment(s,d,{move:h}))?s.closePath():s.lineTo(a.x,n)}s.lineTo(r.first().x,n),s.closePath(),s.clip()}function Ir(o,t){let{line:l,target:h,property:d,color:c,scale:u}=t,e=((t,e,s)=>{let i=t.segments,r=t.points,n=e.points,o=[];for(let t of i){var{start:a,end:l}=t,l=kr(a,l,r),h=Mr(s,r[a],r[l],t.loop);if(e.segments){var d=Ii(e,h);for(let a of d){let e=Mr(s,n[a.start],n[a.end],a.loop),i=Li(t,r,e);for(let t of i)o.push({source:t,target:a,start:{[s]:Sr(h,e,"start",Math.max)},end:{[s]:Sr(h,e,"end",Math.min)}})}}else o.push({source:t,target:h,start:r[a],end:r[l]})}return o})(l,h,d);for(let{source:a,target:s,start:r,end:n}of e){let{style:{backgroundColor:t=c}={}}=a,e=!0!==h;o.save(),o.fillStyle=t,v=b=m=f=p=g=void 0;var g=o,p=u,f=e&&Mr(d,r,n),{top:p,bottom:m}=p.chart.chartArea,{property:f,start:b,end:v}=f||{},f=("x"===f&&(g.beginPath(),g.rect(b,p,v-b,m-p),g.clip()),o.beginPath(),!!l.pathSegment(o,a));let i;if(e){f?o.closePath():Rr(o,h,n,d);let t=!!h.pathSegment(o,s,{move:f,reverse:!0});(i=f&&t)||Rr(o,h,r,d)}o.closePath(),o.fill(i?"evenodd":"nonzero"),o.restore()}}function Rr(t,e,i,a){e=e.interpolate(i,a);e&&t.lineTo(e.x,e.y)}var zr={id:"filler",afterDatasetsUpdate(t,e,i){var a=(t.data.datasets||[]).length,s=[];let r,n,o,l;for(n=0;n<a;++n)o=(r=t.getDatasetMeta(n)).dataset,l=null,o&&o.options&&o instanceof Rs&&(l={visible:t.isDatasetVisible(n),index:n,fill:Dr(o,n,a),chart:t,axis:r.controller.options.indexAxis,scale:r.vScale,line:o}),r.$filler=l,s.push(l);for(n=0;n<a;++n)(l=s[n])&&!1!==l.fill&&(l.fill=((t,e,i)=>{let a=t[e].fill;var s,r=[e];if(!i)return a;for(;!1!==a&&-1===r.indexOf(a);){if(!f(a))return a;if(!(s=t[a]))return!1;if(s.visible)return a;r.push(a),a=s.fill}return!1})(s,n,i.propagate))},beforeDraw(i,t,e){var a="beforeDraw"===e.drawTime,s=i.getSortedVisibleDatasetMetas(),r=i.chartArea;for(let e=s.length-1;0<=e;--e){let t=s[e].$filler;t&&(t.line.updateControlPoints(r,t.axis),a)&&t.fill&&Er(i.ctx,t,r)}},beforeDatasetsDraw(i,t,e){if("beforeDatasetsDraw"===e.drawTime){var a=i.getSortedVisibleDatasetMetas();for(let e=a.length-1;0<=e;--e){let t=a[e].$filler;Cr(t)&&Er(i.ctx,t,i.chartArea)}}},beforeDatasetDraw(t,e,i){e=e.meta.$filler;Cr(e)&&"beforeDatasetDraw"===i.drawTime&&Er(t.ctx,e,t.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};let Fr=(t,e)=>{let{boxHeight:i=e,boxWidth:a=e}=t;return t.usePointStyle&&(i=Math.min(i,e),a=t.pointStyleWidth||Math.min(a,e)),{boxWidth:a,boxHeight:i,itemHeight:Math.max(e,i)}};class Br extends e{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let i=this.options.labels||{},t=c(i.generateLabels,[this.chart],this)||[];i.filter&&(t=t.filter(t=>i.filter(t,this.chart.data))),i.sort&&(t=t.sort((t,e)=>i.sort(t,e,this.chart.data))),this.options.reverse&&t.reverse(),this.legendItems=t}fit(){var{options:i,ctx:a}=this;if(i.display){var s=i.labels,r=z(s.font),n=r.size,o=this._computeTitleHeight(),{boxWidth:s,itemHeight:l}=Fr(s,n);let t,e;a.font=r.string,this.isHorizontal()?(t=this.maxWidth,e=this._fitRows(o,n,s,l)+10):(e=this.maxHeight,t=this._fitCols(o,r,s,l)+10),this.width=Math.min(t,i.maxWidth||this.maxWidth),this.height=Math.min(e,i.maxHeight||this.maxHeight)}else this.width=this.height=0}_fitRows(t,i,a,s){let{ctx:r,maxWidth:n,options:{labels:{padding:o}}}=this,l=this.legendHitBoxes=[],h=this.lineWidths=[0],d=s+o,c=t,u=(r.textAlign="left",r.textBaseline="middle",-1),g=-d;return this.legendItems.forEach((t,e)=>{t=a+i/2+r.measureText(t.text).width;(0===e||h[h.length-1]+t+2*o>n)&&(c+=d,h[h.length-(0<e?0:1)]=0,g+=d,u++),l[e]={left:0,top:g,row:u,width:t,height:s},h[h.length-1]+=t+o}),c}_fitCols(t,o,l,h){let{ctx:d,maxHeight:e,options:{labels:{padding:c}}}=this,u=this.legendHitBoxes=[],g=this.columnSizes=[],p=e-t,f=c,m=0,b=0,v=0,x=0;return this.legendItems.forEach((t,e)=>{n=l,i=o,a=d,s=t,r=h;var i,a,s,r,{itemWidth:t,itemHeight:n}={itemWidth:((t,e,i)=>{let a=s.text;return a&&"string"!=typeof a&&(a=a.reduce((t,e)=>t.length>e.length?t:e)),t+e.size/2+i.measureText(a).width})(n,i,a),itemHeight:(t=>{let e=r;return e="string"!=typeof s.text?Vr(s,t):e})(i.lineHeight)};0<e&&b+n+2*c>p&&(f+=m+c,g.push({width:m,height:b}),v+=m+c,x++,m=b=0),u[e]={left:v,top:b,col:x,width:t,height:n},m=Math.max(m,t),b+=n+c}),f+=m,g.push({width:m,height:b}),f}adjustHitBoxes(){if(this.options.display){var i=this._computeTitleHeight(),{legendHitBoxes:a,options:{align:s,labels:{padding:r},rtl:t}}=this,n=Di(t,this.left,this.width);if(this.isHorizontal()){let t=0,e=L(s,this.left+r,this.right-this.lineWidths[t]);for(var o of a)t!==o.row&&(t=o.row,e=L(s,this.left+r,this.right-this.lineWidths[t])),o.top+=this.top+i+r,o.left=n.leftForLtr(n.x(e),o.width),e+=o.width+r}else{let t=0,e=L(s,this.top+i+r,this.bottom-this.columnSizes[t].height);for(var l of a)l.col!==t&&(t=l.col,e=L(s,this.top+i+r,this.bottom-this.columnSizes[t].height)),l.top=e,l.left+=this.left+r,l.left=n.leftForLtr(n.x(l.left),l.width),e+=l.height+r}}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){var t;this.options.display&&(Ve(t=this.ctx,this),this._draw(),Ne(t))}_draw(){let{options:h,columnSizes:d,lineWidths:c,ctx:u}=this,{align:g,labels:p}=h,f=I.color,m=Di(h.rtl,this.left,this.width),b=z(p.font),v=p.padding,x=b.size,y=x/2,_,{boxWidth:w,boxHeight:M,itemHeight:k}=(this.drawTitle(),u.textAlign=m.textAlign("left"),u.textBaseline="middle",u.lineWidth=.5,u.font=b.string,Fr(p,x)),S=this.isHorizontal(),P=this._computeTitleHeight(),C=(_=S?{x:L(g,this.left+v,this.right-c[0]),y:this.top+v+P,line:0}:{x:this.left+v,y:L(g,this.top+P+v,this.bottom-d[0].height),line:0},Ai(this.ctx,h.textDirection),k+v);this.legendItems.forEach((e,a)=>{u.strokeStyle=e.fontColor,u.fillStyle=e.fontColor;var t=u.measureText(e.text).width,i=m.textAlign(e.textAlign||(e.textAlign=p.textAlign)),t=w+y+t;let s=_.x,r=_.y;m.setWidth(this.width),S?0<a&&s+t+v>this.right&&(r=_.y+=C,_.line++,s=_.x=L(g,this.left+v,this.right-c[_.line])):0<a&&r+C>this.bottom&&(s=_.x=s+d[_.line].width+v,_.line++,r=_.y=L(g,this.top+P+v,this.bottom-d[_.line].height));var a=m.x(s),n=r,o=e;if(!(isNaN(w)||w<=0||isNaN(M)||M<0)){u.save();var l=O(o.lineWidth,1);if(u.fillStyle=O(o.fillStyle,f),u.lineCap=O(o.lineCap,"butt"),u.lineDashOffset=O(o.lineDashOffset,0),u.lineJoin=O(o.lineJoin,"miter"),u.lineWidth=l,u.strokeStyle=O(o.strokeStyle,f),u.setLineDash(O(o.lineDash,[])),p.usePointStyle){let t={radius:M*Math.SQRT2/2,pointStyle:o.pointStyle,rotation:o.rotation,borderWidth:l},e=m.xPlus(a,w/2);Be(u,t,e,n+y,p.pointStyleWidth&&w)}else{let t=n+Math.max((x-M)/2,0),e=m.leftForLtr(a,w),i=ki(o.borderRadius);u.beginPath(),Object.values(i).some(t=>0!==t)?$e(u,{x:e,y:t,w:w,h:M,radius:i}):u.rect(e,t,w,M),u.fill(),0!==l&&u.stroke()}u.restore()}if(s=Dt(i,s+w+y,S?s+t:this.right,h.rtl),n=m.x(s),a=r,o=e,je(u,o.text,n,a+k/2,b,{strikethrough:o.hidden,textAlign:m.textAlign(o.textAlign)}),S)_.x+=t+v;else if("string"!=typeof e.text){let t=b.lineHeight;_.y+=Vr(e,t)+v}else _.y+=C}),Ti(this.ctx,h.textDirection)}drawTitle(){let a=this.options,s=a.title,r=z(s.font),n=R(s.padding);if(s.display){var o=Di(a.rtl,this.left,this.width),l=this.ctx,h=s.position,d=r.size/2,d=n.top+d;let e,t=this.left,i=this.width;if(this.isHorizontal())i=Math.max(...this.lineWidths),e=this.top+d,t=L(a.align,t,this.right-i);else{let t=this.columnSizes.reduce((t,e)=>Math.max(t,e.height),0);e=d+L(a.align,this.top,this.bottom-t-a.labels.padding-this._computeTitleHeight())}d=L(h,t,t+i);l.textAlign=o.textAlign(Ct(h)),l.textBaseline="middle",l.strokeStyle=s.color,l.fillStyle=s.color,l.font=r.string,je(l,s.text,d,e,r)}}_computeTitleHeight(){var t=this.options.title,e=z(t.font),i=R(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,a,s;if(d(t,this.left,this.right)&&d(e,this.top,this.bottom))for(s=this.legendHitBoxes,i=0;i<s.length;++i)if(d(t,(a=s[i]).left,a.left+a.width)&&d(e,a.top,a.top+a.height))return this.legendItems[i];return null}handleEvent(t){var e,i,a,s=this.options;("mousemove"!==(e=t.type)&&"mouseout"!==e||!s.onHover&&!s.onLeave)&&(!s.onClick||"click"!==e&&"mouseup"!==e)||(e=this._getLegendItemAt(t.x,t.y),"mousemove"===t.type||"mouseout"===t.type?(a=null!==(i=this._hoveredItem)&&null!==e&&i.datasetIndex===e.datasetIndex&&i.index===e.index,i&&!a&&c(s.onLeave,[t,i,this],this),(this._hoveredItem=e)&&!a&&c(s.onHover,[t,e,this],this)):e&&c(s.onClick,[t,e,this],this))}}function Vr(t,e){return e*(t.text?t.text.length:0)}var Nr={id:"legend",_element:Br,start(t,e,i){var a=t.legend=new Br({ctx:t.ctx,options:i,chart:t});s.configure(t,a,i),s.addBox(t,a)},stop(t){s.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){var a=t.legend;s.configure(t,a,i),a.options=i},afterUpdate(t){t=t.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){var a=e.datasetIndex,i=i.chart;i.isDatasetVisible(a)?(i.hide(a),e.hidden=!0):(i.show(a),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){let a=t.data.datasets,{usePointStyle:s,pointStyle:r,textAlign:n,color:o,useBorderRadius:l,borderRadius:h}=t.legend.options.labels;return t._getSortedDatasetMetas().map(t=>{var e=t.controller.getStyle(s?0:void 0),i=R(e.borderWidth);return{text:a[t.index].label,fillStyle:e.backgroundColor,fontColor:o,hidden:!t.visible,lineCap:e.borderCapStyle,lineDash:e.borderDash,lineDashOffset:e.borderDashOffset,lineJoin:e.borderJoinStyle,lineWidth:(i.width+i.height)/4,strokeStyle:e.borderColor,pointStyle:r||e.pointStyle,rotation:e.rotation,textAlign:n||e.textAlign,borderRadius:l&&(h||e.borderRadius),datasetIndex:t.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class Wr extends e{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){var i=this.options;this.left=0,this.top=0,i.display?(this.width=this.right=t,this.height=this.bottom=e,t=A(i.text)?i.text.length:1,this._padding=R(i.padding),e=t*z(i.font).lineHeight+this._padding.height,this.isHorizontal()?this.height=e:this.width=e):this.width=this.height=this.right=this.bottom=0}isHorizontal(){var t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){var{top:e,left:i,bottom:a,right:s,options:r}=this,n=r.align;let o,l,h,d=0;return o=this.isHorizontal()?(l=L(n,i,s),h=e+t,s-i):(d="left"===r.position?(l=i+t,h=L(n,a,e),-.5*P):(l=s-t,h=L(n,e,a),.5*P),a-e),{titleX:l,titleY:h,maxWidth:o,rotation:d}}draw(){var t,e,i,a,s,r=this.ctx,n=this.options;n.display&&(e=(t=z(n.font)).lineHeight/2+this._padding.top,{titleX:e,titleY:i,maxWidth:a,rotation:s}=this._drawArgs(e),je(r,n.text,0,0,t,{color:n.color,maxWidth:a,rotation:s,textAlign:Ct(n.align),textBaseline:"middle",translation:[e,i]}))}}var Hr={id:"title",_element:Wr,start(t,e,i){var a;t=t,i=i,a=new Wr({ctx:t.ctx,options:i,chart:t}),s.configure(t,a,i),s.addBox(t,a),t.titleBlock=a},stop(t){var e=t.titleBlock;s.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){var a=t.titleBlock;s.configure(t,a,i),a.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};let jr=new WeakMap;var $r={id:"subtitle",start(t,e,i){var a=new Wr({ctx:t.ctx,options:i,chart:t});s.configure(t,a,i),s.addBox(t,a),jr.set(t,a)},stop(t){s.removeBox(t,jr.get(t)),jr.delete(t)},beforeUpdate(t,e,i){var a=jr.get(t);s.configure(t,a,i),a.options=i},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};let Ur={average(t){if(!t.length)return!1;let i,e,a=0,s=0,r=0;for(i=0,e=t.length;i<e;++i){let e=t[i].element;if(e&&e.hasValue()){let t=e.tooltipPosition();a+=t.x,s+=t.y,++r}}return{x:a/r,y:s/r}},nearest(t,i){if(!t.length)return!1;let a,e,s,r=i.x,n=i.y,o=Number.POSITIVE_INFINITY;for(a=0,e=t.length;a<e;++a){let e=t[a].element;if(e&&e.hasValue()){let t=gt(i,e.getCenterPoint());t<o&&(o=t,s=e)}}if(s){let t=s.tooltipPosition();r=t.x,n=t.y}return{x:r,y:n}}};function b(t,e){return e&&(A(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function Yr(t){return("string"==typeof t||t instanceof String)&&-1<t.indexOf("\n")?t.split("\n"):t}function qr(t,e){function i(t){b=Math.max(b,a.measureText(t).width+x)}let a=t.chart.ctx,{body:s,footer:r,title:n}=t,{boxWidth:o,boxHeight:l}=e,h=z(e.bodyFont),d=z(e.titleFont),c=z(e.footerFont),u=n.length,g=r.length,p=s.length,f=R(e.padding),m=f.height,b=0,v=s.reduce((t,e)=>t+e.before.length+e.lines.length+e.after.length,0),x=(v+=t.beforeBody.length+t.afterBody.length,u&&(m+=u*d.lineHeight+(u-1)*e.titleSpacing+e.titleMarginBottom),v&&(m+=p*(e.displayColors?Math.max(l,h.lineHeight):h.lineHeight)+(v-p)*h.lineHeight+(v-1)*e.bodySpacing),g&&(m+=e.footerMarginTop+g*c.lineHeight+(g-1)*e.footerSpacing),0);return a.save(),a.font=d.string,k(t.title,i),a.font=h.string,k(t.beforeBody.concat(t.afterBody),i),x=e.displayColors?o+2+e.boxPadding:0,k(s,t=>{k(t.before,i),k(t.lines,i),k(t.after,i)}),x=0,a.font=c.string,k(t.footer,i),a.restore(),{width:b+=f.width,height:m}}function Xr(s,r,n,t){var{x:e,width:i}=n,{width:a,chartArea:{left:o,right:l}}=s;let h="center";return"center"===t?h=e<=(o+l)/2?"left":"right":e<=i/2?h="left":a-i/2<=e&&(h="right"),h=(t=>{var{x:e,width:i}=n,a=r.caretSize+r.caretPadding;return"left"===t&&e+i+a>s.width||"right"===t&&e-i-a<0})(h)?"center":h}function Kr(i,t,a){var e=a.yAlign||t.yAlign||(()=>{var{y:t,height:e}=a;return t<e/2?"top":t>i.height-e/2?"bottom":"center"})();return{xAlign:a.xAlign||t.xAlign||Xr(i,t,a,e),yAlign:e}}function Gr(t,i,e,a){var{caretSize:t,caretPadding:s,cornerRadius:r}=t,{xAlign:n,yAlign:o}=e,l=t+s,{topLeft:e,topRight:s,bottomLeft:r,bottomRight:h}=ki(r);let d=(()=>{let{x:t,width:e}=i;return"right"===n?t-=e:"center"===n&&(t-=e/2),t})();var c=(()=>{let{y:t,height:e}=i;return"top"===o?t+=l:t-="bottom"===o?e+l:e/2,t})();return"center"===o?"left"===n?d+=l:"right"===n&&(d-=l):"left"===n?d-=Math.max(e,r)+t:"right"===n&&(d+=Math.max(s,h)+t),{x:D(d,0,a.width-i.width),y:D(c,0,a.height-i.height)}}function Jr(t,e,i){i=R(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-i.right:t.x+i.left}function Qr(t){return b([],Yr(t))}function Zr(t,e){e=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return e?t.override(e):t}let tn={beforeTitle:t,title(t){if(0<t.length){var t=t[0],e=t.chart.data.labels,i=e?e.length:0;if(this&&this.options&&"dataset"===this.options.mode)return t.dataset.label||"";if(t.label)return t.label;if(0<i&&t.dataIndex<i)return e[t.dataIndex]}return""},afterTitle:t,beforeBody:t,beforeLabel:t,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");t=t.formattedValue;return S(t)||(e+=t),e},labelColor(t){t=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:t.borderColor,backgroundColor:t.backgroundColor,borderWidth:t.borderWidth,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){t=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:t.pointStyle,rotation:t.rotation}},afterLabel:t,afterBody:t,beforeFooter:t,footer:t,afterFooter:t};function M(t,e,i,a){t=t[e].call(i,a);return void 0===t?tn[e].call(i,a):t}class en extends e{static positioners=Ur;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){var t,e=this._cachedAnimations;return e||(e=this.chart,e=(t=this.options.setContext(this.getContext())).enabled&&e.options.animation&&t.animations,t=new wa(this.chart,e),e._cacheable&&(this._cachedAnimations=Object.freeze(t)),t)}getContext(){return this.$context||(this.$context=(t=this.chart.getContext(),Ci(t,{tooltip:this,tooltipItems:this._tooltipItems,type:"tooltip"})));var t}getTitle(t,e){var e=e.callbacks,i=M(e,"beforeTitle",this,t),a=M(e,"title",this,t),e=M(e,"afterTitle",this,t),t=b([],Yr(i));return t=b(t,Yr(a)),b(t,Yr(e))}getBeforeBody(t,e){return Qr(M(e.callbacks,"beforeBody",this,t))}getBody(t,e){let a=e.callbacks,s=[];return k(t,t=>{var e={before:[],lines:[],after:[]},i=Zr(a,t);b(e.before,Yr(M(i,"beforeLabel",this,t))),b(e.lines,M(i,"label",this,t)),b(e.after,Yr(M(i,"afterLabel",this,t))),s.push(e)}),s}getAfterBody(t,e){return Qr(M(e.callbacks,"afterBody",this,t))}getFooter(t,e){var e=e.callbacks,i=M(e,"beforeFooter",this,t),a=M(e,"footer",this,t),e=M(e,"afterFooter",this,t),t=b([],Yr(i));return t=b(t,Yr(a)),b(t,Yr(e))}_createItems(a){let t=this._active,s=this.chart.data,i=[],r=[],n=[],e,o,l=[];for(e=0,o=t.length;e<o;++e)l.push(((t,e)=>{var{element:e,datasetIndex:i,index:a}=e,s=t.getDatasetMeta(i).controller,{label:r,value:n}=s.getLabelAndValue(a);return{chart:t,label:r,parsed:s.getParsed(a),raw:t.data.datasets[i].data[a],formattedValue:n,dataset:s.getDataset(),dataIndex:a,datasetIndex:i,element:e}})(this.chart,t[e]));return a.filter&&(l=l.filter((t,e,i)=>a.filter(t,e,i,s))),k(l=a.itemSort?l.sort((t,e)=>a.itemSort(t,e,s)):l,t=>{var e=Zr(a.callbacks,t);i.push(M(e,"labelColor",this,t)),r.push(M(e,"labelPointStyle",this,t)),n.push(M(e,"labelTextColor",this,t))}),this.labelColors=i,this.labelPointStyles=r,this.labelTextColors=n,this.dataPoints=l}update(t,e){var r=this.options.setContext(this.getContext()),n=this._active;let o,l=[];if(n.length){let t=Ur[r.position].call(this,n,this._eventPosition),e=(l=this._createItems(r),this.title=this.getTitle(l,r),this.beforeBody=this.getBeforeBody(l,r),this.body=this.getBody(l,r),this.afterBody=this.getAfterBody(l,r),this.footer=this.getFooter(l,r),this._size=qr(this,r)),i=Object.assign({},t,e),a=Kr(this.chart,r,i),s=Gr(r,i,a,this.chart);this.xAlign=a.xAlign,this.yAlign=a.yAlign,o={opacity:1,x:s.x,y:s.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(o={opacity:0});this._tooltipItems=l,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&r.external&&r.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,a){t=this.getCaretPosition(t,i,a);e.lineTo(t.x1,t.y1),e.lineTo(t.x2,t.y2),e.lineTo(t.x3,t.y3)}getCaretPosition(t,e,i){var{xAlign:a,yAlign:s}=this,{caretSize:i,cornerRadius:r}=i,{topLeft:r,topRight:n,bottomLeft:o,bottomRight:l}=ki(r),{x:t,y:h}=t,{width:e,height:d}=e;let c,u,g,p,f,m;return"center"===s?(f=h+d/2,m="left"===a?(c=t,u=c-i,p=f+i,f-i):(c=t+e,u=c+i,p=f-i,f+i),g=c):(u="left"===a?t+Math.max(r,o)+i:"right"===a?t+e-Math.max(n,l)-i:this.caretX,g="top"===s?(p=h,f=p-i,c=u-i,u+i):(p=h+d,f=p+i,c=u+i,u-i),m=p),{x1:c,x2:u,x3:g,y1:p,y2:f,y3:m}}drawTitle(t,e,i){var a=this.title,s=a.length;let r,n,o;if(s){var l=Di(i.rtl,this.x,this.width);for(t.x=Jr(this,i.titleAlign,i),e.textAlign=l.textAlign(i.titleAlign),e.textBaseline="middle",r=z(i.titleFont),n=i.titleSpacing,e.fillStyle=i.titleColor,e.font=r.string,o=0;o<s;++o)e.fillText(a[o],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+n,o+1===s&&(t.y+=i.titleMarginBottom-n)}}_drawColorBox(a,t,e,s,r){let n=this.labelColors[e],o=this.labelPointStyles[e],{boxHeight:l,boxWidth:h}=r,i=z(r.bodyFont),d=Jr(this,"left",r),c=s.x(d),u=l<i.lineHeight?(i.lineHeight-l)/2:0,g=t.y+u;if(r.usePointStyle){let t={radius:Math.min(h,l)/2,pointStyle:o.pointStyle,rotation:o.rotation,borderWidth:1},e=s.leftForLtr(c,h)+h/2,i=g+l/2;a.strokeStyle=r.multiKeyBackground,a.fillStyle=r.multiKeyBackground,Fe(a,t,e,i),a.strokeStyle=n.borderColor,a.fillStyle=n.backgroundColor,Fe(a,t,e,i)}else{a.lineWidth=T(n.borderWidth)?Math.max(...Object.values(n.borderWidth)):n.borderWidth||1,a.strokeStyle=n.borderColor,a.setLineDash(n.borderDash||[]),a.lineDashOffset=n.borderDashOffset||0;let t=s.leftForLtr(c,h),e=s.leftForLtr(s.xPlus(c,1),h-2),i=ki(n.borderRadius);Object.values(i).some(t=>0!==t)?(a.beginPath(),a.fillStyle=r.multiKeyBackground,$e(a,{x:t,y:g,w:h,h:l,radius:i}),a.fill(),a.stroke(),a.fillStyle=n.backgroundColor,a.beginPath(),$e(a,{x:e,y:g+1,w:h-2,h:l-2,radius:i}),a.fill()):(a.fillStyle=r.multiKeyBackground,a.fillRect(t,g,h,l),a.strokeRect(t,g,h,l),a.fillStyle=n.backgroundColor,a.fillRect(e,g+1,h-2,l-2))}a.fillStyle=this.labelTextColors[e]}drawBody(e,i,t){function a(t){i.fillText(t,p.x(e.x+g),e.y+u/2),e.y+=u+r}let s=this.body,{bodySpacing:r,bodyAlign:n,displayColors:o,boxHeight:l,boxWidth:h,boxPadding:d}=t,c=z(t.bodyFont),u=c.lineHeight,g=0,p=Di(t.rtl,this.x,this.width),f=p.textAlign(n),m,b,v,x,y,_,w;for(i.textAlign=n,i.textBaseline="middle",i.font=c.string,e.x=Jr(this,f,t),i.fillStyle=t.bodyColor,k(this.beforeBody,a),g=o&&"right"!==f?"center"===n?h/2+d:h+2+d:0,x=0,_=s.length;x<_;++x){for(m=s[x],b=this.labelTextColors[x],i.fillStyle=b,k(m.before,a),v=m.lines,o&&v.length&&(this._drawColorBox(i,e,x,p,t),u=Math.max(c.lineHeight,l)),y=0,w=v.length;y<w;++y)a(v[y]),u=c.lineHeight;k(m.after,a)}g=0,u=c.lineHeight,k(this.afterBody,a),e.y-=r}drawFooter(t,e,i){var a=this.footer,s=a.length;let r,n;if(s){var o=Di(i.rtl,this.x,this.width);for(t.x=Jr(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=o.textAlign(i.footerAlign),e.textBaseline="middle",r=z(i.footerFont),e.fillStyle=i.footerColor,e.font=r.string,n=0;n<s;++n)e.fillText(a[n],o.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+i.footerSpacing}}drawBackground(t,e,i,a){var{xAlign:s,yAlign:r}=this,{x:n,y:o}=t,{width:l,height:h}=i,{topLeft:d,topRight:c,bottomLeft:u,bottomRight:g}=ki(a.cornerRadius);e.fillStyle=a.backgroundColor,e.strokeStyle=a.borderColor,e.lineWidth=a.borderWidth,e.beginPath(),e.moveTo(n+d,o),"top"===r&&this.drawCaret(t,e,i,a),e.lineTo(n+l-c,o),e.quadraticCurveTo(n+l,o,n+l,o+c),"center"===r&&"right"===s&&this.drawCaret(t,e,i,a),e.lineTo(n+l,o+h-g),e.quadraticCurveTo(n+l,o+h,n+l-g,o+h),"bottom"===r&&this.drawCaret(t,e,i,a),e.lineTo(n+u,o+h),e.quadraticCurveTo(n,o+h,n,o+h-u),"center"===r&&"left"===s&&this.drawCaret(t,e,i,a),e.lineTo(n,o+d),e.quadraticCurveTo(n,o,n+d,o),e.closePath(),e.fill(),0<a.borderWidth&&e.stroke()}_updateAnimationTarget(e){let i=this.chart,t=this.$animations,a=t&&t.x,s=t&&t.y;if(a||s){let t=Ur[e.position].call(this,this._active,this._eventPosition);var r,n;t&&(r=this._size=qr(this,e),n=Gr(e,n=Object.assign({},t,this._size),e=Kr(i,e,n),i),a._to===n.x&&s._to===n.y||(this.xAlign=e.xAlign,this.yAlign=e.yAlign,this.width=r.width,this.height=r.height,this.caretX=t.x,this.caretY=t.y,this._resolveAnimations().update(this,n)))}}_willRender(){return!!this.opacity}draw(t){var e,i,a,s,r=this.options.setContext(this.getContext()),n=this.opacity;n&&(this._updateAnimationTarget(r),e={width:this.width,height:this.height},i={x:this.x,y:this.y},n=Math.abs(n)<.001?0:n,a=R(r.padding),s=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length,r.enabled)&&s&&(t.save(),t.globalAlpha=n,this.drawBackground(i,t,e,r),Ai(t,r.textDirection),i.y+=a.top,this.drawTitle(i,t,r),this.drawBody(i,t,r),this.drawFooter(i,t,r),Ti(t,r.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){var i=this._active,t=t.map(({datasetIndex:t,index:e})=>{var i=this.chart.getDatasetMeta(t);if(i)return{datasetIndex:t,element:i.data[e],index:e};throw new Error("Cannot find a dataset at index "+t)}),i=!N(i,t),a=this._positionChanged(t,e);(i||a)&&(this._active=t,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;var a=this.options,s=this._active||[],i=this._getActiveElements(t,s,e,i),r=this._positionChanged(i,t),s=e||!N(i,s)||r;return s&&(this._active=i,a.enabled||a.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e)),s}_getActiveElements(t,e,i,a){var s=this.options;return"mouseout"===t.type?[]:a?(a=this.chart.getElementsAtEventForMode(t,s.mode,s,i),s.reverse&&a.reverse(),a):e.filter(t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index))}_positionChanged(t,e){var{caretX:i,caretY:a,options:s}=this,s=Ur[s.position].call(this,t,e);return!1!==s&&(i!==s.x||a!==s.y)}}var an={id:"tooltip",_element:en,positioners:Ur,afterInit(t,e,i){i&&(t.tooltip=new en({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){var e,i=t.tooltip;i&&i._willRender()&&!(e={tooltip:i})!==t.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})&&(i.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",e))},afterEvent(t,e){var i;t.tooltip&&(i=e.replay,t.tooltip.handleEvent(e.event,i,e.inChartArea))&&(e.changed=!0)},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:tn},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};return i.register(Te,ur,Hs,a),i.helpers={...Bi},i._adapters=fs,i.Animation=_a,i.Animations=wa,i.animator=r,i.controllers=w.controllers.items,i.DatasetController=La,i.Element=e,i.elements=Hs,i.Interaction=$i,i.layouts=s,i.platforms=va,i.Scale=Wa,i.Ticks=pe,Object.assign(i,Te,ur,Hs,a,va),i.Chart=i,"undefined"!=typeof window&&(window.Chart=i),i}),((o,l)=>{function h(t){let e=[],i=[],a={},s,r=!1,n=t.tHead.rows[0].cells,o=t.tBodies[0].rows;if("TH"==o[0].cells[0].nodeName){for(let t=0;t<o.length;t++){var l=o[t].cells[0];l.dataset.chartValue?e.push(l.dataset.chartValue):e.push(l.textContent)}a.labels=e,r=!0}for(s=r?1:0;s<n.length;s++)if(1==!n[s].dataset.chartSetIgnore){let e={label:"",data:[]},t=n[s].dataset.chartSetOptions;e.label=n[s].textContent;for(let t=0;t<o.length;t++){var h=o[t].cells[s];if(h.dataset.chartValue)e.data.push(h.dataset.chartValue);else{let t=h.textContent;t=parseFloat(t.replace(/[^0-9]*([\d]+(?:[ ,.]?\d+)*)?(?:[.,](\d{1,2}))?[^0-9]*/,function(t,e,i){return e.replace(/ |,/g,"")+"."+i||"0"}),10),e.data.push(t)}}t&&(t=JSON.parse(t),e={...e,...t}),i.push(e)}return a.datasets=i,a}var d="[data-chart]";l.doc.on("timerpoke.wb wb-init[data-chart]",d,function(t){var e=l.init(t,"chart",d);if(e){var i,a,s,t="",r={events:["mousemove","mouseout","touchmove"]},n=e.dataset.chart;if(!["bar","line","doughnut","pie","polarArea","radar"].includes(n))return console.error('chart: The chart type "'+n+'" is not supported.'),!1;s=s||{tableMention:(n=l.i18n)("hyphen")+n("tbl-txt"),tableFollowing:"en"==l.lang?" - Chart. Details of the data displayed in this chart can be found in the following table.":" - Graphique. Les détails des données affichées dans ce graphique peuvent être trouvés dans le tableau suivant."},e.dataset.chartDetailsOpen&&(t="open"),(n=document.createElement("figure")).innerHTML='<figcaption class="h3">'+e.caption.innerHTML+`</figcaption>
				<div><canvas aria-label="`+e.caption.innerHTML+s.tableFollowing+`" role="img"></canvas></div>
				<details class="mrgn-tp-md"`+t+`>
					<summary>`+e.caption.innerHTML+s.tableMention+`</summary>
					<div class="table-responsive"></div>
				</details>`,e.parentNode.insertBefore(n,e),t=n.querySelector("canvas"),n.querySelector(".table-responsive").appendChild(e),s=e.dataset.chart,n=e.dataset.chartOptions?{...r,...JSON.parse(e.dataset.chartOptions)}:r,a=e.dataset.chartData?JSON.parse(e.dataset.chartData):h(e),i=new Chart(t,{type:s,data:a,options:n}),new MutationObserver(function(){i.data=e.dataset.chartData?JSON.parse(e.dataset.chartData):h(e),i.options=e.dataset.chartOptions?{...r,...JSON.parse(e.dataset.chartOptions)}:r,i.update()}).observe(e,{attributes:!0,subtree:!0,childList:!0}),l.ready(o(e),"chart")}}),l.add(d)})(jQuery,(window,wb)),((r,t)=>{let n=" of ",i="Questionnaire progress:";function e(t){let e,i=t.currentTarget;var a,s;!(e=i.classList.contains("quiz")&&i.classList.contains("wb-steps")?i:r(i).parentsUntil(o).parent().get(0))||e instanceof HTMLDocument||(t=r("legend.wb-steps-active:first-child",e).parents().prevAll(".steps-wrapper").length+1,s=(a=r(".progressBar",e)).attr("max"),r("p.progressText",e).text(t+n+s),a.val(t),r(".steps-wrapper",e).removeClass("hidden"),r(".steps-wrapper:has( div.hidden )",e).addClass("hidden"))}"fr"===wb.lang&&(n=" de ",i="Progression du questionnaire : ");var o=".provisional.wb-steps.quiz";t.querySelectorAll(o).forEach(t=>{var t=r(t),e=r("fieldset",t).length;t.prepend("<label><span class='wb-inv'>"+i+"</span><progress class='progressBar' max='"+e+"'></progress><p class='progressText' role='status'></p></label>")}),r(t).on("click",o+" .steps-wrapper div.buttons > :button",e),r(o).on("wb-ready.wb-steps",e)})(jQuery,document);