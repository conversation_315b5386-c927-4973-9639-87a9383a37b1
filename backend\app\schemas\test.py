from pydantic import BaseModel, UUID4, ConfigDict
from typing import Optional, List, Union
from datetime import datetime

class TestTypeBase(BaseModel):
    name: str
    description: Optional[str] = None
    is_active: Optional[bool] = True
    lab_id: Optional[UUID4] = None

class TestTypeCreate(TestTypeBase):
    pass

class TestTypeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    lab_id: Optional[UUID4] = None

class TestType(TestTypeBase):
    test_type_id: UUID4
    created_at: datetime
    updated_at: Optional[datetime]

    model_config = ConfigDict(from_attributes=True)

# Batch update schemas
class TestTypeBatchUpdate(BaseModel):
    """Batch update request - list of test type IDs with update data"""
    updates: List[dict]  # Each dict contains test_type_id and update fields