"""
API responses for all work dealing with <PERSON><PERSON>

This script handles all API response to do with Samples such as:
    GET /samples # Get ALL samples
    GET /samples/{SMS_ID} #Get sample details
    POST /samples #Makes a new sample

Future Work:
    Figuring out how to get SMS info with our own DB info
    What should /samples return (more or less)?
"""
from fastapi import APIRouter, Depends, HTTPException, Path, Body
from sqlalchemy.orm import Session
from typing import List, Optional

from ..database import get_db
from ..crud import sample as sample_crud
from ..schemas.sample import SampleBase
from ..common.constants import UserRole, ERROR_MESSAGES 
from ..common.exceptions import ValidationError, NotFoundError, StateError, AccessDeniedError
from ..core.dependencies import enforce_role_selection
from ..schemas.user import User
from ..core.logging import log_error, log_warning, log_info

router = APIRouter()

@router.get("/", response_model=List[SampleBase])
async def get_samples(
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Returns all SMS sample IDs

    ### Response Model
    - Returns the SMS sample Ids in the DB  + all info about samples

    ### Errors:
    - 403 Forbidden: If the user does not have access to the requested sample - Need to be logged in
    - 500 Internal Server Error: If an unexpected error occurs during the Sample retrieval
    """
    try:
        #Get all Samples from the DB
        samples = sample_crud.get_samples(db)
        log_info(f"User {current_user.user_id} listed {len(samples)} samples")
        return samples
    except StateError as e:
        log_error(f"State error in get_samples: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        log_error(f"Server error in get_samples: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, 
            detail=ERROR_MESSAGES["sample"]["list_failed"].format(error=str(e))
        )

@router.get("/{sms_id}")
async def get_Sample_with_SMS(
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection),
    sms_id: str = Path(title="sms ID", description="Get information about a certain sample", examples="12345")
):
    """
    Returns all information about the sample, from SMS and our own DB

    - **sms_id**: The ID of the sample

    ### Response Model
    - Returns the JSON of all data about the sample

    ### Errors:
    - 403 Forbidden: If the user does not have access to the requested sample - Need to be logged in
    - 404 Not Found: If the SMS sample doesn't exist
    - 502 Bad Gateway: If the external SMS service is unreachable
    - 500 Internal Server Error: If an unexpected error occurs during the Sample retrieval
    """
    try:
        #replace this with SMS function when that function is pushed!
        c_number = "c-"+sms_id
        ############
        #Put sample SMS function HERE!!!
        ############

        #Now get the sample info from out DB
        log_info(f"User {current_user.user_id} retrieved sample {sms_id}")
        sample_db=sample_crud.get_sample_by_SMS(db,sms_id)
        return sample_db

    except ValueError as e:
        log_warning(f"Sample not found: SMS ID {sms_id} requested by user {current_user.user_id} - {str(e)}")
        raise HTTPException(
            status_code=404, 
            detail=ERROR_MESSAGES["sample"]["not_found"]
        )
    
    except ConnectionError as e:
        log_error(f"Error connecting to SMS service when retrieving sample {sms_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=502, 
            detail=ERROR_MESSAGES["sample"]["sms_unreachable"]
        )
    
    except Exception as e:
        log_error(f"Unexpected error in get_Sample_with_SMS for sample {sms_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["sample"]["get_failed"].format(error=str(e))
        )

@router.post("/")
async def post_Sample(
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection),
    sample: SampleBase = Body(title="Sample Info")
):
    """
    Puts all relevant information into our Sample DB

    - **sample**: Sample info to be put in the DB

    ###Response Model
    - If successful, returns json with all info inputted into the DB

    ### Errors:
    - 403 Forbidden: If the user does not have access to the requested sample - Need to be logged in
    - 400 Bad Request: If the sample data is invalid or already exists
    - 500 Internal Server Error: If an unexpected error occurs when inputting the sample
    """

    #IF sample doesn't fit SampleBase parameters, it will cause an error anyways - so no need to check

    try:
        sample_db = sample_crud.post_sample(db, sample)
        
        log_info(f"User {current_user.user_id} created sample {sample_db.sample_id}")
        
        return sample_db
    
    except ValidationError as e:
        log_warning(f"Validation error in post_Sample: {str(e)}")
        # Check if this is an "already exists" error
        if "already exists" in str(e):
            raise HTTPException(
                status_code=403,
                detail=str(e)
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=str(e)
            )
    except AccessDeniedError as e:
        log_warning(f"Access denied error in post_Sample: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(
            status_code=403,
            detail=str(e)
        )
    except Exception as e:
        log_error(f"Unexpected error in post_Sample: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["sample"]["create_failed"].format(error=str(e))
        )