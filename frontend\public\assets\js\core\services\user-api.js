/*
 * User API Service
 * Handles all API calls related to user operations
 */
import { ApiBase } from './api-base.js';

// API methods for user operations
export class UserApi {
  // Get current user profile information
  static getMyProfile() {
    return ApiBase.get('/users/me/profile');
  }

  // Update user profile
  static updateProfile(profileData) {
    return ApiBase.put('/users/me/profile', profileData);
  }

  // Get user roles and lab assignments
  static getMyRoles() {
    return ApiBase.get('/users/me/roles');
  }
}
