"""
User API endpoints for managing users in the LIMS system.
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from ..database import get_db
from ..crud import user as user_crud
from ..crud import staff as staff_crud
from ..schemas.user import User, UserCreate, UserUpdate
from ..common.constants import ERROR_MESSAGES
from ..common.exceptions import NotFoundError, AccessDeniedError, ValidationError
from ..core.dependencies import enforce_role_selection, get_current_user
from ..core.logging import log_error, log_warning, log_info

router = APIRouter()

@router.get("/", response_model=List[User])
async def list_users(
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    List all users. Only accessible by lab administrators.

    Args:
        db (Session): Database session dependency
        current_user (User): Current authenticated user with role and lab selected

    Returns:
        List[User]: List of all users

    Raises:
        HTTPException: If user not authorized
    """
    try:
        return user_crud.get_users(
            db, 
            str(current_user.user_id), 
            getattr(current_user, "lab", None), 
            current_user.role
        )
    except AccessDeniedError as e:
        log_warning(f"Access denied in list_users: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        log_error(f"Error listing users: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/me", response_model=User)
async def get_current_user_info(
    current_user: User = Depends(enforce_role_selection)
):
    """
    Get the current authenticated user's information.

    Args:
        current_user (User): Current authenticated user with role and lab selected

    Returns:
        User: Current user's profile information
    """
    return current_user

@router.get("/me/profile", response_model=User)
async def get_current_user_profile(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get the current authenticated user's profile information including all lab roles.
    This endpoint doesn't require role selection and is made for the My Account page.

    Args:
        current_user (User): Current authenticated user (no role selection required)
        db (Session): Database session dependency

    Returns:
        User: Current user's profile information with all available lab roles
    """
    try:
        # Get user's staff roles with lab names
        staff_roles_with_labs = staff_crud.get_staff_with_lab_names_by_user_id(db, str(current_user.user_id))
        
        # Build lab_roles with lab names included
        lab_roles_data = []
        for staff, lab_name in staff_roles_with_labs:
            lab_roles_data.append({
                "lab": str(staff.lab_id) if staff.lab_id else None,
                "role": staff.role,
                "lab_name": lab_name
            })
        
        # Return user profile with updated lab_roles containing lab names
        return User(
            email=current_user.email,
            role=current_user.role, 
            is_active=current_user.is_active,
            lab=current_user.lab,    
            lab_roles=lab_roles_data,
            user_id=current_user.user_id,
            azure_ad_id=current_user.azure_ad_id 
        )
    except Exception as e:
        log_error(f"Error fetching user profile: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{user_id}", response_model=User)
async def get_user(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Get a specific user by ID. Only accessible by lab administrators.

    Args:
        user_id (str): ID of the user to retrieve
        db (Session): Database session dependency
        current_user (User): Current authenticated user with role and lab selected

    Returns:
        User: User profile information

    Raises:
        HTTPException: If not authorized or user not found
    """
    try:
        
        return user_crud.get_user(
            db=db, 
            user_id=user_id, 
            user_id_requester=str(current_user.user_id), 
            lab_id_requester=getattr(current_user, "lab", None), 
            role_requester=current_user.role
        )
    except NotFoundError as e:
        log_info(f"User not found in get_user: {str(e)} - Requested ID: {user_id}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in get_user: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        log_error(f"Error fetching user: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

    #If they have staff info - return that
    return User(
        email=user.email,
        role= None,
        is_active=user.is_active,
        lab= None,
        lab_roles=[{"lab": str(role.lab_id), "role": role.role} for role in staff],
        user_id = user.user_id
    )

@router.post("/", response_model=User)
async def create_user(
    user: UserCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Create a new user. Only accessible by lab administrators.

    Args:
        user (UserCreate): User creation data
        db (Session): Database session dependency
        current_user (User): Current authenticated user with role and lab selected

    Returns:
        User: Created user profile

    Raises:
        HTTPException: If not authorized or validation fails
    """
    try:
        new_user = user_crud.create_user(
            db=db, 
            user=user, 
            user_id=str(current_user.user_id), 
            lab_id=getattr(current_user, "lab", None), 
            role=current_user.role
        )
        
        log_info(f"User {current_user.user_id} created user {new_user.user_id}")
        
        return new_user
    except AccessDeniedError as e:
        log_warning(f"Access denied in create_user: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in create_user: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        log_error(f"Error creating user: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{user_id}", response_model=User)
async def update_user(
    user_id: str,
    user: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Update a user. Only accessible by lab administrators.

    Args:
        user_id (str): ID of the user to update
        user (UserUpdate): User data to update
        db (Session): Database session dependency
        current_user (User): Current authenticated user with role and lab selected

    Returns:
        User: Updated user profile

    Raises:
        HTTPException: If not authorized or user not found
    """
    try:
        updated_user = user_crud.update_user(
            db=db, 
            user_id=user_id, 
            user=user, 
            user_id_updater=str(current_user.user_id), 
            lab_id_updater=getattr(current_user, "lab", None), 
            role_updater=current_user.role
        )
        
        log_info(f"User {current_user.user_id} updated user {user_id}")
        
        return updated_user
    except NotFoundError as e:
        log_info(f"User not found in update_user: {str(e)} - Requested ID: {user_id}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in update_user: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in update_user: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log_error(f"Error updating user: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Delete a user. Only accessible by lab administrators.

    Args:
        user_id (str): ID of the user to delete
        db (Session): Database session dependency
        current_user (User): Current authenticated user with role and lab selected

    Returns:
        dict: Success message with deleted user ID

    Raises:
        HTTPException: If not authorized or user not found
    """
    try:
        result = user_crud.delete_user(
            db=db, 
            user_id=user_id, 
            user_id_deleter=str(current_user.user_id), 
            lab_id_deleter=getattr(current_user, "lab", None), 
            role_deleter=current_user.role
        )
        
        log_info(f"User {current_user.user_id} deleted user {user_id}")
        
        return {
            "message": ERROR_MESSAGES["user"]["delete_success"],
            "user_id": user_id
        }
    except NotFoundError as e:
        log_info(f"User not found in delete_user: {str(e)} - Requested ID: {user_id}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in delete_user: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in delete_user: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        log_error(f"Error deleting user: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
