/*!
 * Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v4.0.85 - 2025-02-20
 *
 */
!function(f,s){var e;(e=document.createElement("input")).setAttribute("type","date"),("date"===e.type?describe.skip:describe)('Input type="date" polyfill (date picker)',function(){function a(t,n){return t=t||'<input type="date" id="appointment"/>',n=n||'<label for="appointment">Appointment Date</label>',function(e){l=r.spy(f.prototype,"trigger"),d=d||e,i=f("<div>"+n+"</div>").prependTo(u),(c=f(t).appendTo(i)).trigger("wb-init.wb-date")}}function e(){r.restore(),i.remove(),d=null}var i,o,c,l,d,r=sinon.sandbox.create(),t=s.doc,u=t.find("body"),n="#wb-picker";before(function(){t.on("wb-init.wb-date","input[type=date]",function(){o=f(n),d&&d()})}),describe("initialization",function(){before(a()),after(e),it("should have marked the element as initialized",function(){expect(c.hasClass("wb-date-inited")).to.equal(!0)}),it("should have created the date picker toggle button",function(){var e=c.next().find("a");expect(e.attr("class")).to.contain("picker-toggle"),expect(e.attr("id")).to.equal("appointment-picker-toggle")}),it("should have created an instance of the calendar plugin",function(){expect(o.length).to.equal(1),expect(o.find(".wb-clndr").length).to.equal(1)}),it("should have created a close icon",function(){expect(o.find(".picker-close").length).to.equal(1)}),it("should have hidden the calendar",function(){expect(o.css("display")).to.equal("none"),expect(o.attr("aria-hidden")).to.equal("true")}),it("should have added links to the calendar",function(){var e=new Date;e.setMonth(e.getMonth()+1,0),expect(o.find(".cal-days a").length).to.equal(e.getDate())}),it("should have stored a state object in the field element",function(){var e=c.get(0),t=e.state,n=new Date;expect(typeof t).to.equal("object"),expect(t.labelText).to.equal("Appointment Date"),expect(t.field).to.equal(e),expect(t.$field.get(0)).to.equal(c.get(0)),expect(t.minDate.toString()).to.equal(new Date(1800,0,1).toString()),expect(t.maxDate.toString()).to.equal(new Date(2100,0,1).toString()),expect(t.year).to.equal(n.getFullYear()),expect(t.month).to.equal(n.getMonth()),expect(typeof t.daysCallback).to.equal("function")})}),describe("with a date format and error in the label",function(){before(a(null,'<label for="appointment"><span class="field-name">Appointment Date</span><span class="datepicker-format"> (<abbr title="Four digits year, dash, two digits month, dash, two digits day">YYYY-MM-DD</abbr>)</span><strong class="error" id="appointment-error"><span class="label label-danger"><span class="prefix">Error 1: </span>Please enter a valid date</span></strong></label>')),after(e),it("should have stored only the field name in the label",function(){var e=c.get(0).state;expect(e.labelText).to.equal("Appointment Date")})}),describe("with a populated date",function(){before(a('<input type="date" id="appointment" value="2014-08-07"/>')),after(e),it("should have stored the date in the state object",function(){var e=c.get(0).state;expect(e.date.toString()).to.equal(new Date(2014,7,7).toString())})}),describe("toggle button",function(){var t="Pick a date from a calendar for field: Appointment Date";before(a()),after(e),it("should have added alternative text indentifying the parent control",function(){var e=c.next().find("a");expect(e.text()).to.equal(t),expect(e.attr("title")).to.equal(t)}),describe("click while calendar is closed",function(){before(function(){c.next().find("a").click()}),after(function(){c.next().find("a").click()}),it("should have opened the calendar",function(){expect(o.hasClass("open")).to.equal(!0),expect(o.css("display")).to.equal("block")}),it("should have updated the alternative text on open",function(){var e=c.next().find("a"),t="Hide calendar  (escape key)";expect(e.text()).to.equal(t),expect(e.attr("title")).to.equal(t)})}),describe("click while calendar is opened",function(){before(function(){c.next().find("a").click().click()}),it("should have closed the calendar on a second click",function(){expect(o.hasClass("open")).to.equal(!1),expect(o.css("display")).to.equal("none")}),it("should have updated the alternative text on close",function(){var e=c.next().find("a");expect(e.text()).to.equal(t),expect(e.attr("title")).to.equal(t)})})}),describe("toggle button for disabled date field",function(){before(function(e){d=function(){c.next().find("a").click(),e()},a('<input type="date" readonly id="appointment"/>')()}),after(function(){c.next().find("a").click(),e()}),it("should not have opend the calendar on click",function(){expect(o.hasClass("open")).to.equal(!1),expect(o.css("display")).to.equal("none")})}),describe("toggle button for read-only date field",function(){before(function(e){d=function(){c.next().find("a").click(),e()},a('<input type="date" readonly id="appointment"/>')()}),after(function(){c.next().find("a").click(),e()}),it("should not have opend the calendar on click",function(){expect(o.hasClass("open")).to.equal(!1),expect(o.css("display")).to.equal("none")})}),describe("close button",function(){before(function(e){d=function(){c.next().find("a").click(),f(".picker-close").click(),e()},a('<input type="date" id="appointment"/>')()}),after(e),it("should have closed the calendar on click",function(){expect(o.hasClass("open")).to.equal(!1),expect(o.css("display")).to.equal("none")})}),describe("keyboard shortcut",function(){before(function(e){d=function(){c.next().find("a").click(),o.trigger({type:"keydown",which:27}),e()},a()()}),after(e),it("should have close the calendar on pressing the escape key",function(){expect(o.hasClass("open")).to.equal(!1),expect(o.css("display")).to.equal("none")})}),describe("opening the calendar",function(){before(function(e){d=function(){c.next().find("a").click(),e()},a('<input type="date" id="test" min="2014-03-04" max="2014-03-18"/>','<label for="test">Test Date</label>')()}),after(function(){c.next().find("a").click(),e()}),it("should have update the calendar settings object with the field state object",function(){var e,t=c.get(0).state,n=o.get(0).firstChild.lib;for(e in t)expect(n[e]).to.equal(t[e])}),it("should have updated the aria-hidden attribute",function(){expect(o.attr("aria-hidden")).to.equal("false")}),it("should have updated the aria-controls to the date field",function(){expect(o.attr("aria-controls")).to.equal("test")}),it("should have updated the aria-labelled-by to the toggle button",function(){expect(o.attr("aria-labelledby")).to.equal("test-picker-toggle")}),it("should have positioned the date picker immediately under the control",function(){var e=o.offset(),t=c.offset();expect(Math.floor(e.left)).to.equal(Math.floor(t.left)),expect(Math.floor(e.top)).to.equal(Math.floor(t.top+c.outerHeight()))})}),describe("opening the calendar when the field has no date and the current date is inside the date range",function(){var t=new Date;before(function(e){var t=new Date,n=new Date;t.setMonth(-1),n.setMonth(1),d=function(){c.next().find("a").click(),e()},a('<input type="date" id="appointment" min="'+t.toISOString().split("T")[0]+' max="'+n.toISOString().split("T")[0]+' "/>')()}),after(function(){c.next().find("a").click(),e()}),it("should have set the month and year to the current month and year",function(){var e=o.get(0).firstChild.lib;expect(e.year).to.equal(t.getFullYear()),expect(e.month).to.equal(t.getMonth())})}),describe("opening the calendar when the field has no date and the current date is outside the date range",function(){var n=new Date;before(function(e){var t=new Date;t.setMonth(-2),n.setMonth(-1),d=function(){c.next().find("a").click(),e()},a('<input type="date" id="appointment" min="'+t.toISOString().split("T")[0]+'" max="'+n.toISOString().split("T")[0]+'" />')()}),after(function(){c.next().find("a").click(),e()}),it("should have set the month and year to the maximum data's month and year",function(){var e=o.get(0).firstChild.lib;expect(e.year).to.equal(n.getFullYear()),expect(e.month).to.equal(n.getMonth())})}),describe("opening the calendar when the associated field has a date that is inside the date range",function(){before(function(e){d=function(){c.next().find("a").click(),e()},a('<input type="date" id="appointment" min="2014-03-18" max="2015-03-18" value="2014-08-07"/>')()}),after(function(){c.next().find("a").click(),e()}),it("should have set the calendar to the same month as the field's date",function(){var e=o.get(0).firstChild.lib;expect(e.year).to.equal(2014),expect(e.month).to.equal(7)}),it("should have highlighted the selected date",function(){expect(o.find(".cal-index-7 > a").attr("aria-selected")).to.equal("true")})}),describe("closing the calendar",function(){before(function(e){d=function(){c.next().find("a").click().click(),e()},a()()}),after(e),it("should have updated the aria-hidden attribute",function(){expect(o.attr("aria-hidden")).to.equal("true")})}),describe("minimum date",function(){before(function(e){d=function(){c.next().find("a").click(),f(".cal-year").val(2014).trigger("change"),f(".cal-month").val(2).trigger("change"),e()},a('<input type="date" id="appointment" min="2014-03-18"/>')()}),after(function(){c.next().find("a").click(),e()}),it("should have been added to the state object",function(){expect(c.get(0).state.minDate.toString()).to.equal(new Date(2014,2,18).toString())}),it("should have been passed the minimum to the calendar plugin",function(){expect(o.get(0).firstChild.lib.minDate.toString()).to.equal(new Date(2014,2,18).toString())}),it("should have prevented the creation of links before the minimum date",function(){expect(o.find(".cal-days a").length).to.equal(14)})}),describe("maximum date",function(){before(function(e){d=function(){c.next().find("a").click(),f(".cal-year").val(2014).trigger("change"),f(".cal-month").val(2).trigger("change"),e()},a('<input type="date" id="appointment" max="2014-03-18"/>')()}),after(function(){c.next().find("a").click(),e()}),it("should have been added to the state object",function(){expect(c.get(0).state.maxDate.toString()).to.equal(new Date(2014,2,18).toString())}),it("should have been passed the minimum to the calendar plugin",function(){expect(o.get(0).firstChild.lib.maxDate.toString()).to.equal(new Date(2014,2,18).toString())}),it("should have prevented the creation of links past the maximum date",function(){expect(o.find(".cal-days a").length).to.equal(18)})}),describe("minimum and maximum dates in same month",function(){before(function(e){d=function(){c.next().find("a").click(),e()},a('<input type="date" id="appointment" min="2014-03-04" max="2014-03-18"/>')()}),after(function(){c.next().find("a").click(),e()}),it("should have been passed the minimum and maximum to the calendar plugin",function(){var e=o.get(0).firstChild.lib;expect(e.minDate.toString()).to.equal(new Date(2014,2,4).toString()),expect(e.maxDate.toString()).to.equal(new Date(2014,2,18).toString())}),it("should have prevented the creation of links before the minimum date and past the maximum date",function(){expect(f(n).find(".cal-days a").length).to.equal(15)})}),describe("selecting a date",function(){before(function(e){d=function(){c.next().find("a").click(),o.find(".cal-days a").eq(5).click(),e()},a('<input type="date" id="appointment" min="2014-03-01" max="2014-03-31"/>')()}),after(e),it("should have populated the from field with the selected date",function(){expect(c.val()).to.equal("2014-03-06")}),it("should have triggered the change event on the form field",function(){expect(l.calledWith("change")).to.equal(!0),expect(l.calledOn(c)).to.equal(!0)}),it("should have closed the calendar",function(){expect(o.hasClass("open")).to.equal(!1),expect(o.css("display")).to.equal("none")})}),describe("selecting a date for a disabled field",function(){before(function(e){d=function(){c.next().find("a").click(),c.attr("disabled","true"),o.find(".cal-days a").eq(5).click(),e()},a()()}),after(function(){c.next().find("a").click(),e()}),it("should not have populated the form field",function(){expect(c.val()).to.equal("")}),it("should not have triggered the change event on the form field",function(){expect(l.calledWith("change")).to.equal(!1)})}),describe("selecting a date for a read-only field",function(){before(function(e){d=function(){c.next().find("a").click(),c.attr("readonly","true"),o.find(".cal-days a").eq(5).click(),e()},a()()}),after(function(){c.next().find("a").click(),e()}),it("should not have populated the form field",function(){expect(c.val()).to.equal("")}),it("should not have triggered the change event on the form field",function(){expect(l.calledWith("change")).to.equal(!1)})}),describe("updating the min and max date of the field after the creation of the element",function(){before(function(e){d=function(){c.next().find("a").click().click(),c.attr("min","2014-03-12"),c.attr("max","2014-03-17"),c.next().find("a").click(),e()},a('<input type="date" id="appointment" min="2014-03-08" max="2014-03-22"/>')()}),after(function(){c.next().find("a").click(),e()}),it("should have update the field's state",function(){var e=c.get(0).state;expect(e.minDate.toString()).to.equal(new Date(2014,2,12).toString()),expect(e.maxDate.toString()).to.equal(new Date(2014,2,17).toString())}),it("should have update the min and max date of the date picker",function(){expect(f(n).find(".cal-days a").length).to.equal(6)})})})}(jQuery,wb);
//# sourceMappingURL=test.min.js.map