/*
 * Navigation Helpers
 * Generic navigation utilities for language detection and redirects
 */
import { GLOBAL_CONFIGS } from '../config/global-configs.js';

// Get current language prefix from URL path
// Returns language prefix ('/en' or '/fr')
export function getCurrentLanguagePrefix() {
    const currentPath = window.location.pathname;
    return currentPath.startsWith('/en/') ? '/en' : '/fr';
}

// Generic redirect function using page keys from configuration
// This is the primary redirect function - all redirects should use this for consistency
// pageKey: Key from GLOBAL_CONFIGS.navigation.pages (e.g., 'home', 'manageTestTypes')
// delay: Optional delay in milliseconds (uses config default if not provided)
export function redirectToPageByKey(pageKey, delay = null) {
    const pagePath = GLOBAL_CONFIGS.navigation.pages[pageKey];
    if (!pagePath) {
        console.error(`Page key '${pageKey}' not found in navigation configuration`);
        return;
    }

    const langPrefix = getCurrentLanguagePrefix();
    const redirectDelay = delay !== null ? delay : GLOBAL_CONFIGS.navigation.redirectDelay;

    // For immediate redirects (delay = 0), execute directly without setTimeout
    if (redirectDelay === 0) {
        window.location.href = `${langPrefix}/${pagePath}`;
    } else {
        setTimeout(() => {
            window.location.href = `${langPrefix}/${pagePath}`;
        }, redirectDelay);
    }
}
