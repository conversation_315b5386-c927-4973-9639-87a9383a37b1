// Fetches AAD configuration from the backend API
async function getMsalConfig() {
    try {
        const response = await fetch('/api/auth-config');
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Failed to fetch AAD config: ${response.statusText}`);
        }
        const config = await response.json();

        return {
            auth: {
                clientId: config.clientId,
                authority: config.authority,
                redirectUri: config.redirectUri
            },
            cache: {
                cacheLocation: "localStorage",
                storeAuthStateInCookie: true 
            }
        };
    } catch (error) {
        console.error("Error fetching MSAL config:", error);
        throw error; // Propagate error to alert developer
    }
}

// Asynchronously initializes and exports the msalInstance
export const msalInstancePromise = (async () => {
    const config = await getMsalConfig();
    return new msal.PublicClientApplication(config);
})();