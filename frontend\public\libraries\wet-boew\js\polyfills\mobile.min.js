/*!
 * Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v4.0.85 - 2025-02-20
 *
 */
!function(t,e,n){"function"==typeof define&&define.amd?define(["jquery"],function(t){return n(t,0,e),t.mobile}):n(t.jQuery,0,e)}(this,document,function(t,F,e,H){(n=t).event.special.throttledresize={setup:function(){n(this).bind("resize",r)},teardown:function(){n(this).unbind("resize",r)}},r=function(){i=(new Date).getTime(),250<=(s=i-a)?(a=i,n(this).trigger("throttledresize")):(o&&clearTimeout(o),o=setTimeout(r,250-s))},a=0;var n,o,i,s,r,a,c,u,l,h,p,d,v=t,f=e,m="virtualMouseBindings",g="virtualTouchID",b="vmouseover vmousedown vmousemove vmouseup vclick vmouseout vmousecancel".split(" "),w="clientX clientY pageX pageY screenX screenY".split(" "),T=v.event.mouseHooks?v.event.mouseHooks.props:[],L=v.event.props.concat(T),D={},E=0,V=0,q=0,k=!1,y=[],X=!1,Y=!1,M="addEventListener"in f,P=v(f),Q=1,z=0;function S(t){for(;t&&void 0!==t.originalEvent;)t=t.originalEvent;return t}function I(t){for(var e,n,o={};t;){for(n in e=v.data(t,m))e[n]&&(o[n]=o.hasVirtualBinding=!0);t=t.parentNode}return o}function A(){Y=!0}function C(){Y=!1}function x(){G(),E=setTimeout(function(){z=E=0,y.length=0,X=!1,A()},v.vmouse.resetTimerDuration)}function G(){E&&(clearTimeout(E),E=0)}function B(t,e,n){var o;return(n&&n[t]||!n&&function(t,e){for(var n;t;){if((n=v.data(t,m))&&(!e||n[e]))return t;t=t.parentNode}}(e.target,t))&&(o=function(t,e){var n,o,i,s,r,a,c,u=t.type;if((t=v.Event(t)).type=e,n=t.originalEvent,o=v.event.props,-1<u.search(/^(mouse|click)/)&&(o=L),n)for(r=o.length;r;)t[i=o[--r]]=n[i];if(-1<u.search(/mouse(down|up)|click/)&&!t.which&&(t.which=1),-1!==u.search(/^touch/)&&(u=(e=S(n)).touches,e=e.changedTouches,s=u&&u.length?u[0]:e&&e.length?e[0]:void 0))for(a=0,c=w.length;a<c;a++)t[i=w[a]]=s[i];return t}(e,t),v(e.target).trigger(o)),o}function J(t){var e=v.data(t.target,g);X||z&&z===e||(e=B("v"+t.type,t))&&(e.isDefaultPrevented()&&t.preventDefault(),e.isPropagationStopped()&&t.stopPropagation(),e.isImmediatePropagationStopped())&&t.stopImmediatePropagation()}function K(t){var e,n=S(t).touches;n&&1===n.length&&(e=I(n=t.target)).hasVirtualBinding&&(z=Q++,v.data(n,g,z),G(),C(),k=!1,n=S(t).touches[0],V=n.pageX,q=n.pageY,B("vmouseover",t,e),B("vmousedown",t,e))}function O(t){Y||(k||B("vmousecancel",t,I(t.target)),k=!0,x())}function R(t){var e,n,o,i;Y||(e=S(t).touches[0],n=k,o=v.vmouse.moveDistanceThreshold,i=I(t.target),(k=k||Math.abs(e.pageX-V)>o||Math.abs(e.pageY-q)>o)&&!n&&B("vmousecancel",t,i),B("vmousemove",t,i),x())}function U(t){var e,n;Y||(A(),B("vmouseup",t,e=I(t.target)),k||(n=B("vclick",t,e))&&n.isDefaultPrevented()&&(n=S(t).changedTouches[0],y.push({touchID:z,x:n.clientX,y:n.clientY}),X=!0),B("vmouseout",t,e),k=!1,x())}function W(t){var e,n=v.data(t,m);if(n)for(e in n)if(n[e])return 1}function Z(){}v.vmouse={moveDistanceThreshold:10,clickDistanceThreshold:10,resetTimerDuration:1500};for(var N=0;N<b.length;N++)v.event.special[b[N]]=function(i){var s=i.substr(1);return{setup:function(t,e){W(this)||v.data(this,m,{}),v.data(this,m)[i]=!0,D[i]=(D[i]||0)+1,1===D[i]&&P.bind(s,J),v(this).bind(s,Z),M&&(D.touchstart=(D.touchstart||0)+1,1===D.touchstart)&&P.bind("touchstart",K).bind("touchend",U).bind("touchmove",R).bind("scroll",O)},teardown:function(t,e){--D[i],D[i]||P.unbind(s,J),M&&(--D.touchstart,D.touchstart||P.unbind("touchstart",K).unbind("touchmove",R).unbind("touchend",U).unbind("scroll",O));var n=v(this),o=v.data(this,m);o&&(o[i]=!1),n.unbind(s,Z),W(this)||n.removeData(m)}}}(b[N]);function j(t,e,n){var o=n.type;n.type=e,c.event.dispatch.call(t,n),n.type=o}M&&f.addEventListener("click",function(t){var e,n,o,i,s,r=y.length,a=t.target;if(r)for(e=t.clientX,n=t.clientY,d=v.vmouse.clickDistanceThreshold,o=a;o;){for(i=0;i<r;i++)if(s=y[i],o===a&&Math.abs(s.x-e)<d&&Math.abs(s.y-n)<d||v.data(o,g)===s.touchID)return t.preventDefault(),void t.stopPropagation();o=o.parentNode}},!0),t.mobile={},T=t,f={touch:"ontouchend"in e},T.mobile.support=T.mobile.support||{},T.extend(T.support,f),T.extend(T.mobile.support,f),u=(c=t)(e),c.each("touchstart touchmove touchend tap taphold swipe swipeleft swiperight scrollstart scrollstop".split(" "),function(t,e){c.fn[e]=function(t){return t?this.bind(e,t):this.trigger(e)},c.attrFn&&(c.attrFn[e]=!0)}),T=c.mobile.support.touch,l=T?"touchstart":"mousedown",h=T?"touchend":"mouseup",p=T?"touchmove":"mousemove",c.event.special.scrollstart={enabled:!0,setup:function(){var n,e,o=this;function i(t,e){j(o,(n=e)?"scrollstart":"scrollstop",t)}c(o).bind("touchmove scroll",function(t){c.event.special.scrollstart.enabled&&(n||i(t,!0),clearTimeout(e),e=setTimeout(function(){i(t,!1)},50))})}},c.event.special.tap={tapholdThreshold:750,setup:function(){var r=this,a=c(r);a.bind("vmousedown",function(t){if(t.which&&1!==t.which)return!1;var e,n=t.target;t.originalEvent;function o(){clearTimeout(e)}function i(){o(),a.unbind("vclick",s).unbind("vmouseup",o),u.unbind("vmousecancel",i)}function s(t){i(),n===t.target&&j(r,"tap",t)}a.bind("vmouseup",o).bind("vclick",s),u.bind("vmousecancel",i),e=setTimeout(function(){j(r,"taphold",c.Event("taphold",{target:n}))},c.event.special.tap.tapholdThreshold)})}},c.event.special.swipe={scrollSupressionThreshold:30,durationThreshold:1e3,horizontalDistanceThreshold:30,verticalDistanceThreshold:75,start:function(t){var e=t.originalEvent.touches?t.originalEvent.touches[0]:t;return{time:(new Date).getTime(),coords:[e.pageX,e.pageY],origin:c(t.target)}},stop:function(t){t=t.originalEvent.touches?t.originalEvent.touches[0]:t;return{time:(new Date).getTime(),coords:[t.pageX,t.pageY]}},handleSwipe:function(t,e){e.time-t.time<c.event.special.swipe.durationThreshold&&Math.abs(t.coords[0]-e.coords[0])>c.event.special.swipe.horizontalDistanceThreshold&&Math.abs(t.coords[1]-e.coords[1])<c.event.special.swipe.verticalDistanceThreshold&&t.origin.trigger("swipe").trigger(t.coords[0]>e.coords[0]?"swipeleft":"swiperight")},setup:function(){var i=c(this);i.bind(l,function(t){var e,n=c.event.special.swipe.start(t);function o(t){n&&(e=c.event.special.swipe.stop(t),Math.abs(n.coords[0]-e.coords[0])>c.event.special.swipe.scrollSupressionThreshold)&&t.preventDefault()}i.bind(p,o).one(h,function(){i.unbind(p,o),n&&e&&c.event.special.swipe.handleSwipe(n,e),n=e=void 0})})}},c.each({scrollstop:"scrollstart",taphold:"tap",swipeleft:"swipe",swiperight:"swipe"},function(t,e){c.event.special[t]={setup:function(){c(this).bind(e,c.noop)}}})});
//# sourceMappingURL=mobile.min.js.map