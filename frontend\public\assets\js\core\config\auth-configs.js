/*
 * Authentication Configuration
 *
 * 1. FUNCTIONAL SETTINGS - Global technical configurations (form lengths, navigation delays, etc.)
 * 2. UI TEXT - Global UI text with bilingual support (common buttons, navigation, etc.)
 * 3. MESSAGES - Cross-cutting messages organized by type and category
 * 
 */

export const AUTH_CONFIGS = {
    // ===== 1. FUNCTIONAL SETTINGS =====
    // Authentication technical configurations
    // Add authentication-specific functional settings here as needed (timeouts, retry attempts, etc.)

    // ===== 2. UI TEXT (Bilingual) =====
    // Authentication UI text
    // Add authentication-specific UI text here as needed (login form labels, button text, etc.)
    ui: {
        // Add authentication UI sections here as needed
    },

    // ===== 3. MESSAGES (Bilingual) =====
    // Authentication messages organized by type and category
    messages: {
        errors: {
            // Basic login errors
            loginFailed: {
                message: {
                    en: 'Invalid email or password. Please try again.',
                    fr: '<PERSON><PERSON><PERSON> ou mot de passe invalide. Veuillez réessayer.'
                },
                type: 'danger'
            },
            noTokenReturned: {
                message: {
                    en: 'Authentication failed. No token received from server.',
                    fr: 'Échec de l\'authentification. Aucun jeton reçu du serveur.'
                },
                type: 'danger'
            },

            // Azure AD login errors
            azureLoginFailed: {
                message: {
                    en: 'Microsoft login failed. Please try again.',
                    fr: 'Échec de la connexion Microsoft. Veuillez réessayer.'
                },
                type: 'danger'
            },
            azureTokenMissing: {
                message: {
                    en: 'Microsoft login failed. No token received. Try again.',
                    fr: 'Échec de la connexion Microsoft. Aucun jeton reçu. Réessayez.'
                },
                type: 'danger'
            },

            // Permission and access errors
            accessDenied: {
                message: {
                    en: 'You do not have permission to access this feature. Contact your administrator.',
                    fr: 'Vous n\'avez pas la permission d\'accéder à cette fonctionnalité. Contactez votre administrateur.'
                },
                type: 'danger'
            },
            missingLabAssignment: {
                message: {
                    en: 'Cannot determine lab assignment. Contact administrator.',
                    fr: 'Impossible de déterminer l\'attribution de laboratoire. Contactez l\'administrateur.'
                },
                type: 'danger'
            }
        },

        warnings: {
            // Credential validation warnings
            missingCredentials: {
                message: {
                    en: 'Please enter both email and password.',
                    fr: 'Veuillez saisir votre courriel et votre mot de passe.'
                },
                type: 'warning'
            },
            noRolesFound: {
                message: {
                    en: 'No valid roles found. Contact your administrator.',
                    fr: 'Aucun rôle valide trouvé. Contactez votre administrateur.'
                },
                type: 'warning'
            },

            // Azure-specific connection warnings
            azureConnectionError: {
                message: {
                    en: 'Unable to connect to Microsoft login service. Please check your connection.',
                    fr: 'Impossible de se connecter au service de connexion Microsoft. Veuillez vérifier votre connexion.'
                },
                type: 'warning'
            },
            azureServiceError: {
                message: {
                    en: 'Microsoft login service is temporarily unavailable. Please try again later.',
                    fr: 'Le service de connexion Microsoft est temporairement indisponible. Veuillez réessayer plus tard.'
                },
                type: 'warning'
            }
        },

        info: {
            // User action info messages
            azureUserCancelled: {
                message: {
                    en: 'Microsoft login was cancelled. You can try again or use regular login.',
                    fr: 'La connexion Microsoft a été annulée. Vous pouvez réessayer ou utiliser la connexion normale.'
                },
                type: 'info'
            }
        }
    }
};
