"""Tests for the Test Types API endpoints.

Test Coverage:
1. GET /
    - test_list_test_types
    - test_list_test_types_with_pagination
    - test_list_test_types_invalid_pagination
    - test_list_test_types_with_lab_filter
    - test_list_test_types_with_nonexistent_lab

2. POST /
    - test_create_test_type_admin
    - test_create_test_type_unauthorized
    - test_create_test_type_validation_error
    - test_create_test_type_server_error

3. PUT /{test_type_id}
    - test_update_test_type_admin
    - test_update_test_type_unauthorized
    - test_update_test_type_not_found
    - test_update_test_type_validation_error
    - test_update_test_type_server_error

4. DELETE /{test_type_id}
    - test_delete_test_type_admin
    - test_delete_test_type_unauthorized
    - test_delete_test_type_not_found
    - test_delete_test_type_server_error

5. POST /batch-update
    - test_batch_update_test_types_admin_success
    - test_batch_update_test_types_single_update
    - test_batch_update_test_types_unauthorized
    - test_batch_update_test_types_empty_batch
    - test_batch_update_test_types_invalid_data
    - test_batch_update_test_types_mixed_results
    - test_batch_update_test_types_server_error
"""

import pytest
import uuid
from unittest.mock import patch

from tests.test_utils.helpers import TestHelpers
from tests.test_utils.constants import TEST_TYPE_DATA, TEST_TYPE_TEST_CASES
from app.common.constants import ERROR_MESSAGES, UserRole
from app.api.tests import list_test_types, create_test_type, update_test_type, delete_test_type
from app.crud.test import get_test_type
from app.common.exceptions import NotFoundError, AccessDeniedError, ValidationError, StateError
from tests.test_utils.verification import TestTypeVerifier, verify_api_error_response


class TestTestTypesAPI:
    #
    # GET / endpoint tests
    #
    def test_list_test_types(self, client, user_token, test_types):
        """Test listing all test types"""
        response = client.get(
            "/test-types/",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        assert response.status_code == 200
        test_types_data = response.json()
        assert len(test_types_data) == len(test_types)
        
        # Verify each test type using the verifier
        for test_type in test_types_data:
            TestTypeVerifier.verify_common_fields(test_type)

    def test_list_test_types_with_pagination(self, client, user_token, test_types):
        """Test listing test types with pagination"""
        # First page with limit=1
        response = client.get(
            "/test-types/?skip=0&limit=1",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        assert response.status_code == 200
        first_page = response.json()
        assert len(first_page) == 1
        TestTypeVerifier.verify_common_fields(first_page[0])
        
        # Second page with limit=1
        response = client.get(
            "/test-types/?skip=1&limit=1",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        assert response.status_code == 200
        second_page = response.json()
        assert len(second_page) == 1
        TestTypeVerifier.verify_common_fields(second_page[0])
        
        # Ensure they are different test types
        assert first_page[0]["test_type_id"] != second_page[0]["test_type_id"]

    def test_list_test_types_invalid_pagination(self, client, user_token):
        """Test listing test types with invalid pagination parameters"""
        # Test negative skip
        response = client.get(
            "/test-types/?skip=-1",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        verify_api_error_response(response, 422)
        
        # Test invalid limit
        response = client.get(
            "/test-types/?limit=0",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        verify_api_error_response(response, 422)

    def test_list_test_types_with_lab_filter(self, client, user_token, test_types_multiple_labs, test_labs):
        """Test listing test types filtered by lab_id"""
        lab_id = str(test_labs[0].lab_id)
        response = client.get(
            f"/test-types/?lab_id={lab_id}",
            headers=TestHelpers.Auth.get_headers(user_token)
        )

        assert response.status_code == 200
        test_types_data = response.json()

        # Verify all returned test types belong to the specified lab
        assert len(test_types_data) > 0
        for test_type in test_types_data:
            assert test_type["lab_id"] == lab_id
            TestTypeVerifier.verify_common_fields(test_type)

    def test_list_test_types_with_nonexistent_lab(self, client, user_token):
        """Test listing test types with non-existent lab_id"""
        non_existent_lab_id = str(uuid.uuid4())
        response = client.get(
            f"/test-types/?lab_id={non_existent_lab_id}",
            headers=TestHelpers.Auth.get_headers(user_token)
        )

        assert response.status_code == 200
        test_types_data = response.json()
        assert len(test_types_data) == 0  # Should return empty list

    #
    # POST / endpoint tests
    #
    def test_create_test_type_admin(self, client, admin_token):
        """Test that admin users can create test types"""
        test_type_data = {
            "name": "New API Test Type",
            "description": "Created through API test",
            "is_active": True
        }
        
        response = client.post(
            "/test-types/",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=test_type_data
        )
        
        assert response.status_code == 200
        created_test_type = response.json()
        TestTypeVerifier.verify_response(created_test_type, {
            "name": test_type_data["name"],
            "description": test_type_data["description"],
            "is_active": test_type_data["is_active"]
        })

        # Verify lab_id is automatically assigned (should not be None)
        assert created_test_type["lab_id"] is not None

    def test_create_test_type_unauthorized(self, client, user_token):
        """Test that non-admin users cannot create test types"""
        test_type_data = {
            "name": "Unauthorized Test Type",
            "description": "This should not be created",
            "is_active": True
        }
        
        response = client.post(
            "/test-types/",
            headers=TestHelpers.Auth.get_headers(user_token),
            json=test_type_data
        )
        
        verify_api_error_response(
            response,
            403,
            ERROR_MESSAGES["test"]["unauthorized_create"]
        )

    def test_create_test_type_validation_error(self, client, admin_token):
        """Test validation errors when creating a test type"""
        # Missing required name field
        test_type_data = {
            "description": "Missing name field",
            "is_active": True
        }
        
        response = client.post(
            "/test-types/",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=test_type_data
        )
        
        verify_api_error_response(response, 422)

    @patch('app.crud.test.create_test_type')
    def test_create_test_type_server_error(self, mock_create, client, admin_token):
        """Test server error handling when creating a test type"""
        mock_create.side_effect = StateError(ERROR_MESSAGES["test"]["create_failed"].format(error="Server error"))
        
        test_type_data = {
            "name": "Error Test Type",
            "description": "This should trigger an error",
            "is_active": True
        }
        
        response = client.post(
            "/test-types/",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=test_type_data
        )
        
        verify_api_error_response(
            response,
            500,
            ERROR_MESSAGES["test"]["create_failed"].format(error="Server error")
        )

    #
    # PUT /{test_type_id} endpoint tests
    #
    def test_update_test_type_admin(self, client, admin_token, test_type):
        """Test that admin users can update test types"""
        update_data = {
            "name": "Updated API Test Type",
            "description": "Updated through API test",
            "is_active": False
        }
        
        response = client.put(
            f"/test-types/{test_type.test_type_id}",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=update_data
        )
        
        assert response.status_code == 200
        updated_test_type = response.json()
        TestTypeVerifier.verify_response(updated_test_type, {
            "test_type_id": str(test_type.test_type_id),
            "name": update_data["name"],
            "description": update_data["description"],
            "is_active": update_data["is_active"]
        })

    def test_update_test_type_unauthorized(self, client, user_token, test_type):
        """Test that non-admin users cannot update test types"""
        update_data = {
            "name": "Unauthorized Update"
        }
        
        response = client.put(
            f"/test-types/{test_type.test_type_id}",
            headers=TestHelpers.Auth.get_headers(user_token),
            json=update_data
        )
        
        verify_api_error_response(
            response,
            403,
            ERROR_MESSAGES["test"]["unauthorized_update"]
        )

    def test_update_test_type_not_found(self, client, admin_token):
        """Test updating a non-existent test type"""
        non_existent_id = uuid.uuid4()
        update_data = {
            "name": "Non-existent Update"
        }
        
        response = client.put(
            f"/test-types/{non_existent_id}",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=update_data
        )
        
        verify_api_error_response(
            response,
            404,
            ERROR_MESSAGES["test"]["not_found"]
        )

    @patch('app.crud.test.update_test_type')
    def test_update_test_type_server_error(self, mock_update, client, admin_token, test_type):
        """Test server error handling when updating a test type"""
        mock_update.side_effect = StateError(ERROR_MESSAGES["test"]["update_failed"].format(error="Server error"))
        
        update_data = {
            "name": "Error Update"
        }
        
        response = client.put(
            f"/test-types/{test_type.test_type_id}",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=update_data
        )
        
        verify_api_error_response(
            response,
            500,
            ERROR_MESSAGES["test"]["update_failed"].format(error="Server error")
        )

    #
    # DELETE /{test_type_id} endpoint tests
    #
    def test_delete_test_type_admin(self, client, admin_token, test_type, db):
        """Test that admin users can delete test types"""
        response = client.delete(
            f"/test-types/{test_type.test_type_id}",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == ERROR_MESSAGES["test"]["delete_success"]
        assert response_data["test_type_id"] == str(test_type.test_type_id)
        
        # Verify the test type is deleted
        deleted_test_type = get_test_type(db, test_type.test_type_id)
        assert deleted_test_type is None

    def test_delete_test_type_unauthorized(self, client, user_token, test_type):
        """Test that non-admin users cannot delete test types"""
        response = client.delete(
            f"/test-types/{test_type.test_type_id}",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        verify_api_error_response(
            response,
            403,
            ERROR_MESSAGES["test"]["unauthorized_delete"]
        )

    def test_delete_test_type_not_found(self, client, admin_token):
        """Test deleting a non-existent test type"""
        non_existent_id = uuid.uuid4()
        
        response = client.delete(
            f"/test-types/{non_existent_id}",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        verify_api_error_response(
            response,
            404,
            ERROR_MESSAGES["test"]["not_found"]
        )

    @patch('app.crud.test.delete_test_type')
    def test_delete_test_type_server_error(self, mock_delete, client, admin_token, test_type):
        """Test server error handling when deleting a test type"""
        mock_delete.side_effect = StateError(ERROR_MESSAGES["test"]["delete_failed"].format(error="Server error"))
        
        response = client.delete(
            f"/test-types/{test_type.test_type_id}",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        verify_api_error_response(
            response,
            500,
            ERROR_MESSAGES["test"]["delete_failed"].format(error="Server error")
        )

    #
    # POST /batch-update endpoint tests
    #
    def test_batch_update_test_types_admin_success(self, client, admin_token):
        """Test successful batch update by admin user"""
        # Create test types for batch update
        test_types = []
        for test_data in TEST_TYPE_DATA.values():
            response = client.post(
                "/test-types/",
                headers=TestHelpers.Auth.get_headers(admin_token),
                json=test_data
            )
            assert response.status_code == 200
            test_types.append(response.json())

        # Prepare batch update data
        update_data = TEST_TYPE_TEST_CASES["batch_update"]["valid_batch"]
        batch_updates = []
        for i, update in enumerate(update_data):
            if i < len(test_types):
                batch_item = {"test_type_id": test_types[i]["test_type_id"]}
                batch_item.update(update)
                batch_updates.append(batch_item)

        batch_request = {"updates": batch_updates}

        # Execute batch update
        response = client.post(
            "/test-types/batch-update",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=batch_request
        )

        # Verify response
        assert response.status_code == 200
        batch_result = response.json()

        # Verify batch update response using verifier
        TestTypeVerifier.verify_batch_update_response(batch_result, expected_count=2)

    def test_batch_update_test_types_single_update(self, client, admin_token):
        """Test batch update with single test type"""
        # Create single test type
        test_data = TEST_TYPE_DATA["test_type1"]
        response = client.post(
            "/test-types/",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=test_data
        )
        assert response.status_code == 200
        test_type = response.json()

        # Prepare single item batch update
        update_data = TEST_TYPE_TEST_CASES["batch_update"]["single_item"][0]
        batch_request = {
            "updates": [
                {
                    "test_type_id": test_type["test_type_id"],
                    **update_data
                }
            ]
        }

        # Execute batch update
        response = client.post(
            "/test-types/batch-update",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=batch_request
        )

        # Verify response
        assert response.status_code == 200
        batch_result = response.json()

        # Verify batch update response using verifier
        TestTypeVerifier.verify_batch_update_response(batch_result, expected_count=1)

    def test_batch_update_test_types_unauthorized(self, client, user_token):
        """Test batch update with unauthorized user (non-admin)"""
        # Prepare batch update data 
        batch_request = {
            "updates": [
                {
                    "test_type_id": str(uuid.uuid4()),
                    "name": "Unauthorized Update"
                }
            ]
        }

        # Attempt batch update with non-admin user
        response = client.post(
            "/test-types/batch-update",
            headers=TestHelpers.Auth.get_headers(user_token),
            json=batch_request
        )

        # Verify unauthorized response
        verify_api_error_response(
            response,
            403,
            ERROR_MESSAGES["test"]["unauthorized_update"]
        )

    def test_batch_update_test_types_empty_batch(self, client, admin_token):
        """Test batch update with empty batch"""
        # Prepare empty batch
        batch_request = {"updates": []}

        # Attempt batch update with empty batch
        response = client.post(
            "/test-types/batch-update",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=batch_request
        )

        # Verify bad request response
        verify_api_error_response(
            response,
            400,
            ERROR_MESSAGES["test"]["batch_empty"]
        )

    def test_batch_update_test_types_invalid_data(self, client, admin_token):
        """Test batch update with invalid request data format"""
        # Test with invalid JSON structure that should fail schema validation
        schema_invalid_requests = [
            {},  # Missing updates field
            {"updates": "not_a_list"},  # Updates not a list
        ]

        for invalid_request in schema_invalid_requests:
            response = client.post(
                "/test-types/batch-update",
                headers=TestHelpers.Auth.get_headers(admin_token),
                json=invalid_request
            )

            # Should return 422 for validation error
            assert response.status_code == 422

        # Test with structurally valid but logically invalid data
        # This should now fail entirely due to atomic transaction
        logical_invalid_request = {"updates": [{"invalid": "structure"}]}  # Missing test_type_id
        response = client.post(
            "/test-types/batch-update",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=logical_invalid_request
        )

        # Should return 500 error due to complete failure
        assert response.status_code == 500

    def test_batch_update_test_types_invalid_update_fails_all(self, client, admin_token):
        """Test batch update with invalid update - should fail entire batch"""
        # Create one valid test type
        test_data = TEST_TYPE_DATA["test_type1"]
        response = client.post(
            "/test-types/",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=test_data
        )
        assert response.status_code == 200
        valid_test_type = response.json()

        # Prepare batch with one valid and one invalid update
        batch_request = {
            "updates": [
                {
                    "test_type_id": valid_test_type["test_type_id"],
                    "name": "Valid Update"
                },
                {
                    "test_type_id": str(uuid.uuid4()),  # Nonexistent ID
                    "name": "Invalid Update"
                }
            ]
        }

        # Execute batch update - should fail entirely
        response = client.post(
            "/test-types/batch-update",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=batch_request
        )

        # Verify complete failure (500 error due to atomic transaction failure)
        assert response.status_code == 500

    def test_batch_update_test_types_server_error(self, client, admin_token):
        """Test batch update with server error"""
        # Create test type
        test_data = TEST_TYPE_DATA["test_type1"]
        response = client.post(
            "/test-types/",
            headers=TestHelpers.Auth.get_headers(admin_token),
            json=test_data
        )
        assert response.status_code == 200
        test_type = response.json()

        # Prepare batch update data
        batch_request = {
            "updates": [
                {
                    "test_type_id": test_type["test_type_id"],
                    "name": "Updated Name"
                }
            ]
        }

        # Mock server error in batch update function
        with patch('app.crud.test.batch_update_test_types') as mock_batch_update:
            mock_batch_update.side_effect = Exception("Server error")

            # Execute batch update
            response = client.post(
                "/test-types/batch-update",
                headers=TestHelpers.Auth.get_headers(admin_token),
                json=batch_request
            )

            # Verify server error response
            verify_api_error_response(
                response,
                500,
                ERROR_MESSAGES["test"]["batch_update_failed"].format(error="Server error")
            )
