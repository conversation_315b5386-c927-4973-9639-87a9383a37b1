/*!
 * Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v4.0.85 - 2025-02-20
 *
 */
!function(){"use strict";wb.i18nDict={"lang-code":"lt","lang-native":"Lietuvių kalba",add:"papildyti",all:"Visi",tphp:"Puslapio viršus",load:"pakrovimo ...",process:"perdirbimo ...",srch:"<PERSON>ie<PERSON><PERSON>","no-match":"Atitikmenų nerasta",matches:{mixin:"[MIXIN] atitikmuo (-enys) rasti"},current:"(dabartinė)",hide:"Slėpti",err:"Klaid<PERSON>",colon:":",hyphen:" - ","full-stop":".","comma-space":", ",space:"&#32;",start:"<PERSON>rad<PERSON>ti",stop:"<PERSON><PERSON><PERSON>",back:"Atgal",cancel:"<PERSON><PERSON><PERSON><PERSON>","min-ago":"<PERSON><PERSON><PERSON> minutę","coup-mins":"Prieš kelias minutes","mins-ago":{mixin:"Prieš [MIXIN] minutes"},"hour-ago":"Prieš valandą","hours-ago":{mixin:"Prieš [MIXIN] valandas"},"days-ago":{mixin:"Prieš [MIXIN] dienas"},yesterday:"Vakar",nxt:"Sekantis","nxt-r":"Sekantis (rodyklės dešinėn klavišu)",prv:"Ankstenis","prv-l":"Ankstenis (rodyklės dešinėn klavišu)",first:"Pirmasis",last:"Paskutinis",page:"Jump to: Page","srch-menus":"Ieškoti ir meniu",email:"El. paštas","menu-close":"Uždaryti meniu","overlay-close":"Uždaryti perdangos","esc-key":'("escape" klavišu)',show:"Rodyti","tab-rot":{off:"Sustabdyti rotaciją",on:"Pradėti rotaciją"},"tab-list":"Skirtukas sąrašas","tab-pnl-end1":"Šio skirtuko skydelyje pabaiga.","tab-pnl-end2":"Grįžti į skirtukų sąrašą","tab-pnl-end3":"ar toliau likusia puslapio dalimi.","tab-play":"Žaisti","mmp-play":"Žaisti",pause:"Pauzė",open:"Atidaryti",close:"Uždaryti",volume:"Apimtis",mute:{on:"Užtylinti",off:"Įjungti garsą"},cc:{off:"Slėpti didžiųjų raidžių įjungimą",on:"Rodyti didžiųjų raidžių įjungimą"},"cc-err":"Klaida kraunant didžiųjų raidžių įjungimą",fs:"Enter full screen",adesc:{on:"Aktyvuoti audio aprašą",off:"Išjungti audio aprašą"},pos:"Esama pozicija:",dur:"Visas laikas:",msgYoutubeNotLoad:"Video encountered loading issues",msgYoutubeVdLoad:"Loading Youtube video","shr-txt":"Dalytis ","shr-pg":" šiuo puslapiu","shr-vid":" šiuo vaizdo","shr-aud":" tai garso failas","shr-hnt":" su {s} ","shr-disc":"Išreikštų ar numanomų ne bet kokius produktus ar paslaugas įrašas.","frm-nosubmit":"Formos pateikti negalima, nes ","errs-fnd":" rastos klaidos.","err-fnd":" rasta klaida.","err-correct":"(Correct and resubmit)","date-hide":"Slėpti kalendorių","date-show":"Pasirinkti datą iš kalendoriaus:","date-sel":"Atrinkta",days:["Sekmadienis","Pirmadienis","Antradienis","Trečiadienis","Ketvirtadienis","Penktadienis","Šeštadienis"],mnths:["Sausis","Vasaris","Kovas","Balandis","Gegužė","Birželis","Liepa","Rugpjūtis","Rugsėjis","Spalis","Lapkritis","Gruodis"],cal:"Kalendorius","cal-format":"<span class='wb-inv'>{ddd}, {M} </span>{d}<span class='wb-inv'>, {Y}</span>",currDay:"(Šiandien)","cal-goToLnk":'Eiti į<span class="wb-inv"> mėnesius</span>',"cal-goToTtl":"Eiti į mėnesius","cal-goToMnth":"Mėnesiai:","cal-goToYr":"Metai:","cal-goToBtn":"Eiti",prvMnth:"Ankstesnis mėnuo: ",nxtMnth:"Sekantis mėnuo: ","lb-curr":"Punktas %curr% iš %total%","lb-xhr-err":"Turinio nepavyko užkrauti.","lb-img-err":"Nuotraukos nepavyko užkrauti.","tbl-txt":"Lentelė","tbl-dtls":"Grafika. Daugiau detalių sekančioje lentelėje.","chrt-cmbslc":"Combined slice","st-to-msg-bgn":"Jūsų sesija pasibaigs automatiškai #min# min #sec# sek.","st-to-msg-end":'Pasirinkite "Tęsti sesiją" pratęsti savo sesiją.',"st-msgbx-ttl":"Sesijos laiko įspėjimas","st-alrdy-to-msg":"Deja, Jūsų sesija jau baigėsi. Prašome prisijungti vėl.","st-btn-cont":"Tęsti sesiją","st-btn-end":"Sesijos pabaigos dabar","td-toggle":"Perjungti visi","td-open":"Išskleisti viską","td-close":"Sutraukti viską","td-ttl-open":"Išskleisti visus turinio skyrius","td-ttl-close":"Sutraukti visus turinio skyrius",sortAsc:": suaktyvinkite didėjimo tvarka rūšiuoti",sortDesc:": suaktyvinkite rikiuojama",emptyTbl:"Nėra duomenų apie vaisto pateiktoje lentelėje",infoEntr:"Rodoma _START_ iki _END_ iš _TOTAL_ įrašų",infoEmpty:"Rodoma 0 iki 0 iš 0 įrašų",infoFilt:"(filtruojamas iš _MAX_ Iš viso įrašų)",info1000:",",lenMenu:"Rodyti _MENU_ įrašai",filter:"Filtruoti",tbFilterInst:"This table provides a sorting feature via the buttons across the column header row with only one instance visible at a time.","twitter-start-notice":"Start of @%username%’s X timeline","twitter-end-notice":"End of @%username%’s X timeline","twitter-skip-end":"Skip to end of @%username%’s X timeline","twitter-skip-start":"Skip to start of @%username%’s X timeline","twitter-timeline-title":"X timeline","geo-mapctrl":"@geo-mapctrl@","geo-zmin":"Artinti","geo-zmout":"Tolinti","geo-zmwrld":"Padidinti iki map mastą","geo-zmfeat":"Padidinti iki elemento","geo-sclln":"Žemėlapio mastelis","geo-msepos":"Platuma ir ilguma pelės žymeklį","geo-ariamap":"Žemėlapis objektas. Žemėlapio funkcijų aprašymai žemiau pateiktoje lentelėje.","geo-ally":"<strong>Klaviatūros Vartotojų:</strong> žemėlapis sufokusuotas, naudokite rodyklių klavišus į panoraminį vaizdą, žemėlapį ir pliuso ir minuso klavišus. Rodyklių klavišus nebus przesuniesz žemėlapį, kai didinimas žemėlapyje,.","geo-allyttl":"Instrukcijos: Žemėlapis navigacijos","geo-tgllyr":"Perjungti sluoksnio rodymą","geo-hdnlyr":"Šis sluoksnis yra paslėptas.","geo-bmap-url":"//geoappext.nrcan.gc.ca/arcgis/rest/services/BaseMaps/CBMT3978/MapServer/WMTS/","geo-bmap-matrix-set":"default028mm","geo-bmapttl":"BaseMaps_CBMT3978","geo-bmapurltxt":"//geoappext.nrcan.gc.ca/arcgis/rest/services/BaseMaps/CBMT_TXT_3978/MapServer/WMTS/tile/1.0.0/BaseMaps_CBMT3978/{Style}/{TileMatrixSet}/{TileMatrix}/{TileRow}/{TileCol}.jpg","geo-attrlnk":"//geogratis.gc.ca/geogratis/CBM_CBC?lang=en","geo-attrttl":"GeoGratis - Kanada bazė žemėlapis (anglų arba prancūzų kalba)","geo-sel":"Pasirinkti","geo-lblsel":"Patikrinkite, pasirinkite elementą žemėlapyje","geo-locurl-geogratis":"//geogratis.gc.ca/services/geolocation/en/locate","geo-loc-placeholder":"Nurodykite vietą&#44; pašto kodą&#44; adresą (pašto)&#44; į NBA numeris Pavadinimas ...","geo-loc-label":"Vieta","geo-aoi-north":"Į šiaurę","geo-aoi-east":"Rytų","geo-aoi-south":"Į pietus","geo-aoi-west":"Vakarai","geo-aoi-instructions":'Lygiosios langelį žemėlapyje arba įveskite koordinates žemiau ir spustelėkite mygtuką "Pridėti".',"geo-aoi-title":"Draw box on map or enter coordinates","geo-aoi-btndraw":"Atkreipti","geo-aoi-btnclear":"Pašalinti","geo-geoloc-btn":"Padidinti dabartinę vietą","geo-geoloc-fail":"Vieta nepavyko. Prašome užtikrinti, kad vietos nustatymo paslaugos yra įjungtas.","geo-geoloc-uncapable":"Lokalizacijos nepalaikomas jūsų naršyklėje.","geo-lgnd-grphc":"Legenda grafinis už žemėlapio sluoksnį.","wb-disable":"Switch to basic HTML version","wb-enable":"Switch to standard version","disable-notice-h":"Notice: Basic HTML","disable-notice":"You are viewing Basic HTML view. Some features may be disabled.","skip-prefix":"Skip to:",dismiss:"Dismiss","tmpl-signin":"Prisijungti","fltr-lbl":'Filter<span class="wb-inv"> content: results appear below as you type.</span>',"fltr-info":"Showing <span data-nbitem></span> filtered from <span data-total></span> total entries","pii-header":"Remove Personal information","pii-intro":"Some information in your form is identified as personal information and it will be replaced as follows:","pii-view-more":"What is considered personal information?","pii-view-more-info":"<p>The following types of information are considered personal information:</p><ul><li>email address</li><li>telephone number</li><li>postal code</li><li>passport number</li><li>business number</li><li>social insurance number (SIN)</li></ul>","pii-yes-btn":"Remove personal information and submit","pii-cancel-btn":"Go back and edit fields",redacted:"redacted"}}(),wb.doc.one("formLanguages.wb",function(){var i;i=function(i){return i.extend(i.validator.messages,{required:"Šis laukas yra privalomas.",remote:"Prašau pataisyti šį lauką.",email:"Prašau įvesti teisingą elektroninio pašto adresą.",url:"Prašau įvesti teisingą URL.",date:"Prašau įvesti teisingą datą.",dateISO:"Prašau įvesti teisingą datą (ISO).",number:"Prašau įvesti teisingą skaičių.",digits:"Prašau naudoti tik skaitmenis.",creditcard:"Prašau įvesti teisingą kreditinės kortelės numerį.",equalTo:"Prašau įvestį tą pačią reikšmę dar kartą.",extension:"Prašau įvesti reikšmę su teisingu plėtiniu.",maxlength:i.validator.format("Prašau įvesti ne daugiau kaip {0} simbolių."),minlength:i.validator.format("Prašau įvesti bent {0} simbolius."),rangelength:i.validator.format("Prašau įvesti reikšmes, kurių ilgis nuo {0} iki {1} simbolių."),range:i.validator.format("Prašau įvesti reikšmę intervale nuo {0} iki {1}."),max:i.validator.format("Prašau įvesti reikšmę mažesnę arba lygią {0}."),min:i.validator.format("Prašau įvesti reikšmę didesnę arba lygią {0}.")}),i},"function"==typeof define&&define.amd?define(["jquery","../jquery.validate"],i):"object"==typeof module&&module.exports?module.exports=i(require("jquery")):i(jQuery)});