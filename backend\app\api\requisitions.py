"""
API responses for all work dealing with Requisitions

This script handles all API responses related to Requisitions including:
    GET /requisitions/lab/{lab_id} # List all requisitions for a specific lab
    GET /requisitions/mine # List all requisitions for the current scientist user
    POST /requisitions/{lab_id} # Create new requisition in a specific lab
    GET /requisitions/{req_id} # Get specific requisition
    PUT /requisitions/{req_id} # Update requisition
    POST /requisitions/{req_id}/samples # Add samples to requisition
    GET /requisitions/{req_id}/samples # List samples in requisition
    DELETE /requisitions/{req_id}/samples/{sample_id} # Remove sample from requisition
    POST /requisitions/{req_id}/samples/{sample_id}/tests # Assign tests to a sample in a requisition
    GET /requisitions/{req_id}/samples/{sample_id}/tests # List tests for a sample in a requisition
    PUT /requisitions/{req_id}/samples/{sample_id}/tests/{test_id} # Update test status for a sample in a requisition
    DELETE /requisitions/{req_id}/samples/{sample_id}/tests/{test_id} # Remove a test from a sample in a requisition

Future Work:
    Add lab_id to all requisitions
    When uploading, take the current user's lab as lab to upload to. No need to request lab_id
    We can assume they are working on a req in the lab they set to work in
"""
from app.models.requisition_sample_test import RequisitionSampleTest
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from ..database import get_db
from ..crud import requisition as requisition_crud
from ..schemas.requisition import (
    Requisition,
    RequisitionCreate,
    RequisitionSampleTestUpdate,
    RequisitionUpdate,
    RequisitionStatus,
    RequisitionSampleResponse,
    RequisitionSampleTestCreate,
    RequisitionSampleTestResponse,
    RequisitionReadWithDetails
)
from ..core.dependencies import enforce_role_selection
from ..schemas.user import User
from ..common.constants import UserRole, ERROR_MESSAGES, SampleTestStatus
from ..common.exceptions import NotFoundError, AccessDeniedError, ValidationError, StateError
from ..core.logging import log_error, log_warning, log_info
from ..utils.auth_helpers import validate_lab_requisition_access

router = APIRouter()


@router.get("/lab/{lab_id}", response_model=List[RequisitionReadWithDetails])
async def list_requisitions(
        lab_id: str = Path(title="Lab_ID", description="The ID of which lab you want to get the requisitions for", examples="UUID"),
        status: Optional[List[RequisitionStatus]] = Query(default=None, description="Filter by one or more requisition statuses"),
        skip: int = Query(default=0, ge=0),
        limit: int = Query(default=100, ge=1, le=100),
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    List requisitions with optional filtering and pagination.

    Args:
        lab_id (str) : ID of Lab
        status (Optional[List[RequisitionStatus]], optional): Filter by one or more requisition statuses. Pass multiple status parameters e.g. ?status=submitted&status=in_progress
        skip (int): Number of records to skip
        limit (int): Maximum number of records to return
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        List[Requisition]: List of requisitions

    Raises:
        HTTPException: If operation fails
    """
    try:
        # Validate user access to lab requisitions endpoint
        validate_lab_requisition_access(current_user, lab_id)

        status_values: Optional[List[str]] = None
        if status:
            status_values = [s.value for s in status]

        requisitions = requisition_crud.get_requisitions(
            db,
            str(current_user.user_id),
            current_user.role,
            lab_id,
            skip,
            limit,
            statuses=status_values
        )
        log_info(f"User {current_user.user_id} listed requisitions for lab {lab_id} with statuses {status_values}")
        return requisitions

    except ValidationError as e:
        log_warning(f"Validation error in list_requisitions: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except StateError as e:
        log_warning(f"State error in list_requisitions: {str(e)}")
        raise HTTPException(status_code=409, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"AccessDeniedError caught in API exception handler: {str(e)}. Raising HTTPException 403.")
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error in list_requisitions: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["list_failed"].format(error=str(e))
        )


@router.get("/mine", response_model=List[RequisitionReadWithDetails])
async def list_my_requisitions(
        status: Optional[List[RequisitionStatus]] = Query(default=None, description="Filter by one or more requisition statuses"),
        skip: int = Query(default=0, ge=0),
        limit: int = Query(default=100, ge=1, le=100),
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    List requisitions submitted by the current user (scientist).

    Args:
        status (Optional[List[RequisitionStatus]], optional): Filter by one or more requisition statuses. Pass multiple status parameters e.g. ?status=submitted&status=in_progress
        skip (int): Number of records to skip
        limit (int): Maximum number of records to return
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        List[RequisitionReadWithDetails]: List of requisitions submitted by the user.

    Raises:
        HTTPException: If the user is not a scientist or if the operation fails.
    """
    try:
        if current_user.role != UserRole.SCIENTIST:
            log_warning(f"Non-scientist user {current_user.user_id} attempted to access /requisitions/mine")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_mine"])

        status_values: Optional[List[str]] = None
        if status:
            status_values = [s.value for s in status]

        requisitions = requisition_crud.get_requisitions(
            db,
            user_id=str(current_user.user_id),
            user_role=current_user.role,
            lab_id=None,  # Explicitly set lab_id to None to fetch all requisitions for the user
            skip=skip,
            limit=limit,
            statuses=status_values
        )
        log_info(f"Scientist {current_user.user_id} listed their requisitions with statuses {status_values}")
        return requisitions

    except AccessDeniedError as e:
        log_warning(f"AccessDeniedError in list_my_requisitions: {str(e)}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in list_my_requisitions: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except StateError as e:
        log_warning(f"State error in list_my_requisitions: {str(e)}")
        raise HTTPException(status_code=409, detail=str(e))
    except Exception as e:
        log_error(f"Unexpected error in list_my_requisitions: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["list_failed"].format(error=str(e))
        )


@router.post("/{lab_id}", response_model=Requisition)
async def create_requisition(
        lab_id: str = Path(...,title="Lab_ID", description="The ID of which lab you want to get the requisitions for", examples="UUID"),
        requisition: RequisitionCreate = Body(...),
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    Create a new requisition.

    Args:
        requisition (RequisitionCreate): Requisition data
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        Requisition: Created requisition

    Raises:
        HTTPException: If user not authorized or operation fails
    """
    try:
        if current_user.role == UserRole.SCIENTIST or (
            current_user.role != UserRole.SCIENTIST and lab_id == str(current_user.lab)
        ):
            created_requisition = requisition_crud.create_requisition(
                db,
                requisition,
                str(current_user.user_id),
                current_user.role,
                lab_id
            )
            log_info(f"User {current_user.user_id} created requisition {created_requisition.req_id}")
            return created_requisition
        else:
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_lab"])

    except AccessDeniedError as e:
        log_warning(f"Access denied in create_requisition: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))

    except ValidationError as e:
        log_warning(f"Validation error in create_requisition: {str(e)}")
        raise HTTPException(status_code=422, detail=str(e))

    except StateError as e:
        log_error(f"State error in create_requisition: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

    except Exception as e:
        log_error(f"Error creating requisition: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["create_failed"].format(error=str(e))
        )



@router.get("/{req_id}", response_model=Requisition)
async def get_requisition(
        req_id: str,
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    Get a specific requisition by ID.

    Args:
        req_id (str): Requisition ID
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        Requisition: Requisition details

    Raises:
        HTTPException: If requisition not found or user not authorized
    """
    try:
        requisition = requisition_crud.get_requisition(
            db,
            req_id,
            str(current_user.user_id),
            current_user.role,
            getattr(current_user, "lab", None)
        )
        return requisition
    except NotFoundError as e:
        log_info(f"Requisition not found in get_requisition: {str(e)} - Requested ID: {req_id}")
        raise HTTPException(status_code=404, detail=str(e))
    except StateError as e:
        log_warning(f"State error in get_requisition: {str(e)}")
        raise HTTPException(status_code=409, detail=str(e))
    except Exception as e:
        log_error(f"Error fetching requisition {req_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["fetch_failed"].format(error=str(e))
        )


@router.put("/{req_id}", response_model=Requisition)
async def update_requisition(
        req_id: str,
        requisition_update: RequisitionUpdate,
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    Update an existing requisition.

    Args:
        req_id (str): Requisition ID to update
        requisition (RequisitionUpdate): Updated requisition data
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        Requisition: Updated requisition

    Raises:
        HTTPException: If not authorized or requisition not found
    """
    try:
        updated_requisition = requisition_crud.update_requisition(
            db,
            req_id,
            requisition_update,
            str(current_user.user_id),
            current_user.role,
            getattr(current_user, "lab", None)
        )
        
        log_info(f"User {current_user.user_id} updated requisition {req_id}")

        return updated_requisition

    except NotFoundError as e:
        log_info(f"Requisition not found in update_requisition: {str(e)} - Requested ID: {req_id}")
        raise HTTPException(status_code=404, detail=str(e))

    except AccessDeniedError as e:
        log_warning(f"Access denied in update_requisition: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))

    except StateError as e:
        log_warning(f"State error in update_requisition: {str(e)}")
        raise HTTPException(status_code=409, detail=str(e))

    except Exception as e:
        log_error(f"Error updating requisition {req_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["update_failed"].format(error=str(e))
        )



@router.post("/{req_id}/samples", response_model=List[RequisitionSampleResponse])
async def add_samples_to_requisition(
        req_id: str,
        sample_ids: dict,
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    Add samples to the specified requisition.

    Args:
        req_id (str): Requisition ID
        sample_ids (dict): Dict containing list of sample IDs under 'sample_ids' key
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        List[RequisitionSampleResponse]: List of added samples

    Raises:
        HTTPException: If operation fails
    """
    try:
        if not isinstance(sample_ids, dict) or 'sample_ids' not in sample_ids:
            log_warning(f"Missing sample_ids in add_samples_to_requisition for requisition {req_id}")
            raise ValidationError(ERROR_MESSAGES["requisition"]["missing_sample_ids"])
            
        samples = requisition_crud.add_samples_to_requisition(
            db,
            req_id,
            sample_ids['sample_ids'],
            str(current_user.user_id),
            current_user.role,
            lab_id = getattr(current_user, "lab", None)
        )
        
        log_info(f"User {current_user.user_id} added samples to requisition {req_id}")
        
        return samples
    except ValidationError as e:
        log_warning(f"Validation error in add_samples_to_requisition: {str(e)}")
        raise HTTPException(status_code=422, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in add_samples_to_requisition: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except NotFoundError as e:
        log_info(f"Not found error in add_samples_to_requisition: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except StateError as e:
        log_warning(f"State error in add_samples_to_requisition: {str(e)}")
        raise HTTPException(status_code=409, detail=str(e))
    except Exception as e:
        log_error(f"Error adding samples to requisition {req_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["add_samples_failed"].format(error=str(e))
        )


@router.get("/{req_id}/samples", response_model=List[RequisitionSampleResponse])
async def get_requisition_samples(
        req_id: str,
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    List all samples in the specified requisition.

    Args:
        req_id (str): Requisition ID
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        List[RequisitionSampleResponse]: List of samples in the requisition

    Raises:
        HTTPException: If requisition not found
    """
    try:
        samples = requisition_crud.get_requisition_samples(
            db,
            req_id,
            str(current_user.user_id),
            current_user.role,
            lab_id = getattr(current_user, "lab", None)
        )
        log_info(f"User {current_user.user_id} listed samples for requisition {req_id}")
        return samples
    except NotFoundError as e:
        log_info(f"Requisition not found in get_requisition_samples: {str(e)} - Requested ID: {req_id}")
        raise HTTPException(status_code=404, detail=str(e))
    except StateError as e:
        log_warning(f"State error in get_requisition_samples: {str(e)}")
        raise HTTPException(status_code=409, detail=str(e))
    except Exception as e:
        log_error(f"Error fetching samples for requisition {req_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["fetch_samples_failed"].format(error=str(e))
        )


@router.delete("/{req_id}/samples/{sample_id}")
async def remove_sample_from_requisition(
        req_id: str,
        sample_id: str,
        db: Session = Depends(get_db),
        current_user: User = Depends(enforce_role_selection)
):
    """
    Remove a sample from the specified requisition.

    Args:
        req_id (str): Requisition ID
        sample_id (str): Sample ID to remove
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        dict: Success message with requisition ID and sample ID

    Raises:
        HTTPException: If operation fails
    """
    try:
        requisition_crud.remove_sample_from_requisition(
            db,
            req_id,
            sample_id,
            str(current_user.user_id),
            current_user.role,
            lab_id = getattr(current_user, "lab", None)
        )
        
        log_info(f"User {current_user.user_id} removed sample {sample_id} from requisition {req_id}")
        
        return {
            "message": ERROR_MESSAGES["requisition"]["remove_sample_success"],
            "requisition_id": req_id,
            "sample_id": sample_id
        }
    except NotFoundError as e:
        log_info(f"Not found error in remove_sample_from_requisition: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in remove_sample_from_requisition: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except StateError as e:
        log_warning(f"State error in remove_sample_from_requisition: {str(e)}")
        raise HTTPException(status_code=409, detail=str(e))
    except Exception as e:
        log_error(f"Error removing sample {sample_id} from requisition {req_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["remove_sample_failed"].format(error=str(e))
        )


@router.post("/{req_id}/samples/{sample_id}/tests", response_model=List[RequisitionSampleTestResponse])
def add_tests_to_sample(
    req_id: str,
    sample_id: str,
    test_data: RequisitionSampleTestCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Add tests to a sample in a requisition.

    Args:
        req_id (str): Requisition ID
        sample_id (str): Sample ID
        test_data (RequisitionSampleTestCreate): Test type IDs to add
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        List[RequisitionSampleTestResponse]: List of added tests

    Raises:
        HTTPException: If operation fails
    """
    try:
        added_tests = requisition_crud.add_tests_to_requisition_sample(
            db,
            req_id,
            sample_id,
            [str(test_id) for test_id in test_data.test_type_ids],
            str(current_user.user_id),
            current_user.role,
            lab_id = getattr(current_user, "lab", None)
        )
        
        log_info(f"User {current_user.user_id} added tests to sample {sample_id} in requisition {req_id}")
        
        return added_tests
    except ValidationError as e:
        log_warning(f"Validation error in add_tests_to_sample: {str(e)}")
        if str(e) == ERROR_MESSAGES["test"]["already_exists"]:
            raise HTTPException(status_code=409, detail=str(e))
        else:
            raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        log_info(f"Not found error in add_tests_to_sample: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in add_tests_to_sample: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except StateError as e:
        log_warning(f"State error in add_tests_to_sample: {str(e)}")
        raise HTTPException(status_code=409, detail=str(e))
    except Exception as e:
        log_error(f"Error adding tests to sample {sample_id} in requisition {req_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["add_tests_failed"].format(error=str(e))
        )


@router.get("/{req_id}/samples/{sample_id}/tests", response_model=List[RequisitionSampleTestResponse])
def get_sample_tests(
    req_id: str,
    sample_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Get all tests associated with a sample in a requisition.

    Args:
        req_id (str): Requisition ID
        sample_id (str): Sample ID
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        List[RequisitionSampleTestResponse]: List of tests associated with the sample

    Raises:
        HTTPException: If operation fails or if sample/requisition not found
    """
    try:
        tests = requisition_crud.get_requisition_sample_tests(
            db,
            req_id,
            sample_id,
            str(current_user.user_id),
            current_user.role,
            lab_id = getattr(current_user, "lab", None)
        )
        log_info(f"User {current_user.user_id} listed tests for sample {sample_id} in requisition {req_id}")
        return tests
    except NotFoundError as e:
        log_info(f"Not found error in get_sample_tests: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in get_sample_tests: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in get_sample_tests: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except StateError as e:
        log_error(f"State error in get_sample_tests: {str(e)}", exc_info=True)
        raise HTTPException(status_code=409, detail=str(e))
    except Exception as e:
        log_error(f"Error getting tests for sample {sample_id} in requisition {req_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["get_sample_tests_failed"].format(error=str(e))
        )

@router.put("/{req_id}/samples/{sample_id}/tests/{test_id}", response_model=RequisitionSampleTestResponse)
async def update_test_status(
    req_id: str,
    sample_id: str,
    test_id: str,
    test_update: RequisitionSampleTestUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Update the status of a test in a requisition sample.

    Args:
        req_id (str): Requisition ID
        sample_id (str): Sample ID
        test_id (str): Test ID to update
        test_update (RequisitionSampleTestUpdate): Updated test data
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        RequisitionSampleTestResponse: Updated test details

    Raises:
        HTTPException: If operation fails or user not authorized
    """
    try:
        updated_test = requisition_crud.update_requisition_sample_test(
            db,
            req_id,
            sample_id,
            test_id,
            test_update,
            str(current_user.user_id),
            current_user.role,
            lab_id = getattr(current_user, "lab", None)
        )
        
        log_info(f"User {current_user.user_id} updated test {test_id} for sample {sample_id} in requisition {req_id}")
        
        return updated_test
    except NotFoundError as e:
        log_info(f"Not found error in update_test_status: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in update_test_status: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in update_test_status: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except StateError as e:
        log_error(f"State error in update_test_status: {str(e)}", exc_info=True)
        raise HTTPException(status_code=409, detail=str(e))
    except Exception as e:
        log_error(f"Error updating test status for test {test_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["update_test_failed"].format(error=str(e))
        )

@router.delete("/{req_id}/samples/{sample_id}/tests/{test_id}")
async def remove_test_from_sample(
    req_id: str,
    sample_id: str,
    test_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Remove a test from a sample in a requisition.

    Args:
        req_id (str): Requisition ID
        sample_id (str): Sample ID
        test_id (str): Test ID to remove
        db (Session): Database session dependency
        current_user (User): Current authenticated user

    Returns:
        dict: Success message with requisition ID, sample ID and test ID

    Raises:
        HTTPException: If operation fails or user not authorized
    """
    try:
        requisition_crud.remove_test_from_requisition_sample(
            db,
            req_id,
            sample_id,
            test_id,
            str(current_user.user_id),
            current_user.role,
            lab_id = getattr(current_user, "lab", None)
        )
        
        log_info(f"User {current_user.user_id} removed test {test_id} from sample {sample_id} in requisition {req_id}")
        
        return {
            "message": ERROR_MESSAGES["requisition"]["remove_test_success"],
            "requisition_id": req_id,
            "sample_id": sample_id,
            "test_id": test_id
        }
    except NotFoundError as e:
        log_info(f"Not found error in remove_test_from_sample: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except AccessDeniedError as e:
        log_warning(f"Access denied in remove_test_from_sample: {str(e)} - User: {current_user.user_id}, Role: {current_user.role}")
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        log_warning(f"Validation error in remove_test_from_sample: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except StateError as e:
        log_error(f"State error in remove_test_from_sample: {str(e)}", exc_info=True)
        raise HTTPException(status_code=409, detail=str(e))
    except Exception as e:
        log_error(f"Error removing test {test_id} from sample {sample_id} in requisition {req_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ERROR_MESSAGES["requisition"]["remove_test_failed"].format(error=str(e))
        )