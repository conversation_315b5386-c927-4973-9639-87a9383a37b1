/*
 * Staff Table
 * Displaying laboratory staff information
 */
import { Table } from '../../core/components/table.js';
import { createTableConfig } from '../../core/helpers/table-helpers.js';
import { LabApi } from '../../core/services/lab-api.js';
import { STAFF_CONFIGS } from '../../core/config/staff-configs.js';

class StaffTable extends Table {
    constructor(options = {}) {
        super(createTableConfig(STAFF_CONFIGS, 'staffTable', 'staff', options));
    }

    // Fetch staff data from API with error handling
    async fetchData() {
        try {
            const staffData = await LabApi.getLabStaff();
            return staffData || [];
        } catch (error) {
            console.error('Failed to fetch staff data:', error);
            throw error;
        }
    }
}

// Factory function and default instance
export const createStaffTable = (options = {}) => new StaffTable(options);
export const staffTable = createStaffTable();
