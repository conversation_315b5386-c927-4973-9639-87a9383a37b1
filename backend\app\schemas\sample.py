"""
Schemas for Samples

This script input schemas for Samples. These schema help define different Samples

This file contains the following schemas
    - SampleBase - The schema used for when an user is trying to add a sample to the DB
"""

from pydantic import BaseModel, Field, UUID4, field_validator, root_validator, ValidationInfo
from typing import Optional, List
from datetime import datetime, timezone

#This class represents the kind of data we need from the user
class SampleBase(BaseModel):
    sms_number: str = Field(..., description="The SMS number related to the ID")
    external_reference: Optional[str] = None
    station_id: Optional[str] = None
    depth_top: Optional[float] = None
    depth_bottom: Optional[float] = None
    sample_type: Optional[str] = None

    @field_validator("depth_bottom")
    def validate_depth_bottom(cls, v: Optional[float], info: ValidationInfo) -> Optional[float]:
        if v is not None:
            depth_top = info.data.get("depth_top")
            if depth_top is not None and v < depth_top:
                raise ValueError("Depth Bottom cannot be less than Depth Top")
        return v