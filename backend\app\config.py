import os
from pydantic_settings import BaseSettings
from pydantic import AnyHttpUrl, field_validator, ConfigDict
from typing import List, Union


class Settings(BaseSettings):
    # Project info
    PROJECT_NAME: str = "LIMS API"

    # Database
    DATABASE_URL: str
    TEST_DATABASE_URL: str | None = None

    # CORS
    BACKEND_CORS_ORIGINS: Union[str, List[AnyHttpUrl]] = []

    # JWT (for development only)
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 300

    # Default admin account
    DEFAULT_ADMIN_EMAIL: str
    DEFAULT_ADMIN_PASSWORD: str

    #MSAL LOGIN
    TENANT_ID: str
    CLIENT_ID: str

    TENANT_ID_TEST: str
    CLIENT_ID_TEST: str

    model_config = ConfigDict(
        env_file=".env",
        extra="ignore"
    )

    @field_validator("DATABASE_URL")
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("DATABASE_URL must be set")
        return v

    @field_validator("TEST_DATABASE_URL")
    def set_test_database_url(cls, v):
        # If TEST_DATABASE_URL is not set, use DATABASE_URL with a test suffix
        if not v and os.getenv("TESTING") == "1":
            database_url = os.getenv("DATABASE_URL", "")
            if database_url.endswith("/"):
                database_url = database_url[:-1]
            return f"{database_url}_test"
        return v
    
    @field_validator("TENANT_ID")
    def validate_tenant_id(cls, v):
        if os.getenv("TESTING") == "1":
            if not os.getenv("TENANT_ID_TEST"):
                return os.getenv("TENANT_ID_TEST")
        
        if not v:
            raise ValueError("TENANT_ID must be set")
        return v
    
    @field_validator("CLIENT_ID")
    def validate_client_id(cls, v):
        if os.getenv("TESTING") == "1":
            if not os.getenv("CLIENT_ID_TEST"):
                return os.getenv("CLIENT_ID_TEST")
        if not v:
            raise ValueError("CLIENT_ID must be set")
        return v

    @field_validator("DEFAULT_ADMIN_EMAIL")
    def validate_admin_email(cls, v):
        if not v:
            raise ValueError("DEFAULT_ADMIN_EMAIL must be set")
        return v

    @field_validator("DEFAULT_ADMIN_PASSWORD")
    def validate_admin_password(cls, v):
        if not v:
            raise ValueError("DEFAULT_ADMIN_PASSWORD must be set")
        return v

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, value: Union[str, List[str]]):
        if isinstance(value, str):
            # Remove any surrounding quotes that might be in the .env file
            value = value.strip('"\'')
            # Split the comma-separated string into a list of strings
            return [i.strip() for i in value.split(",") if i.strip()]
        elif isinstance(value, list):
            # Ensure all items are strings
            return [str(item) for item in value]
        return []


settings = Settings()
