/*
 * Table Helper Functions
 * Utility functions for table creation and configuration
 */
import { GLOBAL_CONFIGS } from '../config/global-configs.js';

/**
 * Create standardized table configuration
 * Eliminates boilerplate code in table constructors
 */
export function createTableConfig(entityConfig, tableConfigKey, entityKey, options = {}) {
    const tableConfig = entityConfig.ui[tableConfigKey];
    if (!tableConfig) {
        throw new Error(`Table config '${tableConfigKey}' not found in entity configuration`);
    }

    const messageConfigMaps = options.messageConfigMaps || {
        global: GLOBAL_CONFIGS,
        [entityKey]: entityConfig
    };

    return {
        containerId: options.containerId || tableConfig.containerId,
        tableId: options.tableId || tableConfig.tableId,
        columns: tableConfig.columns,
        entityTerms: tableConfig.entityTerms,
        tableOptions: tableConfig.tableOptions,
        bulkCheckboxColumn: tableConfig.bulkCheckboxColumn,
        messageConfigMaps: messageConfigMaps,
        entityFormatters: options.entityFormatters || {},
        ...options
    };
}
