"""
API responses for all work dealing with user/lab relations (aka Staff)

Endpoints:
    POST /staff #Create a new relation between an existing staff and a new role
    PUT /staff #Edit an existing staff
    DELETE /staff/{staff_id} #Delete a current staff
"""
import sqlalchemy
from fastapi import APIRouter, Depends, HTTPException, Path, Body
from sqlalchemy.orm import Session
from typing import List, Optional

from ..database import get_db
from ..core.dependencies import enforce_role_selection
from ..schemas.user import User
from ..common.constants import UserRole, ERROR_MESSAGES
from ..schemas.staff import StaffBase, StaffCreate, StaffType
from ..crud.staff import create_staff, edit_staff, delete_staff, get_staff_by_staff_id
from ..core.logging import log_error, log_warning, log_info


router = APIRouter()

@router.post("/")
async def create_new_staff(
    new_staff: StaffCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Creates a new staff relation of an existing user 
    
    - **new_staff**: an existing userID and a new role for the user to be

    ### Response Model
    - If successful, returns the staff_id, lab_id, user_id and role of the new staff that was just made

    ### Errors:
    - 403 Forbidden: Only Lab Admins can add a staff to their lab
    - 403 Already exists: If this staff already exists in the db
    - 422 Unprocessable Entity: Not a possible role
    - 500 Internal Server Error: If an unexpected error occurs
    """
    if current_user.role != UserRole.LAB_ADMIN:
        log_error(f"User Permission error", exc_info=True)
        raise HTTPException(status_code=403, detail=ERROR_MESSAGES["staff"]["not_authorized"])    
    
    try:
        return create_staff(db, new_staff, current_user.lab)
    except ValueError as e:
        log_error(f"User Error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=403, detail="User Error - "+str(e))

    except sqlalchemy.exc.IntegrityError as e:
        log_error(f"ForeignKeyViolation: {str(e)}", exc_info=True)
        raise HTTPException(status_code=422, detail=ERROR_MESSAGES["staff"]["missing_id"])

    except Exception as e:
        log_error(f"Server Error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Server Error - "+str(e))

@router.put("/")
async def edit_existing_staff(
    new_staff: StaffType,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Edits staff relation of an existing user 
    
    - **new_staff**: an existing staff_ID, user_id and lab_id and a new role for the staff to become

    ### Response Model
    - If successful, returns the staff_id, lab_id, user_id and role of the staff

    ### Errors:
    - 403 Forbidden: Only Lab Admins can add a staff to their lab
    - 403 Already exists: If this staff already exists in the db
    - 422 Unprocessable Entity: Not a possible role
    - 500 Internal Server Error: If an unexpected error occurs
    """
    if current_user.role != UserRole.LAB_ADMIN:
        log_error(f"User Permission error", exc_info=True)
        raise HTTPException(status_code=403, detail=ERROR_MESSAGES["staff"]["not_authorized"])

    if current_user.lab != new_staff.lab_id:
        log_error(f"User is trying to edit lab {new_staff.lab_id} when they belong to {current_user.lab}", exc_info=True)
        raise HTTPException(status_code=403, detail=ERROR_MESSAGES["staff"]["not_authorized"])
    
    try:
        #Function to change roll
        return edit_staff(db, new_staff)
    
    except ValueError as e:
        log_error(f"User Error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=403, detail="User Error - "+str(e))
    except Exception as e:
        log_error(f"Server Error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Server Error - "+str(e))
    

@router.delete("/{staff_id}")
async def delete_existing_staff(
    staff_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(enforce_role_selection)
):
    """
    Deletes staff relation of an existing user 
    
    - **staff_id**: an existing staff_ID to delete

    ### Response Model
    - If successful, returns a message saying it was successful

    ### Errors:
    - 403 Forbidden: Only Lab Admins can add a staff to their lab
    - 403 Already exists: If this staff already exists in the db
    - 422 Unprocessable Entity: Not a possible role
    - 500 Internal Server Error: If an unexpected error occurs
    """
    if current_user.role != UserRole.LAB_ADMIN:
        log_error(f"Must be a lab admin to delete a staff", exc_info=True)
        raise HTTPException(status_code=403, detail=ERROR_MESSAGES["staff"]["not_authorized"])
    
    try:
        to_be_deleted_staff = get_staff_by_staff_id(db, staff_id)
        #check to see if staff exists
        if not to_be_deleted_staff:
            raise ValueError(ERROR_MESSAGES["staff"]["not_found"])
        
        if current_user.lab != to_be_deleted_staff.lab_id or to_be_deleted_staff.role == UserRole.SCIENTIST:
            log_error(f"User is trying to delete a staff {staff_id}. This staff does not belong to the users own lab or is a scientist")
            raise ValueError(ERROR_MESSAGES["staff"]["not_authorized"])

        return delete_staff(db, staff_id)

    except ValueError as e:
        log_error(f"User Error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=403, detail="User Error - "+str(e))
    except Exception as e:
        log_error(f"Server Error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Server Error - "+str(e))
