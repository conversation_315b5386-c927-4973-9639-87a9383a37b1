/*
 * Authentication logic (sign-in or sign-off) for header and log out logic
 */

import { GLOBAL_CONFIGS } from "../config/global-configs.js";
import { redirectToPageByKey } from "../helpers/navigation-helpers.js";

$(document).ready(function() {
    const role = localStorage.getItem("role")
    const token = localStorage.getItem("access_token")

    let navFile = "/components/en/navbars/default.html"

    if (role === GLOBAL_CONFIGS.application.roles.SCIENTIST){
        navFile = "/components/en/navbars/scientist-bar.html"
    }
    else if (role === GLOBAL_CONFIGS.application.roles.LAB_PERSONNEL){
        navFile = "/components/en/navbars/personnel-bar.html"
    }
    else if (role === GLOBAL_CONFIGS.application.roles.LAB_ADMIN){
        navFile = "/components/en/navbars/admin-bar.html"
    }
    else if (token != null){
        navFile = "/components/en/navbars/auth-bar.html"
    }

    document.getElementById("navbar").setAttribute("data-wb-ajax", `{ "url": "${navFile}", "type": "replace" }`)
    $(document).trigger("wb-init.wb-ajax")

    //Sign out set up -> Basically add  the sign out when the bar is finished loading
    $(document).on("wb-contentupdated.wb ajax-fetched.wb", function(){
        if ($("#navbar").length){
            //If there is a sign out button, then add sign out functionality
            if(document.getElementById("sign-out")){
                document.getElementById("sign-out").addEventListener("click", async function (e) {
                    e.preventDefault()
                    localStorage.clear()
                    sessionStorage.clear()

                    redirectToPageByKey('login', 0)
                    //DO NOT DO MSAL SIGN OUT -> IT WILL SIGN THEM OUT OF MIRCOSOFT
                })

            }
        }

    })
    //data-wb-ajax

});
