from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, ForeignKey, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.sql import func
import uuid
from .base import Base
from app.common.constants import SampleTestStatus
from .requisition_sample import RequisitionSample

class RequisitionSampleTest(Base):
    __tablename__ = "requisition_sample_tests"

    req_sample_test_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    req_sample_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey('requisition_samples.req_sample_id'), nullable=False)
    test_type_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey('test_types.test_type_id'), nullable=False)
    status: Mapped[str] = mapped_column(
        #Enum('submitted', 'in_progress', "complete", name='test_status', values_callable=lambda x: [e.value for e in x]),
        Enum(SampleTestStatus, name='test_status', values_callable=lambda x: [e.value for e in x]),
        default=SampleTestStatus.SUBMITTED
    )
    processed_by: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey('users.user_id'))
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())

    requisition_sample = relationship("RequisitionSample", back_populates="tests")
    test_type = relationship("TestType")
    processed_user = relationship("User", foreign_keys=[processed_by])

    @hybrid_property
    def requisition_id(self) -> uuid.UUID:
        return self.requisition_sample.requisition_id if self.requisition_sample else None

    @requisition_id.setter
    def requisition_id(self, value):
        # This is a pass-through setter since the actual value is stored in requisition_sample
        pass

    @hybrid_property
    def sample_id(self) -> uuid.UUID:
        return self.requisition_sample.sample_id if self.requisition_sample else None

    @sample_id.setter
    def sample_id(self, value):
        # This is a pass-through setter since the actual value is stored in requisition_sample
        pass