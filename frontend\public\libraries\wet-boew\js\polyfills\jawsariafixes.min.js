/*!
 * Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 * v4.0.85 - 2025-02-20
 *
 */
/*!
 *
 * Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW)
 * wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html
 *
 * Version: @wet-boew-build.version@
 *
 */
!function(e){"use strict";for(var t,i,r,a=e("h1, h2, h3, h4, h5, h6").get(),l=e("nav, section, article, aside, main").get(),h=l.length,n=0;n!==h;n+=1)for(t=r=l[n];null!==(t=t.firstElementChild||t.firstChild);)if(/h[1-6]/i.test(t.nodeName)){0===(i=t.id).length&&(t.id=i="wb-h-aria-"+n),r.setAttribute("aria-labelledby",i);break}for(h=a.length,n=0;n!==h;n+=1)(t=a[n]).setAttribute("role","heading"),t.setAttribute("aria-level",t.nodeName.substring(1))}(jQuery);
//# sourceMappingURL=jawsariafixes.min.js.map