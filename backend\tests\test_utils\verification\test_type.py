"""
Test type verification helpers for API response testing.
"""

from typing import Any, Dict, Optional, List
from tests.test_utils.verification.base import EntityVerifier


class TestTypeVerifier(EntityVerifier):
    """Verifier for test type entity responses"""

    COMMON_FIELDS = ["test_type_id", "name", "description", "is_active", "lab_id"]
    DATETIME_FIELDS = ["created_at", "updated_at"]
    OPTIONAL_FIELDS = ["created_at", "updated_at", "lab_id", "description"]
    TYPE_VALIDATIONS = {
        "test_type_id": str,
        "name": str,
        "description": str,
        "is_active": bool,
        "lab_id": str
    }

    @staticmethod
    def verify_batch_update_response(response: Dict[str, Any], expected_count: int) -> None:
        """
        Verify simplified batch update response structure.

        Args:
            response: The batch update response to verify
            expected_count: Expected number of updated items
        """
        # Verify required fields exist
        required_fields = ["message", "updated_count"]
        for field in required_fields:
            assert field in response, f"Missing required field: {field}"

        # Verify updated count
        assert response["updated_count"] == expected_count, f"Expected {expected_count} updates, got {response['updated_count']}"

        # Verify message indicates success
        assert isinstance(response["message"], str), "Message should be a string"
        assert "successfully" in response["message"].lower(), "Message should indicate success"