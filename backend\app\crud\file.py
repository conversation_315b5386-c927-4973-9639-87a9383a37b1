import logging
from uuid import UUI<PERSON>

from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional, Type, Any

from ..models import File
from ..models.file import File
from ..schemas.file import FileCreate
from ..common.constants import UserRole, ERROR_MESSAGES
from ..common.exceptions import NotFoundError, AccessDeniedError, ValidationError
from ..core.logging import log_info, log_error, log_warning

def upload_file(
        db: Session,
        file: FileCreate,
        user_role: UserRole
) -> File:
    """
    Upload a file with role-based access control.
    
    Args:
        db (Session): Database session
        file (FileCreate): File data to upload
        user_role (UserRole): Role of the user performing the action
        
    Returns:
        File: The uploaded file object
        
    Raises:
        ValidationError: If file data is invalid or upload fails
    """
    log_info(f"Uploading new file with name: {file.file_name}")
    try:
        file_data = file.model_dump()
        file_data.setdefault('requisition_id', None)
        file_data.setdefault('req_sample_id', None)
        file_data.setdefault('req_sample_test_id', None)

        # Ensure uploaded_by is included
        if 'uploaded_by' not in file_data:
            log_warning("Upload failed: uploaded_by field is required")
            raise ValidationError(ERROR_MESSAGES["file"]["uploaded_by_required"])

        log_info(f"Creating file record with data: {file_data}")
        db_file = File(**file_data, is_published=False)
        db.add(db_file)
        db.commit()
        db.refresh(db_file)
        log_info(f"Successfully uploaded file with id: {db_file.file_id}")
        return db_file
    except ValidationError:
        log_warning(f"Validation error during file upload: {file.file_name}")
        raise
    except Exception as e:
        db.rollback()
        log_error(f"Error uploading file {file.file_name}: {str(e)}")
        raise ValidationError(ERROR_MESSAGES["file"]["upload_failed"].format(error=str(e)))

def download_file(
    db: Session,
    file_id: UUID,
    user_role: UserRole
) -> Optional[File]:
    """
    Get file for download with role-based access control.
    
    Args:
        db (Session): Database session
        file_id (UUID): ID of the file to download
        user_role (UserRole): Role of the user performing the action
        
    Returns:
        Optional[File]: The file if found and accessible, None otherwise
        
    Raises:
        NotFoundError: If file does not exist
        AccessDeniedError: If user does not have permission to download the file
        ValidationError: If an unexpected error occurs
    """
    log_info(f"Attempting to download file with id: {file_id} by user with role: {user_role}")
    try:
        # First check if file exists at all
        file = db.query(File).filter(File.file_id == file_id).first()
        
        if not file:
            log_warning(f"Download failed: File not found: {file_id}")
            raise NotFoundError(ERROR_MESSAGES["file"]["not_found"])

        # Then check access permissions
        if user_role == UserRole.SCIENTIST and not file.is_published:
            log_warning(f"Download denied: User with role {user_role} attempted to download unpublished file: {file_id}")
            raise AccessDeniedError(ERROR_MESSAGES["file"]["unauthorized_download"])

        log_info(f"Successfully retrieved file for download: {file_id}")
        return file
        
    except NotFoundError:
        raise
    except AccessDeniedError:
        raise
    except Exception as e:
        log_error(f"Error retrieving file {file_id} for download: {str(e)}")
        raise ValidationError(ERROR_MESSAGES["file"]["download_failed"].format(error=str(e)))

def get_file(
        db: Session,
        file_id: UUID,
        user_role: UserRole
) -> Optional[File]:
    """
    Get file metadata by ID with role-based access control.
    
    Args:
        db (Session): Database session
        file_id (UUID): ID of the file to retrieve
        user_role (UserRole): Role of the user performing the action
        
    Returns:
        Optional[File]: The file if found and accessible, None otherwise
        
    Raises:
        NotFoundError: If file does not exist or user does not have access
    """
    log_info(f"Attempting to get file with id: {file_id} by user with role: {user_role}")
    try:
        query = db.query(File).filter(File.file_id == file_id)

        log_info(f"SQL Query: {query.statement}")

        # Scientists can only access published files
        if user_role == UserRole.SCIENTIST:
            log_info(f"Applying scientist role filter for file access: {file_id}")
            query = query.filter(File.is_published == True)

        file = query.first()
        log_info(f"Query result for file {file_id}: {file}")
        
        if not file:
            log_warning(f"File not found or access denied: {file_id} for user role: {user_role}")
            raise NotFoundError(ERROR_MESSAGES["file"]["not_found"])
            
        log_info(f"Successfully retrieved file metadata: {file_id}")
        return file
    except NotFoundError:
        raise
    except Exception as e:
        log_error(f"Error retrieving file metadata {file_id}: {str(e)}")
        raise ValidationError(ERROR_MESSAGES["file"]["get_failed"].format(error=str(e)))


def publish_file(
        db: Session,
        file_id: UUID,
        is_published: bool,
        user_role: UserRole
) -> Type[File] | None:
    """
    Update file publish status with role-based access control.
    
    Args:
        db (Session): Database session
        file_id (UUID): ID of the file to update
        is_published (bool): New publish status
        user_role (UserRole): Role of the user performing the action
        
    Returns:
        Type[File] | None: The updated file if successful
        
    Raises:
        AccessDeniedError: If user does not have permission to publish files
        NotFoundError: If file does not exist
        ValidationError: If an unexpected error occurs
    """
    log_info(f"Attempting to {'publish' if is_published else 'unpublish'} file with id: {file_id} by user with role: {user_role}")
    
    if user_role != UserRole.LAB_ADMIN:
        log_warning(f"Publish denied: User with role {user_role} attempted to change publish status of file: {file_id}")
        raise AccessDeniedError(ERROR_MESSAGES["file"]["unauthorized_publish"])

    db_file = db.query(File).filter(File.file_id == file_id).first()
    if not db_file:
        log_warning(f"Publish failed: File not found: {file_id}")
        raise NotFoundError(ERROR_MESSAGES["file"]["not_found"])

    try:
        log_info(f"Changing publish status of file {file_id} from {db_file.is_published} to {is_published}")
        db_file.is_published = is_published
        db.commit()
        db.refresh(db_file)
        log_info(f"Successfully updated publish status for file {file_id} to {is_published}")
        return db_file
    except Exception as e:
        db.rollback()
        log_error(f"Error publishing file {file_id}: {str(e)}")
        raise ValidationError(ERROR_MESSAGES["file"]["publish_failed"].format(error=str(e)))

def list_files(
    db: Session,
    user_role: UserRole,
    requisition_id: Optional[UUID] = None,
    req_sample_id: Optional[UUID] = None,
    req_sample_test_id: Optional[UUID] = None
) -> list[Type[File]]:
    """
    List files with optional filters and role-based access control.
    
    Args:
        db (Session): Database session
        user_role (UserRole): Role of the user performing the action
        requisition_id (Optional[UUID]): Filter by requisition ID
        req_sample_id (Optional[UUID]): Filter by sample ID
        req_sample_test_id (Optional[UUID]): Filter by sample test ID
        
    Returns:
        list[Type[File]]: List of files matching the criteria
        
    Raises:
        ValidationError: If filter parameters are invalid or an unexpected error occurs
    """
    log_info(f"Listing files for user with role: {user_role} with filters: requisition_id={requisition_id}, req_sample_id={req_sample_id}, req_sample_test_id={req_sample_test_id}")
    
    query = db.query(File)

    # Apply role-based access control
    if user_role == UserRole.SCIENTIST:
        log_info("Applying scientist role filter for file listing")
        query = query.filter(File.is_published == True)

    try:
        if requisition_id:
            log_info(f"Applying requisition filter: {requisition_id}")
            query = query.filter(File.requisition_id == requisition_id)
            
        if req_sample_id:
            log_info(f"Applying sample filter: {req_sample_id}")
            query = query.filter(File.req_sample_id == req_sample_id)
            
        if req_sample_test_id:
            log_info(f"Applying sample test filter: {req_sample_test_id}")
            query = query.filter(File.req_sample_test_id == req_sample_test_id)
    except ValueError as e:
        log_warning(f"Invalid UUID in file listing filters: {str(e)}")
        raise ValidationError(ERROR_MESSAGES["file"]["invalid_uuid"])
    except Exception as e:
        log_error(f"Error listing files: {str(e)}")
        raise ValidationError(ERROR_MESSAGES["file"]["list_failed"].format(error=str(e)))

    # Sort by timestamp (newest first)
    # Use COALESCE to prioritize updated_at, fall back to created_at if updated_at is NULL
    query = query.order_by(func.coalesce(File.updated_at, File.created_at).desc())
    log_info("Applied default sorting by timestamp (newest first)")
    
    result = query.all()
    log_info(f"Successfully retrieved {len(result)} files")
    log_info(f"SQL Query: {query.statement}")
    
    return result


def delete_file(db: Session, file_id: UUID, user_role: UserRole):
    """
    Delete file record from database with role-based access control.
    
    Args:
        db (Session): Database session
        file_id (UUID): ID of the file to delete
        user_role (UserRole): Role of the user performing the action
        
    Returns:
        dict: Success message and file ID
        
    Raises:
        NotFoundError: If file does not exist
        ValidationError: If an unexpected error occurs
    """
    log_info(f"Attempting to delete file with id: {file_id} by user with role: {user_role}")
    
    # Check if user has permission to delete files based on role
    if user_role == UserRole.SCIENTIST:
        log_warning(f"Delete denied: User with role {user_role} attempted to delete file: {file_id}")
        raise AccessDeniedError(ERROR_MESSAGES["file"]["unauthorized_delete"])
    
    file = db.query(File).filter(File.file_id == file_id).first()

    if not file:
        log_warning(f"Delete failed: File not found: {file_id}")
        raise NotFoundError(ERROR_MESSAGES["file"]["not_found"])

    try:
        db.delete(file)
        db.commit()
        log_info(f"Successfully deleted file: {file_id}")
        return {
            "message": ERROR_MESSAGES["file"]["delete_success"],
            "file_id": str(file_id)
        }
    except Exception as e:
        db.rollback()
        log_error(f"Error deleting file {file_id}: {str(e)}")
        raise ValidationError(ERROR_MESSAGES["file"]["delete_failed"].format(error=str(e)))
