"""
External verification functions that hit other endpoints

This script helps us verify requisitions and samples through external means

This file contains the following functions:
    - LSA_verify : This function verifies the LSA_id exists and returns info about it
    - Sample_verify: This function verifies if the sms_id exists and return info about it

"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
import requests
import traceback
from app.core.logging import log_error

def LSA_verify(lsa_id):
    """
    Check if LSA number is valid
    This function gets LSA information from https://sln.nrcan.gc.ca/slnws/GetLsaForRefno.ashx?refno=${lsa_id}
    - **las_id**: The LSA number
    ### Returns Model:
    - Returns the response from the LSA - Example:
        [
            {
                "RefNo": "24-4-007",
                "ProjectProponent": "Wouter Bleeker",
                "LabProponent": "<PERSON> Polivchuk",
                "StudyTitle": "Sample preparation and petrographic characterization for TGI-6 projects - OS2-Bleeker",
                "Status": "SLNApproved",
                "Program": "TARGETED GEOSCIENCE INITIATIVE [TGI]",
                "LabGroup": "MPP",
                "Labs": "Mineralogy"
            }
        ]
    
    ### Errors:
    - ValueError : If LSA number cannot be found
    - Request Exception : If request to LSA server cannot be done
    - Exception : Something else has gone wrong
    """
    LSA_URL = f"https://sln.nrcan.gc.ca/slnws/GetLsaForRefno.ashx?refno={lsa_id}"
    try:
        response = requests.get(LSA_URL, headers={
            "accept": "application/json",
            "Content-Type": "application/json"
        })
        if response.status_code == 404:
            raise ValueError("LSA could not be found, please enter an existing LSA number.")
        elif not response.ok:
            response.raise_for_status()
        
        #otherwise its ok!
        return response.json()
            
    except requests.exceptions.RequestException as e:
        #Throw to be handled by the caller
        log_error(f"Error with request to LSA: {str(e)}")
        raise e
    except Exception as e:
        traceback.print_exc()
        log_error(f"Server Error: {str(e)}")
        raise e


def Sample_verify(c_number):
    """
    Check if c-number is valid
    This function gets sms information from to https://s-cal-dbsrv1.gscc.nrcan.gc.ca:8443/ords/sms_api/validate/${c_number}
    - **c_number**: The c_number that is connected to SMS info
    ### Returns Model:
    - Returns the response from SMS - Example:
        {
            "results": [
            {
                "c_number": "C-592081",
                "sample_name": "14-13-78-16W6 2287.7m",
                "sample_type": "Core",
                "collection_date": "17 Dec 2014",
                "collector": "Omid Haeri Ardakani",
                "latitude": null,
                "longitude": null,
                "study_group": "Shale Reservoir Characterization - GNES"
            }
        ]
    }
    
    ### Errors:
    - ValueError : If c-number cannot be found
    - Request Exception : If request to SMS server cannot be done
    - Exception : Something else has gone wrong
    """
    SMS_URL = f"https://s-cal-dbsrv1.gscc.nrcan.gc.ca:8443/ords/sms_api/validate/CNumber/{c_number}"
    try:
        response = requests.get(SMS_URL, headers={
            "accept": "application/json",
            "Content-Type": "application/json"
        }, verify=False)
        if response.status_code == 404:
            raise ValueError("SMS c-number could not be found, please enter an existing SMS c-number.")
        elif not response.ok:
            response.raise_for_status()
        
        #otherwise its ok!
        return response.json()
            
    except requests.exceptions.RequestException as e:
        #Throw to be handled by the caller
        log_error(f"Error with request to SMS: {str(e)}")
        raise e
    except Exception as e:
        log_error(f"Server Error: {str(e)}")
        raise e