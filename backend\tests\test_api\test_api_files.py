"""
Test suite for the File API endpoints.

This class contains tests for the following endpoints:
- GET /files/list:
    - test_list_files
    - test_list_files_scientist_sees_only_published
    - test_list_files_pagination
    - test_list_files_with_filters
- GET /files/{file_id}:
    - test_get_file_metadata
    - test_get_file_metadata_scientist_published
    - test_get_file_metadata_scientist_unpublished
    - test_get_file_metadata_not_found
- POST /files/:
    - test_upload_file_success
    - test_upload_file_scientist_forbidden
    - test_upload_file_invalid_requisition_id
- GET /files/{file_id}/download:
    - test_download_file_success
    - test_download_file_unauthorized_scientist
    - test_download_file_not_found
- PUT /files/{file_id}/publish:
    - test_publish_file
    - test_publish_file_scientist_forbidden
    - test_publish_file_not_found
- DELETE /files/{file_id}:
    - test_delete_file_admin_allowed
    - test_delete_file_lab_personnel_allowed
    - test_delete_file_scientist_forbidden
    - test_delete_file_not_found
- Input validation:
    - test_invalid_file_id_format
"""

import uuid
import pytest
from io import BytesIO
from unittest.mock import AsyncMock

from app.models.file import File
import app.api.files as files_module
from app.common.constants import ERROR_MESSAGES
from tests.test_utils.helpers import TestHelpers
from tests.test_utils.constants import FILE_TEST_CONSTANTS
from tests.test_utils.verification import FileVerifier, verify_api_error_response


class TestFileAPI:
    @pytest.fixture(autouse=True)
    def setup(self, file_test_setup):
        self.db = file_test_setup["db"]
        self.storage_service = file_test_setup["storage_service"]
        self.client = file_test_setup["client"]

    #
    # GET /files/list endpoint tests
    #
    def test_list_files(self, admin_token, test_file):
        """Test listing files"""
        response = self.client.get(
            "/files/list",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        files = response.json()
        assert len(files) > 0
        
        for file in files:
            FileVerifier.verify_common_fields(file)
        # Verify test file is in the list
        matching_files = [f for f in files if f["file_id"] == str(test_file.file_id)]
        assert len(matching_files) == 1
        FileVerifier.verify_response(matching_files[0], {
            "file_id": str(test_file.file_id),
            "file_name": test_file.file_name
        })

    def test_list_files_scientist_sees_only_published(
        self, user_token, test_file, test_published_file
    ):
        """Test that scientists can only see published files"""
        response = self.client.get(
            "/files/list",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        assert response.status_code == 200
        files = response.json()
        
        # Verify each file
        for file in files:
            FileVerifier.verify_common_fields(file)
            FileVerifier.verify_response(file, {
                "is_published": True
            })
        
        # Verify specific files
        file_ids = [f["file_id"] for f in files]
        assert str(test_published_file.file_id) in file_ids
        assert str(test_file.file_id) not in file_ids

    def test_list_files_pagination(self, admin_token, db, test_admin):
        """Test listing files with pagination"""
        # Create multiple test files
        for file_data in FILE_TEST_CONSTANTS["TEST_FILES"]["batch"]:
            TestHelpers.Files.create_test_file(db, test_admin.user_id, file_data)
        
        response = self.client.get(
            "/files/list",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        files = response.json()
        assert len(files) >= 5
        
        # Verify each file in the response
        for file in files:
            FileVerifier.verify_common_fields(file)

    def test_list_files_with_filters(
        self, admin_token, test_requisition, test_file, db
    ):
        """Test listing files with query parameters"""
        test_file.requisition_id = test_requisition.req_id
        db.commit()
        db.refresh(test_file)
        
        response = self.client.get(
            "/files/list",
            params={
                "requisition_id": str(test_requisition.req_id)
            },
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        files = response.json()
        assert isinstance(files, list)
        assert len(files) > 0
        
        # Verify files have the correct requisition_id
        for file in files:
            FileVerifier.verify_response(file, {
                "requisition_id": str(test_requisition.req_id)
            })

    #
    # GET /files/{file_id} endpoint tests
    #
    def test_get_file_metadata(self, admin_token, test_file):
        """Test getting file metadata"""
        response = self.client.get(
            f"/files/{test_file.file_id}",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        data = response.json()
        FileVerifier.verify_common_fields(data)
        FileVerifier.verify_response(data, {
            "file_id": str(test_file.file_id),
            "file_name": test_file.file_name
        })

    def test_get_file_metadata_scientist_published(
        self, user_token, test_published_file
    ):
        """Test scientist can access published file metadata"""
        response = self.client.get(
            f"/files/{test_published_file.file_id}",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        assert response.status_code == 200
        data = response.json()
        FileVerifier.verify_common_fields(data)
        FileVerifier.verify_response(data, {
            "file_id": str(test_published_file.file_id),
            "is_published": True
        })

    def test_get_file_metadata_scientist_unpublished(
        self, user_token, test_file
    ):
        """Test scientist cannot access unpublished file metadata"""
        response = self.client.get(
            f"/files/{test_file.file_id}",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        verify_api_error_response(response, 404, ERROR_MESSAGES["file"]["not_found"])

    def test_get_file_metadata_not_found(self, admin_token):
        """Test getting metadata for non-existent file"""
        non_existent_id = str(uuid.uuid4())
        response = self.client.get(
            f"/files/{non_existent_id}",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        verify_api_error_response(response, 404, ERROR_MESSAGES["file"]["not_found"])

    #
    # POST /files/ endpoint tests
    #
    def test_upload_file_success(self, admin_token, file_test_data, monkeypatch):
        """Test successful file upload"""
        # Create a mock storage service
        mock_storage = AsyncMock()
        mock_storage.upload_file = AsyncMock(return_value="mocked_storage_id")
        mock_storage.get_token = AsyncMock(return_value="fake_token")
        
        # Patch the storage_service singleton
        import app.api.files as files_module
        monkeypatch.setattr(files_module, "storage_service", mock_storage)
        
        upload_data = TestHelpers.Files.create_upload_data(
            file_content=file_test_data["file_content"],
            file_name=file_test_data["file_name"],
            file_type=file_test_data["file_type"]
        )
        
        response = self.client.post(
            "/files/",
            files=upload_data["files"],
            data=upload_data["data"],
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        data = response.json()
        FileVerifier.verify_common_fields(data)
        FileVerifier.verify_response(data, {
            "file_name": file_test_data["file_name"],
            "file_type": file_test_data["file_type"],
            "file_size": int(upload_data["data"]["file_size"])
        })
        
        # Verify the mock was called
        mock_storage.upload_file.assert_called_once()

    def test_upload_file_scientist_forbidden(self, user_token, mock_storage, monkeypatch):
        """Test that scientists cannot upload files"""
        # Create a mock storage service
        mock_storage = AsyncMock()
        mock_storage.upload_file = AsyncMock(return_value="mocked_storage_id")
        mock_storage.get_token = AsyncMock(return_value="fake_token")
        
        # Patch the storage_service singleton
        import app.api.files as files_module
        monkeypatch.setattr(files_module, "storage_service", mock_storage)
        
        file_content = b"test content"
        upload_data = TestHelpers.Files.create_upload_data(
            file_content=file_content,
            file_name="test.txt",
            file_type="text/plain"
        )
        
        response = self.client.post(
            "/files/",
            files=upload_data["files"],
            data=upload_data["data"],
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        verify_api_error_response(response, 403, ERROR_MESSAGES["file"]["unauthorized_upload"])
        
        # Verify that storage service was never called
        mock_storage.upload_file.assert_not_called()

    def test_upload_file_invalid_requisition_id(self, admin_token, mock_storage):
        """Test file upload with invalid requisition ID"""
        file_content = b"test content"
        files = {
            "file": ("test.txt", BytesIO(file_content), "text/plain")
        }
        data = {
            "requisition_id": "invalid-uuid",
            "file_type": "text/plain",
            "file_size": str(len(file_content))
        }
        
        response = self.client.post(
            "/files/",
            files=files,
            data=data,
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        verify_api_error_response(response, 422, ERROR_MESSAGES["validation"]["invalid_uuid"])

    #
    # GET /files/{file_id}/download endpoint tests
    #
    def test_download_file_success(self, admin_token, test_file, monkeypatch):
        """Test successful file download"""
        # Create a mock storage service with the required async methods
        mock_storage = AsyncMock()
        mock_storage.download_file = AsyncMock(return_value=b"mocked_file_content")
        mock_storage.get_token = AsyncMock(return_value="fake_token")
        
        # Patch the storage_service singleton in the files module
        import app.api.files as files_module
        monkeypatch.setattr(files_module, "storage_service", mock_storage)
        
        response = self.client.get(
            f"/files/{test_file.file_id}/download",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        # Verify response
        assert response.status_code == 200
        assert response.content == b"mocked_file_content"
        assert "Content-Disposition" in response.headers
        
        # Verify the mock was called with correct parameters
        mock_storage.download_file.assert_called_once_with(test_file.storage_id)

    def test_download_file_unauthorized_scientist(
        self, user_token, test_file, mock_storage
    ):
        """Test scientist cannot download unpublished file"""
        response = self.client.get(
            f"/files/{test_file.file_id}/download",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        verify_api_error_response(response, 403, ERROR_MESSAGES["file"]["unauthorized_download"])

    def test_download_file_not_found(self, admin_token):
        """Test downloading non-existent file"""
        non_existent_id = str(uuid.uuid4())
        response = self.client.get(
            f"/files/{non_existent_id}/download",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["file"]["not_found"])

    #
    # PUT /files/{file_id}/publish endpoint tests
    #
    def test_publish_file(self, admin_token, test_file):
        """Test publishing a file"""
        response = self.client.put(
            f"/files/{test_file.file_id}/publish",
            json={"is_published": True},
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        assert response.status_code == 200
        data = response.json()
        FileVerifier.verify_common_fields(data)
        FileVerifier.verify_response(data, {
            "file_id": str(test_file.file_id),
            "is_published": True
        })

    def test_publish_file_scientist_forbidden(self, user_token, test_file):
        """Test that scientists cannot publish files"""
        response = self.client.put(
            f"/files/{test_file.file_id}/publish",
            json={"is_published": True},
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        verify_api_error_response(response, 403, ERROR_MESSAGES['file']['unauthorized_publish'])

    def test_publish_file_not_found(self, admin_token):
        """Test publishing non-existent file"""
        non_existent_id = str(uuid.uuid4())
        response = self.client.put(
            f"/files/{non_existent_id}/publish",
            json={"is_published": True},
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["file"]["not_found"])

    #
    # DELETE /files/{file_id} endpoint tests
    #
    def test_delete_file_admin_allowed(self, admin_token, test_file, monkeypatch):
        """Test admin can delete files"""
        # Create a mock storage service
        mock_storage = AsyncMock()
        mock_storage.delete_file = AsyncMock(return_value=True)
        mock_storage.get_token = AsyncMock(return_value="fake_token")
        
        # Patch the storage_service singleton
        import app.api.files as files_module
        monkeypatch.setattr(files_module, "storage_service", mock_storage)
        
        response = self.client.delete(
            f"/files/{test_file.file_id}",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == ERROR_MESSAGES["file"]["delete_success"]
        assert data["file_id"] == str(test_file.file_id)
        
        # Verify the mock was called correctly
        mock_storage.delete_file.assert_called_once_with(test_file.storage_id)
        
        # Verify file was deleted from database
        deleted_file = self.db.query(File).filter(File.file_id == test_file.file_id).first()
        assert deleted_file is None

    def test_delete_file_lab_personnel_allowed(self, lab_personnel_token, test_file, monkeypatch):
        """Test successful file deletion by lab personnel"""
        # Create a mock storage service
        mock_storage = AsyncMock()
        mock_storage.delete_file = AsyncMock(return_value=True)
        mock_storage.get_token = AsyncMock(return_value="fake_token")
        
        # Patch the storage_service singleton
        import app.api.files as files_module
        monkeypatch.setattr(files_module, "storage_service", mock_storage)
        
        response = self.client.delete(
            f"/files/{test_file.file_id}",
            headers=TestHelpers.Auth.get_headers(lab_personnel_token)
        )
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == ERROR_MESSAGES["file"]["delete_success"]
        assert data["file_id"] == str(test_file.file_id)
        
        # Verify the mock was called correctly
        mock_storage.delete_file.assert_called_once_with(test_file.storage_id)
        
        # Verify file was deleted from database
        deleted_file = self.db.query(File).filter(File.file_id == test_file.file_id).first()
        assert deleted_file is None

    def test_delete_file_scientist_forbidden(self, user_token, test_file):
        """Test that scientists cannot delete files"""
        response = self.client.delete(
            f"/files/{test_file.file_id}",
            headers=TestHelpers.Auth.get_headers(user_token)
        )
        
        verify_api_error_response(response, 403, ERROR_MESSAGES["file"]["unauthorized_delete"])

    def test_delete_file_not_found(self, admin_token):
        """Test deleting non-existent file"""
        non_existent_id = str(uuid.uuid4())
        response = self.client.delete(
            f"/files/{non_existent_id}",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        
        verify_api_error_response(response, 404, ERROR_MESSAGES["file"]["not_found"])

    #
    # Input validation tests
    #
    def test_invalid_file_id_format(self, admin_token):
        """Test operations with invalid file ID format"""
        invalid_id = "not-a-uuid"
        
        # Test get metadata
        response = self.client.get(
            f"/files/{invalid_id}",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        verify_api_error_response(response, 422, ERROR_MESSAGES["validation"]["invalid_uuid"])
        
        # Test publish
        response = self.client.put(
            f"/files/{invalid_id}/publish",
            json={"is_published": True},
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        verify_api_error_response(response, 422, ERROR_MESSAGES["validation"]["invalid_uuid"])
        
        # Test download
        response = self.client.get(
            f"/files/{invalid_id}/download",
            headers=TestHelpers.Auth.get_headers(admin_token)
        )
        verify_api_error_response(response, 422, ERROR_MESSAGES["validation"]["invalid_uuid"])
