"""
Requisition CRUD Operations

This module contains all database operations related to requisitions in the LIMS system.
It provides functions for creating, reading, updating, and deleting requisitions,
as well as managing the relationship between requisitions and samples.

Key Functionality:
- Create and update requisitions
- Retrieve requisitions with various filters
- Manage samples associated with requisitions
- Handle requisition status transitions

All operations include access control based on user roles and permissions.
"""
from datetime import datetime, timezone

from app.models.requisition_sample_test import RequisitionSampleTest
from sqlalchemy.orm import Session, Query
from sqlalchemy import and_, func
from typing import List, Optional, Dict
from ..models.requisition import Requisition
from ..models.requisition_sample import RequisitionSample
from ..models.labs import Lab
from ..schemas.requisition import (
    RequisitionCreate, 
    RequisitionUpdate, 
    Requisition as RequisitionSchema,
    RequisitionReadWithDetails,
    RequisitionSampleTestResponse, 
    RequisitionSampleTestUpdate
)
from ..common.constants import UserRole, ERROR_MESSAGES, RequisitionSampleStatus, RequisitionStatus, PriorityLevel, SampleTestStatus
from ..models.sample import Sample  # Import Sample model
from ..common.exceptions import NotFoundError, AccessDeniedError, ValidationError, StateError
from ..core.logging import log_error, log_warning, log_info

def get_requisitions_query(
    db: Session,
    user_id: str,
    user_role: UserRole,
    lab_id: Optional[str] = None,
    statuses: Optional[List[str]] = None
) -> Query:
    """Build the base query for requisitions with filters"""
    log_info(f"Building requisition query for user: {user_id} with role: {user_role}")
    query = db.query(Requisition, Lab.lab_name.label("lab_name")) \
        .outerjoin(Lab, Requisition.lab_id == Lab.lab_id)

    # Filter based on user role
    if user_role == UserRole.SCIENTIST:
        log_info(f"Applying scientist-specific filter for user: {user_id}")
        query = query.filter(Requisition.submitted_by == user_id)
    elif user_role == UserRole.LAB_ADMIN:
        # Lab admin must have lab_id to access requisitions
        if not lab_id:
            log_warning(f"Lab admin {user_id} has no lab_id, which is required.")
            raise StateError(ERROR_MESSAGES["requisition"]["unauthorized_lab"])
        log_info(f"Applying lab filter for lab admin: {lab_id}")
        query = query.filter(Requisition.lab_id == lab_id)
    elif user_role == UserRole.LAB_PERSONNEL:
        # Lab personnel must have lab_id to access requisitions
        if not lab_id:
            log_warning(f"Lab personnel {user_id} has no lab_id, which is required.")
            raise StateError(ERROR_MESSAGES["requisition"]["unauthorized_lab"])
        log_info(f"Applying lab filter for lab personnel: {lab_id}")
        query = query.filter(Requisition.lab_id == lab_id)
    else:
        log_error(f"Unhandled user role: {user_role} for user {user_id}")
        raise ValueError(ERROR_MESSAGES["auth"]["unauthorized"].format(role=user_role))

    # Apply status filter if provided
    if statuses:
        log_info(f"Applying status filter: {statuses}")
        query = query.filter(Requisition.status.in_(statuses))

    # Don't show archived requisitions by default
    log_info("Excluding archived requisitions")
    query = query.filter(Requisition.is_archived == False)

    # Sort by timestamp (newest first)
    # Use COALESCE to prioritize updated_at, fall back to created_at if updated_at is NULL
    query = query.order_by(func.coalesce(Requisition.updated_at, Requisition.created_at).desc())
    log_info("Applied default sorting by timestamp (newest first)")

    return query

def get_requisitions(
        db: Session,
        user_id: str,
        user_role: UserRole,
        lab_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
        statuses: Optional[List[str]] = None
) -> List[RequisitionReadWithDetails]:
    """Get paginated requisitions based on filters"""
    log_info(f"Retrieving requisitions for user: {user_id} with role: {user_role}, status filter: {statuses}")
    try:
        if skip < 0:
            log_error(f"Invalid skip value: {skip}")
            raise ValidationError(ERROR_MESSAGES["validation"]["skip_negative"])
        if limit < 1:
            log_error(f"Invalid limit value: {limit}")
            raise ValidationError(ERROR_MESSAGES["validation"]["limit_positive"])

        query = get_requisitions_query(db, user_id, user_role, lab_id, statuses)

        results = query.offset(skip).limit(limit).all()

        # Construct the response to include lab_name and req_name
        response = []
        for requisition, lab_name in results:
            # Extract attributes from requisition object, excluding SQLAlchemy internal properties
            # This is more dynamic than listing each attribute individually
            req_dict = {k: v for k, v in requisition.__dict__.items() if not k.startswith('_')}
            
            # Add the additional fields
            req_dict["lab_name"] = lab_name
            req_dict["req_name"] = f"{requisition.lsa_number} - {requisition.project_name}" if requisition.project_name else requisition.lsa_number
            response.append(RequisitionReadWithDetails.model_validate(req_dict))
        return response
    except ValidationError:
        raise
    except Exception as e:
        log_error(f"Error fetching requisitions: {str(e)}")
        raise StateError(ERROR_MESSAGES['requisition']['fetch_failed'].format(error=str(e)))

def get_requisition(
        db: Session,
        req_id: str,
        user_id: str,
        user_role: UserRole,
        lab_id: str
) -> Optional[Requisition]:
    """Get a requisition by ID with access control"""
    log_info(f"Retrieving requisition with id: {req_id} for user: {user_id} with role: {user_role}")
    try:
        requisition = db.query(Requisition).filter(Requisition.req_id == req_id).first()
        
        # Return 404 if requisition doesn't exist or if scientist tries to access another user's requisition or lab workers tries to access another labs' req
        if not requisition or (user_role == UserRole.SCIENTIST and str(requisition.submitted_by) != str(user_id)) or (user_role != UserRole.SCIENTIST and str(requisition.lab_id) != str(lab_id)):
            log_info(f"Requisition not found or access denied: {req_id}")
            raise NotFoundError(ERROR_MESSAGES["requisition"]["not_found"])
            
        # Check if scientist tries to access another user's requisition
        if user_role == UserRole.SCIENTIST and str(requisition.submitted_by) != str(user_id):
            log_warning(f"Access denied: Scientist {user_id} attempted to access requisition {req_id} owned by {requisition.submitted_by}")
            raise NotFoundError(ERROR_MESSAGES["requisition"]["not_found"])

        log_info(f"Successfully retrieved requisition: {req_id}")
        return requisition
    except NotFoundError:
        raise
    except Exception as e:
        log_error(f"Error fetching requisition {req_id}: {str(e)}")
        raise StateError(ERROR_MESSAGES['requisition']['fetch_failed'].format(error=str(e)))

def create_requisition(
        db: Session,
        requisition: RequisitionCreate,
        user_id: str,
        user_role: UserRole,
        lab_id: str
) -> Requisition:
    log_info(f"Creating new requisition by user: {user_id} with role: {user_role}")
    if user_role not in [UserRole.SCIENTIST, UserRole.LAB_ADMIN]:
        log_warning(f"Unauthorized requisition creation attempt by user: {user_id} with role: {user_role}")
        raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_create"])

    try:
        # First check if the lab exists
        lab = db.query(Lab).filter(Lab.lab_id == lab_id).first()
        if not lab:
            log_warning(f"Lab {lab_id} does not exist")
            raise StateError(ERROR_MESSAGES['requisition']['create_failed'].format(error="(psycopg2.errors.ForeignKeyViolation)"))

        # Validate priority value if provided
        if requisition.priority and requisition.priority not in PriorityLevel:
            log_warning(f"Invalid priority value in requisition data: {requisition.priority}")
            raise ValidationError(ERROR_MESSAGES["requisition"]["invalid_data"])

        log_info(f"Creating requisition with data: {requisition.model_dump(exclude={'notes'})}")
        db_requisition = Requisition(
            **requisition.model_dump(),
            submitted_by=user_id,
            status=RequisitionStatus.SUBMITTED.value,
            lab_id = lab_id
        )
        
        db.add(db_requisition)
        db.commit()
        db.refresh(db_requisition)
        log_info(f"Successfully created requisition with ID: {db_requisition.req_id}")
        return db_requisition
    except ValidationError:
        raise
    except AccessDeniedError:
        raise
    except Exception as e:
        db.rollback()
        log_error(f"Error creating requisition: {str(e)}")
        raise StateError(ERROR_MESSAGES['requisition']['create_failed'].format(error=str(e)))

def update_requisition(
        db: Session,
        req_id: str,
        requisition: RequisitionUpdate,
        user_id: str,
        user_role: UserRole,
        lab_id: str
):
    """Update a requisition"""
    log_info(f"Updating requisition {req_id} by user: {user_id} with role: {user_role}")
    try:
        db_requisition = db.query(Requisition).filter(Requisition.req_id == req_id).first()

        if not db_requisition:
            log_warning(f"Requisition not found for update: {req_id}")
            raise NotFoundError(ERROR_MESSAGES["requisition"]["not_found"])

        update_data = requisition.model_dump(exclude_unset=True)
        log_info(f"Update data for requisition {req_id}: {update_data}")
        
        # Only admin can archive the requisition - check this before applying any updates
        if "is_archived" in update_data and update_data["is_archived"]:
            if user_role != UserRole.LAB_ADMIN:
                log_warning(f"Non-admin user {user_id} attempted to archive requisition {req_id}")
                raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_archive"])
                
            log_info(f"Archiving requisition {req_id} by admin {user_id}")

        # Check if user has permission to modify requisitions
        if user_role not in [UserRole.LAB_PERSONNEL, UserRole.LAB_ADMIN]:
            log_warning(f"User {user_id} with role {user_role} attempted to modify requisition {req_id}")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])
        
        #We know that they must be LAB personnel OR admin now 
        if lab_id != db_requisition.lab_id:
            log_warning(f"User {user_id} with in lab {lab_id} attempted to modify requisition {req_id}")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])

        # Update the updated_at timestamp
        update_data["updated_at"] = datetime.now(timezone.utc)
        
        for key, value in update_data.items():
            log_info(f"Setting {key} = {value} for requisition {req_id}")
            setattr(db_requisition, key, value)

        try:
            log_info(f"Committing updates to requisition {req_id}")
            db.commit()
            db.refresh(db_requisition)
            log_info(f"Successfully updated requisition {req_id}")
            return db_requisition
        except Exception as e:
            db.rollback()
            log_error(f"Database error during requisition update: {str(e)}")
            raise StateError(ERROR_MESSAGES['requisition']['update_failed'].format(error=str(e)))
    except NotFoundError:
        raise
    except AccessDeniedError:
        raise
    except StateError:
        raise
    except Exception as e:
        log_error(f"Error updating requisition {req_id}: {str(e)}")
        raise StateError(ERROR_MESSAGES['requisition']['update_failed'].format(error=str(e)))

def add_samples_to_requisition(
        db: Session,
        req_id: str,
        sample_ids: List[str],
        user_id: str,
        user_role: UserRole,
        lab_id: str
):
    """Add samples to a requisition"""
    log_info(f"Adding samples to requisition {req_id} by user: {user_id} with role: {user_role}")
    
    try:
        # Check if user has permission to modify requisitions first
        if user_role not in [UserRole.LAB_PERSONNEL, UserRole.LAB_ADMIN]:
            log_warning(f"User {user_id} with role {user_role} attempted to modify requisition {req_id}")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])

        # Then check if requisition exists
        db_requisition = db.query(Requisition).filter(Requisition.req_id == req_id).first()
        if not db_requisition:
            log_warning(f"Requisition not found: {req_id}")
            raise NotFoundError(ERROR_MESSAGES["requisition"]["not_found"])
        
        #Validate that the user is allowed to edit the requisition
        if str(lab_id) != str(db_requisition.lab_id):
            log_warning(f"User {user_id} with in lab {lab_id} attempted to modify requisition {req_id}")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])

        # Validate sample_ids
        if not sample_ids:
            log_warning(f"No sample IDs provided for requisition {req_id}")
            raise ValidationError(ERROR_MESSAGES["requisition"]["missing_sample_ids"])

        # Check for duplicate samples in the request
        if len(set(sample_ids)) != len(sample_ids):
            log_warning(f"Duplicate sample IDs detected in request for requisition {req_id}")
            raise ValidationError(ERROR_MESSAGES["requisition"]["duplicate_samples"])

        # Get all samples
        samples = db.query(Sample).filter(Sample.sample_id.in_(sample_ids)).all()
        if len(samples) != len(sample_ids):
            log_warning(f"Some samples not found for requisition {req_id}")
            raise NotFoundError(ERROR_MESSAGES["sample"]["not_found"])

        # Check if any samples are already in the requisition
        existing_samples = db.query(RequisitionSample).filter(
            RequisitionSample.requisition_id == req_id,
            RequisitionSample.sample_id.in_(sample_ids)
        ).all()

        if existing_samples:
            log_warning(f"Samples already added to requisition {req_id}: {[s.sample_id for s in existing_samples]}")
            raise ValidationError(ERROR_MESSAGES["requisition"]["samples_already_added"])

        # Add samples to requisition
        log_info(f"Adding {len(sample_ids)} samples to requisition {req_id}")
        requisition_samples = []
        for sample_id in sample_ids:
            requisition_sample = RequisitionSample(
                requisition_id=req_id,
                sample_id=sample_id,
                status= RequisitionSampleStatus.SUBMITTED.value
            )
            db.add(requisition_sample)
            requisition_samples.append(requisition_sample)

        try:
            db.commit()
            log_info(f"Successfully added {len(sample_ids)} samples to requisition {req_id}")
            return requisition_samples
        except Exception as e:
            db.rollback()
            log_error(f"Database error adding samples to requisition: {str(e)}")
            raise StateError(ERROR_MESSAGES["requisition"]["add_samples_failed"].format(error=str(e)))

    except NotFoundError:
        raise
    except AccessDeniedError:
        raise
    except ValidationError:
        raise
    except StateError:
        raise
    except Exception as e:
        log_error(f"Error adding samples to requisition: {str(e)}")
        raise StateError(ERROR_MESSAGES["requisition"]["add_samples_failed"].format(error=str(e)))

def list_requisitions_for_lab(
    db: Session,
    lab_id: str,
    user_id: str,
    user_role: UserRole,
    statuses: Optional[List[str]] = None
) -> List[RequisitionReadWithDetails]:
    """List all requisitions for a specific lab, with status filtering."""
    log_info(f"Listing requisitions for lab: {lab_id} with statuses: {statuses}")
    
    # Base query
    query = get_requisitions_query(db, user_id, user_role, lab_id, statuses)
    
    # Execute the query
    results = query.all()
    
    # Process results
    requisitions_with_details = []
    for requisition, lab_name in results:
        req_data = requisition.__dict__
        req_data['lab_name'] = lab_name
        req_data['req_name'] = f"LSA-{requisition.lsa_number}"
        requisitions_with_details.append(RequisitionReadWithDetails.model_validate(req_data))

    return requisitions_with_details

def get_requisition_samples(
        db: Session,
        req_id: str,
        user_id: str,
        user_role: UserRole,
        lab_id: str
) -> List[RequisitionSample]:
    """Get all samples associated with a requisition"""
    log_info(f"Retrieving samples for requisition {req_id} by user: {user_id} with role: {user_role}")
    try:
        # Verify access to requisition - this will also check for lab_id
        log_info(f"Verifying access to requisition {req_id}")
        requisition = get_requisition(db, req_id, user_id, user_role, lab_id)
        if not requisition:
            log_warning(f"Requisition not found or access denied: {req_id}")
            raise NotFoundError(ERROR_MESSAGES["requisition"]["not_found"])
        
        # Query all samples for this requisition
        log_info(f"Querying samples for requisition {req_id}")
        samples = db.query(RequisitionSample).filter(
            RequisitionSample.requisition_id == req_id
        ).all()
        
        if not samples:
            log_info(f"No samples found for requisition {req_id}")
            return []
            
        log_info(f"Successfully retrieved {len(samples)} samples for requisition {req_id}")
        return samples
    except NotFoundError:
        raise
    except Exception as e:
        log_error(f"Error fetching samples for requisition {req_id}: {str(e)}")
        raise StateError(ERROR_MESSAGES['requisition']['fetch_failed'].format(error=str(e)))

def remove_sample_from_requisition(
        db: Session,
        req_id: str,
        sample_id: str,
        user_id: str,
        user_role: UserRole,
        lab_id: str
):
    """Remove a sample from a requisition"""
    log_info(f"Removing sample {sample_id} from requisition {req_id} by user: {user_id} with role: {user_role}")
    try:
        # Check if user has permission to modify requisitions first
        if user_role not in [UserRole.LAB_PERSONNEL, UserRole.LAB_ADMIN]:
            log_warning(f"User {user_id} with role {user_role} attempted to modify requisition {req_id}")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])

        # Then check if requisition exists
        log_info(f"Verifying requisition exists: {req_id}")
        db_requisition = db.query(Requisition).filter(Requisition.req_id == req_id).first()
        if not db_requisition:
            log_warning(f"Requisition not found: {req_id}")
            raise NotFoundError(ERROR_MESSAGES["requisition"]["not_found"])
        
        #We know that they must be LAB personnel OR admin now 
        if lab_id != db_requisition.lab_id:
            log_warning(f"User {user_id} with in lab {lab_id} attempted to modify requisition {req_id}")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])

        # Check if sample exists in requisition
        log_info(f"Checking if sample {sample_id} exists in requisition {req_id}")
        requisition_sample = db.query(RequisitionSample).filter(
            RequisitionSample.requisition_id == req_id,
            RequisitionSample.sample_id == sample_id
        ).first()

        if not requisition_sample:
            log_warning(f"Sample {sample_id} not found in requisition {req_id}")
            raise NotFoundError(ERROR_MESSAGES["sample"]["not_found_in_requisition"])

        try:
            log_info(f"Deleting sample {sample_id} from requisition {req_id}")
            db.delete(requisition_sample)
            db.commit()
            log_info(f"Successfully removed sample {sample_id} from requisition {req_id}")
            return {
                "message": ERROR_MESSAGES["requisition"]["remove_sample_success"],
                "requisition_id": str(req_id),
                "sample_id": str(sample_id)
            }
        except Exception as e:
            db.rollback()
            log_error(f"Database error removing sample from requisition: {str(e)}")
            raise StateError(ERROR_MESSAGES['requisition']['update_failed'].format(error=str(e)))

    except NotFoundError:
        raise
    except AccessDeniedError:
        raise
    except StateError:
        raise
    except Exception as e:
        log_error(f"Error removing sample {sample_id} from requisition {req_id}: {str(e)}")
        raise StateError(ERROR_MESSAGES['requisition']['update_failed'].format(error=str(e)))

##########################
#
##########################
def add_tests_to_requisition_sample(
    db: Session,
    req_id: str,
    sample_id: str,
    test_type_ids: List[str],
    user_id: str,
    user_role: UserRole,
    lab_id: str,
) -> List[RequisitionSampleTest]:
    """Add tests to a sample in a requisition.

    Args:
        db (Session): Database session
        req_id (str): Requisition ID
        sample_id (str): Sample ID
        test_type_ids (List[str]): List of test type IDs to add
        user_id (str): ID of user making the request
        user_role (UserRole): Role of user making the request
        lab_id (str): Lab ID

    Returns:
        List[RequisitionSampleTest]: List of added tests

    Raises:
        ValidationError: If no test type IDs provided
        NotFoundError: If requisition or sample not found
        AccessDeniedError: If user not authorized
        StateError: If test already exists
    """
    log_info(f"Adding tests to sample {sample_id} in requisition {req_id} by user: {user_id} with role: {user_role}")
    
    try:
        if not test_type_ids:
            log_warning("No test type IDs provided")
            raise ValidationError(ERROR_MESSAGES["test"]["no_test_types"])

        # This will raise NotFoundError if requisition doesn't exist or if user doesn't have access
        log_info(f"Verifying requisition access: {req_id}")
        requisition = get_requisition(db, req_id, user_id, user_role, lab_id)

        # Check authorization - only lab personnel and lab admin can add tests
        if user_role == UserRole.SCIENTIST:
            log_warning(f"Scientist {user_id} attempted to add tests to requisition sample")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])

        # Get the requisition sample
        log_info(f"Checking if sample {sample_id} exists in requisition {req_id}")
        req_sample = db.query(RequisitionSample).filter(
            RequisitionSample.requisition_id == req_id,
            RequisitionSample.sample_id == sample_id
        ).first()
        if not req_sample:
            log_warning(f"Sample {sample_id} not found in requisition {req_id}")
            raise NotFoundError(ERROR_MESSAGES["sample"]["not_found_in_requisition"])

        # Check if any of the tests already exist
        log_info(f"Checking for existing tests for sample {sample_id}")
        existing_tests = db.query(RequisitionSampleTest).filter(
            RequisitionSampleTest.req_sample_id == req_sample.req_sample_id,
            RequisitionSampleTest.test_type_id.in_(test_type_ids)
        ).first()
        if existing_tests:
            log_warning(f"Duplicate test types found for sample {sample_id}")
            raise StateError(ERROR_MESSAGES["test"]["already_exists"])

        # Create new test entries
        log_info(f"Creating {len(test_type_ids)} new test entries for sample {sample_id}")
        new_tests = []
        for test_type_id in test_type_ids:
            test = RequisitionSampleTest(
                req_sample_id=req_sample.req_sample_id,
                test_type_id=test_type_id,
                status=SampleTestStatus.SUBMITTED.value
            )
            db.add(test)
            new_tests.append(test)

        db.commit()
        
        # Refresh the tests to get their IDs and add requisition_id and sample_id
        for test in new_tests:
            db.refresh(test)
            # Add requisition_id and sample_id to the test object for verification
            test.requisition_id = req_id
            test.sample_id = sample_id
            
        return new_tests

    except NotFoundError:
        raise
    except AccessDeniedError:
        raise
    except ValidationError:
        raise
    except StateError:
        raise
    except Exception as e:
        db.rollback()
        log_error(f"Error adding tests to sample {sample_id}: {str(e)}")
        raise StateError(ERROR_MESSAGES["test"]["add_tests_failed"].format(error=str(e)))

def get_requisition_sample_tests(
    db: Session,
    req_id: str,
    sample_id: str,
    user_id: str,
    user_role: UserRole,
    lab_id: str
) -> List[RequisitionSampleTest]:
    """Get all tests associated with a sample in a requisition.

    Args:
        db (Session): Database session
        req_id (str): Requisition ID
        sample_id (str): Sample ID
        user_id (str): ID of user making the request
        user_role (UserRole): Role of user making the request

    Returns:
        List[RequisitionSampleTest]: List of tests associated with the sample

    Raises:
        NotFoundError: If requisition or sample not found
        AccessDeniedError: If user not authorized
    """
    log_info(f"Retrieving tests for sample {sample_id} in requisition {req_id} by user: {user_id} with role: {user_role}")
    try:
        # First check if requisition exists
        log_info(f"Verifying requisition exists: {req_id}")
        requisition = db.query(Requisition).filter(Requisition.req_id == req_id).first()
        if not requisition:
            log_warning(f"Requisition not found: {req_id}")
            raise NotFoundError(ERROR_MESSAGES["requisition"]["not_found"])

        # For scientists, check if they own the requisition
        if user_role == UserRole.SCIENTIST and str(requisition.submitted_by) != user_id:
            log_warning(f"Scientist {user_id} attempted to access tests for requisition {req_id} they don't own")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_access"])
        
        # For non scientists, check if they share a lab
        if user_role != UserRole.SCIENTIST and str(lab_id) != str(requisition.lab_id):
            log_warning(f"User {user_id} with in lab {lab_id} attempted to modify requisition {req_id}")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])

        # Get the requisition sample
        log_info(f"Checking if sample {sample_id} exists in requisition {req_id}")
        requisition_sample = db.query(RequisitionSample).filter(
            RequisitionSample.requisition_id == req_id,
            RequisitionSample.sample_id == sample_id
        ).first()

        if not requisition_sample:
            log_warning(f"Sample {sample_id} not found in requisition {req_id}")
            raise NotFoundError(ERROR_MESSAGES["sample"]["not_found_in_requisition"])

        # Get all tests for this sample
        log_info(f"Retrieving tests for sample {sample_id} in requisition {req_id}")
        tests = db.query(RequisitionSampleTest).filter(
            RequisitionSampleTest.req_sample_id == requisition_sample.req_sample_id
        ).all()
        
        # Add requisition_id and sample_id to each test for verification
        for test in tests:
            test.requisition_id = req_id
            test.sample_id = sample_id

        log_info(f"Successfully retrieved {len(tests)} tests for sample {sample_id} in requisition {req_id}")
        return tests

    except NotFoundError:
        raise
    except AccessDeniedError:
        raise
    except Exception as e:
        log_error(f"Error getting tests for sample {sample_id} in requisition {req_id}: {str(e)}")
        raise StateError(ERROR_MESSAGES["test"]["get_tests_failed"].format(error=str(e)))

def update_requisition_sample_test(
    db: Session,
    req_id: str,
    sample_id: str,
    test_id: str,
    test_update: RequisitionSampleTestUpdate,
    user_id: str,
    user_role: UserRole,
    lab_id: str
) -> RequisitionSampleTest:
    """Update a test's status in a requisition sample.

    Args:
        db (Session): Database session
        req_id (str): Requisition ID
        sample_id (str): Sample ID
        test_id (str): Test ID to update
        test_update (RequisitionSampleTestUpdate): Updated test data
        user_id (str): ID of user making the request
        user_role (UserRole): Role of user making the request

    Returns:
        RequisitionSampleTest: Updated test

    Raises:
        NotFoundError: If test not found
        AccessDeniedError: If user not authorized
        ValidationError: If invalid status transition
    """
    log_info(f"Updating test {test_id} for sample {sample_id} in requisition {req_id} by user: {user_id} with role: {user_role}")
    
    try:
        # Check if user has permission to modify tests
        if user_role not in [UserRole.LAB_PERSONNEL, UserRole.LAB_ADMIN]:
            log_warning(f"User {user_id} with role {user_role} attempted to modify test status")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])

        # Get the requisition sample test
        log_info(f"Checking if sample {sample_id} exists in requisition {req_id}")
        requisition_sample = db.query(RequisitionSample).filter(
            RequisitionSample.requisition_id == req_id,
            RequisitionSample.sample_id == sample_id
        ).first()

        if not requisition_sample:
            log_warning(f"Sample {sample_id} not found in requisition {req_id}")
            raise NotFoundError(ERROR_MESSAGES["requisition"]["sample_not_found"])
        
        #We need to check if the user should have access to this functionality
        requisition = db.query(Requisition).filter(Requisition.req_id == req_id).first()
        if not requisition or str(lab_id) != str(requisition.lab_id):
            log_warning(f"User {user_id} with in lab {lab_id} attempted to modify requisition {req_id}")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])

        log_info(f"Retrieving test {test_id} for sample {sample_id}")
        test = db.query(RequisitionSampleTest).filter(
            RequisitionSampleTest.req_sample_id == requisition_sample.req_sample_id,
            RequisitionSampleTest.req_sample_test_id == test_id
        ).first()

        if not test:
            log_warning(f"Test {test_id} not found for sample {sample_id} in requisition {req_id}")
            raise NotFoundError(ERROR_MESSAGES["test"]["not_found"])

        # Update test status
        log_info(f"Updating test {test_id} status to: {test_update.status}")
        test.status = test_update.status
        test.processed_by = user_id
        
        # If status is completed, set completed_at
        if test_update.status == SampleTestStatus.COMPLETE:
            log_info(f"Setting completed_at timestamp for test {test_id}")
            test.completed_at = datetime.now(timezone.utc)

        db.commit()
        db.refresh(test)
        return test

    except NotFoundError:
        raise
    except AccessDeniedError:
        raise
    except Exception as e:
        db.rollback()
        log_error(f"Error updating test status: {str(e)}")
        raise StateError(ERROR_MESSAGES["test"]["update_failed"].format(error=str(e)))
    
def remove_test_from_requisition_sample(
    db: Session,
    req_id: str,
    sample_id: str,
    test_id: str,
    user_id: str,
    user_role: UserRole,
    lab_id: str
) -> RequisitionSampleTestResponse:
    """
    Remove a test from a requisition sample.

    Args:
        db (Session): Database session
        req_id (str): Requisition ID
        sample_id (str): Sample ID
        test_id (str): Test ID to remove
        user_id (str): ID of user making the request
        user_role (UserRole): Role of user making the request

    Raises:
        AccessDeniedError: If user not authorized
        NotFoundError: If test or requisition not found
        StateError: If test cannot be removed
    """
    log_info(f"Removing test {test_id} from sample {sample_id} in requisition {req_id} by user: {user_id} with role: {user_role}")
    try:
        # Check if user has permission to modify tests
        if user_role not in [UserRole.LAB_PERSONNEL, UserRole.LAB_ADMIN]:
            log_warning(f"User {user_id} with role {user_role} attempted to remove test")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])

        # First check if requisition exists
        log_info(f"Verifying requisition exists: {req_id}")
        requisition = db.query(Requisition).filter(Requisition.req_id == req_id).first()
        if not requisition:
            log_warning(f"Requisition not found: {req_id}")
            raise NotFoundError(ERROR_MESSAGES["requisition"]["not_found"])
        
        if str(lab_id) != str(requisition.lab_id):
            log_warning(f"User {user_id} with in lab {lab_id} attempted to modify requisition {req_id}")
            raise AccessDeniedError(ERROR_MESSAGES["requisition"]["unauthorized_modify"])

        # Then get the requisition sample
        log_info(f"Checking if sample {sample_id} exists in requisition {req_id}")
        requisition_sample = db.query(RequisitionSample).filter(
            RequisitionSample.requisition_id == req_id,
            RequisitionSample.sample_id == sample_id
        ).first()

        if not requisition_sample:
            log_warning(f"Sample {sample_id} not found in requisition {req_id}")
            raise NotFoundError(ERROR_MESSAGES["sample"]["not_found_in_requisition"])

        # Get the sample test
        log_info(f"Retrieving test {test_id} for sample {sample_id}")
        sample_test = db.query(RequisitionSampleTest).filter(
            RequisitionSampleTest.req_sample_id == requisition_sample.req_sample_id,
            RequisitionSampleTest.req_sample_test_id == test_id
        ).first()

        if not sample_test:
            log_warning(f"Test {test_id} not found for sample {sample_id} in requisition {req_id}")
            raise NotFoundError(ERROR_MESSAGES["test"]["not_found"])

        # Remove the test
        db.delete(sample_test)
        db.commit()
        return {
            "message": ERROR_MESSAGES["requisition"]["remove_test_success"],
            "requisition_id": str(req_id),
            "sample_id": str(sample_id),
            "test_id": str(test_id)
        }

    except NotFoundError:
        raise
    except AccessDeniedError:
        raise
    except StateError:
        raise
    except Exception as e:
        db.rollback()
        log_error(f"Error removing test: {str(e)}")
        raise StateError(ERROR_MESSAGES["requisition"]["remove_test_failed"].format(error=str(e)))