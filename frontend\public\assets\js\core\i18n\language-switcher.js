/*
    Javascript to create the href reference for switching the language
*/
$(document).ready(function() {
    function updateLanguageSwitcher() {
        var languageSwitcher = document.getElementById("language-switcher");
        if (!languageSwitcher) {
            // Element not found - will be handled by AJAX events when header loads
            return;
        }

        var currentUrl = window.location.pathname;

        if (currentUrl.includes("/en/")) {
            languageSwitcher.href = currentUrl.replace("/en/", "/fr/");
        }
        else if (currentUrl.includes("/fr/")) {
            languageSwitcher.href = currentUrl.replace("/fr/", "/en/");
        }
    }

    // Initial call to set up language switcher
    updateLanguageSwitcher();

    // Run again when content is loaded via WET-BOEW AJAX
    $(document).on("wb-contentupdated.wb ajax-fetched.wb", function() {
        // Ensure this runs ONLY when the header is updated
        if ($("#wb-lng").length) {
            updateLanguageSwitcher();
        }
    });
});
