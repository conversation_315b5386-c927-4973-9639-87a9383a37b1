<!-- English Header for all pages. More info here: https://wet-boew.github.io/GCWeb/sites/header/header-docs-en.html-->
<html lang="en">
	<!-- GCWeb and WET-BOEW Stylesheets -->
    <link rel="stylesheet" href="/libraries/GCWeb/css/theme.min.css">
	
<header>
	<div id="wb-bnr" class="container">
		<div class="row">
			<!-- Language toggle [version 1.1] -->
			<section id="wb-lng" class="col-xs-3 col-sm-12 pull-right text-right">
				<h2 class="wb-inv">Language selection</h2>
				<ul class="list-inline mrgn-bttm-0">
					<li><a id="language-switcher" lang="fr" hreflang="fr" href="#">
							<span class="hidden-xs" translate="no">Français</span>
							<abbr title="Français" translate="no" class="visible-xs h3 mrgn-tp-sm mrgn-bttm-0 text-uppercase">fr</abbr>
					</a></li>
				</ul>
			</section>
			<!-- Branding [version 1.0] -->
			<div class="brand col-xs-9 col-sm-5 col-md-4" property="publisher" typeof="GovernmentOrganization">
				<a href="https://www.canada.ca/en.html" property="url">
					<img src="https://wet-boew.github.io/themes-dist/GCWeb/GCWeb/assets/sig-blk-en.svg" alt="Government of Canada" property="logo" /><span class="wb-inv"> / <span lang="fr">Gouvernement du Canada</span></span>
				</a>
				<meta property="name" content="Government of Canada">
				<meta property="areaServed" typeof="Country" content="Canada" />
				<link property="logo" href="https://wet-boew.github.io/themes-dist/GCWeb/GCWeb/assets/wmms-blk.svg" />
			</div>
			<!-- Search [version 1.0] -->
		</div>
	</div>
	<hr>
	<!-- Breadcrumbs [version 1.0] -->

</header>
</html>
