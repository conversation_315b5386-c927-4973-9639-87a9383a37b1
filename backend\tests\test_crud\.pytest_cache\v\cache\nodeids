["test_requisition.py::test_create_requisition", "test_requisition.py::test_get_requisitions", "test_requisitions.py::TestRequisitionCRUD::test_archive_requisition", "test_requisitions.py::TestRequisitionCRUD::test_create_requisition_success", "test_requisitions.py::TestRequisitionCRUD::test_get_nonexistent_requisition", "test_requisitions.py::TestRequisitionCRUD::test_get_requisition_by_id", "test_requisitions.py::TestRequisitionCRUD::test_get_requisitions_admin", "test_requisitions.py::TestRequisitionCRUD::test_get_requisitions_scientist", "test_requisitions.py::TestRequisitionCRUD::test_get_requisitions_with_status", "test_requisitions.py::TestRequisitionCRUD::test_update_nonexistent_requisition", "test_requisitions.py::TestRequisitionCRUD::test_update_requisition_success"]